'use client';

import * as React from 'react';
import { ContextMenu as RadixContextMenu } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

const ContextMenu = RadixContextMenu.Root;
const ContextMenuTrigger = RadixContextMenu.Trigger;
const ContextMenuContent = RadixContextMenu.Content;
const ContextMenuItem = RadixContextMenu.Item;

// Simplified implementations for backward compatibility
const ContextMenuGroup = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuPortal = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuSub = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuRadioGroup = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuSubTrigger = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuSubContent = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuCheckboxItem = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuRadioItem = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuLabel = ({ children }: { children: React.ReactNode }) => children;
const ContextMenuSeparator = () => <div className="my-1 h-px bg-muted" />;
const ContextMenuShortcut = ({ children }: { children: React.ReactNode }) => <span className="ml-auto text-xs">{children}</span>;

export {
  ContextMenu,
  ContextMenuTrigger,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuCheckboxItem,
  ContextMenuRadioItem,
  ContextMenuLabel,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuGroup,
  ContextMenuPortal,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuRadioGroup,
};


