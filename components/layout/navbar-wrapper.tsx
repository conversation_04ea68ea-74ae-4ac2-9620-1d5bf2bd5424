import { ClientNavbar } from './client-navbar'
import { getLocale } from '@/lib/locale'
import { Suspense } from 'react'
import { StaticNavbar } from './static-navbar'
import { defaultLocale } from '@/lib/i18n'

// Fallback component for SSG
function NavbarFallback() {
  return <StaticNavbar />;
}

export function NavbarWrapper() {
  // Use default locale during static generation
  let locale = defaultLocale;

  // Only try to get locale when headers are available
  if (typeof window === 'undefined') {
    try {
      // Check if we're in a server environment with headers available
      const { headers } = require('next/headers');
      headers(); // This will throw if not available
      locale = getLocale();
    } catch (error) {
      // During static generation or when headers are not available, use default locale
      locale = defaultLocale;
    }
  }

  return (
    <Suspense fallback={<NavbarFallback />}>
      <ClientNavbar locale={locale} />
    </Suspense>
  );
}