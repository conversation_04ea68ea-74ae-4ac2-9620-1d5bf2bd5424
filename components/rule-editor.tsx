"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  TextField,
  Text,
  TextArea,
  Select,
  Badge,
  Flex
} from "@radix-ui/themes";
import { X, Plus, Code, Settings } from "lucide-react";
import { CodeEditor } from "@/components/ui/code-editor";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Rule, RulePayload, IDEType, VisibilityType, RuleSection } from "@/lib/store";
import { RuleSectionsList } from "./rule-sections-list";
import { parseRuleContent, formatRuleContent } from "@/lib/rule-sections";

interface RuleEditorProps {
  rule?: Rule;
  onSave: (rule: Partial<RulePayload>) => void;
  onCancel: () => void;
}

export function RuleEditor({ rule, onSave, onCancel }: RuleEditorProps) {
  const [title, setTitle] = useState(rule?.title || "");
  const [description, setDescription] = useState(rule?.description || "");
  const [sections, setSections] = useState<RuleSection[]>([]);
  const [rawContent, setRawContent] = useState(rule?.content || "");
  const [editingMode, setEditingMode] = useState<"simple" | "advanced">("simple");
  const [ideType, setIdeType] = useState(rule?.ideType || "GENERAL");
  const [visibility, setVisibility] = useState(rule?.visibility || "PRIVATE");
  const [tags, setTags] = useState<string[]>(
    rule?.tags.map(t => t.tag.name) || []
  );
  const [newTag, setNewTag] = useState("");

  // Initialize sections and raw content from rule content
  useEffect(() => {
    if (rule?.content) {
      const parsedSections = parseRuleContent(rule.content);
      setSections(parsedSections);
      setRawContent(rule.content);
    } else {
      // Create a default empty section for new rules
      const defaultSections = [{
        id: `section_${Date.now()}`,
        title: 'Section 1',
        content: '',
      }];
      setSections(defaultSections);
      setRawContent('');
    }
  }, [rule?.content]);

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleModeChange = (newMode: "simple" | "advanced") => {
    if (newMode === editingMode) return;

    if (newMode === "simple") {
      // Switching to simple mode: convert sections to raw content
      const formattedContent = formatRuleContent(sections);
      setRawContent(formattedContent);
    } else {
      // Switching to advanced mode: parse raw content into sections
      const parsedSections = parseRuleContent(rawContent);
      setSections(parsedSections);
    }

    setEditingMode(newMode);
  };

  const handleSectionsChange = (newSections: RuleSection[]) => {
    setSections(newSections);
    // Keep raw content in sync when in advanced mode
    if (editingMode === "advanced") {
      const formattedContent = formatRuleContent(newSections);
      setRawContent(formattedContent);
    }
  };

  const handleRawContentChange = (newContent: string) => {
    setRawContent(newContent);
    // Keep sections in sync when in simple mode
    if (editingMode === "simple") {
      const parsedSections = parseRuleContent(newContent);
      setSections(parsedSections);
    }
  };

  const handleSave = () => {
    // Use the current content based on the active mode
    const finalContent = editingMode === "simple" ? rawContent : formatRuleContent(sections);

    onSave({
      title,
      description,
      content: finalContent,
      ideType: ideType as any,
      visibility: visibility as any,
      tags,
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Text as="label" htmlFor="title" size="2" weight="medium">Rule Title</Text>
          <TextField.Root
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter rule title..."
          />
        </div>

        <div>
          <Text as="label" htmlFor="description" size="2" weight="medium">Description</Text>
          <TextArea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe what this rule does..."
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Text as="label" htmlFor="ide-type" size="2" weight="medium">IDE Type</Text>
            <Select.Root value={ideType} onValueChange={(value) => setIdeType(value as IDEType)}>
              <Select.Trigger placeholder="Select IDE type" />
              <Select.Content>
                <Select.Item value="GENERAL">General</Select.Item>
                <Select.Item value="CURSOR">Cursor</Select.Item>
                <Select.Item value="AUGMENT">Augment Code</Select.Item>
                <Select.Item value="WINDSURF">Windsurf</Select.Item>
                <Select.Item value="CLAUDE">Claude</Select.Item>
                <Select.Item value="GITHUB_COPILOT">GitHub Copilot</Select.Item>
                <Select.Item value="GEMINI">Gemini</Select.Item>
                <Select.Item value="OPENAI_CODEX">OpenAI Codex</Select.Item>
                <Select.Item value="CLINE">Cline</Select.Item>
                <Select.Item value="JUNIE">Junie</Select.Item>
                <Select.Item value="TRAE">Trae</Select.Item>
                <Select.Item value="LINGMA">Lingma</Select.Item>
                <Select.Item value="KIRO">Kiro</Select.Item>
                <Select.Item value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</Select.Item>
              </Select.Content>
            </Select.Root>
          </div>

          <div>
            <Text as="label" htmlFor="visibility" size="2" weight="medium">Visibility</Text>
            <Select.Root value={visibility} onValueChange={(value) => setVisibility(value as VisibilityType)}>
              <Select.Trigger placeholder="Select visibility" />
              <Select.Content>
                <Select.Item value="PRIVATE">Private</Select.Item>
                <Select.Item value="PUBLIC">Public</Select.Item>
              </Select.Content>
            </Select.Root>
          </div>
        </div>

        <div>
          <Text as="label" size="2" weight="medium">Tags</Text>
          <div className="flex flex-wrap gap-2 mb-2">
            {tags.map((tag) => (
              <Badge key={tag} variant="soft" className="gap-1">
                {tag}
                <Button
                  variant="ghost"
                  size="1"
                  className="h-auto p-0 w-4 h-4"
                  onClick={() => handleRemoveTag(tag)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <TextField.Root
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
            />
            <Button onClick={handleAddTag} size="1">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Rule Content Section with Mode Toggle */}
        <div className="space-y-4">
          <Flex justify="between" align="center">
            <Text as="label" size="2" weight="medium">Rule Content</Text>
            <ToggleGroup
              type="single"
              value={editingMode}
              onValueChange={(value) => value && handleModeChange(value as "simple" | "advanced")}
              className="bg-gray-50 dark:bg-gray-800 rounded-md p-1"
            >
              <ToggleGroupItem
                value="simple"
                className="flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700"
              >
                <Code className="h-4 w-4" />
                Simple
              </ToggleGroupItem>
              <ToggleGroupItem
                value="advanced"
                className="flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700"
              >
                <Settings className="h-4 w-4" />
                Advanced
              </ToggleGroupItem>
            </ToggleGroup>
          </Flex>

          {editingMode === "simple" ? (
            <div>
              <Text size="1" color="gray" className="mb-2 block">
                Edit your rule content in a simple code editor format
              </Text>
              <CodeEditor
                value={rawContent}
                onChange={handleRawContentChange}
                placeholder="Enter your rule content here..."
                className="min-h-[300px]"
              />
            </div>
          ) : (
            <div>
              <Text size="1" color="gray" className="mb-2 block">
                Edit your rule content using structured sections with metadata
              </Text>
              <RuleSectionsList
                sections={sections}
                onSectionsChange={handleSectionsChange}
              />
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>
          {rule ? "Update Rule" : "Create Rule"}
        </Button>
      </div>
    </div>
  );
}