"use client";

import { Button, DropdownMenu } from "@radix-ui/themes";
import { Share2, Twitter, Link2, Linkedin, Facebook } from "lucide-react";
import { toast } from "sonner";

interface SocialShareButtonProps {
  url: string;
  title: string;
  description?: string;
  size?: "1" | "2" | "3" | "4";
  variant?: "solid" | "soft" | "outline" | "surface" | "ghost";
}

export function SocialShareButton({
  url,
  title,
  description,
  size = "2",
  variant = "outline"
}: SocialShareButtonProps) {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description || "");

  const shareLinks = {
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
  };

  const handleCopyLink = async () => {
    await navigator.clipboard.writeText(url);
    toast.success("Link copied to clipboard");
  };

  const handleNativeShare = async () => {
    if (typeof navigator !== 'undefined' && navigator.share) {
      try {
        await navigator.share({
          title,
          text: description || title,
          url,
        });
      } catch (err) {
        // User cancelled or error - fail silently
      }
    } else {
      handleCopyLink();
    }
  };

  // If native share is available and we're on mobile, use it directly
  if (typeof window !== 'undefined' && typeof navigator !== 'undefined' && 'share' in navigator && window.matchMedia("(max-width: 768px)").matches) {
    return (
      <Button variant={variant} size={size} onClick={handleNativeShare}>
        <Share2 className="h-4 w-4 mr-2" />
        Share
      </Button>
    );
  }

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <Button variant={variant} size={size}>
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        <DropdownMenu.Item onClick={handleCopyLink}>
          <Link2 className="mr-2 h-4 w-4" />
          Copy Link
        </DropdownMenu.Item>
        <DropdownMenu.Item onClick={() => window.open(shareLinks.twitter, '_blank')}>
          <Twitter className="mr-2 h-4 w-4" />
          Share on Twitter
        </DropdownMenu.Item>
        <DropdownMenu.Item onClick={() => window.open(shareLinks.linkedin, '_blank')}>
          <Linkedin className="mr-2 h-4 w-4" />
          Share on LinkedIn
        </DropdownMenu.Item>
        <DropdownMenu.Item onClick={() => window.open(shareLinks.facebook, '_blank')}>
          <Facebook className="mr-2 h-4 w-4" />
          Share on Facebook
        </DropdownMenu.Item>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}