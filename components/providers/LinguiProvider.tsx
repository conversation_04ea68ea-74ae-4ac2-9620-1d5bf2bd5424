'use client'

import { I18nProvider } from '@lingui/react'
import { i18n } from '@lingui/core'
import { ReactNode, useEffect, useState } from 'react'
import { setupI18n, type Locale } from '@/lib/i18n'

interface LinguiProviderProps {
  children: ReactNode
  locale: Locale
}

export function LinguiProvider({ children, locale }: LinguiProviderProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setupI18n(locale)
      .then(() => {
        setIsLoaded(true)
      })
      .catch((error) => {
        console.warn('Failed to setup i18n:', error)
        setIsLoaded(true) // Continue without i18n
      })
  }, [locale])

  if (!isLoaded) {
    return <div>{children}</div> // Render children directly if i18n fails to load
  }

  return (
    <I18nProvider i18n={i18n}>
      {children}
    </I18nProvider>
  )
}