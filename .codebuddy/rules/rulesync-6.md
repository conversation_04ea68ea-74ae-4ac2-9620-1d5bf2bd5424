# rulesync-6

## Metadata

```yaml
title: Bun
id: cmdttkbuq0005l504hizxt6sx
description: 
author: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>
createdAt: 2025-08-02T05:35:55.645Z
updatedAt: 2025-08-02T05:35:55.645Z
ideType: GENERAL
visibility: PUBLIC
tags: []
```

## Rule Type

- **Type**: Project-Specific Rule
- **AI Assistant**: Tencent Cloud CodeBuddy
- **Supported IDEs**: VS Code, JetBrains IDEs

## Usage

This rule will be automatically loaded by <PERSON>Buddy when:
- You are working in this project directory
- <PERSON>Buddy detects the `.codebuddy` configuration


## Development Guidelines

## Bun



### Metadata

- **IDE Type:** GENERAL
- **Author:** <PERSON><PERSON><PERSON> Z<PERSON><PERSON>
- **Created:** 8/2/2025
- **Updated:** 8/2/2025


### Rule Content


Prefer Bun as package manager kits
The bun CLI contains a Node.js-compatible package manager designed to be a dramatically faster replacement for npm, yarn, and pnpm.

---

### CodeBuddy Integration Notes

- <PERSON><PERSON><PERSON><PERSON> will use these guidelines to provide context-aware code suggestions
- The AI assistant will follow these rules when generating code completions
- Use CodeBuddy's chat feature to ask questions about these guidelines
- These rules work with CodeBuddy's MCP (Model Context Protocol) integration