# global

## Metadata

```yaml
title: global
description: 這對處理 CSS 檔案很有幫助
name: global
globs: **.css
```

## Rule Type

- **Type**: Global Rule (Always Active)
- **AI Assistant**: Tencent Cloud CodeBuddy
- **Supported IDEs**: VS Code, JetBrains IDEs

## Usage

This rule will be automatically loaded by Code<PERSON>uddy when:
- CodeBuddy is active in your IDE (always applied)


## Development Guidelines

## Basic AI Rules

### General Instructions
- Respond in a clear, concise manner
- Provide accurate information based on your knowledge
- When unsure, acknowledge limitations rather than guessing
- Maintain a helpful and respectful tone

### Code Generation
- Follow best practices for the requested programming language
- Include comments for complex sections
- Prioritize readability over clever solutions
- Ensure code is secure and follows modern standards

### Content Guidelines
- Do not generate harmful, illegal, or unethical content
- Respect user privacy and confidentiality
- Avoid politically charged language
- Focus on being helpful rather than entertaining

---

### CodeBuddy Integration Notes

- CodeBuddy will use these guidelines to provide context-aware code suggestions
- The AI assistant will follow these rules when generating code completions
- Use CodeBuddy's chat feature to ask questions about these guidelines
- These rules work with CodeBuddy's MCP (Model Context Protocol) integration