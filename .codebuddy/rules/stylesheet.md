# stylesheet

## Metadata

```yaml
description: 這對處理 CSS 檔案很有幫助
globs: **.css
name: stylesheet
title: stylesheet
```

## Rule Type

- **Type**: Project-Specific Rule
- **AI Assistant**: Tencent Cloud CodeBuddy
- **Supported IDEs**: VS Code, JetBrains IDEs

## Usage

This rule will be automatically loaded by CodeBuddy when:
- You are working in this project directory
- CodeBuddy detects the `.codebuddy` configuration


## Development Guidelines

### Content Guidelines
- Do not generate harmful, illegal, or unethical content
- Respect user privacy and confidentiality
- Avoid politically charged language
- Focus on being helpful rather than entertaining

---

### CodeBuddy Integration Notes

- <PERSON><PERSON>uddy will use these guidelines to provide context-aware code suggestions
- The AI assistant will follow these rules when generating code completions
- Use CodeBuddy's chat feature to ask questions about these guidelines
- These rules work with CodeBuddy's MCP (Model Context Protocol) integration