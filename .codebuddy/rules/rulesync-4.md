# rulesync-4

## Metadata

```yaml
title: pull-request
id: cmdtqrjkz0001jt04akgttu8k
description: Describe how to make a  pull request
author: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>
createdAt: 2025-08-02T04:17:34.472Z
updatedAt: 2025-08-02T05:22:17.169Z
ideType: GENERAL
visibility: PUBLIC
tags: []
```

## Rule Type

- **Type**: Project-Specific Rule
- **AI Assistant**: Tencent Cloud CodeBuddy
- **Supported IDEs**: VS Code, JetBrains IDEs

## Usage

This rule will be automatically loaded by <PERSON>Buddy when:
- You are working in this project directory
- <PERSON><PERSON><PERSON><PERSON> detects the `.codebuddy` configuration


## Development Guidelines

## pull-request

> Describe how to make a  pull request


### Metadata

- **IDE Type:** GENERAL
- **Author:** <PERSON><PERSON><PERSON><PERSON><PERSON>
- **Created:** 8/2/2025
- **Updated:** 8/2/2025


### Rule Content

Each time before you make a pull request, run build command , and fix any build typescript typo issue

---

### CodeBuddy Integration Notes

- <PERSON><PERSON><PERSON><PERSON> will use these guidelines to provide context-aware code suggestions
- The AI assistant will follow these rules when generating code completions
- Use CodeBuddy's chat feature to ask questions about these guidelines
- These rules work with CodeBuddy's MCP (Model Context Protocol) integration