/**
 * Shared constants across OnlyRules packages
 */

export const IDE_TYPES = {
  CURSOR: 'CURSOR',
  AUGMENT: 'AUGMENT',
  WINDSURF: 'WINDSURF',
  CLAUDE: 'CLAUDE',
  GITHUB_COPILOT: 'GITHUB_COPILOT',
  GEMINI: 'GEMINI',
  OPENAI_CODEX: 'OPENAI_CODEX',
  CLINE: 'CLINE',
  JUNIE: 'JUNIE',
  TRAE: 'TRAE',
  LINGMA: 'LINGMA',
  KIRO: 'KIRO',
  TENCENT_CODEBUDDY: 'TENCENT_CODEBUDDY',
  GENERAL: 'GENERAL',
} as const;

export const VISIBILITY_TYPES = {
  PRIVATE: 'PRIVATE',
  PUBLIC: 'PUBLIC',
} as const;

export const DEFAULT_RULE_CONTENT = `# Rule Template

## Purpose
Describe what this rule is for.

## Instructions
- Add your specific instructions here
- Be clear and concise
- Use bullet points for better readability

## Examples
\`\`\`
// Example code or usage
\`\`\`
`;

export const APP_CONFIG = {
  NAME: 'OnlyRules',
  DESCRIPTION: 'AI Prompt Management Platform',
  VERSION: '0.1.0',
  GITHUB_URL: 'https://github.com/ranglang/onlyrules.codes',
} as const;
