/**
 * Shared TypeScript types across OnlyRules packages
 */

import { z } from 'zod';
import { IDE_TYPES, VISIBILITY_TYPES } from './constants';

// IDE Types
export type IDEType = keyof typeof IDE_TYPES;

// Visibility Types
export type VisibilityType = keyof typeof VISIBILITY_TYPES;

// Base Rule interface
export interface BaseRule {
  id: string;
  title: string;
  description?: string;
  content: string;
  ideType: IDEType;
  visibility: VisibilityType;
  createdAt: Date;
  updatedAt: Date;
}

// User interface
export interface BaseUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Tag interface
export interface BaseTag {
  id: string;
  name: string;
  color: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Validation schemas
export const CreateRuleSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  description: z.string().max(500, 'Description too long').optional(),
  content: z.string().min(1, 'Content is required'),
  ideType: z.enum(Object.keys(IDE_TYPES) as [string, ...string[]]),
  visibility: z.enum(Object.keys(VISIBILITY_TYPES) as [string, ...string[]]),
  tags: z.array(z.string()).optional(),
});

export type CreateRuleInput = z.infer<typeof CreateRuleSchema>;

// Search and filter types
export interface RuleFilters {
  search?: string;
  tags?: string[];
  ideType?: IDEType;
  visibility?: VisibilityType;
  userId?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}
