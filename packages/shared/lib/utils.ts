/**
 * Shared utility functions across OnlyRules packages
 */

import { clsx, type ClassValue } from 'clsx';
import { IDE_TYPES } from './constants';
import type { IDEType, RuleFilters } from './types';

/**
 * Utility function to combine class names (similar to the existing utils)
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Format IDE type for display
 */
export function formatIDEType(ideType: IDEType): string {
  const formatMap: Record<IDEType, string> = {
    CURSOR: 'Cursor',
    AUGMENT: 'Augment Code',
    WINDSURF: 'Windsurf',
    CLAUDE: 'Claude',
    GITHUB_COPILOT: 'GitHub Copilot',
    GEMINI: 'Gemini',
    OPENAI_CODEX: 'OpenAI Codex',
    CLINE: 'Cline',
    JUNIE: 'Junie',
    TRAE: 'Trae',
    LINGMA: 'Lingma',
    KIRO: '<PERSON><PERSON>',
    TENCENT_CODEBUDDY: 'Tencent CodeBuddy',
    GENERAL: 'General',
  };
  
  return formatMap[ideType] || ideType;
}

/**
 * Get IDE type color for UI display
 */
export function getIDETypeColor(ideType: IDEType): string {
  const colorMap: Record<IDEType, string> = {
    CURSOR: '#007ACC',
    AUGMENT: '#FF6B35',
    WINDSURF: '#00D4AA',
    CLAUDE: '#FF8C00',
    GITHUB_COPILOT: '#24292E',
    GEMINI: '#4285F4',
    OPENAI_CODEX: '#412991',
    CLINE: '#8B5CF6',
    JUNIE: '#EC4899',
    TRAE: '#10B981',
    LINGMA: '#F59E0B',
    KIRO: '#EF4444',
    TENCENT_CODEBUDDY: '#06B6D4',
    GENERAL: '#6B7280',
  };
  
  return colorMap[ideType] || '#6B7280';
}

/**
 * Validate IDE type
 */
export function isValidIDEType(ideType: string): ideType is IDEType {
  return Object.keys(IDE_TYPES).includes(ideType as IDEType);
}

/**
 * Generate a slug from a string
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Truncate text to a specified length
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
}

/**
 * Build query string from rule filters
 */
export function buildFilterQuery(filters: RuleFilters): string {
  const params = new URLSearchParams();
  
  if (filters.search) params.set('search', filters.search);
  if (filters.ideType) params.set('ideType', filters.ideType);
  if (filters.visibility) params.set('visibility', filters.visibility);
  if (filters.userId) params.set('userId', filters.userId);
  if (filters.tags && filters.tags.length > 0) {
    params.set('tags', filters.tags.join(','));
  }
  
  return params.toString();
}

/**
 * Format date for display
 */
export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  
  return formatDate(d);
}
