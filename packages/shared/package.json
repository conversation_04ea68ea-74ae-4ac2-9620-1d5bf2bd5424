{"name": "@onlyrules/shared", "version": "0.1.0", "private": true, "main": "./lib/index.ts", "types": "./lib/index.ts", "exports": {".": "./lib/index.ts", "./constants": "./lib/constants.ts", "./types": "./lib/types.ts", "./utils": "./lib/utils.ts"}, "scripts": {"build": "echo 'Shared package - no build needed for TypeScript'", "type-check": "tsc --noEmit"}, "dependencies": {"clsx": "^2.1.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "24.1.0", "typescript": "^5.8.3"}}