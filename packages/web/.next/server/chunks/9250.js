exports.id=9250,exports.ids=[9250],exports.modules={4474:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,580,23)),Promise.resolve().then(r.t.bind(r,1328,23)),Promise.resolve().then(r.t.bind(r,988,23)),Promise.resolve().then(r.t.bind(r,1554,23)),Promise.resolve().then(r.t.bind(r,3288,23)),Promise.resolve().then(r.t.bind(r,6144,23))},3245:()=>{},3168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>s});var o=r(3326);let s={title:{template:"%s | OnlyRules - AI Code IDE Integration",default:"Supported IDEs | OnlyRules"},description:"Learn how to integrate OnlyRules with your favorite AI-powered code IDE. Step-by-step tutorials for Cursor, VS Code, WebStorm, and more.",keywords:"AI IDE integration, OnlyRules tutorial, Cursor AI, VS Code AI, WebStorm AI, IDE setup, AI coding assistant, prompt rules, code generation",openGraph:{title:"Supported IDEs - OnlyRules Integration",description:"Complete guides for integrating OnlyRules with popular AI-powered IDEs",type:"website"}};function i({children:e}){return o.jsx("div",{className:"min-h-screen",children:e})}}};