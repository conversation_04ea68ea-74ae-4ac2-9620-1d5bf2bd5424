"use strict";exports.id=583,exports.ids=[583],exports.modules={583:(e,s,i)=>{i.d(s,{q:()=>U});var r=i(2395),a=i(8726),t=i(6223),n=i(2751),l=i(3840),c=i(3282),d=i(4202),o=i(5539),m=i(3058),x=i(1459),p=i(5104),u=i(4748),h=i(689),j=i(949),f=i(767),g=i(8214),w=i(6052),b=i(8555),N=i(1607),y=i(3382),v=i(7094),C=i(7972);function k(e,s){let i=C.gA[s].command;return`npx onlyrules -f "${e}" --target ${i}`}function I(e){return`npx onlyrules -f "${e}"`}function D(e,s){return[...e].sort((e,i)=>e.id===s?-1:i.id===s?1:e.name.localeCompare(i.name))}async function E(e){try{return await navigator.clipboard.writeText(e),!0}catch(e){return console.error("Failed to copy to clipboard:",e),!1}}var L=i(6483),$=i(7999),Z=i(4288),A=i.n(Z);function U({rule:e,onEdit:s,onDelete:i,isOwner:Z=!1}){let[U,O]=(0,a.useState)(!1),[T,R]=(0,a.useState)(!1),[B]=(0,t.KO)(C.Ym),F=async()=>{await navigator.clipboard.writeText(e.content),$.Am.success("Rule content copied to clipboard")},S=async()=>{if("PUBLIC"!==e.visibility){$.Am.error("Only public rules can be used with npx command");return}let s=`${window.location.origin}/api/rules/raw?id=${e.id}`,i=I(s),r=await E(i);r?(R(!0),$.Am.success("CLI command copied to clipboard"),setTimeout(()=>R(!1),2e3)):$.Am.error("Failed to copy command to clipboard")},P=async s=>{if("PUBLIC"!==e.visibility){$.Am.error("Only public rules can be used with npx command");return}let i=B.preferredIDEs.find(e=>e.id===s);if(!i)return;let r=`${window.location.origin}/api/rules/raw?id=${e.id}`,a=k(r,i.type),t=await E(a);t?$.Am.success(`${i.name} command copied to clipboard`):$.Am.error("Failed to copy command to clipboard")},z=async()=>{if("PUBLIC"!==e.visibility){$.Am.error("Only public rules can be used with npx command");return}let s=B.preferredIDEs.find(e=>e.id===B.defaultIDE);if(!s)return S();let i=`${window.location.origin}/api/rules/raw?id=${e.id}`,r=k(i,s.type),a=await E(r);a?(R(!0),$.Am.success(`${s.name} command copied to clipboard`),setTimeout(()=>R(!1),2e3)):$.Am.error("Failed to copy command to clipboard")},M=async()=>{if("PUBLIC"===e.visibility){let s=`${window.location.origin}/rules/${e.id}`;await navigator.clipboard.writeText(s),$.Am.success("Share link copied to clipboard")}else if(e.shareToken){let s=`${window.location.origin}/shared/${e.shareToken}`;await navigator.clipboard.writeText(s),$.Am.success("Share link copied to clipboard")}},G=async()=>{if("PUBLIC"!==e.visibility){$.Am.error("Only public rules can be downloaded as MDX");return}try{let s=await fetch(`/api/rules/download?id=${e.id}`);if(!s.ok)throw Error("Failed to download rule");let i=await s.blob(),r=URL.createObjectURL(i),a=document.createElement("a");a.href=r,a.download=`${e.title.toLowerCase().replace(/\s+/g,"-")}.mdx`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),$.Am.success("Rule downloaded as MDX")}catch(e){$.Am.error("Failed to download rule as MDX")}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n.Z,{className:"group hover:shadow-md transition-all duration-200",children:[r.jsx("div",{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:["PUBLIC"===e.visibility?r.jsx(A(),{href:`/rules/${e.id}`,children:(0,r.jsxs)("div",{className:"text-lg font-semibold cursor-pointer hover:text-primary transition-colors inline-flex items-center gap-1",children:[e.title,r.jsx(m.Z,{className:"h-3 w-3 opacity-0 group-hover:opacity-50"})]})}):r.jsx("div",{className:"text-lg font-semibold cursor-pointer hover:text-primary transition-colors",onClick:()=>O(!0),children:e.title}),e.description&&r.jsx("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.description})]}),(0,r.jsxs)(l.fC,{children:[r.jsx(l.xz,{children:r.jsx(c.z,{variant:"ghost",size:"1",className:"opacity-0 group-hover:opacity-100 transition-opacity",children:r.jsx(x.Z,{className:"h-4 w-4"})})}),(0,r.jsxs)(l.VY,{align:"end",children:[(0,r.jsxs)(l.ck,{onClick:()=>O(!0),children:[r.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"View"]}),"PUBLIC"===e.visibility&&r.jsx(l.ck,{asChild:!0,children:(0,r.jsxs)(A(),{href:`/rules/${e.id}`,children:[r.jsx(m.Z,{className:"mr-2 h-4 w-4"}),"Open Rule Page"]})}),(0,r.jsxs)(l.ck,{onClick:F,children:[r.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Copy Content"]}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.Tr,{children:[(0,r.jsxs)(l.fF,{children:[r.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Copy CLI Command"]}),(0,r.jsxs)(l.tu,{children:[B.defaultIDE&&B.preferredIDEs.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.ck,{onClick:z,children:[r.jsx(j.Z,{className:"mr-2 h-4 w-4 fill-current"}),B.preferredIDEs.find(e=>e.id===B.defaultIDE)?.name," (Default)"]}),r.jsx(l.Z0,{})]}),(0,r.jsxs)(l.ck,{onClick:S,children:[r.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Basic Command"]}),B.preferredIDEs.length>0&&(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z0,{}),D(B.preferredIDEs,B.defaultIDE).filter(e=>e.id!==B.defaultIDE).map(e=>(0,r.jsxs)(l.ck,{onClick:()=>P(e.id),children:[r.jsx(f.Z,{className:"mr-2 h-4 w-4"}),e.name]},e.id))]}),0===B.preferredIDEs.length&&r.jsx("div",{className:"px-2 py-1.5 text-xs text-muted-foreground",children:"Add IDE preferences in settings for quick commands"})]})]}),(0,r.jsxs)(l.ck,{onClick:M,children:[r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Share"]})]}),(0,r.jsxs)(l.Tr,{children:[(0,r.jsxs)(l.fF,{children:[r.jsx(w.Z,{className:"mr-2 h-4 w-4"}),"Download"]}),(0,r.jsxs)(l.tu,{children:[(0,r.jsxs)(l.ck,{onClick:()=>{let s={title:e.title,description:e.description,content:e.content,ideType:e.ideType,tags:e.tags.map(e=>e.tag.name)},i=new Blob([JSON.stringify(s,null,2)],{type:"application/json"}),r=URL.createObjectURL(i),a=document.createElement("a");a.href=r,a.download=`${e.title.toLowerCase().replace(/\s+/g,"-")}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),$.Am.success("Rule downloaded as JSON")},children:[r.jsx(b.Z,{className:"mr-2 h-4 w-4"}),"JSON"]}),(0,r.jsxs)(l.ck,{onClick:G,children:[r.jsx(N.Z,{className:"mr-2 h-4 w-4"}),"MDX"]})]})]}),Z&&s&&(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z0,{}),(0,r.jsxs)(l.ck,{onClick:()=>s(e),children:[r.jsx(y.Z,{className:"mr-2 h-4 w-4"}),"Edit"]})]}),Z&&i&&(0,r.jsxs)(l.ck,{onClick:()=>i(e.id),className:"text-destructive",children:[r.jsx(v.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(d.C,{variant:"soft",className:`${(e=>{switch(e){case"CURSOR":return"bg-blue-500";case"AUGMENT":return"bg-green-500";case"WINDSURF":return"bg-purple-500";case"CLAUDE":return"bg-orange-500";case"GITHUB_COPILOT":return"bg-gray-800";case"GEMINI":return"bg-indigo-500";case"OPENAI_CODEX":return"bg-teal-500";case"CLINE":return"bg-pink-500";case"JUNIE":return"bg-yellow-500";case"TRAE":return"bg-red-500";case"LINGMA":return"bg-cyan-500";case"KIRO":return"bg-emerald-500";case"TENCENT_CODEBUDDY":return"bg-violet-500";default:return"bg-gray-500"}})(e.ideType)} text-white`,children:[r.jsx(f.Z,{className:"mr-1 h-3 w-3"}),e.ideType]}),"PUBLIC"===e.visibility&&r.jsx(d.C,{variant:"outline",children:"Public"})]}),e.tags.length>0&&r.jsx("div",{className:"flex flex-wrap gap-1",children:e.tags.map(e=>r.jsx(d.C,{variant:"outline",style:{borderColor:e.tag.color},className:"text-xs",children:e.tag.name},e.tag.id))}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated ",new Date(e.updatedAt).toLocaleDateString()]})]})]}),r.jsx(o.fC,{open:U,onOpenChange:O,children:(0,r.jsxs)(o.VY,{className:"max-w-4xl max-h-[80vh] overflow-hidden",children:[(0,r.jsxs)("div",{children:[r.jsx(o.Dx,{children:e.title}),r.jsx(o.dk,{children:e.description})]}),r.jsx("div",{className:"flex-1 overflow-hidden",children:r.jsx(L.p,{value:e.content,onChange:()=>{},className:"h-[60vh]"})}),"PUBLIC"===e.visibility&&(0,r.jsxs)("div",{className:"mt-4 space-y-3",children:[B.defaultIDE&&B.preferredIDEs.length>0&&r.jsx("div",{className:"p-3 bg-primary/5 border border-primary/20 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("p",{className:"text-sm font-medium flex items-center gap-2",children:[r.jsx(j.Z,{className:"h-4 w-4 fill-current text-primary"}),B.preferredIDEs.find(e=>e.id===B.defaultIDE)?.name," (Default):"]}),r.jsx("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:k(`${window.location.origin}/api/rules/raw?id=${e.id}`,B.preferredIDEs.find(e=>e.id===B.defaultIDE)?.type||"GENERAL")})]}),(0,r.jsxs)(c.z,{size:"1",variant:"outline",onClick:z,className:"ml-2",children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Copy"]})]})}),r.jsx("div",{className:"p-3 bg-muted rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm font-medium",children:"Basic CLI Command:"}),r.jsx("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:I(`${window.location.origin}/api/rules/raw?id=${e.id}`)})]}),(0,r.jsxs)(c.z,{size:"1",variant:"outline",onClick:S,className:"ml-2",children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),T?"Copied!":"Copy"]})]})}),B.preferredIDEs.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("p",{className:"text-sm font-medium",children:"Other IDE Commands:"}),r.jsx("div",{className:"grid gap-2",children:D(B.preferredIDEs,B.defaultIDE).filter(e=>e.id!==B.defaultIDE).map(s=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted/50 rounded text-xs",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[r.jsx(f.Z,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsxs)("span",{className:"font-medium flex-shrink-0",children:[s.name,":"]}),r.jsx("code",{className:"bg-background px-1 py-0.5 rounded truncate",children:k(`${window.location.origin}/api/rules/raw?id=${e.id}`,s.type)})]}),r.jsx(c.z,{size:"1",variant:"ghost",onClick:()=>P(s.id),className:"ml-2 h-6 w-6 p-0",children:r.jsx(u.Z,{className:"h-3 w-3"})})]},s.id))})]})]})]})})]})}},6483:(e,s,i)=>{i.d(s,{p:()=>d});var r=i(2395),a=i(516),t=i(9510),n=i(9319),l=i(8700),c=i(8136);function d({value:e,onChange:s,placeholder:i,className:d}){let{theme:o}=(0,a.F)(),m=[(0,n.eJ)(),c.tk.theme({"&":{fontSize:"14px"},".cm-content":{padding:"16px",minHeight:"200px"},".cm-focused":{outline:"none"},".cm-editor":{borderRadius:"8px"}})];return r.jsx("div",{className:d,children:r.jsx(t.ZP,{value:e,onChange:e=>s(e),placeholder:i,theme:"dark"===o?l.vk:void 0,extensions:m,basicSetup:{lineNumbers:!0,foldGutter:!0,dropCursor:!1,allowMultipleSelections:!1,indentOnInput:!0,bracketMatching:!0,closeBrackets:!0,autocompletion:!0,highlightSelectionMatches:!1}})})}}};