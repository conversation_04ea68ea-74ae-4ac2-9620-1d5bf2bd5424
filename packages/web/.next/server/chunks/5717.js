"use strict";exports.id=5717,exports.ids=[5717],exports.modules={4602:(e,s,a)=>{a.d(s,{x:()=>o});var r=a(8726),t=a(7253),n=a(5351),l=a(3564);let i={as:{type:"enum",values:["div","span"],default:"div"},...l.C,display:{type:"enum",className:"rt-r-display",values:["none","inline","inline-block","block","contents"],responsive:!0}};var u=a(38),m=a(8683),p=a(1260);let o=r.forwardRef((e,s)=>{let{className:a,asChild:l,as:o="div",...c}=(0,u.y)(e,i,m.P,p.E);return r.createElement(l?n.g7:o,{...c,ref:s,className:t("rt-Box",a)})});o.displayName="Box"},9336:(e,s,a)=>{a.d(s,{r:()=>d});var r=a(8726),t=a(7253),n=a(5351),l=a(3564),i=a(666);let u={as:{type:"enum",values:["div","span"],default:"div"},...l.C,display:{type:"enum",className:"rt-r-display",values:["none","inline-grid","grid"],responsive:!0},areas:{type:"string",className:"rt-r-gta",customProperties:["--grid-template-areas"],responsive:!0},columns:{type:"enum | string",className:"rt-r-gtc",customProperties:["--grid-template-columns"],values:["1","2","3","4","5","6","7","8","9"],parseValue:m,responsive:!0},rows:{type:"enum | string",className:"rt-r-gtr",customProperties:["--grid-template-rows"],values:["1","2","3","4","5","6","7","8","9"],parseValue:m,responsive:!0},flow:{type:"enum",className:"rt-r-gaf",values:["row","column","dense","row-dense","column-dense"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(e){return"between"===e?"space-between":e},responsive:!0},...i.c};function m(e){return u.columns.values.includes(e)?e:e?.match(/^\d+$/)?`repeat(${e}, minmax(0, 1fr))`:e}var p=a(38),o=a(8683),c=a(1260);let d=r.forwardRef((e,s)=>{let{className:a,asChild:l,as:i="div",...m}=(0,p.y)(e,u,o.P,c.E);return r.createElement(l?n.g7:i,{...m,ref:s,className:t("rt-Grid",a)})});d.displayName="Grid"}};