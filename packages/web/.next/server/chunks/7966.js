"use strict";exports.id=7966,exports.ids=[7966],exports.modules={7966:(e,a,t)=>{t.d(a,{JO:()=>N,fC:()=>v,xv:()=>p});var l=t(8726),s=t(7253),r=t(120),o=t(2602),i=t(3564),u=t(3929),c=t(7946);let d={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["soft","surface","outline"],default:"soft"},...u.o3,...c.K};var m=t(38),n=t(7507),f=t(1260);let C=l.createContext({}),v=l.forwardRef((e,a)=>{let{size:t=d.size.default}=e,{asChild:o,children:i,className:u,color:c,...n}=(0,m.y)(e,d,f.E),v=o?r.fC:"div";return l.createElement(v,{"data-accent-color":c,...n,className:s("rt-CalloutRoot",u),ref:a},l.createElement(C.Provider,{value:l.useMemo(()=>({size:t}),[t])},i))});v.displayName="Callout.Root";let N=l.forwardRef(({className:e,...a},t)=>l.createElement("div",{...a,className:s("rt-CalloutIcon",e),ref:t}));N.displayName="Callout.Icon";let p=l.forwardRef(({className:e,...a},t)=>{let{size:r}=l.useContext(C);return l.createElement(o.x,{as:"p",size:(0,n.qz)(r,n.uJ),...a,asChild:!1,ref:t,className:s("rt-CalloutText",e)})});p.displayName="Callout.Text"}};