exports.id=9536,exports.ids=[9536],exports.modules={846:(e,s,r)=>{var t={"./en/messages.js":[1388,1388],"./zh-CN/messages.js":[1765,1765],"./zh-HK/messages.js":[2853,2853]};function a(e){if(!r.o(t,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=t[e],a=s[0];return r.e(s[1]).then(()=>r.t(a,23))}a.keys=()=>Object.keys(t),a.id=846,e.exports=a},9049:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6508,23)),Promise.resolve().then(r.bind(r,5046)),Promise.resolve().then(r.bind(r,7029)),Promise.resolve().then(r.bind(r,9294)),Promise.resolve().then(r.bind(r,7425))},8925:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6508,23))},5046:(e,s,r)=>{"use strict";r.r(s),r.d(s,{ClientNavbar:()=>S});var t=r(2395),a=r(4288),n=r.n(a),i=r(8726),l=r(516),d=r(767),o=r(7132),c=r(7638),m=r(9529),u=r(3390),x=r(1155),h=r(8568),p=r(3282),f=r(3840),g=r(8838),j=r(6643),N=r(5353);let v={en:"English","zh-CN":"简体中文","zh-HK":"繁體中文"},b={en:"EN","zh-CN":"简","zh-HK":"繁"};function y({currentLocale:e}){let[s,r]=(0,i.useTransition)(),[a,n]=(0,i.useState)(e);return(0,t.jsxs)(N.fC,{value:a,onValueChange:e=>{n(e),r(()=>{document.cookie=`locale=${e};path=/;max-age=31536000`,window.location.reload()})},disabled:s,children:[t.jsx(N.xz,{className:"w-[60px] px-2 h-8 text-xs",placeholder:b[a]}),t.jsx(N.VY,{children:Object.entries(b).map(([e,s])=>(0,t.jsxs)(N.ck,{value:e,children:[t.jsx("span",{className:"text-xs",children:s}),t.jsx("span",{className:"ml-2 text-xs text-muted-foreground",children:v[e]})]},e))})]})}var I=r(6223),w=r(5539),k=r(4202),D=r(8467),E=r(949),C=r(8373),A=r(546),z=r(7972),O=r(7999);function T({open:e,onOpenChange:s}){let[r,a]=(0,I.KO)(z.Ym),[n,l]=(0,i.useState)(""),o=Object.entries(z.gA).filter(([e])=>!r.preferredIDEs.some(s=>s.type===e)),c=e=>{let s=r.preferredIDEs.find(s=>s.id===e),t=r.preferredIDEs.filter(s=>s.id!==e),n=r.defaultIDE;r.defaultIDE===e&&(n=t.length>0?t[0].id:void 0),a({preferredIDEs:t,defaultIDE:n}),s&&O.Am.success(`${s.name} removed from preferences`)},m=e=>{let s=r.preferredIDEs.find(s=>s.id===e);a({...r,defaultIDE:e}),s&&O.Am.success(`${s.name} set as default IDE`)};return t.jsx(w.fC,{open:e,onOpenChange:s,children:(0,t.jsxs)(w.VY,{className:"max-w-2xl",children:[(0,t.jsxs)(w.Dx,{className:"flex items-center gap-2",children:[t.jsx(x.Z,{className:"h-5 w-5"}),"IDE Preferences"]}),t.jsx(w.dk,{children:"Manage your preferred IDEs for quick command generation. Set a default IDE and add your favorites."}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx("h3",{className:"text-sm font-medium",children:"Add IDE"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(N.fC,{value:n,onValueChange:e=>l(e),children:[t.jsx(N.xz,{className:"flex-1",placeholder:"Select an IDE to add..."}),t.jsx(N.VY,{children:o.map(([e,s])=>t.jsx(N.ck,{value:e,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(d.Z,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:s.name}),t.jsx("div",{className:"text-xs text-muted-foreground",children:s.description})]})]})},e))})]}),(0,t.jsxs)(p.z,{onClick:()=>{if(!n)return;let e={id:`${n}-${Date.now()}`,name:z.gA[n].name,type:n,isDefault:0===r.preferredIDEs.length,addedAt:new Date().toISOString()},s={...r,preferredIDEs:[...r.preferredIDEs,e],defaultIDE:0===r.preferredIDEs.length?e.id:r.defaultIDE};a(s),l(""),O.Am.success(`${z.gA[n].name} added to preferences`)},disabled:!n,size:"2",children:[t.jsx(D.Z,{className:"h-4 w-4 mr-1"}),"Add"]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h3",{className:"text-sm font-medium",children:["Preferred IDEs (",r.preferredIDEs.length,")"]}),0===r.preferredIDEs.length?(0,t.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[t.jsx(d.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),t.jsx("p",{children:"No preferred IDEs added yet"}),t.jsx("p",{className:"text-xs",children:"Add your favorite IDEs to generate quick commands"})]}):t.jsx("div",{className:"space-y-2",children:r.preferredIDEs.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(d.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"font-medium",children:e.name}),r.defaultIDE===e.id&&t.jsx(k.C,{variant:"soft",className:"text-xs",children:"Default"})]}),t.jsx("div",{className:"text-xs text-muted-foreground",children:z.gA[e.type].description})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(p.z,{variant:"ghost",size:"2",onClick:()=>m(e.id),disabled:r.defaultIDE===e.id,className:"h-8 w-8 p-0",children:r.defaultIDE===e.id?t.jsx(E.Z,{className:"h-4 w-4 fill-current"}):t.jsx(C.Z,{className:"h-4 w-4"})}),t.jsx(p.z,{variant:"ghost",size:"2",onClick:()=>c(e.id),className:"h-8 w-8 p-0 text-destructive hover:text-destructive",children:t.jsx(A.Z,{className:"h-4 w-4"})})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground bg-muted p-3 rounded-lg",children:[t.jsx("p",{className:"font-medium mb-1",children:"How it works:"}),(0,t.jsxs)("ul",{className:"space-y-1",children:[t.jsx("li",{children:"• Add your favorite IDEs to generate quick npx commands"}),t.jsx("li",{children:"• Set a default IDE for one-click command copying"}),t.jsx("li",{children:"• Commands will include the --target flag for your selected IDE"})]})]})]})]})})}function P({locale:e}){let{theme:s,setTheme:r}=(0,l.F)(),{data:a}=(0,j.kP)(),[N,v]=(0,i.useState)(!1),[b,I]=(0,i.useState)(!1),w=async()=>{await (0,j.w7)(),I(!1)},k=()=>{I(!1)};return(0,t.jsxs)("nav",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:[(0,t.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-8",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center gap-2",children:[t.jsx(d.Z,{className:"h-6 w-6 text-primary"}),t.jsx("span",{className:"font-bold text-xl",children:"OnlyRules"})]}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center gap-6",children:[t.jsx(n(),{href:"/dashboard",className:"text-sm font-medium transition-colors hover:text-primary",children:"Dashboard"}),t.jsx(n(),{href:"/templates",className:"text-sm font-medium transition-colors hover:text-primary",children:"Templates"}),t.jsx(n(),{href:"/ides",className:"text-sm font-medium transition-colors hover:text-primary",children:"IDEs"}),t.jsx(n(),{href:"/tutorials",className:"text-sm font-medium transition-colors hover:text-primary",children:"Tutorials"}),t.jsx(n(),{href:"/shared",className:"text-sm font-medium transition-colors hover:text-primary",children:"Community"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[t.jsx(y,{currentLocale:e}),t.jsx(p.z,{variant:"ghost",size:"2",asChild:!0,children:(0,t.jsxs)(n(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[t.jsx(o.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"hidden sm:inline",children:"GitHub"})]})}),(0,t.jsxs)(p.z,{variant:"ghost",size:"2",onClick:()=>r("dark"===s?"light":"dark"),children:[t.jsx(c.Z,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),t.jsx(m.Z,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),t.jsx("span",{className:"sr-only",children:"Toggle theme"})]}),a?.user?(0,t.jsxs)(f.fC,{open:b,onOpenChange:I,children:[t.jsx(f.xz,{children:t.jsx(p.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:t.jsx(g.q,{className:"h-8 w-8",src:a.user.image||"",fallback:a.user.name?.charAt(0)||a.user.email?.charAt(0)||"U"})})}),(0,t.jsxs)(f.VY,{className:"w-56",align:"end",children:[t.jsx("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[a.user.name&&t.jsx("p",{className:"font-medium",children:a.user.name}),a.user.email&&t.jsx("p",{className:"w-[200px] truncate text-sm text-muted-foreground",children:a.user.email})]})}),t.jsx(f.Z0,{}),t.jsx(f.ck,{asChild:!0,children:(0,t.jsxs)(n(),{href:"/dashboard",onClick:k,children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,t.jsxs)(f.ck,{onClick:()=>{v(!0),I(!1)},children:[t.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"IDE Preferences"]}),t.jsx(f.ck,{asChild:!0,children:(0,t.jsxs)(n(),{href:"/settings",onClick:k,children:[t.jsx(x.Z,{className:"mr-2 h-4 w-4"}),"Settings"]})}),t.jsx(f.Z0,{}),(0,t.jsxs)(f.ck,{onClick:w,children:[t.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Sign Out"]})]})]}):t.jsx("div",{className:"flex items-center gap-2",children:t.jsx(p.z,{variant:"ghost",size:"2",children:t.jsx(n(),{href:"/auth/signin",children:"Sign In"})})})]})]}),t.jsx(T,{open:N,onOpenChange:v})]})}function S({locale:e}){return t.jsx(P,{locale:e})}},7029:(e,s,r)=>{"use strict";r.r(s),r.d(s,{JotaiProvider:()=>n});var t=r(2395),a=r(6223);function n({children:e}){return t.jsx(a.zt,{children:e})}},9294:(e,s,r)=>{"use strict";r.r(s),r.d(s,{ThemeProvider:()=>d});var t=r(2395),a=r(8726),n=r(516),i=r(2426);function l({children:e}){let{theme:s,systemTheme:r}=(0,n.F)(),[l,d]=a.useState(!1);return(a.useEffect(()=>{d(!0)},[]),l)?t.jsx(i.Q2,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:("system"===s?r||"dark":s)||"dark",panelBackground:"translucent",hasBackground:!1,children:e}):t.jsx(i.Q2,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:"dark",panelBackground:"translucent",hasBackground:!1,children:e})}function d({children:e,...s}){return t.jsx(n.f,{defaultTheme:"dark",...s,children:t.jsx(l,{children:e})})}},7425:(e,s,r)=>{"use strict";r.r(s),r.d(s,{Toaster:()=>i});var t=r(2395),a=r(516),n=r(7999);let i=({...e})=>{let{theme:s="system"}=(0,a.F)();return t.jsx(n.x7,{theme:s,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})}},6643:(e,s,r)=>{"use strict";r.d(s,{kP:()=>d,w7:()=>l,zB:()=>n});var t=r(6481);let a=(0,t.XL)({baseURL:"http://localhost:3000"}),{signIn:n,signUp:i,signOut:l,useSession:d,getSession:o}=a},7972:(e,s,r)=>{"use strict";r.d(s,{Ym:()=>i,gA:()=>n});var t=r(2584),a=r(7055);let n={CURSOR:{name:"Cursor",description:"AI-powered code editor",command:"cursor"},AUGMENT:{name:"Augment",description:"AI coding assistant",command:"augment"},WINDSURF:{name:"Windsurf",description:"AI development environment",command:"windsurf"},CLAUDE:{name:"Claude",description:"Anthropic AI assistant",command:"claude"},GITHUB_COPILOT:{name:"GitHub Copilot",description:"AI pair programmer",command:"github-copilot"},GEMINI:{name:"Gemini",description:"Google AI assistant",command:"gemini"},OPENAI_CODEX:{name:"OpenAI Codex",description:"OpenAI code assistant",command:"openai-codex"},CLINE:{name:"Cline",description:"AI coding assistant",command:"cline"},JUNIE:{name:"Junie",description:"AI development tool",command:"junie"},TRAE:{name:"Trae",description:"AI coding companion",command:"trae"},LINGMA:{name:"Lingma",description:"AI programming assistant",command:"lingma"},KIRO:{name:"Kiro",description:"AI development environment",command:"kiro"},TENCENT_CODEBUDDY:{name:"Tencent CodeBuddy",description:"Tencent AI coding assistant",command:"tencent-codebuddy"},GENERAL:{name:"General",description:"General purpose IDE",command:"general"}};(0,t.cn)([]),(0,t.cn)([]),(0,t.cn)(null),(0,t.cn)(""),(0,t.cn)([]),(0,t.cn)("ALL"),(0,a.O4)("theme","system");let i=(0,a.O4)("ide-preferences",{preferredIDEs:[],defaultIDE:void 0});(0,t.cn)(e=>e(i).preferredIDEs,(e,s,r)=>{let t=e(i);s(i,{...t,preferredIDEs:r})}),(0,t.cn)(e=>{let s=e(i);return s.preferredIDEs.find(e=>e.id===s.defaultIDE)},(e,s,r)=>{let t=e(i);s(i,{...t,defaultIDE:r})})},5329:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>z,dynamic:()=>C,metadata:()=>A});var t=r(3326);r(1);var a=r(6962);let n=(0,a.createProxy)(String.raw`/mnt/persist/workspace/packages/web/components/providers/theme-provider.tsx`),{__esModule:i,$$typeof:l}=n;n.default;let d=n.ThemeProvider,o=(0,a.createProxy)(String.raw`/mnt/persist/workspace/packages/web/components/providers/jotai-provider.tsx`),{__esModule:c,$$typeof:m}=o;o.default;let u=o.JotaiProvider,x=(0,a.createProxy)(String.raw`/mnt/persist/workspace/packages/web/components/layout/client-navbar.tsx`),{__esModule:h,$$typeof:p}=x;x.default;let f=x.ClientNavbar;var g=r(1051);let j=["en","zh-CN","zh-HK"];function N(){let e=(0,g.cookies)(),s=e.get("locale");if(s&&j.includes(s.value))return s.value;let r=(0,g.headers)(),t=r.get("accept-language");if(t){let e=t.split(",").map(e=>e.split(";")[0].trim().toLowerCase()).find(e=>{if(j.includes(e))return!0;if(e.startsWith("zh"))return e.includes("hk")||e.includes("tw")?j.includes("zh-HK"):j.includes("zh-CN");let s=e.split("-")[0];return j.includes(s)});if(e){if(e.startsWith("zh"))return e.includes("hk")||e.includes("tw")?"zh-HK":"zh-CN";let s=e.split("-")[0];if(j.includes(s))return s;if(j.includes(e))return e}}return"en"}var v=r(8304),b=r(9893);function y(){return t.jsx(b.K,{})}function I(){let e="en";try{let{headers:s}=r(1051);s(),e=N()}catch(s){e="en"}return t.jsx(v.Suspense,{fallback:t.jsx(y,{}),children:t.jsx(f,{locale:e})})}let w=(0,a.createProxy)(String.raw`/mnt/persist/workspace/packages/web/components/ui/sonner.tsx`),{__esModule:k,$$typeof:D}=w;w.default;let E=w.Toaster,C="force-dynamic",A={metadataBase:new URL("http://localhost:3000"),title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.",keywords:"AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy",authors:[{name:"OnlyRules Team"}],openGraph:{title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs.",type:"website"}};function z({children:e}){let s="en";try{s=N()}catch(e){s="en"}return(0,t.jsxs)("html",{lang:s,suppressHydrationWarning:!0,children:[(0,t.jsxs)("head",{children:[t.jsx("script",{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=G-CYBBJ5J4SH"}),t.jsx("script",{dangerouslySetInnerHTML:{__html:`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-CYBBJ5J4SH');
            `}})]}),t.jsx("body",{className:"min-h-screen bg-background font-sans antialiased",children:t.jsx(d,{attribute:"class",defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:(0,t.jsxs)(u,{children:[(0,t.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[t.jsx(I,{}),t.jsx("main",{className:"flex-1",children:e}),t.jsx("footer",{className:"border-t border-border",children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4",children:[t.jsx("div",{className:"flex flex-col md:flex-row items-center gap-4",children:t.jsx("p",{className:"text-sm text-center text-muted-foreground",children:"Built with ❤️ for the AI coding community."})}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-4",children:[t.jsx("p",{className:"text-sm text-muted-foreground",children:"Links:"}),t.jsx("a",{href:"https://toolsdk.ai/",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-primary hover:underline focus-visible-ring rounded",children:"ToolSDK.ai"})]})]})})})]}),t.jsx(E,{})]})})})]})}},3310:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(3326),a=r(2467),n=r.n(a);function i(){return t.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-6xl font-bold mb-4",children:"404"}),t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Page Not Found"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"The page you're looking for doesn't exist or has been moved."}),t.jsx(n(),{href:"/",className:"inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Go Home"})]})})}},9893:(e,s,r)=>{"use strict";r.d(s,{K:()=>i});var t=r(3326),a=r(2467),n=r.n(a);function i(){return t.jsx("nav",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container mx-auto flex h-16 items-center justify-between px-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-8",children:[t.jsx(n(),{href:"/",className:"flex items-center gap-2",children:t.jsx("span",{className:"font-bold text-xl text-foreground",children:"OnlyRules"})}),(0,t.jsxs)("div",{className:"hidden md:flex items-center gap-6",children:[t.jsx(n(),{href:"/templates",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Templates"}),t.jsx(n(),{href:"/tutorials",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Tutorials"}),t.jsx(n(),{href:"/dashboard",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Dashboard"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[t.jsx(n(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-muted-foreground hover:text-foreground transition-colors",children:"GitHub"}),t.jsx(n(),{href:"/auth/signin",className:"bg-primary text-primary-foreground px-4 py-2 rounded text-sm hover:bg-primary/90 transition-colors",children:"Sign In"})]})]})})}},1:()=>{}};