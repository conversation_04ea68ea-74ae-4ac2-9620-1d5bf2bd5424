"use strict";exports.id=6335,exports.ids=[6335],exports.modules={5482:(e,r,t)=>{t.d(r,{Z:()=>o});var a=t(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2205:(e,r,t)=>{t.d(r,{Z:()=>o});var a=t(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a.Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},2507:(e,r,t)=>{t.d(r,{Z:()=>o});var a=t(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a.Z)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]])},2801:(e,r,t)=>{t.d(r,{Z:()=>o});var a=t(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,a.Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},7670:(e,r,t)=>{t.d(r,{Z:()=>i});var a=t(8304);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),d=(...e)=>e.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:l="",children:i,iconNode:c,...h},m)=>(0,a.createElement)("svg",{ref:m,...s,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:d("lucide",l),...h},[...c.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(i)?i:[i]])),i=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...s},i)=>(0,a.createElement)(l,{ref:i,iconNode:r,className:d(`lucide-${o(e)}`,t),...s}));return t.displayName=`${e}`,t}},5145:(e,r,t)=>{let{createProxy:a}=t(6962);e.exports=a("/mnt/persist/workspace/node_modules/next/dist/client/components/error-boundary.js")}};