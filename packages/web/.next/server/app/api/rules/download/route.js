"use strict";(()=>{var e={};e.id=4313,e.ids=[4313],e.modules={3524:e=>{e.exports=require("@prisma/client")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1780:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>$,originalPathname:()=>h,requestAsyncStorage:()=>m,routeModule:()=>c,serverHooks:()=>w,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>f});var n={};r.r(n),r.d(n,{GET:()=>d,dynamic:()=>l}),r(7718);var a=r(5103),i=r(7953),o=r(2482),s=r(4459);let l="force-dynamic";async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id"),n=t.get("format")||"mdx";if(r){let e=await s._.rule.findFirst({where:{id:r,visibility:"PUBLIC"},include:{tags:{include:{tag:!0}},user:{select:{name:!0}}}});if(!e)return o.Z.json({error:"Rule not found or not public"},{status:404});let t=u(e);return new o.Z(t,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":`attachment; filename="${e.title.replace(/[^a-z0-9]/gi,"-").replace(/-+/g,"-").replace(/^-|-$/g,"").toLowerCase().substring(0,100)}.mdx"`}})}let a=await s._.rule.findMany({where:{visibility:"PUBLIC"},include:{tags:{include:{tag:!0}},user:{select:{name:!0}}},orderBy:{updatedAt:"desc"}});if("json"===n)return o.Z.json(a);let i=a.map(e=>u(e)).join("\n\n---\n\n");return new o.Z(i,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":'attachment; filename="public-rules.mdx"'}})}catch(e){return console.error("Error downloading rules:",e),o.Z.json({error:"Failed to download rules"},{status:500})}}function u(e){let t=e.tags.map(e=>e.tag.name).join(", "),r=e.user?.name||"Unknown",n=new Date(e.createdAt).toLocaleDateString(),a=`---
title: "${e.title}"
description: "${e.description||""}"
author: "${r}"
date: "${n}"
ideType: "${e.ideType}"
tags: [${e.tags.map(e=>`"${e.tag.name}"`).join(", ")}]
---

# ${e.title}

${e.description?`> ${e.description}

`:""}

**IDE Type:** ${e.ideType}  
**Author:** ${r}  
**Created:** ${n}  
${t?`**Tags:** ${t}  `:""}

## Rule Content

${e.content}
`;return a}let p=a.AppRouteRouteModule,c=new p({definition:{kind:i.x.APP_ROUTE,page:"/api/rules/download/route",pathname:"/api/rules/download",filename:"route",bundlePath:"app/api/rules/download/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/rules/download/route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:w,headerHooks:$,staticGenerationBailout:f}=c,h="/api/rules/download/route"},4459:(e,t,r)=>{r.d(t,{_:()=>i});var n=r(3524);let a=globalThis,i=a.prisma??new n.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8208,4502,2482],()=>r(1780));module.exports=n})();