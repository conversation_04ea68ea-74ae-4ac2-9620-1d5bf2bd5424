"use strict";(()=>{var e={};e.id=5780,e.ids=[5780],e.modules={3524:e=>{e.exports=require("@prisma/client")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1331:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>w,originalPathname:()=>h,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>$});var a={};r.r(a),r.d(a,{GET:()=>d,dynamic:()=>u}),r(7718);var i=r(5103),n=r(7953),o=r(2482),s=r(4459);let u="force-dynamic";async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return o.Z.json({error:"Rule ID is required"},{status:400});let a=await s._.rule.findFirst({where:{id:r,visibility:"PUBLIC"},include:{tags:{include:{tag:!0}},user:{select:{name:!0}}}});if(!a)return o.Z.json({error:"Rule not found or not public"},{status:404});let i=function(e){let t=e.tags.map(e=>e.tag.name).join(", "),r=e.user?.name||"Unknown",a=new Date(e.createdAt).toISOString(),i=new Date(e.updatedAt).toISOString(),n=`---
id: "${e.id}"
title: "${e.title}"
description: "${e.description||""}"
author: "${r}"
createdAt: "${a}"
updatedAt: "${i}"
ideType: "${e.ideType}"
visibility: "${e.visibility}"
tags: [${e.tags.map(e=>`"${e.tag.name}"`).join(", ")}]
---

# ${e.title}

${e.description?`> ${e.description}

`:""}

## Metadata

- **IDE Type:** ${e.ideType}
- **Author:** ${r}
- **Created:** ${new Date(e.createdAt).toLocaleDateString()}
- **Updated:** ${new Date(e.updatedAt).toLocaleDateString()}
${t?`- **Tags:** ${t}`:""}

## Rule Content

${e.content}
`;return n}(a);return new o.Z(i,{headers:{"Content-Type":"text/mdx; charset=utf-8","Cache-Control":"public, max-age=3600"}})}catch(e){return console.error("Error fetching raw rule:",e),o.Z.json({error:"Failed to fetch rule"},{status:500})}}let l=i.AppRouteRouteModule,p=new l({definition:{kind:n.x.APP_ROUTE,page:"/api/rules/raw/route",pathname:"/api/rules/raw",filename:"route",bundlePath:"app/api/rules/raw/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/rules/raw/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:c,staticGenerationAsyncStorage:g,serverHooks:m,headerHooks:w,staticGenerationBailout:$}=p,h="/api/rules/raw/route"},4459:(e,t,r)=>{r.d(t,{_:()=>n});var a=r(3524);let i=globalThis,n=i.prisma??new a.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8208,4502,2482],()=>r(1331));module.exports=a})();