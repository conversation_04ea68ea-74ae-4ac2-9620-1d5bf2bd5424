"use strict";(()=>{var e={};e.id=6260,e.ids=[6260],e.modules={3524:e=>{e.exports=require("@prisma/client")},7158:e=>{e.exports=require("better-auth")},4492:e=>{e.exports=require("better-auth/adapters/prisma")},2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5610:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>j,originalPathname:()=>y,requestAsyncStorage:()=>g,routeModule:()=>w,serverHooks:()=>m,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>x});var a={};t.r(a),t.d(a,{DELETE:()=>p,GET:()=>l,PUT:()=>c}),t(7718);var i=t(5103),s=t(7953),n=t(2482),u=t(4459),o=t(2981),d=t(1051);async function l(e,{params:r}){try{if(!process.env.DATABASE_URL)return n.Z.json({error:"Service temporarily unavailable"},{status:503});let e=await o.I.api.getSession({headers:await (0,d.headers)()}),t=await u._.rule.findUnique({where:{id:r.id},include:{tags:{include:{tag:!0}},user:{select:{id:!0,name:!0,email:!0}}}});if(!t)return n.Z.json({error:"Rule not found"},{status:404});let a="PUBLIC"===t.visibility||e?.user&&t.userId===e.user.id;if(!a)return n.Z.json({error:"Unauthorized"},{status:403});return n.Z.json(t)}catch(e){return console.error("Error fetching rule:",e),n.Z.json({error:"Failed to fetch rule"},{status:500})}}async function c(e,{params:r}){try{let t=await o.I.api.getSession({headers:await (0,d.headers)()});if(!t?.user)return n.Z.json({error:"Unauthorized"},{status:401});let a=await u._.rule.findUnique({where:{id:r.id}});if(!a)return n.Z.json({error:"Rule not found"},{status:404});if(a.userId!==t.user.id)return n.Z.json({error:"Forbidden"},{status:403});let i=await e.json(),{title:s,description:l,content:c,ideType:p,visibility:h,tags:w}=i;if(await u._.rule.update({where:{id:r.id},data:{title:s,description:l,content:c,ideType:p,visibility:h,shareToken:"PUBLIC"===h?a.shareToken||crypto.randomUUID():null},include:{tags:{include:{tag:!0}}}}),w&&Array.isArray(w))for(let e of(await u._.ruleTag.deleteMany({where:{ruleId:r.id}}),w)){let t=await u._.tag.findUnique({where:{name:e}});t||(t=await u._.tag.create({data:{name:e}})),await u._.ruleTag.create({data:{ruleId:r.id,tagId:t.id}})}let g=await u._.rule.findUnique({where:{id:r.id},include:{tags:{include:{tag:!0}}}});return n.Z.json(g)}catch(e){return console.error("Error updating rule:",e),n.Z.json({error:"Failed to update rule"},{status:500})}}async function p(e,{params:r}){try{let e=await o.I.api.getSession({headers:await (0,d.headers)()});if(!e?.user)return n.Z.json({error:"Unauthorized"},{status:401});let t=await u._.rule.findUnique({where:{id:r.id}});if(!t)return n.Z.json({error:"Rule not found"},{status:404});if(t.userId!==e.user.id)return n.Z.json({error:"Forbidden"},{status:403});return await u._.rule.delete({where:{id:r.id}}),n.Z.json({success:!0})}catch(e){return console.error("Error deleting rule:",e),n.Z.json({error:"Failed to delete rule"},{status:500})}}let h=i.AppRouteRouteModule,w=new h({definition:{kind:s.x.APP_ROUTE,page:"/api/rules/[id]/route",pathname:"/api/rules/[id]",filename:"route",bundlePath:"app/api/rules/[id]/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/rules/[id]/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:m,headerHooks:j,staticGenerationBailout:x}=w,y="/api/rules/[id]/route"},4459:(e,r,t)=>{t.d(r,{_:()=>s});var a=t(3524);let i=globalThis,s=i.prisma??new a.PrismaClient}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8208,1051,4502,2482,2981],()=>t(5610));module.exports=a})();