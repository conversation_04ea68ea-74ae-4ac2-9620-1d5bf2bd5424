"use strict";(()=>{var e={};e.id=5531,e.ids=[5531],e.modules={3524:e=>{e.exports=require("@prisma/client")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2868:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>$,originalPathname:()=>y,requestAsyncStorage:()=>g,routeModule:()=>c,serverHooks:()=>h,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>f});var i={};n.r(i),n.d(i,{GET:()=>p,dynamic:()=>l}),n(7718);var r=n(5103),a=n(7953),o=n(2482),s=n(4459);let l="force-dynamic";async function p(e){try{let{searchParams:t}=new URL(e.url),n=t.get("ids")?.split(",").filter(Boolean)||[],i=t.get("tags")?.split(",").filter(<PERSON>olean)||[],r=t.get("ideType"),a=t.get("search")||"",l={visibility:"PUBLIC"};n.length>0&&(l.id={in:n}),i.length>0&&(l.tags={some:{tag:{name:{in:i}}}}),r&&"ALL"!==r&&(l.ideType=r),a&&(l.OR=[{title:{contains:a,mode:"insensitive"}},{description:{contains:a,mode:"insensitive"}}]);let p=await s._.rule.findMany({where:l,include:{tags:{include:{tag:!0}},user:{select:{name:!0}}},orderBy:{updatedAt:"desc"}});if(0===p.length)return o.Z.json({error:"No rules found matching the criteria"},{status:404});let d=p.map(e=>({filename:`${u(e.title)}.mdx`,content:function(e){let t=e.tags.map(e=>e.tag.name).join(", "),n=e.user?.name||"Unknown",i=new Date(e.createdAt).toLocaleDateString(),r=new Date(e.updatedAt).toLocaleDateString(),a=`---
title: "${e.title}"
description: "${e.description||""}"
author: "${n}"
createdAt: "${i}"
updatedAt: "${r}"
ideType: "${e.ideType}"
visibility: "${e.visibility}"
tags: [${e.tags.map(e=>`"${e.tag.name}"`).join(", ")}]
---

# ${e.title}

${e.description?`> ${e.description}

`:""}

## Metadata

- **IDE Type:** ${e.ideType}
- **Author:** ${n}
- **Created:** ${i}
- **Updated:** ${r}
${t?`- **Tags:** ${t}`:""}

## Rule Content

${e.content}
`;return a}(e)}));if(1===d.length)return new o.Z(d[0].content,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":`attachment; filename="${d[0].filename}"`}});let c=d.map(e=>`<!-- File: ${e.filename} -->
${e.content}`).join("\n\n<!-- ============================================ -->\n\n"),g=function(e,t,n){let i=["rules"];e&&i.push(u(e)),n&&"ALL"!==n&&i.push(n.toLowerCase()),t&&t.length>0&&i.push(t.slice(0,3).map(e=>u(e)).join("-"));let r=new Date().toISOString().split("T")[0];return i.push(r),i.join("-")+".mdx"}(a||void 0,i,r||void 0);return new o.Z(c,{headers:{"Content-Type":"text/plain; charset=utf-8","Content-Disposition":`attachment; filename="${g}"`}})}catch(e){return console.error("Error exporting rules:",e),o.Z.json({error:"Failed to export rules"},{status:500})}}function u(e){return e.replace(/[^a-z0-9]/gi,"-").replace(/-+/g,"-").replace(/^-|-$/g,"").toLowerCase().substring(0,100)}let d=r.AppRouteRouteModule,c=new d({definition:{kind:a.x.APP_ROUTE,page:"/api/rules/export/route",pathname:"/api/rules/export",filename:"route",bundlePath:"app/api/rules/export/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/rules/export/route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:h,headerHooks:$,staticGenerationBailout:f}=c,y="/api/rules/export/route"},4459:(e,t,n)=>{n.d(t,{_:()=>a});var i=n(3524);let r=globalThis,a=r.prisma??new i.PrismaClient}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[8208,4502,2482],()=>n(2868));module.exports=i})();