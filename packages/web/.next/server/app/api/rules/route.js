"use strict";(()=>{var e={};e.id=9344,e.ids=[9344],e.modules={3524:e=>{e.exports=require("@prisma/client")},7158:e=>{e.exports=require("better-auth")},4492:e=>{e.exports=require("better-auth/adapters/prisma")},2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2573:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>y,originalPathname:()=>x,requestAsyncStorage:()=>m,routeModule:()=>h,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>j});var a={};r.r(a),r.d(a,{GET:()=>d,POST:()=>c,PUT:()=>p}),r(7718);var i=r(5103),s=r(7953),n=r(2482),o=r(4459),u=r(2981),l=r(1051);async function d(e){try{let t=await u.I.api.getSession({headers:await (0,l.headers)()}),{searchParams:r}=new URL(e.url),a=r.get("search")||"",i=r.get("tags")?.split(",").filter(Boolean)||[],s=r.get("ideType"),d=r.get("visibility"),c={OR:[{visibility:"PUBLIC"},t?.user?{userId:t.user.id}:{}]};a&&(c.OR=[{title:{contains:a,mode:"insensitive"}},{description:{contains:a,mode:"insensitive"}}]),i.length>0&&(c.tags={some:{tag:{name:{in:i}}}}),s&&"ALL"!==s&&(c.ideType=s),d&&(c.visibility=d);let p=await o._.rule.findMany({where:c,include:{tags:{include:{tag:!0}},user:{select:{id:!0,name:!0,email:!0}}},orderBy:{updatedAt:"desc"}});return n.Z.json(p)}catch(e){return console.error("Error fetching rules:",e),n.Z.json({error:"Failed to fetch rules"},{status:500})}}async function c(e){try{let t=await u.I.api.getSession({headers:await (0,l.headers)()});if(!t?.user)return n.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),{title:a,description:i,content:s,ideType:d,visibility:c,tags:p}=r,g=await o._.rule.create({data:{title:a,description:i,content:s,ideType:d,visibility:c,userId:t.user.id,shareToken:"PUBLIC"===c?crypto.randomUUID():null},include:{tags:{include:{tag:!0}}}});if(p&&p.length>0)for(let e of p){let t=await o._.tag.findUnique({where:{name:e}});t||(t=await o._.tag.create({data:{name:e}})),await o._.ruleTag.create({data:{ruleId:g.id,tagId:t.id}})}return n.Z.json(g)}catch(e){return console.error("Error creating rule:",e),n.Z.json({error:"Failed to create rule"},{status:500})}}async function p(e){try{let t=await u.I.api.getSession({headers:await (0,l.headers)()});if(!t?.user)return n.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),{id:a,title:i,description:s,content:d,ideType:c,visibility:p,tags:g}=r;if(!a)return n.Z.json({error:"Rule ID is required"},{status:400});let h=await o._.rule.findUnique({where:{id:a},include:{tags:{include:{tag:!0}}}});if(!h)return n.Z.json({error:"Rule not found"},{status:404});if(h.userId!==t.user.id)return n.Z.json({error:"Forbidden"},{status:403});if(await o._.rule.update({where:{id:a},data:{title:i,description:s,content:d,ideType:c,visibility:p,shareToken:"PUBLIC"===p?crypto.randomUUID():null},include:{tags:{include:{tag:!0}},user:{select:{id:!0,name:!0,email:!0}}}}),await o._.ruleTag.deleteMany({where:{ruleId:a}}),g&&g.length>0)for(let e of g){let t=await o._.tag.findUnique({where:{name:e}});t||(t=await o._.tag.create({data:{name:e}})),await o._.ruleTag.create({data:{ruleId:a,tagId:t.id}})}let m=await o._.rule.findUnique({where:{id:a},include:{tags:{include:{tag:!0}},user:{select:{id:!0,name:!0,email:!0}}}});return n.Z.json(m)}catch(e){return console.error("Error updating rule:",e),n.Z.json({error:"Failed to update rule"},{status:500})}}let g=i.AppRouteRouteModule,h=new g({definition:{kind:s.x.APP_ROUTE,page:"/api/rules/route",pathname:"/api/rules",filename:"route",bundlePath:"app/api/rules/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/rules/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:w,serverHooks:f,headerHooks:y,staticGenerationBailout:j}=h,x="/api/rules/route"},4459:(e,t,r)=>{r.d(t,{_:()=>s});var a=r(3524);let i=globalThis,s=i.prisma??new a.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8208,1051,4502,2482,2981],()=>r(2573));module.exports=a})();