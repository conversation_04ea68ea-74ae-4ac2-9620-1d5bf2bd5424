"use strict";(()=>{var e={};e.id=2191,e.ids=[2191],e.modules={3524:e=>{e.exports=require("@prisma/client")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9457:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>v,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>c,staticGenerationBailout:()=>h});var a={};r.r(a),r.d(a,{GET:()=>u}),r(7718);var s=r(5103),o=r(7953),i=r(2482),n=r(4459);async function u(){try{let e=await n._.tag.findMany({orderBy:{name:"asc"}});return i.<PERSON>.json(e)}catch(e){return console.error("Error fetching tags:",e),i.Z.json({error:"Failed to fetch tags"},{status:500})}}let p=s.AppRouteRouteModule,d=new p({definition:{kind:o.x.APP_ROUTE,page:"/api/tags/route",pathname:"/api/tags",filename:"route",bundlePath:"app/api/tags/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/tags/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:c,serverHooks:g,headerHooks:m,staticGenerationBailout:h}=d,v="/api/tags/route"},4459:(e,t,r)=>{r.d(t,{_:()=>o});var a=r(3524);let s=globalThis,o=s.prisma??new a.PrismaClient}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8208,4502,2482],()=>r(9457));module.exports=a})();