"use strict";(()=>{var e={};e.id=8310,e.ids=[8310],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1884:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>m,originalPathname:()=>b,requestAsyncStorage:()=>u,routeModule:()=>p,serverHooks:()=>x,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>c});var a={};o.r(a),o.d(a,{GET:()=>n}),o(7718);var r=o(5103),s=o(7953),i=o(2482);async function n(){let e="http://localhost:3000",t=`# Robots.txt for OnlyRules
User-agent: *
Allow: /
Disallow: /api/
Disallow: /dashboard/
Disallow: /admin/

User-agent: Googlebot
Allow: /
Disallow: /api/

Sitemap: ${e}/api/sitemap.xml
Host: ${e}`;return new i.Z(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}let l=r.AppRouteRouteModule,p=new l({definition:{kind:s.x.APP_ROUTE,page:"/api/robots.txt/route",pathname:"/api/robots.txt",filename:"route",bundlePath:"app/api/robots.txt/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/robots.txt/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:d,serverHooks:x,headerHooks:m,staticGenerationBailout:c}=p,b="/api/robots.txt/route"}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),a=t.X(0,[8208,4502,2482],()=>o(1884));module.exports=a})();