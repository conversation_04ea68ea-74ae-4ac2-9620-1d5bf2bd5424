"use strict";(()=>{var e={};e.id=7112,e.ids=[7112],e.modules={3524:e=>{e.exports=require("@prisma/client")},7158:e=>{e.exports=require("better-auth")},4492:e=>{e.exports=require("better-auth/adapters/prisma")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5722:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>E,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>A,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>l}),r(7718);var u=r(5103),n=r(7953),o=r(2981);let i=require("better-auth/next-js"),s=(0,i.toNextJsHandler)((0,o.E)());async function p(e){return s.GET(e)}async function l(e){return s.POST(e)}let P=u.AppRouteRouteModule,d=new P({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/[...all]/route",pathname:"/api/auth/[...all]",filename:"route",bundlePath:"app/api/auth/[...all]/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/auth/[...all]/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:c,staticGenerationAsyncStorage:A,serverHooks:h,headerHooks:m,staticGenerationBailout:x}=d,E="/api/auth/[...all]/route"},7953:(e,t)=>{var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4502,2981],()=>r(5722));module.exports=a})();