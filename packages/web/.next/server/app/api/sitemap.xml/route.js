"use strict";(()=>{var e={};e.id=1573,e.ids=[1573],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4448:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>d,originalPathname:()=>g,requestAsyncStorage:()=>u,routeModule:()=>p,serverHooks:()=>c,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>h});var a={};t.r(a),t.d(a,{GET:()=>s}),t(7718);var o=t(5103),l=t(7953),i=t(2482);async function s(){let e="http://localhost:3000",r=new Date().toISOString(),t=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${e}</loc>
    <lastmod>${r}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${e}/dashboard</loc>
    <lastmod>${r}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${e}/templates</loc>
    <lastmod>${r}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${e}/tutorials</loc>
    <lastmod>${r}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>${e}/auth/signin</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
  <url>
    <loc>${e}/auth/signup</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>
</urlset>`;return new i.Z(t,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}let n=o.AppRouteRouteModule,p=new n({definition:{kind:l.x.APP_ROUTE,page:"/api/sitemap.xml/route",pathname:"/api/sitemap.xml",filename:"route",bundlePath:"app/api/sitemap.xml/route"},resolvedPagePath:"/mnt/persist/workspace/packages/web/app/api/sitemap.xml/route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:m,serverHooks:c,headerHooks:d,staticGenerationBailout:h}=p,g="/api/sitemap.xml/route"}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8208,4502,2482],()=>t(4448));module.exports=a})();