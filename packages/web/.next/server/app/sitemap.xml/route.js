"use strict";(()=>{var e={};e.id=6717,e.ids=[6717],e.modules={3524:e=>{e.exports=require("@prisma/client")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4021:e=>{e.exports=import("next/dist/compiled/@vercel/og/index.node.js")},5065:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>q,originalPathname:()=>D,requestAsyncStorage:()=>x,routeModule:()=>w,serverHooks:()=>v,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>F});var i={};a.r(i),a.d(i,{default:()=>d});var r={};a.r(r),a.d(r,{GET:()=>g}),a(7718);var n=a(5103),o=a(7953),s=a(5042),l=a(3524);let u=globalThis;async function d(){let e="http://localhost:3000";"production"===process.env.VERCEL_ENV&&console.log("Build environment detected, returning static sitemap only");let t=[{url:e,lastModified:new Date,changeFrequency:"daily",priority:1},{url:`${e}/templates`,lastModified:new Date,changeFrequency:"daily",priority:.9},{url:`${e}/tutorials`,lastModified:new Date,changeFrequency:"weekly",priority:.8},{url:`${e}/dashboard`,lastModified:new Date,changeFrequency:"daily",priority:.7},{url:`${e}/auth/signin`,lastModified:new Date,changeFrequency:"monthly",priority:.5},{url:`${e}/auth/signup`,lastModified:new Date,changeFrequency:"monthly",priority:.5},{url:`${e}/ides`,lastModified:new Date,changeFrequency:"weekly",priority:.9}],a=["cursor","windsurf","github-copilot","claude","cline","augment","gemini","openai-codex","tencent-codebuddy"].map(t=>({url:`${e}/ides/${t}`,lastModified:new Date,changeFrequency:"weekly",priority:.8}));try{return process.env.DATABASE_URL,console.warn("Skipping database operations during build, returning static sitemap only"),[...t,...a]}catch(e){return console.warn("Failed to generate dynamic sitemap entries:",e),[...t,...a]}}u.prisma??new l.PrismaClient;var p=a(5692);let c={...i},m=c.default,y=c.generateSitemaps;async function g(e,t){let a;let{__metadata_id__:i=[],...r}=t.params||{},n=i[0],o=y?await y():null;if(o&&null==(a=o.find(e=>e.id.toString()===n)?.id))return new s.NextResponse("Not Found",{status:404});let l=await m({id:a}),u=(0,p.resolveRouteData)(l,"sitemap");return new s.NextResponse(u,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let h=n.AppRouteRouteModule,w=new h({definition:{kind:o.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&isDynamic=1!/mnt/persist/workspace/packages/web/app/sitemap.ts?__next_metadata_route__",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:x,staticGenerationAsyncStorage:f,serverHooks:v,headerHooks:q,staticGenerationBailout:F}=w,D="/sitemap.xml/route"}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[4502,26],()=>a(5065));module.exports=i})();