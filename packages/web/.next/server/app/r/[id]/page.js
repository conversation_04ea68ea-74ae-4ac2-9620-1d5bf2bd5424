(()=>{var e={};e.id=1327,e.ids=[1327],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.ZP,__next_app__:()=>u,originalPathname:()=>l,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(6427),n=r(7953),a=r(2086),i=r(5288),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let p=s.AppPageRouteModule,d=["",{children:["r",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6901)),"/mnt/persist/workspace/packages/web/app/r/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],c=["/mnt/persist/workspace/packages/web/app/r/[id]/page.tsx"],l="/r/[id]/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new p({definition:{kind:n.x.APP_PAGE,page:"/r/[id]/page",pathname:"/r/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},679:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4573,23))},6901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(3729);async function n({params:e}){(0,s.redirect)(`/rules/${e.id}`)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8208,1051,6808,3729,9536,4562],()=>r(4797));module.exports=s})();