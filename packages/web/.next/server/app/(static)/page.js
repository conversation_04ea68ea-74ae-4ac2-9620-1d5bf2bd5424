(()=>{var e={};e.id=4767,e.ids=[4767],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2225:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>p,routeModule:()=>u,tree:()=>d});var s=r(6427),n=r(7953),a=r(5145),o=r.n(a),i=r(5288),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=s.AppPageRouteModule,d=["",{children:["(static)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6399)),"/mnt/persist/workspace/packages/web/app/(static)/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8123)),"/mnt/persist/workspace/packages/web/app/(static)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],p=["/mnt/persist/workspace/packages/web/app/(static)/page.tsx"],m="/(static)/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new c({definition:{kind:n.x.APP_PAGE,page:"/(static)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4474:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,580,23)),Promise.resolve().then(r.t.bind(r,1328,23)),Promise.resolve().then(r.t.bind(r,988,23)),Promise.resolve().then(r.t.bind(r,1554,23)),Promise.resolve().then(r.t.bind(r,3288,23)),Promise.resolve().then(r.t.bind(r,6144,23))},3245:()=>{},5145:(e,t,r)=>{"use strict";let{createProxy:s}=r(6962);e.exports=s("/mnt/persist/workspace/node_modules/next/dist/client/components/error-boundary.js")},8123:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>n});var s=r(3326);r(1);let n={title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.",keywords:"AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy",authors:[{name:"OnlyRules Team"}],openGraph:{title:"OnlyRules - AI Prompt Management Platform",description:"Create, organize, and share AI prompt rules for your favorite IDEs.",type:"website"}};function a({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:s.jsx("body",{children:s.jsx("div",{className:"dark",children:(0,s.jsxs)("div",{className:"min-h-screen bg-background flex flex-col",children:[s.jsx("main",{className:"flex-1",children:e}),s.jsx("footer",{className:"border-t py-6 md:py-0",children:(0,s.jsxs)("div",{className:"container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row",children:[s.jsx("div",{className:"flex flex-col items-center gap-4 md:flex-row md:gap-2",children:s.jsx("p",{className:"text-center text-sm leading-loose text-muted-foreground md:text-left",children:"Built with ❤️ for the AI coding community."})}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 md:flex-row",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Links:"}),s.jsx("a",{href:"https://toolsdk.ai/",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-primary hover:text-primary/80 transition-colors",children:"ToolSDK.ai"})]})]})})]})})})})}},6399:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,dynamic:()=>i});var s=r(3326),n=r(2467),a=r.n(n),o=r(9893);let i="force-dynamic";function l(){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(o.K,{}),s.jsx("div",{className:"container mx-auto px-4 py-16 flex-1 flex items-center",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-6 text-center w-full",children:[s.jsx("h1",{className:"text-6xl font-bold tracking-tight",children:"OnlyRules"}),s.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl",children:"AI Prompt Management Platform"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mt-4",children:[s.jsx(a(),{href:"/demo",className:"inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"View Demo"}),s.jsx(a(),{href:"/auth/signin",className:"inline-block px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors",children:"Get Started"}),s.jsx(a(),{href:"/templates",className:"inline-block px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Browse Templates"})]})]})})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8208,1051,6808,9536],()=>r(2225));module.exports=s})();