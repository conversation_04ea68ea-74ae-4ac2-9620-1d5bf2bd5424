(()=>{var e={};e.id=3e3,e.ids=[3e3],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},6996:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.ZP,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var t=a(6427),r=a(7953),l=a(2086),n=a(5288),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(s,i);let c=t.AppPageRouteModule,o=["",{children:["templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1507)),"/mnt/persist/workspace/packages/web/app/templates/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],d=["/mnt/persist/workspace/packages/web/app/templates/page.tsx"],m="/templates/page",u={require:a,loadChunk:()=>Promise.resolve()},x=new c({definition:{kind:r.x.APP_PAGE,page:"/templates/page",pathname:"/templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2706:(e,s,a)=>{Promise.resolve().then(a.bind(a,2544))},2544:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N,dynamic:()=>y});var t=a(2395),r=a(8726),l=a(2751),n=a(4202),i=a(8962),c=a(5353),o=a(4716),d=a(3282),m=a(767),u=a(5331),x=a(5631),p=a(7560),h=a(4850),g=a(6617),j=a(949),f=a(583),v=a(7999);let y="force-dynamic";function N(){let[e,s]=(0,r.useState)([]),[a,y]=(0,r.useState)([]),[N,b]=(0,r.useState)(!0),[w,C]=(0,r.useState)(""),[k,A]=(0,r.useState)([]),[E,L]=(0,r.useState)("ALL"),P=(0,r.useCallback)(async()=>{try{let e=new URLSearchParams;e.set("visibility","PUBLIC"),w&&e.set("search",w),k.length>0&&e.set("tags",k.join(",")),"ALL"!==E&&e.set("ideType",E);let a=await fetch(`/api/rules?${e}`),t=await a.json();s(Array.isArray(t)?t:[])}catch(e){console.error("Error fetching rules:",e),v.Am.error("Failed to fetch templates"),s([])}finally{b(!1)}},[w,k,E]),_=async()=>{try{let e=await fetch("/api/tags"),s=await e.json();y(Array.isArray(s)?s:[])}catch(e){console.error("Error fetching tags:",e),y([])}};(0,r.useEffect)(()=>{P(),_()},[P,w,k,E]);let T=e=>{A(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},I=Array.isArray(e)?e.slice(0,6):[],G=Array.isArray(e)?e:[],Z=[{name:"Code Generation",description:"Templates for generating code snippets and functions",icon:m.Z,count:G.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("generation"))).length},{name:"Code Review",description:"Templates for automated code review and analysis",icon:u.Z,count:G.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("review"))).length},{name:"Optimization",description:"Templates for code optimization and performance",icon:x.Z,count:G.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("optimization"))).length},{name:"Documentation",description:"Templates for generating documentation",icon:p.Z,count:G.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("documentation"))).length}];return(0,t.jsxs)("div",{className:"container py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[t.jsx(u.Z,{className:"h-8 w-8 text-primary"}),t.jsx("h1",{className:"text-4xl font-bold",children:"Template Library"})]}),t.jsx("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Discover and use community-created AI prompt rules to boost your coding productivity"})]}),(0,t.jsxs)("section",{className:"space-y-6",children:[t.jsx("h2",{className:"text-2xl font-bold",children:"Browse by Category"}),t.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:Z.map(e=>(0,t.jsxs)(l.Z,{className:"hover:shadow-lg transition-shadow cursor-pointer",children:[t.jsx("div",{children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center",children:t.jsx(e.icon,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"text-lg font-semibold",children:e.name}),(0,t.jsxs)(n.C,{variant:"soft",children:[e.count," templates"]})]})]})}),t.jsx("div",{children:t.jsx("p",{className:"text-sm text-muted-foreground",children:e.description})})]},e.name))})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[t.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),t.jsx(i.f,{placeholder:"Search templates...",value:w,onChange:e=>C(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(c.fC,{value:E,onValueChange:L,children:[t.jsx(c.xz,{className:"w-full md:w-48",placeholder:"IDE Type"}),(0,t.jsxs)(c.VY,{children:[t.jsx(c.ck,{value:"ALL",children:"All IDEs"}),t.jsx(c.ck,{value:"GENERAL",children:"General"}),t.jsx(c.ck,{value:"CURSOR",children:"Cursor"}),t.jsx(c.ck,{value:"AUGMENT",children:"Augment Code"}),t.jsx(c.ck,{value:"WINDSURF",children:"Windsurf"}),t.jsx(c.ck,{value:"CLAUDE",children:"Claude"}),t.jsx(c.ck,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),t.jsx(c.ck,{value:"GEMINI",children:"Gemini"}),t.jsx(c.ck,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),t.jsx(c.ck,{value:"CLINE",children:"Cline"}),t.jsx(c.ck,{value:"JUNIE",children:"Junie"}),t.jsx(c.ck,{value:"TRAE",children:"Trae"}),t.jsx(c.ck,{value:"LINGMA",children:"Lingma"}),t.jsx(c.ck,{value:"KIRO",children:"Kiro"}),t.jsx(c.ck,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),a.length>0&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(g.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"text-sm font-medium",children:"Filter by tags:"})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:a.map(e=>t.jsx(n.C,{variant:k.includes(e.name)?"solid":"outline",className:"cursor-pointer",onClick:()=>T(e.name),style:{borderColor:e.color,backgroundColor:k.includes(e.name)?e.color:"transparent"},children:e.name},e.id))})]}),(0,t.jsxs)(o.fC,{defaultValue:"featured",className:"space-y-6",children:[(0,t.jsxs)(o.aV,{children:[t.jsx(o.xz,{value:"featured",children:"Featured"}),(0,t.jsxs)(o.xz,{value:"all",children:["All Templates (",G.length,")"]})]}),t.jsx(o.VY,{value:"featured",className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("h3",{className:"text-xl font-semibold",children:"Featured Templates"}),0===I.length?(0,t.jsxs)(l.Z,{className:"py-12 text-center",children:[t.jsx(j.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No featured templates yet"}),t.jsx("p",{className:"text-muted-foreground",children:"Check back later for curated templates from the community"})]}):t.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:I.map(e=>t.jsx(f.q,{rule:e},e.id))})]})}),t.jsx(o.VY,{value:"all",className:"space-y-6",children:N?(0,t.jsxs)("div",{className:"text-center py-12",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),t.jsx("p",{className:"mt-4 text-muted-foreground",children:"Loading templates..."})]}):0===G.length?(0,t.jsxs)(l.Z,{className:"py-12 text-center",children:[t.jsx(u.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No templates found"}),t.jsx("p",{className:"text-muted-foreground mb-4",children:"Try adjusting your search criteria or check back later"}),t.jsx(d.z,{variant:"outline",onClick:()=>{C(""),A([]),L("ALL")},children:"Clear Filters"})]}):t.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:G.map(e=>t.jsx(f.q,{rule:e},e.id))})})]})]})}},1507:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>l,default:()=>o,dynamic:()=>c});var t=a(6962);let r=(0,t.createProxy)(String.raw`/mnt/persist/workspace/packages/web/app/templates/page.tsx`),{__esModule:l,$$typeof:n}=r,i=r.default,c=r.dynamic,o=i}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[8208,1051,6808,3765,4389,1895,8962,6093,9536,4562,583],()=>a(6996));module.exports=t})();