"use strict";(()=>{var e={};e.id=4277,e.ids=[4277],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5669:(e,t,o)=>{o.r(t),o.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>p,routeModule:()=>g,tree:()=>c});var n=o(6427),r=o(7953),s=o(5145),a=o.n(s),i=o(5288),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);o.d(t,d);let l=n.AppPageRouteModule,c=["",{children:["ides",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,1323)),"/mnt/persist/workspace/packages/web/app/ides/page.tsx"]}]},{layout:[()=>Promise.resolve().then(o.bind(o,3168)),"/mnt/persist/workspace/packages/web/app/ides/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(o.bind(o,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(o.bind(o,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],p=["/mnt/persist/workspace/packages/web/app/ides/page.tsx"],u="/ides/page",m={require:o,loadChunk:()=>Promise.resolve()},g=new l({definition:{kind:r.x.APP_PAGE,page:"/ides/page",pathname:"/ides",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5145:(e,t,o)=>{let{createProxy:n}=o(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/components/error-boundary.js")},1323:(e,t,o)=>{o.r(t),o.d(t,{default:()=>l,dynamic:()=>a,metadata:()=>i});var n=o(3326),r=o(2467),s=o.n(r);let a="force-dynamic",i={title:"Supported AI Code IDEs",description:"Explore OnlyRules integration guides for all supported AI-powered code IDEs. Learn how to enhance your coding workflow with AI assistance.",alternates:{canonical:"/ides"}},d=[{id:"cursor",name:"Cursor",description:"The AI-first code editor built for pair programming with AI",icon:"\uD83D\uDD37",color:"bg-blue-500",features:["AI-powered code completion","Natural language to code","Intelligent refactoring"]},{id:"augment",name:"Augment Code",description:"Enhance your coding experience with advanced AI assistance",icon:"\uD83D\uDFE3",color:"bg-purple-500",features:["Smart code suggestions","Context-aware completions","Code optimization"]},{id:"windsurf",name:"Windsurf",description:"Ride the wave of AI-powered development with Windsurf IDE",icon:"\uD83C\uDF0A",color:"bg-cyan-500",features:["Flow-based coding","AI pair programming","Real-time collaboration"]},{id:"claude",name:"Claude Dev",description:"Anthropic's Claude integrated into your development environment",icon:"\uD83E\uDD16",color:"bg-orange-500",features:["Advanced reasoning","Long context windows","Ethical AI coding"]},{id:"github-copilot",name:"GitHub Copilot",description:"Your AI pair programmer powered by OpenAI Codex",icon:"\uD83D\uDC19",color:"bg-gray-700",features:["Code suggestions","Multi-language support","Test generation"]},{id:"gemini",name:"Gemini Code Assist",description:"Google's multimodal AI for enhanced code development",icon:"✨",color:"bg-indigo-500",features:["Multimodal understanding","Code explanation","Bug detection"]},{id:"openai-codex",name:"OpenAI Codex",description:"The AI system that powers GitHub Copilot and more",icon:"\uD83E\uDDE0",color:"bg-green-600",features:["Natural language to code","Code translation","Documentation generation"]},{id:"cline",name:"Cline",description:"Autonomous AI assistant for complex coding tasks",icon:"\uD83C\uDFAF",color:"bg-red-500",features:["Autonomous coding","Multi-file editing","Project understanding"]},{id:"tencent-codebuddy",name:"Tencent CodeBuddy",description:"Enterprise-grade AI coding assistant from Tencent Cloud",icon:"☁️",color:"bg-blue-600",features:["Enterprise features","Security focused","Cloud integration"]}];function l(){return(0,n.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[n.jsx("h1",{className:"text-4xl font-bold",children:"Supported AI Code IDEs"}),n.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto mt-4",children:"OnlyRules seamlessly integrates with the most popular AI-powered code editors."})]}),n.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(e=>(0,n.jsxs)("div",{className:"border rounded-lg p-6 hover:shadow-lg transition-shadow",children:[n.jsx("div",{className:"mb-4",children:n.jsx("span",{className:"text-3xl",children:e.icon})}),n.jsx("h3",{className:"text-xl font-semibold mb-2",children:e.name}),n.jsx("p",{className:"text-gray-600 mb-4",children:e.description}),n.jsx("div",{className:"space-y-2 mb-4",children:e.features.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[n.jsx("div",{className:"h-1.5 w-1.5 rounded-full bg-blue-500"}),n.jsx("span",{className:"text-gray-600",children:e})]},t))}),n.jsx(s(),{href:`/ides/${e.id}`,className:"block w-full bg-blue-500 text-white text-center py-2 rounded hover:bg-blue-600",children:"View Integration Guide"})]},e.id))}),(0,n.jsxs)("div",{className:"mt-16 text-center bg-gray-50 rounded-lg p-8",children:[n.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Don't see your IDE?"}),n.jsx("p",{className:"text-gray-600 mb-6",children:"We're constantly adding support for new AI-powered development tools."}),n.jsx("button",{className:"border border-gray-300 px-6 py-2 rounded hover:bg-gray-100",children:"Request IDE Support"})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),n=t.X(0,[8208,1051,6808,9536,9250],()=>o(5669));module.exports=n})();