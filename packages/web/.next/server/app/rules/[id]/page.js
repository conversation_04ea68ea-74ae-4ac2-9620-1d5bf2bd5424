(()=>{var e={};e.id=760,e.ids=[760],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1849:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.ZP,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>x,tree:()=>p});var r=s(6427),a=s(7953),n=s(2086),i=s(5288),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let l=r.AppPageRouteModule,p=["",{children:["rules",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8169)),"/mnt/persist/workspace/packages/web/app/rules/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],c=["/mnt/persist/workspace/packages/web/app/rules/[id]/page.tsx"],d="/rules/[id]/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new l({definition:{kind:a.x.APP_PAGE,page:"/rules/[id]/page",pathname:"/rules/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},3245:()=>{},8169:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>a,metadata:()=>n});var r=s(3326);let a="force-dynamic",n={title:"OnlyRules - AI Prompt Rules",description:"AI prompt rules for developers"};async function i({params:e}){return r.jsx("div",{className:"container max-w-4xl py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Loading Rule..."}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Rule ID: ",e.id]}),r.jsx("p",{className:"text-sm text-muted-foreground mt-4",children:"This page is being built. Please check back soon."})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8208,1051,6808,9536,4562],()=>s(1849));module.exports=r})();