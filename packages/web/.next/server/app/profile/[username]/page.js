(()=>{var e={};e.id=3443,e.ids=[3443],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8293:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.ZP,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>p});var t=s(6427),n=s(7953),a=s(2086),i=s(5288),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let l=t.AppPageRouteModule,p=["",{children:["profile",{children:["[username]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7790)),"/mnt/persist/workspace/packages/web/app/profile/[username]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],u=["/mnt/persist/workspace/packages/web/app/profile/[username]/page.tsx"],c="/profile/[username]/page",d={require:s,loadChunk:()=>Promise.resolve()},m=new l({definition:{kind:n.x.APP_PAGE,page:"/profile/[username]/page",pathname:"/profile/[username]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},3245:()=>{},7790:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i,dynamic:()=>n,generateMetadata:()=>a});var t=s(3326);let n="force-dynamic";async function a({params:e}){return{title:`${e.username} - OnlyRules`,description:`View ${e.username}'s IDE rules and configurations.`,openGraph:{title:`${e.username} - OnlyRules`,description:`View ${e.username}'s IDE rules and configurations.`,type:"profile"},twitter:{card:"summary",title:`${e.username} - OnlyRules`,description:`View ${e.username}'s IDE rules and configurations.`}}}async function i({params:e}){return t.jsx("div",{className:"container max-w-4xl py-8",children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold mb-4",children:"User Profile"}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Username: ",e.username]}),t.jsx("p",{className:"text-sm text-muted-foreground mt-4",children:"This page is being built. Please check back soon."})]})})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8208,1051,6808,9536,4562],()=>s(8293));module.exports=t})();