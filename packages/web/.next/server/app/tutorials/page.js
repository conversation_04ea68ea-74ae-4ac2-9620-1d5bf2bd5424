(()=>{var e={};e.id=171,e.ids=[171],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8e3:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.ZP,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(6427),a=s(7953),i=s(2086),n=s(5288),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let o=r.AppPageRouteModule,d=["",{children:["tutorials",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4340)),"/mnt/persist/workspace/packages/web/app/tutorials/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],c=["/mnt/persist/workspace/packages/web/app/tutorials/page.tsx"],p="/tutorials/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new o({definition:{kind:a.x.APP_PAGE,page:"/tutorials/page",pathname:"/tutorials",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3245:()=>{},4340:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>a});var r=s(3326);let a="force-dynamic";function i(){return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,r.jsxs)("div",{className:"text-center space-y-4",children:[r.jsx("h1",{className:"text-4xl font-bold",children:"Tutorials & Guides"}),r.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Learn how to maximize your productivity with AI-powered IDEs and prompt engineering"})]}),(0,r.jsxs)("div",{className:"mt-16",children:[r.jsx("h2",{className:"text-2xl font-bold mb-8",children:"All Tutorials"}),r.jsx("div",{className:"grid md:grid-cols-2 gap-6",children:[{title:"Getting Started with Cursor AI",description:"Learn the basics of setting up and using Cursor IDE with AI assistance",duration:"15 min",level:"Beginner"},{title:"Advanced Prompt Engineering in Cursor",description:"Master advanced techniques for writing effective AI prompts",duration:"30 min",level:"Advanced"},{title:"Windsurf IDE Introduction",description:"Get started with Windsurf IDE and its AI capabilities",duration:"18 min",level:"Beginner"}].map((e,t)=>r.jsx("div",{className:"border rounded-lg p-6 hover:shadow-lg transition-shadow",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[r.jsx("h3",{className:"text-xl font-semibold",children:e.title}),r.jsx("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm",children:e.level})]}),r.jsx("p",{className:"text-gray-600",children:e.description}),r.jsx("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:r.jsx("span",{children:e.duration})}),r.jsx("button",{className:"w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600",children:"Start Tutorial"})]})},t))})]}),(0,r.jsxs)("div",{className:"mt-16 text-center bg-gray-50 rounded-lg p-8",children:[r.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Ready to Get Started?"}),r.jsx("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Join thousands of developers who are already using OnlyRules to enhance their AI coding experience."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[r.jsx("button",{className:"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600",children:"Browse All Tutorials"}),r.jsx("button",{className:"border border-gray-300 px-6 py-2 rounded hover:bg-gray-100",children:"Join Community"})]})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8208,1051,6808,9536,4562],()=>s(8e3));module.exports=r})();