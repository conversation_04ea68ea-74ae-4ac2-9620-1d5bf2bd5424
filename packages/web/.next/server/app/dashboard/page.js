(()=>{var e={};e.id=7702,e.ids=[7702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9254:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.ZP,__next_app__:()=>x,originalPathname:()=>h,pages:()=>o,routeModule:()=>u,tree:()=>d});var l=t(6427),i=t(7953),n=t(2086),a=t(5288),r={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>a[e]);t.d(s,r);let c=l.AppPageRouteModule,d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9317)),"/mnt/persist/workspace/packages/web/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],o=["/mnt/persist/workspace/packages/web/app/dashboard/page.tsx"],h="/dashboard/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new c({definition:{kind:i.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3419:(e,s,t)=>{Promise.resolve().then(t.bind(t,2597))},2597:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Q,dynamic:()=>X});var l=t(2395),i=t(8726),n=t(3282),a=t(4602),r=t(6812),c=t(3558),d=t(2602),o=t(2751),h=t(8962),x=t(5353),u=t(4202),m=t(4716),g=t(5539),p=t(8467),j=t(767),f=t(7560),v=t(4105),y=t(5104),b=t(4850),w=t(6617),N=t(583),C=t(1697),k=t(4463),z=t(1155),E=t(6483),S=t(6394),A=t(4693),R=t(6706);function I(...e){return(0,R.m6)((0,A.W)(e))}let T=i.createContext({size:"2",variant:"soft"}),P=i.forwardRef(({className:e,variant:s,size:t,children:i,...n},a)=>l.jsx(S.fC,{ref:a,className:I("flex items-center justify-center gap-1",e),...n,children:l.jsx(T.Provider,{value:{variant:s,size:t},children:i})}));P.displayName=S.fC.displayName;let Z=i.forwardRef(({className:e,children:s,variant:t,size:i,...n},a)=>l.jsx(S.ck,{ref:a,className:I("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",e),...n,children:s}));Z.displayName=S.ck.displayName;var D=t(1607),O=t(3840),$=t(9336),L=t(7146),_=t(5460),U=t(7930),F=t(1657),G=t(4748),V=t(546);function q(e){if(!e.trim())return[{id:B(),title:"Section 1",content:""}];let s=e.split(/^---\s*$/m);if(1===s.length){let s=e.match(/^#\s+(.+)/m),t=s?s[1].trim():"Section 1";return[{id:B(),title:t,content:e.trim()}]}let t=[],l={};for(let e=0;e<s.length;e++){let i=s[e].trim();if(!i)continue;let n=i.split("\n"),a=!0,r={};for(let e of n){let s=e.trim();if(!s)continue;let t=s.match(/^(description|name|globs):\s*(.+)$/);if(t){let[,e,s]=t;r[e]=s.trim()}else{a=!1;break}}if(a&&Object.keys(r).length>0)l={...l,...r};else{let e=i,s={...l},a=n.slice(),r=0;for(let e=0;e<n.length;e++){let t=n[e].trim();if(!t){r=e+1;continue}let l=t.match(/^(description|name|globs):\s*(.+)$/);if(l){let[,t,i]=l;s[t]=i.trim(),r=e+1}else break}if(e=a.slice(r).join("\n").trim()){let i=e.match(/^#\s+(.+)/m),n=i?i[1].trim():`Section ${t.length+1}`;t.push({id:B(),title:n,content:e,...s}),l={}}}}return t.length>0?t:[{id:B(),title:"Section 1",content:e.trim()}]}function M(e){return 0===e.length?"":1!==e.length||e[0].description||e[0].name||e[0].globs?e.map(s=>{let t=[],l=s.description||s.name||s.globs,i=0===e.indexOf(s);return(!i||l)&&t.push("---"),s.description&&t.push(`description: ${s.description}`),s.name&&t.push(`name: ${s.name}`),s.globs&&t.push(`globs: ${s.globs}`),l&&t.push("---"),t.push(""),t.push(s.content),t.join("\n")}).join("\n\n"):e[0].content}function B(){return`section_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function Y({section:e,index:s,totalSections:t,isExpanded:c,onUpdate:x,onDelete:u,onDuplicate:m,onMoveUp:g,onMoveDown:p,onToggleExpanded:j}){let[f,v]=(0,i.useState)([]),y=(s,t)=>{let l={...e,[s]:t};x(l);let i=function(e){let s=[];return e.title.trim()||s.push("Section title is required"),e.content.trim()||s.push("Section content is required"),s}(l);v(i)},b=function(e){let s=e.content.slice(0,100).replace(/\n/g," ");return s.length>100?`${s}...`:s}(e),w=f.length>0;return(0,l.jsxs)(o.Z,{className:w?"error-border":"",style:{transition:"all 0.2s ease"},children:[(0,l.jsxs)("div",{style:{paddingBottom:"var(--space-3)"},children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",children:[(0,l.jsxs)(r.k,{align:"center",gap:"3",style:{flex:1},children:[(0,l.jsxs)(r.k,{align:"center",gap:"1",children:[l.jsx(L.Z,{size:16,style:{color:"var(--gray-9)",cursor:"grab"}}),l.jsx(d.x,{size:"2",weight:"medium",style:{color:"var(--gray-9)"},children:s+1})]}),(0,l.jsxs)(a.x,{style:{flex:1},children:[l.jsx("div",{className:"font-semibold",style:{fontSize:"var(--font-size-3)"},children:e.title||`Section ${s+1}`}),!c&&l.jsx(d.x,{size:"2",style:{color:"var(--gray-9)",marginTop:"var(--space-1)"},truncate:!0,children:b})]})]}),(0,l.jsxs)(r.k,{align:"center",gap:"2",children:[(0,l.jsxs)(r.k,{align:"center",children:[l.jsx(n.z,{variant:"ghost",size:"1",onClick:g,disabled:0===s,style:{width:"32px",height:"32px",padding:0},children:l.jsx(_.Z,{size:16})}),l.jsx(n.z,{variant:"ghost",size:"1",onClick:p,disabled:s===t-1,style:{width:"32px",height:"32px",padding:0},children:l.jsx(U.Z,{size:16})})]}),(0,l.jsxs)(O.fC,{children:[l.jsx(O.xz,{children:l.jsx(n.z,{variant:"ghost",size:"1",style:{width:"32px",height:"32px",padding:0},children:l.jsx(F.Z,{size:16})})}),(0,l.jsxs)(O.VY,{align:"end",children:[(0,l.jsxs)(O.ck,{onClick:m,children:[l.jsx(G.Z,{size:16,style:{marginRight:"var(--space-2)"}}),"Duplicate"]}),(0,l.jsxs)(O.ck,{onClick:u,disabled:1===t,className:"error-text",children:[l.jsx(V.Z,{size:16,style:{marginRight:"var(--space-2)"}}),"Delete"]})]})]}),l.jsx(n.z,{variant:"ghost",size:"1",onClick:j,style:{width:"32px",height:"32px",padding:0},children:c?l.jsx(_.Z,{size:16}):l.jsx(U.Z,{size:16})})]})]}),w&&l.jsx(a.x,{style:{marginTop:"var(--space-2)"},children:f.map((e,s)=>(0,l.jsxs)(d.x,{size:"2",className:"error-text",style:{display:"block"},children:["• ",e]},s))})]}),c&&(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"var(--space-4)"},children:[(0,l.jsxs)(a.x,{children:[l.jsx(d.x,{as:"label",htmlFor:`section-title-${e.id}`,size:"2",weight:"medium",children:"Section Title"}),l.jsx(h.f,{id:`section-title-${e.id}`,value:e.title,onChange:e=>y("title",e.target.value),placeholder:"Enter section title..."})]}),(0,l.jsxs)($.r,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,l.jsxs)(a.x,{children:[l.jsx(d.x,{as:"label",htmlFor:`section-name-${e.id}`,size:"2",weight:"medium",children:"Name (Optional)"}),l.jsx(h.f,{id:`section-name-${e.id}`,value:e.name||"",onChange:e=>y("name",e.target.value),placeholder:"e.g., global, stylesheet"})]}),(0,l.jsxs)(a.x,{children:[l.jsx(d.x,{as:"label",htmlFor:`section-globs-${e.id}`,size:"2",weight:"medium",children:"File Patterns (Optional)"}),l.jsx(h.f,{id:`section-globs-${e.id}`,value:e.globs||"",onChange:e=>y("globs",e.target.value),placeholder:"e.g., **.css, *.js"})]}),(0,l.jsxs)(a.x,{children:[l.jsx(d.x,{as:"label",htmlFor:`section-description-${e.id}`,size:"2",weight:"medium",children:"Description (Optional)"}),l.jsx(h.f,{id:`section-description-${e.id}`,value:e.description||"",onChange:e=>y("description",e.target.value),placeholder:"Brief description"})]})]}),(0,l.jsxs)(a.x,{children:[l.jsx(d.x,{as:"label",htmlFor:`section-content-${e.id}`,size:"2",weight:"medium",children:"Content"}),l.jsx(E.p,{value:e.content,onChange:e=>y("content",e),placeholder:"Enter section content (markdown supported)..."})]})]})]})}function K({sections:e,onSectionsChange:s}){let[t,a]=(0,i.useState)(new Set(1===e.length?[e[0]?.id]:[])),r=(t,l)=>{let i=[...e];i[t]=l,s(i)},c=()=>{var t;let l=(t=e.length,{id:B(),title:`Section ${t+1}`,content:""}),i=[...e,l];s(i),a(e=>new Set([...e,l.id]))},o=t=>{if(1===e.length)return;let l=e[t],i=e.filter((e,s)=>s!==t);s(i),a(e=>{let s=new Set(e);return s.delete(l.id),s})},h=t=>{let l=e[t],i={...l,id:B(),title:`${l.title} (Copy)`},n=[...e];n.splice(t+1,0,i),s(n),a(e=>new Set([...e,i.id]))},x=(t,l)=>{let i=function(e,s,t){let l=[...e],[i]=l.splice(s,1);return l.splice(t,0,i),l}(e,t,l);s(i)},u=e=>{a(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t})};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[l.jsx(d.x,{className:"text-base font-medium",children:"Rule Sections"}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",e.length," section",1!==e.length?"s":"",")"]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[e.length>1&&(0,l.jsxs)(l.Fragment,{children:[l.jsx(n.z,{variant:"ghost",size:"1",onClick:()=>{a(new Set([...e.map(e=>e.id)]))},className:"text-xs",children:"Expand All"}),l.jsx(n.z,{variant:"ghost",size:"1",onClick:()=>{a(new Set([]))},className:"text-xs",children:"Collapse All"})]}),(0,l.jsxs)(n.z,{variant:"outline",size:"1",onClick:c,className:"gap-2",children:[l.jsx(p.Z,{className:"h-4 w-4"}),"Add Section"]})]})]}),l.jsx("div",{className:"space-y-3",children:0===e.length?(0,l.jsxs)("div",{className:"text-center py-12 border-2 border-dashed border-muted rounded-lg",children:[l.jsx(D.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No sections yet"}),l.jsx("p",{className:"text-muted-foreground mb-4",children:"Add your first section to get started"}),(0,l.jsxs)(n.z,{onClick:c,className:"gap-2",children:[l.jsx(p.Z,{className:"h-4 w-4"}),"Add Section"]})]}):e.map((s,i)=>l.jsx(Y,{section:s,index:i,totalSections:e.length,isExpanded:t.has(s.id),onUpdate:e=>r(i,e),onDelete:()=>o(i),onDuplicate:()=>h(i),onMoveUp:()=>x(i,i-1),onMoveDown:()=>x(i,i+1),onToggleExpanded:()=>u(s.id)},s.id))}),e.length>0&&(0,l.jsxs)("div",{className:"text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg",children:[l.jsx("p",{className:"font-medium mb-1",children:"Section Format:"}),(0,l.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,l.jsxs)("li",{children:["• Sections are separated by ",l.jsx("code",{children:"---"})," delimiters"]}),(0,l.jsxs)("li",{children:["• Optional metadata: ",l.jsx("code",{children:"description"}),", ",l.jsx("code",{children:"name"}),", ",l.jsx("code",{children:"globs"})]}),l.jsx("li",{children:"• Content supports markdown formatting"}),l.jsx("li",{children:"• Use file patterns (globs) to target specific file types"})]})]})]})}function W({rule:e,onSave:s,onCancel:t}){let[a,c]=(0,i.useState)(e?.title||""),[o,m]=(0,i.useState)(e?.description||""),[g,f]=(0,i.useState)([]),[v,y]=(0,i.useState)(e?.content||""),[b,w]=(0,i.useState)("simple"),[N,S]=(0,i.useState)(e?.ideType||"GENERAL"),[A,R]=(0,i.useState)(e?.visibility||"PRIVATE"),[I,T]=(0,i.useState)(e?.tags.map(e=>e.tag.name)||[]),[D,O]=(0,i.useState)("");(0,i.useEffect)(()=>{if(e?.content){let s=q(e.content);f(s),y(e.content)}else{let e=[{id:`section_${Date.now()}`,title:"Section 1",content:""}];f(e),y("")}},[e?.content]);let $=()=>{D.trim()&&!I.includes(D.trim())&&(T([...I,D.trim()]),O(""))},L=e=>{T(I.filter(s=>s!==e))},_=e=>{if(e!==b){if("simple"===e){let e=M(g);y(e)}else{let e=q(v);f(e)}w(e)}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[l.jsx(d.x,{as:"label",htmlFor:"title",size:"2",weight:"medium",children:"Rule Title"}),l.jsx(h.f,{id:"title",value:a,onChange:e=>c(e.target.value),placeholder:"Enter rule title..."})]}),(0,l.jsxs)("div",{children:[l.jsx(d.x,{as:"label",htmlFor:"description",size:"2",weight:"medium",children:"Description"}),l.jsx(C.K,{id:"description",value:o,onChange:e=>m(e.target.value),placeholder:"Describe what this rule does...",rows:3})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[l.jsx(d.x,{as:"label",htmlFor:"ide-type",size:"2",weight:"medium",children:"IDE Type"}),(0,l.jsxs)(x.fC,{value:N,onValueChange:e=>S(e),children:[l.jsx(x.xz,{placeholder:"Select IDE type"}),(0,l.jsxs)(x.VY,{children:[l.jsx(x.ck,{value:"GENERAL",children:"General"}),l.jsx(x.ck,{value:"CURSOR",children:"Cursor"}),l.jsx(x.ck,{value:"AUGMENT",children:"Augment Code"}),l.jsx(x.ck,{value:"WINDSURF",children:"Windsurf"}),l.jsx(x.ck,{value:"CLAUDE",children:"Claude"}),l.jsx(x.ck,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),l.jsx(x.ck,{value:"GEMINI",children:"Gemini"}),l.jsx(x.ck,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),l.jsx(x.ck,{value:"CLINE",children:"Cline"}),l.jsx(x.ck,{value:"JUNIE",children:"Junie"}),l.jsx(x.ck,{value:"TRAE",children:"Trae"}),l.jsx(x.ck,{value:"LINGMA",children:"Lingma"}),l.jsx(x.ck,{value:"KIRO",children:"Kiro"}),l.jsx(x.ck,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx(d.x,{as:"label",htmlFor:"visibility",size:"2",weight:"medium",children:"Visibility"}),(0,l.jsxs)(x.fC,{value:A,onValueChange:e=>R(e),children:[l.jsx(x.xz,{placeholder:"Select visibility"}),(0,l.jsxs)(x.VY,{children:[l.jsx(x.ck,{value:"PRIVATE",children:"Private"}),l.jsx(x.ck,{value:"PUBLIC",children:"Public"})]})]})]})]}),(0,l.jsxs)("div",{children:[l.jsx(d.x,{as:"label",size:"2",weight:"medium",children:"Tags"}),l.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:I.map(e=>(0,l.jsxs)(u.C,{variant:"soft",className:"gap-1",children:[e,l.jsx(n.z,{variant:"ghost",size:"1",className:"h-auto p-0 w-4 h-4",onClick:()=>L(e),children:l.jsx(k.Z,{className:"h-3 w-3"})})]},e))}),(0,l.jsxs)("div",{className:"flex gap-2",children:[l.jsx(h.f,{value:D,onChange:e=>O(e.target.value),placeholder:"Add a tag...",onKeyPress:e=>"Enter"===e.key&&$()}),l.jsx(n.z,{onClick:$,size:"1",children:l.jsx(p.Z,{className:"h-4 w-4"})})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",children:[l.jsx(d.x,{as:"label",size:"2",weight:"medium",children:"Rule Content"}),(0,l.jsxs)(P,{type:"single",value:b,onValueChange:e=>e&&_(e),className:"bg-gray-50 dark:bg-gray-800 rounded-md p-1",children:[(0,l.jsxs)(Z,{value:"simple",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[l.jsx(j.Z,{className:"h-4 w-4"}),"Simple"]}),(0,l.jsxs)(Z,{value:"advanced",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[l.jsx(z.Z,{className:"h-4 w-4"}),"Advanced"]})]})]}),"simple"===b?(0,l.jsxs)("div",{children:[l.jsx(d.x,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content in a simple code editor format"}),l.jsx(E.p,{value:v,onChange:e=>{if(y(e),"simple"===b){let s=q(e);f(s)}},placeholder:"Enter your rule content here...",className:"min-h-[300px]"})]}):(0,l.jsxs)("div",{children:[l.jsx(d.x,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content using structured sections with metadata"}),l.jsx(K,{sections:g,onSectionsChange:e=>{if(f(e),"advanced"===b){let s=M(e);y(s)}}})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[l.jsx(n.z,{variant:"outline",onClick:t,children:"Cancel"}),l.jsx(n.z,{onClick:()=>{let e="simple"===b?v:M(g);s({title:a,description:o,content:e,ideType:N,visibility:A,tags:I})},children:e?"Update Rule":"Create Rule"})]})]})}var H=t(6643),J=t(7999);let X="force-dynamic";function Q(){let{data:e}=(0,H.kP)(),[s,t]=(0,i.useState)([]),[C,k]=(0,i.useState)([]),[z,E]=(0,i.useState)(!0),[S,A]=(0,i.useState)(""),[R,I]=(0,i.useState)([]),[T,P]=(0,i.useState)("ALL"),[Z,D]=(0,i.useState)(!1),[O,$]=(0,i.useState)(null);(0,i.useEffect)(()=>{console.log("Dialog state changed:",Z)},[Z]),(0,i.useEffect)(()=>{let e=e=>{"Escape"===e.key&&Z&&D(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[Z]),(0,i.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll("*"),s=[];e.forEach(e=>{let t=window.getComputedStyle(e),l=e.getBoundingClientRect();"fixed"===t.position&&l.width>=.9*window.innerWidth&&l.height>=.9*window.innerHeight&&"none"!==t.pointerEvents&&s.push(e)}),s.length>0&&(console.warn("Found blocking elements:",s),s.forEach(e=>{console.log("Blocking element:",{element:e,classes:e.className,id:e.id,zIndex:window.getComputedStyle(e).zIndex,display:window.getComputedStyle(e).display})}))};e();let s=setTimeout(e,1e3);return()=>clearTimeout(s)},[]);let L=(0,i.useCallback)(async()=>{try{let e=new URLSearchParams;S&&e.set("search",S),R.length>0&&e.set("tags",R.join(",")),"ALL"!==T&&e.set("ideType",T);let s=await fetch(`/api/rules?${e}`),l=await s.json();t(Array.isArray(l)?l:[])}catch(e){console.error("Error fetching rules:",e),J.Am.error("Failed to fetch rules"),t([])}finally{E(!1)}},[S,R,T]),_=async()=>{try{let e=await fetch("/api/tags"),s=await e.json();k(s)}catch(e){console.error("Error fetching tags:",e)}};(0,i.useEffect)(()=>{L(),_()},[L,S,R,T]);let U=()=>{$(null),D(!0)},F=e=>{$(e),D(!0)},G=async e=>{try{let s=O?"PUT":"POST",t=O?`/api/rules/${O.id}`:"/api/rules",l=await fetch(t,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});l.ok?(J.Am.success(O?"Rule updated":"Rule created"),D(!1),$(null),L()):J.Am.error("Failed to save rule")}catch(e){console.error("Error saving rule:",e),J.Am.error("Failed to save rule")}},V=async e=>{try{let s=await fetch(`/api/rules/${e}`,{method:"DELETE"});s.ok?(J.Am.success("Rule deleted"),L()):J.Am.error("Failed to delete rule")}catch(e){console.error("Error deleting rule:",e),J.Am.error("Failed to delete rule")}},q=e=>{I(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])};if(!e?.user)return l.jsx("div",{className:"container py-8",children:(0,l.jsxs)("div",{className:"text-center",children:[l.jsx("h1",{className:"text-2xl font-bold mb-4",style:{color:"hsl(0 0% 98%)"},children:"Please sign in to continue"}),l.jsx(n.z,{asChild:!0,children:l.jsx("a",{href:"/auth/signin",children:"Sign In"})})]})});let M=e||null,B=Array.isArray(s)?s.filter(e=>e.userId===M?.user?.id):[],Y=Array.isArray(s)?s.filter(e=>"PUBLIC"===e.visibility):[],K={totalRules:B.length,publicRules:B.filter(e=>"PUBLIC"===e.visibility).length,privateRules:B.filter(e=>"PRIVATE"===e.visibility).length,totalViews:0};return(0,l.jsxs)(a.x,{className:"container py-8 space-y-8",children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",children:[(0,l.jsxs)(a.x,{children:[l.jsx(c.X,{size:"8",weight:"bold",color:"gray",highContrast:!0,children:"Dashboard"}),l.jsx(d.x,{size:"3",color:"gray",children:"Manage your AI prompt rules and explore the community"})]}),(0,l.jsxs)(n.z,{onClick:U,children:[l.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"New Rule"]})]}),(0,l.jsxs)(a.x,{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,l.jsxs)(o.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[l.jsx(d.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Total Rules"}),l.jsx(j.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),l.jsx(d.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:K.totalRules})]}),(0,l.jsxs)(o.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[l.jsx(d.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Public Rules"}),l.jsx(f.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),l.jsx(d.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:K.publicRules})]}),(0,l.jsxs)(o.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[l.jsx(d.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Private Rules"}),l.jsx(v.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),l.jsx(d.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:K.privateRules})]}),(0,l.jsxs)(o.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[l.jsx(d.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Total Views"}),l.jsx(y.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),l.jsx(d.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:K.totalViews})]})]}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,l.jsxs)("div",{className:"relative flex-1",children:[l.jsx(b.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),l.jsx(h.f,{placeholder:"Search rules...",value:S,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,l.jsxs)(x.fC,{value:T,onValueChange:P,children:[l.jsx(x.xz,{className:"w-full md:w-48",placeholder:"IDE Type"}),(0,l.jsxs)(x.VY,{children:[l.jsx(x.ck,{value:"ALL",children:"All IDEs"}),l.jsx(x.ck,{value:"GENERAL",children:"General"}),l.jsx(x.ck,{value:"CURSOR",children:"Cursor"}),l.jsx(x.ck,{value:"AUGMENT",children:"Augment Code"}),l.jsx(x.ck,{value:"WINDSURF",children:"Windsurf"}),l.jsx(x.ck,{value:"CLAUDE",children:"Claude"}),l.jsx(x.ck,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),l.jsx(x.ck,{value:"GEMINI",children:"Gemini"}),l.jsx(x.ck,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),l.jsx(x.ck,{value:"CLINE",children:"Cline"}),l.jsx(x.ck,{value:"JUNIE",children:"Junie"}),l.jsx(x.ck,{value:"TRAE",children:"Trae"}),l.jsx(x.ck,{value:"LINGMA",children:"Lingma"}),l.jsx(x.ck,{value:"KIRO",children:"Kiro"}),l.jsx(x.ck,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),C.length>0&&(0,l.jsxs)(a.x,{className:"space-y-2",children:[(0,l.jsxs)(r.k,{align:"center",gap:"2",children:[l.jsx(w.Z,{className:"h-4 w-4",style:{color:"var(--gray-12)"}}),l.jsx(d.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Filter by tags:"})]}),l.jsx("div",{className:"flex flex-wrap gap-2",children:C.map(e=>l.jsx(u.C,{variant:R.includes(e.name)?"solid":"outline",className:"cursor-pointer",onClick:()=>q(e.name),style:{borderColor:e.color,backgroundColor:R.includes(e.name)?e.color:"transparent"},children:e.name},e.id))})]}),(0,l.jsxs)(m.fC,{defaultValue:"my-rules",className:"space-y-6",children:[(0,l.jsxs)(m.aV,{children:[(0,l.jsxs)(m.xz,{value:"my-rules",children:["My Rules (",B.length,")"]}),(0,l.jsxs)(m.xz,{value:"community",children:["Community (",Y.length,")"]})]}),l.jsx(m.VY,{value:"my-rules",className:"space-y-6",children:0===B.length?(0,l.jsxs)(o.Z,{className:"py-12 text-center",children:[l.jsx(j.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No rules yet"}),l.jsx("p",{className:"text-muted-foreground mb-4",children:"Create your first AI prompt rule to get started"}),(0,l.jsxs)(n.z,{onClick:U,children:[l.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"Create Rule"]})]}):l.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:B.map(e=>l.jsx(N.q,{rule:e,onEdit:F,onDelete:V,isOwner:!0},e.id))})}),l.jsx(m.VY,{value:"community",className:"space-y-6",children:0===Y.length?(0,l.jsxs)(o.Z,{className:"py-12 text-center",children:[l.jsx(f.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No community rules found"}),l.jsx("p",{className:"text-muted-foreground",children:"adjusting your filters or check back later"})]}):l.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:Y.map(e=>l.jsx(N.q,{rule:e,isOwner:e.userId===M?.user?.id},e.id))})})]}),l.jsx(g.fC,{open:Z,onOpenChange:D,children:(0,l.jsxs)(g.VY,{className:"max-w-4xl max-h-[90vh] overflow-hidden",children:[l.jsx(g.Dx,{children:O?"Edit Rule":"Create New Rule"}),l.jsx("div",{className:"overflow-y-auto max-h-[70vh]",children:l.jsx(W,{rule:O||void 0,onSave:G,onCancel:()=>{D(!1),$(null)}})})]})})]})}},9317:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>a,__esModule:()=>n,default:()=>d,dynamic:()=>c});var l=t(6962);let i=(0,l.createProxy)(String.raw`/mnt/persist/workspace/packages/web/app/dashboard/page.tsx`),{__esModule:n,$$typeof:a}=i,r=i.default,c=i.dynamic,d=r}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),l=s.X(0,[8208,1051,6808,3765,4389,8962,6093,5717,6678,9536,4562,583],()=>t(9254));module.exports=l})();