(()=>{var e={};e.id=5271,e.ids=[5271],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2309:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.ZP,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(6427),r=s(7953),n=s(2086),i=s(5288),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l=a.AppPageRouteModule,o=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4184)),"/mnt/persist/workspace/packages/web/app/auth/signup/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],d=["/mnt/persist/workspace/packages/web/app/auth/signup/page.tsx"],u="/auth/signup/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new l({definition:{kind:r.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2708:(e,t,s)=>{Promise.resolve().then(s.bind(s,3045))},3045:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h,dynamic:()=>x});var a=s(2395),r=s(8726),n=s(4555),i=s(4288),c=s.n(i),l=s(2751),o=s(7966),d=s(3282),u=s(6643),p=s(767),m=s(7132);let x="force-dynamic";function h(){let[e,t]=(0,r.useState)(!1),[s,i]=(0,r.useState)("");(0,n.useRouter)();let x=async()=>{t(!0),i("");try{let e=await u.zB.social({provider:"github",callbackURL:"/dashboard"});e.error&&i(e.error.message||"Failed to sign in with GitHub")}catch(e){i("An unexpected error occurred")}finally{t(!1)}};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[a.jsx("div",{className:"text-center",children:(0,a.jsxs)(c(),{href:"/",className:"inline-flex items-center gap-2 mb-8",children:[a.jsx(p.Z,{className:"h-8 w-8 text-primary"}),a.jsx("span",{className:"font-bold text-2xl",children:"OnlyRules"})]})}),(0,a.jsxs)(l.Z,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[a.jsx("div",{className:"text-2xl text-center font-semibold",children:"Create an account"}),a.jsx("div",{className:"text-center text-muted-foreground",children:"Sign up with your GitHub account to get started"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[s&&a.jsx(o.fC,{color:"red",children:a.jsx(o.xv,{children:s})}),(0,a.jsxs)(d.z,{onClick:x,className:"w-full",disabled:e,size:"3",children:[a.jsx(m.Z,{className:"mr-2 h-5 w-5"}),e?"Signing up...":"Continue with GitHub"]}),a.jsx("div",{className:"text-center text-sm text-muted-foreground",children:"By signing up, you agree to our terms of service and privacy policy."}),(0,a.jsxs)("div",{className:"text-center text-sm",children:["Already have an account?"," ",a.jsx(c(),{href:"/auth/signin",className:"text-primary hover:underline",children:"Sign in"})]})]})]})]})})}},4184:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o,dynamic:()=>l});var a=s(6962);let r=(0,a.createProxy)(String.raw`/mnt/persist/workspace/packages/web/app/auth/signup/page.tsx`),{__esModule:n,$$typeof:i}=r,c=r.default,l=r.dynamic,o=c},4555:(e,t,s)=>{e.exports=s(5086)},2751:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var a=s(8726),r=s(7253),n=s(120),i=s(3564);let c={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var l=s(38),o=s(1260);let d=a.forwardRef((e,t)=>{let{asChild:s,className:i,...d}=(0,l.y)(e,c,o.E),u=s?n.fC:"div";return a.createElement(u,{ref:t,...d,className:r("rt-reset","rt-BaseCard","rt-Card",i)})});d.displayName="Card"}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8208,1051,6808,7966,9536,4562],()=>s(2309));module.exports=a})();