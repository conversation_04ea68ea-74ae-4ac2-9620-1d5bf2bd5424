(()=>{var e={};e.id=8098,e.ids=[8098],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2707:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.ZP,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=t(6427),a=t(7953),n=t(2086),i=t(5288),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let l=r.AppPageRouteModule,o=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8698)),"/mnt/persist/workspace/packages/web/app/auth/signin/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],d=["/mnt/persist/workspace/packages/web/app/auth/signin/page.tsx"],u="/auth/signin/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new l({definition:{kind:a.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9592:(e,s,t)=>{Promise.resolve().then(t.bind(t,1253))},1253:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g,dynamic:()=>x});var r=t(2395),a=t(8726),n=t(4555),i=t(4288),c=t.n(i),l=t(2751),o=t(7966),d=t(3282),u=t(6643),p=t(767),m=t(7132);let x="force-dynamic";function g(){let[e,s]=(0,a.useState)(!1),[t,i]=(0,a.useState)("");(0,n.useRouter)();let x=async()=>{s(!0),i("");try{let e=await u.zB.social({provider:"github",callbackURL:"/dashboard"});e.error&&i(e.error.message||"Failed to sign in with GitHub")}catch(e){i("An unexpected error occurred")}finally{s(!1)}};return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[r.jsx("div",{className:"text-center",children:(0,r.jsxs)(c(),{href:"/",className:"inline-flex items-center gap-2 mb-8",children:[r.jsx(p.Z,{className:"h-8 w-8 text-primary"}),r.jsx("span",{className:"font-bold text-2xl",children:"OnlyRules"})]})}),(0,r.jsxs)(l.Z,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("div",{className:"text-2xl text-center font-semibold",children:"Welcome back"}),r.jsx("div",{className:"text-center text-muted-foreground",children:"Sign in with your GitHub account to continue"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[t&&r.jsx(o.fC,{color:"red",children:r.jsx(o.xv,{children:t})}),(0,r.jsxs)(d.z,{onClick:x,className:"w-full",disabled:e,size:"3",children:[r.jsx(m.Z,{className:"mr-2 h-5 w-5"}),e?"Signing in...":"Continue with GitHub"]}),r.jsx("div",{className:"text-center text-sm text-muted-foreground",children:"By signing in, you agree to our terms of service and privacy policy."})]})]})]})})}},8698:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>n,default:()=>o,dynamic:()=>l});var r=t(6962);let a=(0,r.createProxy)(String.raw`/mnt/persist/workspace/packages/web/app/auth/signin/page.tsx`),{__esModule:n,$$typeof:i}=a,c=a.default,l=a.dynamic,o=c},4555:(e,s,t)=>{e.exports=t(5086)},2751:(e,s,t)=>{"use strict";t.d(s,{Z:()=>d});var r=t(8726),a=t(7253),n=t(120),i=t(3564);let c={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var l=t(38),o=t(1260);let d=r.forwardRef((e,s)=>{let{asChild:t,className:i,...d}=(0,l.y)(e,c,o.E),u=t?n.fC:"div";return r.createElement(u,{ref:s,...d,className:a("rt-reset","rt-BaseCard","rt-Card",i)})});d.displayName="Card"}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8208,1051,6808,7966,9536,4562],()=>t(2707));module.exports=r})();