(()=>{var e={};e.id=4557,e.ids=[4557],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4217:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>t.ZP,__next_app__:()=>h,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o});var i=r(6427),n=r(7953),t=r(2086),l=r(5288),a={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(s,a);let c=i.AppPageRouteModule,o=["",{children:["test-theme",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1643)),"/mnt/persist/workspace/packages/web/app/test-theme/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,5329)),"/mnt/persist/workspace/packages/web/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,3310)),"/mnt/persist/workspace/packages/web/app/not-found.tsx"]}],d=["/mnt/persist/workspace/packages/web/app/test-theme/page.tsx"],x="/test-theme/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new c({definition:{kind:n.x.APP_PAGE,page:"/test-theme/page",pathname:"/test-theme",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8079:(e,s,r)=>{Promise.resolve().then(r.bind(r,3e3))},3e3:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var i=r(2395),n=r(2842),t=r(6812),l=r(4602),a=r(3558),c=r(2602),o=r(9336),d=r(2751),x=r(3282),h=r(8962),p=r(5353),j=r(4716),u=r(1735),m=r(4202),g=r(5539),v=r(8726);function k(){let[e,s]=(0,v.useState)(!1);return i.jsx(n.W,{size:"3",py:"8",children:(0,i.jsxs)(t.k,{direction:"column",gap:"8",children:[(0,i.jsxs)(l.x,{children:[i.jsx(a.X,{size:"8",mb:"4",children:"Radix UI Theme v3 Test Page"}),i.jsx(c.x,{size:"4",color:"gray",children:"This page demonstrates the proper implementation of Radix UI Theme v3 components with consistent styling and design patterns."})]}),(0,i.jsxs)(o.r,{columns:{initial:"1",md:"3"},gap:"4",children:[i.jsx(d.Z,{children:(0,i.jsxs)(t.k,{direction:"column",gap:"3",children:[i.jsx(a.X,{size:"4",children:"Classic Card"}),i.jsx(c.x,{color:"gray",children:"This is a classic card with default styling from Radix UI Themes."}),i.jsx(x.z,{variant:"soft",children:"Learn More"})]})}),i.jsx(d.Z,{variant:"surface",children:(0,i.jsxs)(t.k,{direction:"column",gap:"3",children:[i.jsx(a.X,{size:"4",children:"Surface Card"}),i.jsx(c.x,{color:"gray",children:"Surface variant provides a subtle background color."}),i.jsx(x.z,{variant:"surface",children:"Explore"})]})}),i.jsx(d.Z,{variant:"ghost",children:(0,i.jsxs)(t.k,{direction:"column",gap:"3",children:[i.jsx(a.X,{size:"4",children:"Ghost Card"}),i.jsx(c.x,{color:"gray",children:"Ghost variant has minimal visual styling."}),i.jsx(x.z,{variant:"outline",children:"Discover"})]})})]}),(0,i.jsxs)(d.Z,{size:"3",children:[i.jsx(a.X,{size:"5",mb:"4",children:"Form Elements"}),(0,i.jsxs)(t.k,{direction:"column",gap:"4",children:[(0,i.jsxs)(l.x,{children:[i.jsx(c.x,{as:"label",size:"2",weight:"medium",htmlFor:"name",children:"Name"}),i.jsx(h.f,{id:"name",placeholder:"Enter your name",mt:"1"})]}),(0,i.jsxs)(l.x,{children:[i.jsx(c.x,{as:"label",size:"2",weight:"medium",htmlFor:"role",children:"Role"}),(0,i.jsxs)(p.fC,{defaultValue:"developer",children:[i.jsx(p.xz,{id:"role",placeholder:"Select a role"}),(0,i.jsxs)(p.VY,{children:[i.jsx(p.ck,{value:"developer",children:"Developer"}),i.jsx(p.ck,{value:"designer",children:"Designer"}),i.jsx(p.ck,{value:"manager",children:"Manager"})]})]})]}),(0,i.jsxs)(t.k,{gap:"3",mt:"4",children:[i.jsx(x.z,{variant:"solid",children:"Submit"}),i.jsx(x.z,{variant:"soft",color:"gray",children:"Cancel"})]})]})]}),i.jsx(d.Z,{children:(0,i.jsxs)(j.fC,{defaultValue:"overview",children:[(0,i.jsxs)(j.aV,{children:[i.jsx(j.xz,{value:"overview",children:"Overview"}),i.jsx(j.xz,{value:"details",children:"Details"}),i.jsx(j.xz,{value:"settings",children:"Settings"})]}),(0,i.jsxs)(l.x,{pt:"3",children:[i.jsx(j.VY,{value:"overview",children:i.jsx(c.x,{size:"2",children:"This is the overview tab content. It demonstrates how tabs work in Radix UI Themes with proper spacing and typography."})}),i.jsx(j.VY,{value:"details",children:(0,i.jsxs)(u.fC,{children:[(0,i.jsxs)(u.ck,{children:[i.jsx(u.__,{children:"Status"}),i.jsx(u.B4,{children:i.jsx(m.C,{color:"green",children:"Active"})})]}),(0,i.jsxs)(u.ck,{children:[i.jsx(u.__,{children:"Version"}),i.jsx(u.B4,{children:"3.0.0"})]}),(0,i.jsxs)(u.ck,{children:[i.jsx(u.__,{children:"Theme"}),i.jsx(u.B4,{children:"Radix UI"})]})]})}),i.jsx(j.VY,{value:"settings",children:i.jsx(c.x,{size:"2",children:"Settings configuration would go here."})})]})]})}),i.jsx(t.k,{gap:"3",children:(0,i.jsxs)(g.fC,{open:e,onOpenChange:s,children:[i.jsx(g.xz,{children:i.jsx(x.z,{children:"Open Dialog"})}),(0,i.jsxs)(g.VY,{maxWidth:"450px",children:[i.jsx(g.Dx,{children:"Example Dialog"}),i.jsx(g.dk,{size:"2",mb:"4",children:"This dialog demonstrates proper theming with Radix UI v3."}),(0,i.jsxs)(t.k,{direction:"column",gap:"3",children:[i.jsx(h.f,{placeholder:"Enter some text..."}),i.jsx(c.x,{size:"2",color:"gray",children:"Dialog content is properly styled and accessible."})]}),(0,i.jsxs)(t.k,{gap:"3",mt:"4",justify:"end",children:[i.jsx(g.x8,{children:i.jsx(x.z,{variant:"soft",color:"gray",children:"Cancel"})}),i.jsx(x.z,{children:"Save Changes"})]})]})]})}),(0,i.jsxs)(l.x,{children:[i.jsx(a.X,{size:"5",mb:"4",children:"Color System"}),(0,i.jsxs)(t.k,{gap:"2",wrap:"wrap",children:[i.jsx(m.C,{children:"Default"}),i.jsx(m.C,{color:"blue",children:"Blue"}),i.jsx(m.C,{color:"green",children:"Green"}),i.jsx(m.C,{color:"red",children:"Red"}),i.jsx(m.C,{color:"orange",children:"Orange"}),i.jsx(m.C,{color:"purple",children:"Purple"}),i.jsx(m.C,{color:"pink",children:"Pink"}),i.jsx(m.C,{color:"yellow",children:"Yellow"})]})]})]})})}},1643:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>t,default:()=>c});var i=r(6962);let n=(0,i.createProxy)(String.raw`/mnt/persist/workspace/packages/web/app/test-theme/page.tsx`),{__esModule:t,$$typeof:l}=n,a=n.default,c=a}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),i=s.X(0,[8208,1051,6808,3765,8962,5717,5359,9536,4562],()=>r(4217));module.exports=i})();