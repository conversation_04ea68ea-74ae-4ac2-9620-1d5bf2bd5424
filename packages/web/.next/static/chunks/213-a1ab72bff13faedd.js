"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[213],{4213:function(e,n,i){i.d(n,{q:function(){return T}});var s=i(7573),a=i(7653),r=i(4594),c=i(5543),t=i(7808),o=i(8436),d=i(1467),l=i(1874),m=i(5438),p=i(1233),u=i(2480),x=i(3484),h=i(966),f=i(6434),g=i(273),j=i(6017),w=i(214),b=i(3696),N=i(3511),v=i(248),I=i(5105),y=i(5048);function C(e,n){let i=y.gA[n].command;return'npx onlyrules -f "'.concat(e,'" --target ').concat(i)}function E(e){return'npx onlyrules -f "'.concat(e,'"')}function D(e,n){return[...e].sort((e,i)=>e.id===n?-1:i.id===n?1:e.name.localeCompare(i.name))}async function k(e){try{return await navigator.clipboard.writeText(e),!0}catch(e){return console.error("Failed to copy to clipboard:",e),!1}}var A=i(7811),L=i(3588),O=i(2056),U=i.n(O);function T(e){var n,i,O;let{rule:T,onEdit:Z,onDelete:R,isOwner:B=!1}=e,[S,F]=(0,a.useState)(!1),[G,P]=(0,a.useState)(!1),[M]=(0,r.KO)(y.Ym),z=async()=>{await navigator.clipboard.writeText(T.content),L.Am.success("Rule content copied to clipboard")},_=async()=>{if("PUBLIC"!==T.visibility){L.Am.error("Only public rules can be used with npx command");return}let e="".concat(window.location.origin,"/api/rules/raw?id=").concat(T.id),n=E(e),i=await k(n);i?(P(!0),L.Am.success("CLI command copied to clipboard"),setTimeout(()=>P(!1),2e3)):L.Am.error("Failed to copy command to clipboard")},J=async e=>{if("PUBLIC"!==T.visibility){L.Am.error("Only public rules can be used with npx command");return}let n=M.preferredIDEs.find(n=>n.id===e);if(!n)return;let i="".concat(window.location.origin,"/api/rules/raw?id=").concat(T.id),s=C(i,n.type),a=await k(s);a?L.Am.success("".concat(n.name," command copied to clipboard")):L.Am.error("Failed to copy command to clipboard")},X=async()=>{if("PUBLIC"!==T.visibility){L.Am.error("Only public rules can be used with npx command");return}let e=M.preferredIDEs.find(e=>e.id===M.defaultIDE);if(!e)return _();let n="".concat(window.location.origin,"/api/rules/raw?id=").concat(T.id),i=C(n,e.type),s=await k(i);s?(P(!0),L.Am.success("".concat(e.name," command copied to clipboard")),setTimeout(()=>P(!1),2e3)):L.Am.error("Failed to copy command to clipboard")},Y=async()=>{if("PUBLIC"===T.visibility){let e="".concat(window.location.origin,"/rules/").concat(T.id);await navigator.clipboard.writeText(e),L.Am.success("Share link copied to clipboard")}else if(T.shareToken){let e="".concat(window.location.origin,"/shared/").concat(T.shareToken);await navigator.clipboard.writeText(e),L.Am.success("Share link copied to clipboard")}},H=async()=>{if("PUBLIC"!==T.visibility){L.Am.error("Only public rules can be downloaded as MDX");return}try{let e=await fetch("/api/rules/download?id=".concat(T.id));if(!e.ok)throw Error("Failed to download rule");let n=await e.blob(),i=URL.createObjectURL(n),s=document.createElement("a");s.href=i,s.download="".concat(T.title.toLowerCase().replace(/\s+/g,"-"),".mdx"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i),L.Am.success("Rule downloaded as MDX")}catch(e){L.Am.error("Failed to download rule as MDX")}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(c.Z,{className:"group hover:shadow-md transition-all duration-200",children:[(0,s.jsx)("div",{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:["PUBLIC"===T.visibility?(0,s.jsx)(U(),{href:"/rules/".concat(T.id),children:(0,s.jsxs)("div",{className:"text-lg font-semibold cursor-pointer hover:text-primary transition-colors inline-flex items-center gap-1",children:[T.title,(0,s.jsx)(m.Z,{className:"h-3 w-3 opacity-0 group-hover:opacity-50"})]})}):(0,s.jsx)("div",{className:"text-lg font-semibold cursor-pointer hover:text-primary transition-colors",onClick:()=>F(!0),children:T.title}),T.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:T.description})]}),(0,s.jsxs)(t.fC,{children:[(0,s.jsx)(t.xz,{children:(0,s.jsx)(o.z,{variant:"ghost",size:"1",className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,s.jsx)(p.Z,{className:"h-4 w-4"})})}),(0,s.jsxs)(t.VY,{align:"end",children:[(0,s.jsxs)(t.ck,{onClick:()=>F(!0),children:[(0,s.jsx)(u.Z,{className:"mr-2 h-4 w-4"}),"View"]}),"PUBLIC"===T.visibility&&(0,s.jsx)(t.ck,{asChild:!0,children:(0,s.jsxs)(U(),{href:"/rules/".concat(T.id),children:[(0,s.jsx)(m.Z,{className:"mr-2 h-4 w-4"}),"Open Rule Page"]})}),(0,s.jsxs)(t.ck,{onClick:z,children:[(0,s.jsx)(x.Z,{className:"mr-2 h-4 w-4"}),"Copy Content"]}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(t.Tr,{children:[(0,s.jsxs)(t.fF,{children:[(0,s.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),"Copy CLI Command"]}),(0,s.jsxs)(t.tu,{children:[M.defaultIDE&&M.preferredIDEs.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(t.ck,{onClick:X,children:[(0,s.jsx)(f.Z,{className:"mr-2 h-4 w-4 fill-current"}),null===(n=M.preferredIDEs.find(e=>e.id===M.defaultIDE))||void 0===n?void 0:n.name," (Default)"]}),(0,s.jsx)(t.Z0,{})]}),(0,s.jsxs)(t.ck,{onClick:_,children:[(0,s.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),"Basic Command"]}),M.preferredIDEs.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.Z0,{}),D(M.preferredIDEs,M.defaultIDE).filter(e=>e.id!==M.defaultIDE).map(e=>(0,s.jsxs)(t.ck,{onClick:()=>J(e.id),children:[(0,s.jsx)(g.Z,{className:"mr-2 h-4 w-4"}),e.name]},e.id))]}),0===M.preferredIDEs.length&&(0,s.jsx)("div",{className:"px-2 py-1.5 text-xs text-muted-foreground",children:"Add IDE preferences in settings for quick commands"})]})]}),(0,s.jsxs)(t.ck,{onClick:Y,children:[(0,s.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),"Share"]})]}),(0,s.jsxs)(t.Tr,{children:[(0,s.jsxs)(t.fF,{children:[(0,s.jsx)(w.Z,{className:"mr-2 h-4 w-4"}),"Download"]}),(0,s.jsxs)(t.tu,{children:[(0,s.jsxs)(t.ck,{onClick:()=>{let e={title:T.title,description:T.description,content:T.content,ideType:T.ideType,tags:T.tags.map(e=>e.tag.name)},n=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),i=URL.createObjectURL(n),s=document.createElement("a");s.href=i,s.download="".concat(T.title.toLowerCase().replace(/\s+/g,"-"),".json"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i),L.Am.success("Rule downloaded as JSON")},children:[(0,s.jsx)(b.Z,{className:"mr-2 h-4 w-4"}),"JSON"]}),(0,s.jsxs)(t.ck,{onClick:H,children:[(0,s.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"MDX"]})]})]}),B&&Z&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.Z0,{}),(0,s.jsxs)(t.ck,{onClick:()=>Z(T),children:[(0,s.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Edit"]})]}),B&&R&&(0,s.jsxs)(t.ck,{onClick:()=>R(T.id),className:"text-destructive",children:[(0,s.jsx)(I.Z,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(d.C,{variant:"soft",className:"".concat((e=>{switch(e){case"CURSOR":return"bg-blue-500";case"AUGMENT":return"bg-green-500";case"WINDSURF":return"bg-purple-500";case"CLAUDE":return"bg-orange-500";case"GITHUB_COPILOT":return"bg-gray-800";case"GEMINI":return"bg-indigo-500";case"OPENAI_CODEX":return"bg-teal-500";case"CLINE":return"bg-pink-500";case"JUNIE":return"bg-yellow-500";case"TRAE":return"bg-red-500";case"LINGMA":return"bg-cyan-500";case"KIRO":return"bg-emerald-500";case"TENCENT_CODEBUDDY":return"bg-violet-500";default:return"bg-gray-500"}})(T.ideType)," text-white"),children:[(0,s.jsx)(g.Z,{className:"mr-1 h-3 w-3"}),T.ideType]}),"PUBLIC"===T.visibility&&(0,s.jsx)(d.C,{variant:"outline",children:"Public"})]}),T.tags.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:T.tags.map(e=>(0,s.jsx)(d.C,{variant:"outline",style:{borderColor:e.tag.color},className:"text-xs",children:e.tag.name},e.tag.id))}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated ",new Date(T.updatedAt).toLocaleDateString()]})]})]}),(0,s.jsx)(l.fC,{open:S,onOpenChange:F,children:(0,s.jsxs)(l.VY,{className:"max-w-4xl max-h-[80vh] overflow-hidden",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.Dx,{children:T.title}),(0,s.jsx)(l.dk,{children:T.description})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)(A.p,{value:T.content,onChange:()=>{},className:"h-[60vh]"})}),"PUBLIC"===T.visibility&&(0,s.jsxs)("div",{className:"mt-4 space-y-3",children:[M.defaultIDE&&M.preferredIDEs.length>0&&(0,s.jsx)("div",{className:"p-3 bg-primary/5 border border-primary/20 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("p",{className:"text-sm font-medium flex items-center gap-2",children:[(0,s.jsx)(f.Z,{className:"h-4 w-4 fill-current text-primary"}),null===(i=M.preferredIDEs.find(e=>e.id===M.defaultIDE))||void 0===i?void 0:i.name," (Default):"]}),(0,s.jsx)("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:C("".concat(window.location.origin,"/api/rules/raw?id=").concat(T.id),(null===(O=M.preferredIDEs.find(e=>e.id===M.defaultIDE))||void 0===O?void 0:O.type)||"GENERAL")})]}),(0,s.jsxs)(o.z,{size:"1",variant:"outline",onClick:X,className:"ml-2",children:[(0,s.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Copy"]})]})}),(0,s.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Basic CLI Command:"}),(0,s.jsx)("code",{className:"text-xs bg-background px-2 py-1 rounded block",children:E("".concat(window.location.origin,"/api/rules/raw?id=").concat(T.id))})]}),(0,s.jsxs)(o.z,{size:"1",variant:"outline",onClick:_,className:"ml-2",children:[(0,s.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),G?"Copied!":"Copy"]})]})}),M.preferredIDEs.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Other IDE Commands:"}),(0,s.jsx)("div",{className:"grid gap-2",children:D(M.preferredIDEs,M.defaultIDE).filter(e=>e.id!==M.defaultIDE).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted/50 rounded text-xs",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 min-w-0 flex-1",children:[(0,s.jsx)(g.Z,{className:"h-3 w-3 flex-shrink-0"}),(0,s.jsxs)("span",{className:"font-medium flex-shrink-0",children:[e.name,":"]}),(0,s.jsx)("code",{className:"bg-background px-1 py-0.5 rounded truncate",children:C("".concat(window.location.origin,"/api/rules/raw?id=").concat(T.id),e.type)})]}),(0,s.jsx)(o.z,{size:"1",variant:"ghost",onClick:()=>J(e.id),className:"ml-2 h-6 w-6 p-0",children:(0,s.jsx)(x.Z,{className:"h-3 w-3"})})]},e.id))})]})]})]})})]})}},7811:function(e,n,i){i.d(n,{p:function(){return d}});var s=i(7573),a=i(8255),r=i(2486),c=i(8881),t=i(8996),o=i(188);function d(e){let{value:n,onChange:i,placeholder:d,className:l}=e,{theme:m}=(0,a.F)(),p=[(0,c.eJ)(),o.tk.theme({"&":{fontSize:"14px"},".cm-content":{padding:"16px",minHeight:"200px"},".cm-focused":{outline:"none"},".cm-editor":{borderRadius:"8px"}})];return(0,s.jsx)("div",{className:l,children:(0,s.jsx)(r.ZP,{value:n,onChange:e=>i(e),placeholder:d,theme:"dark"===m?t.vk:void 0,extensions:p,basicSetup:{lineNumbers:!0,foldGutter:!0,dropCursor:!1,allowMultipleSelections:!1,indentOnInput:!0,bracketMatching:!0,closeBrackets:!0,autocompletion:!0,highlightSelectionMatches:!1}})})}},5048:function(e,n,i){i.d(n,{Ym:function(){return c},gA:function(){return r}});var s=i(2732),a=i(8935);let r={CURSOR:{name:"Cursor",description:"AI-powered code editor",command:"cursor"},AUGMENT:{name:"Augment",description:"AI coding assistant",command:"augment"},WINDSURF:{name:"Windsurf",description:"AI development environment",command:"windsurf"},CLAUDE:{name:"Claude",description:"Anthropic AI assistant",command:"claude"},GITHUB_COPILOT:{name:"GitHub Copilot",description:"AI pair programmer",command:"github-copilot"},GEMINI:{name:"Gemini",description:"Google AI assistant",command:"gemini"},OPENAI_CODEX:{name:"OpenAI Codex",description:"OpenAI code assistant",command:"openai-codex"},CLINE:{name:"Cline",description:"AI coding assistant",command:"cline"},JUNIE:{name:"Junie",description:"AI development tool",command:"junie"},TRAE:{name:"Trae",description:"AI coding companion",command:"trae"},LINGMA:{name:"Lingma",description:"AI programming assistant",command:"lingma"},KIRO:{name:"Kiro",description:"AI development environment",command:"kiro"},TENCENT_CODEBUDDY:{name:"Tencent CodeBuddy",description:"Tencent AI coding assistant",command:"tencent-codebuddy"},GENERAL:{name:"General",description:"General purpose IDE",command:"general"}};(0,s.cn)([]),(0,s.cn)([]),(0,s.cn)(null),(0,s.cn)(""),(0,s.cn)([]),(0,s.cn)("ALL"),(0,a.O4)("theme","system");let c=(0,a.O4)("ide-preferences",{preferredIDEs:[],defaultIDE:void 0});(0,s.cn)(e=>e(c).preferredIDEs,(e,n,i)=>{let s=e(c);n(c,{...s,preferredIDEs:i})}),(0,s.cn)(e=>{let n=e(c);return n.preferredIDEs.find(e=>e.id===n.defaultIDE)},(e,n,i)=>{let s=e(c);n(c,{...s,defaultIDE:i})})}}]);