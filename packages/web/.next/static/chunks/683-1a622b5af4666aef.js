(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[683],{9213:function(t,e,n){"use strict";n.d(e,{Z:function(){return u}});var r=n(7653);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...t)=>t.filter((t,e,n)=>!!t&&n.indexOf(t)===e).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:l="",children:u,iconNode:a,...c},f)=>(0,r.createElement)("svg",{ref:f,...s,width:e,height:e,stroke:t,strokeWidth:o?24*Number(n)/Number(e):n,className:i("lucide",l),...c},[...a.map(([t,e])=>(0,r.createElement)(t,e)),...Array.isArray(u)?u:[u]])),u=(t,e)=>{let n=(0,r.forwardRef)(({className:n,...s},u)=>(0,r.createElement)(l,{ref:u,iconNode:e,className:i(`lucide-${o(t)}`,n),...s}));return n.displayName=`${t}`,n}},273:function(t,e,n){"use strict";n.d(e,{Z:function(){return o}});var r=n(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},4850:function(t,e,n){"use strict";var r,o;t.exports=(null==(r=n.g.process)?void 0:r.env)&&"object"==typeof(null==(o=n.g.process)?void 0:o.env)?n.g.process:n(3079)},3079:function(t){!function(){var e={229:function(t){var e,n,r,o=t.exports={};function i(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function l(t){if(e===setTimeout)return setTimeout(t,0);if((e===i||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:i}catch(t){e=i}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(t){n=s}}();var u=[],a=!1,c=-1;function f(){a&&r&&(a=!1,r.length?u=r.concat(u):c=-1,u.length&&p())}function p(){if(!a){var t=l(f);a=!0;for(var e=u.length;e;){for(r=u,u=[];++c<e;)r&&r[c].run();c=-1,e=u.length}r=null,a=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function d(t,e){this.fun=t,this.array=e}function h(){}o.nextTick=function(t){var e=Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];u.push(new d(t,e)),1!==u.length||a||l(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=h,o.addListener=h,o.once=h,o.off=h,o.removeListener=h,o.removeAllListeners=h,o.emit=h,o.prependListener=h,o.prependOnceListener=h,o.listeners=function(t){return[]},o.binding=function(t){throw Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw Error("process.chdir is not supported")},o.umask=function(){return 0}}},n={};function r(t){var o=n[t];if(void 0!==o)return o.exports;var i=n[t]={exports:{}},s=!0;try{e[t](i,i.exports,r),s=!1}finally{s&&delete n[t]}return i.exports}r.ab="//";var o=r(229);t.exports=o}()},2056:function(t,e,n){t.exports=n(9419)},7750:function(t,e,n){"use strict";n.d(e,{XL:function(){return X}});var r=Object.defineProperty,o=Object.defineProperties,i=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,a=(t,e,n)=>e in t?r(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,c=(t,e)=>{for(var n in e||(e={}))l.call(e,n)&&a(t,n,e[n]);if(s)for(var n of s(e))u.call(e,n)&&a(t,n,e[n]);return t},f=(t,e)=>o(t,i(e)),p=class extends Error{constructor(t,e,n){super(e||t.toString(),{cause:n}),this.status=t,this.statusText=e,this.error=n}},d=async(t,e)=>{var n,r,o,i,s,l;let u=e||{},a={onRequest:[null==e?void 0:e.onRequest],onResponse:[null==e?void 0:e.onResponse],onSuccess:[null==e?void 0:e.onSuccess],onError:[null==e?void 0:e.onError],onRetry:[null==e?void 0:e.onRetry]};if(!e||!(null==e?void 0:e.plugins))return{url:t,options:u,hooks:a};for(let c of(null==e?void 0:e.plugins)||[]){if(c.init){let r=await (null==(n=c.init)?void 0:n.call(c,t.toString(),e));u=r.options||u,t=r.url}a.onRequest.push(null==(r=c.hooks)?void 0:r.onRequest),a.onResponse.push(null==(o=c.hooks)?void 0:o.onResponse),a.onSuccess.push(null==(i=c.hooks)?void 0:i.onSuccess),a.onError.push(null==(s=c.hooks)?void 0:s.onError),a.onRetry.push(null==(l=c.hooks)?void 0:l.onRetry)}return{url:t,options:u,hooks:a}},h=class{constructor(t){this.options=t}shouldAttemptRetry(t,e){return this.options.shouldRetry?Promise.resolve(t<this.options.attempts&&this.options.shouldRetry(e)):Promise.resolve(t<this.options.attempts)}getDelay(){return this.options.delay}},y=class{constructor(t){this.options=t}shouldAttemptRetry(t,e){return this.options.shouldRetry?Promise.resolve(t<this.options.attempts&&this.options.shouldRetry(e)):Promise.resolve(t<this.options.attempts)}getDelay(t){let e=Math.min(this.options.maxDelay,this.options.baseDelay*2**t);return e}},v=async t=>{let e={},n=async t=>"function"==typeof t?await t():t;if(null==t?void 0:t.auth){if("Bearer"===t.auth.type){let r=await n(t.auth.token);if(!r)return e;e.authorization=`Bearer ${r}`}else if("Basic"===t.auth.type){let r=n(t.auth.username),o=n(t.auth.password);if(!r||!o)return e;e.authorization=`Basic ${btoa(`${r}:${o}`)}`}else if("Custom"===t.auth.type){let r=n(t.auth.value);if(!r)return e;e.authorization=`${n(t.auth.prefix)} ${r}`}}return e},g=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function m(t){if(void 0===t)return!1;let e=typeof t;return"string"===e||"number"===e||"boolean"===e||null===e||"object"===e&&(!!Array.isArray(t)||!t.buffer&&(t.constructor&&"Object"===t.constructor.name||"function"==typeof t.toJSON))}function w(t){try{return JSON.parse(t)}catch(e){return t}}function b(t){return"function"==typeof t}async function T(t){let e=new Headers(null==t?void 0:t.headers),n=await v(t);for(let[t,r]of Object.entries(n||{}))e.set(t,r);if(!e.has("content-type")){let n=m(null==t?void 0:t.body)?"application/json":null;n&&e.set("content-type",n)}return e}var R=class t extends Error{constructor(e,n){super(n||JSON.stringify(e,null,2)),this.issues=e,Object.setPrototypeOf(this,t.prototype)}};async function O(t,e){let n=await t["~standard"].validate(e);if(n.issues)throw new R(n.issues);return n.value}var E=["get","post","put","patch","delete"],S=t=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(e,n){var r,o,i,s;let l=(null==(o=null==(r=t.plugins)?void 0:r.find(t=>{var n;return null!=(n=t.schema)&&!!n.config&&(e.startsWith(t.schema.config.baseURL||"")||e.startsWith(t.schema.config.prefix||""))}))?void 0:o.schema)||t.schema;if(l){let t=e;(null==(i=l.config)?void 0:i.prefix)&&t.startsWith(l.config.prefix)&&(t=t.replace(l.config.prefix,""),l.config.baseURL&&(e=e.replace(l.config.prefix,l.config.baseURL))),(null==(s=l.config)?void 0:s.baseURL)&&t.startsWith(l.config.baseURL)&&(t=t.replace(l.config.baseURL,""));let r=l.schema[t];if(r){let t=f(c({},n),{method:r.method,output:r.output});return(null==n?void 0:n.disableValidation)||(t=f(c({},t),{body:r.input?await O(r.input,null==n?void 0:n.body):null==n?void 0:n.body,params:r.params?await O(r.params,null==n?void 0:n.params):null==n?void 0:n.params,query:r.query?await O(r.query,null==n?void 0:n.query):null==n?void 0:n.query})),{url:e,options:t}}}return{url:e,options:n}}}),x=t=>async function(e,n){let r=f(c(c({},t),n),{plugins:[...(null==t?void 0:t.plugins)||[],S(t||{})]});return null==t||t.catchAllError,await j(e,r)},j=async(t,e)=>{var n,r,o,i,s,l,u,a;let v;let{hooks:R,url:S,options:x}=await d(t,e),P=function(t){if(null==t?void 0:t.customFetchImpl)return t.customFetchImpl;if("undefined"!=typeof globalThis&&b(globalThis.fetch))return globalThis.fetch;if("undefined"!=typeof window&&b(window.fetch))return window.fetch;throw Error("No fetch implementation found")}(x),U=new AbortController,L=null!=(n=x.signal)?n:U.signal,_=function(t,e){let{baseURL:n,params:r,query:o}=e||{query:{},params:{},baseURL:""},i=t.startsWith("http")?t.split("/").slice(0,3).join("/"):n||"";if(t.startsWith("@")){let e=t.toString().split("@")[1].split("/")[0];E.includes(e)&&(t=t.replace(`@${e}/`,"/"))}i.endsWith("/")||(i+="/");let[s,l]=t.replace(i,"").split("?"),u=new URLSearchParams(l);for(let[t,e]of Object.entries(o||{}))null!=e&&u.set(t,String(e));if(r){if(Array.isArray(r)){let t=s.split("/").filter(t=>t.startsWith(":"));for(let[e,n]of t.entries()){let t=r[e];s=s.replace(n,t)}}else for(let[t,e]of Object.entries(r))s=s.replace(`:${t}`,String(e))}(s=s.split("/").map(encodeURIComponent).join("/")).startsWith("/")&&(s=s.slice(1));let a=u.toString();if(a=a.length>0?`?${a}`.replace(/\+/g,"%20"):"",!i.startsWith("http"))return`${i}${s}${a}`;let c=new URL(`${s}${a}`,i);return c}(S,x),k=function(t){if(!(null==t?void 0:t.body))return null;let e=new Headers(null==t?void 0:t.headers);if(m(t.body)&&!e.has("content-type")){for(let[e,n]of Object.entries(null==t?void 0:t.body))n instanceof Date&&(t.body[e]=n.toISOString());return JSON.stringify(t.body)}return t.body}(x),A=await T(x),$=function(t,e){var n;if(null==e?void 0:e.method)return e.method.toUpperCase();if(t.startsWith("@")){let r=null==(n=t.split("@")[1])?void 0:n.split("/")[0];return E.includes(r)?r.toUpperCase():(null==e?void 0:e.body)?"POST":"GET"}return(null==e?void 0:e.body)?"POST":"GET"}(S,x),I=f(c({},x),{url:_,headers:A,body:k,method:$,signal:L});for(let t of R.onRequest)if(t){let e=await t(I);e instanceof Object&&(I=e)}("pipeTo"in I&&"function"==typeof I.pipeTo||"function"==typeof(null==(r=null==e?void 0:e.body)?void 0:r.pipe))&&!("duplex"in I)&&(I.duplex="half");let{clearTimeout:N}=(!(null==x?void 0:x.signal)&&(null==x?void 0:x.timeout)&&(v=setTimeout(()=>null==U?void 0:U.abort(),null==x?void 0:x.timeout)),{abortTimeout:v,clearTimeout:()=>{v&&clearTimeout(v)}}),C=await P(I.url,I);N();let q={response:C,request:I};for(let t of R.onResponse)if(t){let n=await t(f(c({},q),{response:(null==(o=null==e?void 0:e.hookOptions)?void 0:o.cloneResponse)?C.clone():C}));n instanceof Response?C=n:n instanceof Object&&(C=n.response)}if(C.ok){let t="HEAD"!==I.method;if(!t)return{data:"",error:null};let n=function(t){let e=t.headers.get("content-type"),n=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!e)return"json";let r=e.split(";").shift()||"";return g.test(r)?"json":n.has(r)||r.startsWith("text/")?"text":"blob"}(C),r={data:"",response:C,request:I};if("json"===n||"text"===n){let t=await C.text(),e=null!=(i=I.jsonParser)?i:w,n=await e(t);r.data=n}else r.data=await C[n]();for(let t of((null==I?void 0:I.output)&&I.output&&!I.disableValidation&&(r.data=await O(I.output,r.data)),R.onSuccess))t&&await t(f(c({},r),{response:(null==(s=null==e?void 0:e.hookOptions)?void 0:s.cloneResponse)?C.clone():C}));return(null==e?void 0:e.throw)?r.data:{data:r.data,error:null}}let W=null!=(l=null==e?void 0:e.jsonParser)?l:w,B=await C.text(),D=function(t){try{return JSON.parse(t),!0}catch(t){return!1}}(B),F=D?await W(B):null,H={response:C,responseText:B,request:I,error:f(c({},F),{status:C.status,statusText:C.statusText})};for(let t of R.onError)t&&await t(f(c({},H),{response:(null==(u=null==e?void 0:e.hookOptions)?void 0:u.cloneResponse)?C.clone():C}));if(null==e?void 0:e.retry){let n=function(t){if("number"==typeof t)return new h({type:"linear",attempts:t,delay:1e3});switch(t.type){case"linear":return new h(t);case"exponential":return new y(t);default:throw Error("Invalid retry strategy")}}(e.retry),r=null!=(a=e.retryAttempt)?a:0;if(await n.shouldAttemptRetry(r,C)){for(let t of R.onRetry)t&&await t(q);let o=n.getDelay(r);return await new Promise(t=>setTimeout(t,o)),await j(t,f(c({},e),{retryAttempt:r+1}))}}if(null==e?void 0:e.throw)throw new p(C.status,C.statusText,D?F:B);return{data:null,error:f(c({},F),{status:C.status,statusText:C.statusText})}},P=n(4850);let U=Object.create(null),L=t=>globalThis.process?.env||globalThis.Deno?.env.toObject()||globalThis.__env__||(t?U:globalThis),_=new Proxy(U,{get(t,e){let n=L();return n[e]??U[e]},has(t,e){let n=L();return e in n||e in U},set(t,e,n){let r=L(!0);return r[e]=n,!0},deleteProperty(t,e){if(!e)return!1;let n=L(!0);return delete n[e],!0},ownKeys(){let t=L(!0);return Object.keys(t)}}),k=void 0!==P&&P.env&&"production"||"";"test"!==k&&_.TEST;class A extends Error{constructor(t,e){super(t),this.name="BetterAuthError",this.message=t,this.cause=e,this.stack=""}}function $(t,e="/api/auth"){let n=function(t){try{let e=new URL(t);return"/"!==e.pathname}catch(e){throw new A(`Invalid base URL: ${t}. Please provide a valid base URL.`)}}(t);return n?t:`${t.replace(/\/+$/,"")}${e=e.startsWith("/")?e:`/${e}`}`}let I=[],N=0,C=0,q=t=>{let e=[],n={get:()=>(n.lc||n.listen(()=>{})(),n.value),lc:0,listen:t=>(n.lc=e.push(t),()=>{for(let e=N+4;e<I.length;)I[e]===t?I.splice(e,4):e+=4;let r=e.indexOf(t);!~r||(e.splice(r,1),--n.lc||n.off())}),notify(t,r){C++;let o=!I.length;for(let o of e)I.push(o,n.value,t,r);if(o){for(N=0;N<I.length;N+=4)I[N](I[N+1],I[N+2],I[N+3]);I.length=0}},off(){},set(t){let e=n.value;e!==t&&(n.value=t,n.notify(e))},subscribe(t){let e=n.listen(t);return t(n.value),e},value:t};return n},W=(t,e,n,r)=>(t.events=t.events||{},t.events[n+10]||(t.events[n+10]=r(e=>{t.events[n].reduceRight((t,e)=>(e(t),t),{shared:{},...e})})),t.events[n]=t.events[n]||[],t.events[n].push(e),()=>{let r=t.events[n],o=r.indexOf(e);r.splice(o,1),r.length||(delete t.events[n],t.events[n+10](),delete t.events[n+10])}),B=(t,e)=>W(t,n=>{let r=e(n);r&&t.events[6].push(r)},5,e=>{let n=t.listen;t.listen=(...r)=>(t.lc||t.active||(t.active=!0,e()),n(...r));let r=t.off;return t.events[6]=[],t.off=()=>{r(),setTimeout(()=>{if(t.active&&!t.lc){for(let e of(t.active=!1,t.events[6]))e();t.events[6]=[]}},1e3)},()=>{t.listen=n,t.off=r}}),D="undefined"==typeof window,F=(t,e,n,r)=>{let o=q({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>i()}),i=()=>{let t="function"==typeof r?r({data:o.get().data,error:o.get().error,isPending:o.get().isPending}):r;return n(e,{...t,async onSuccess(e){o.set({data:e.data,error:null,isPending:!1,isRefetching:!1,refetch:o.value.refetch}),await t?.onSuccess?.(e)},async onError(e){let{request:n}=e,r="number"==typeof n.retry?n.retry:n.retry?.attempts,i=n.retryAttempt||0;r&&i<r||(o.set({error:e.error,data:null,isPending:!1,isRefetching:!1,refetch:o.value.refetch}),await t?.onError?.(e))},async onRequest(e){let n=o.get();o.set({isPending:null===n.data,data:n.data,error:null,isRefetching:!0,refetch:o.value.refetch}),await t?.onRequest?.(e)}})};t=Array.isArray(t)?t:[t];let s=!1;for(let e of t)e.subscribe(()=>{D||(s?i():B(o,()=>(setTimeout(()=>{i()},0),s=!0,()=>{o.off(),e.off()})))});return o},H={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},G=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,J={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},M=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/,Z={id:"redirect",name:"Redirect",hooks:{onSuccess(t){if(t.data?.url&&t.data?.redirect&&"undefined"!=typeof window&&window.location&&window.location)try{window.location.href=t.data.url}catch{}}}},z=t=>{let e="credentials"in Request.prototype,n=function(t,e,n){if(t)return $(t,e);let r=_.BETTER_AUTH_URL||_.NEXT_PUBLIC_BETTER_AUTH_URL||_.PUBLIC_BETTER_AUTH_URL||_.NUXT_PUBLIC_BETTER_AUTH_URL||_.NUXT_PUBLIC_AUTH_URL||("/"!==_.BASE_URL?_.BASE_URL:void 0);if(r)return $(r,e);let o=n?.headers.get("x-forwarded-host"),i=n?.headers.get("x-forwarded-proto");if(o&&i)return $(`${i}://${o}`,e);if(n){let t=function(t){try{let e=new URL(t);return e.origin}catch(t){return null}}(n.url);if(!t)throw new A("Could not get origin from request. Please provide a valid base URL.");return $(t,e)}if("undefined"!=typeof window&&window.location)return $(window.location.origin,e)}(t?.baseURL,t?.basePath),r=t?.plugins?.flatMap(t=>t.fetchPlugins).filter(t=>void 0!==t)||[],o={id:"lifecycle-hooks",name:"lifecycle-hooks",hooks:{onSuccess:t?.fetchOptions?.onSuccess,onError:t?.fetchOptions?.onError,onRequest:t?.fetchOptions?.onRequest,onResponse:t?.fetchOptions?.onResponse}},{onSuccess:i,onError:s,onRequest:l,onResponse:u,...a}=t?.fetchOptions||{},c=x({baseURL:n,...e?{credentials:"include"}:{},method:"GET",jsonParser:t=>t?function(t,e={strict:!0}){return function(t,e={}){let{strict:n=!1,warnings:r=!1,reviver:o,parseDates:i=!0}=e;if("string"!=typeof t)return t;let s=t.trim();if('"'===s[0]&&s.endsWith('"')&&!s.slice(1,-1).includes('"'))return s.slice(1,-1);let l=s.toLowerCase();if(l.length<=9&&l in J)return J[l];if(!G.test(s)){if(n)throw SyntaxError("[better-json] Invalid JSON");return t}let u=Object.entries(H).some(([t,e])=>{let n=e.test(s);return n&&r&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${t} pattern`),n});if(u&&n)throw Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(s,(t,e)=>{if("__proto__"===t||"constructor"===t&&e&&"object"==typeof e&&"prototype"in e){r&&console.warn(`[better-json] Dropping "${t}" key to prevent prototype pollution`);return}if(i&&"string"==typeof e){let t=function(t){let e=M.exec(t);if(!e)return null;let[,n,r,o,i,s,l,u,a,c,f]=e,p=new Date(Date.UTC(parseInt(n,10),parseInt(r,10)-1,parseInt(o,10),parseInt(i,10),parseInt(s,10),parseInt(l,10),u?parseInt(u.padEnd(3,"0"),10):0));if(a){let t=(60*parseInt(c,10)+parseInt(f,10))*("+"===a?-1:1);p.setUTCMinutes(p.getUTCMinutes()+t)}return p instanceof Date&&!isNaN(p.getTime())?p:null}(e);if(t)return t}return o?o(t,e):e})}catch(e){if(n)throw e;return t}}(t,e)}(t,{strict:!1}):null,customFetchImpl:async(t,e)=>{try{return await fetch(t,e)}catch(t){return Response.error()}},...a,plugins:[o,...a.plugins||[],...t?.disableDefaultFetchPlugins?[]:[Z],...r]}),{$sessionSignal:f,session:p}=function(t){let e=q(!1),n=F(e,"/get-session",t,{method:"GET"});return{session:n,$sessionSignal:e}}(c),d=t?.plugins||[],h={},y={$sessionSignal:f,session:p},v={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"},g=[{signal:"$sessionSignal",matcher:t=>"/sign-out"===t||"/update-user"===t||t.startsWith("/sign-in")||t.startsWith("/sign-up")||"/delete-user"===t||"/verify-email"===t}];for(let t of d)t.getAtoms&&Object.assign(y,t.getAtoms?.(c)),t.pathMethods&&Object.assign(v,t.pathMethods),t.atomListeners&&g.push(...t.atomListeners);let m={notify:t=>{y[t].set(!y[t].get())},listen:(t,e)=>{y[t].subscribe(e)},atoms:y};for(let e of d)e.getActions&&Object.assign(h,e.getActions?.(c,m,t));return{pluginsActions:h,pluginsAtoms:y,pluginPathMethods:v,atomListeners:g,$fetch:c,$store:m}};var V=n(7653);function X(t){let{pluginPathMethods:e,pluginsActions:n,pluginsAtoms:r,$fetch:o,$store:i,atomListeners:s}=z(t),l={};for(let[t,e]of Object.entries(r))l[`use${t.charAt(0).toUpperCase()+t.slice(1)}`]=()=>(function(t,e={}){let n=(0,V.useRef)(t.get()),{keys:r,deps:o=[t,r]}=e,i=(0,V.useCallback)(e=>{let o=t=>{n.current!==t&&(n.current=t,e())};if(o(t.value),r?.length){let e;return e=new Set(r).add(void 0),t.listen((t,n,r)=>{e.has(r)&&o(t,n,r)})}return t.listen(o)},o),s=()=>n.current;return(0,V.useSyncExternalStore)(i,s,s)})(e);let u={...n,...l,$fetch:o,$store:i},a=function t(n=[]){return new Proxy(function(){},{get(e,r){let o=[...n,r],i=u;for(let t of o)if(i&&"object"==typeof i&&t in i)i=i[t];else{i=void 0;break}return"function"==typeof i?i:t(o)},apply:async(t,i,l)=>{let u="/"+n.map(t=>t.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)).join("/"),a=l[0]||{},c=l[1]||{},{query:f,fetchOptions:p,...d}=a,h={...c,...p},y=function(t,e,n){let r=e[t],{fetchOptions:o,query:i,...s}=n||{};return r||(o?.method?o.method:s&&Object.keys(s).length>0?"POST":"GET")}(u,e,a);return await o(u,{...h,body:"GET"===y?void 0:{...d,...h?.body||{}},query:f||h?.query,method:y,async onSuccess(t){await h?.onSuccess?.(t);let e=s?.find(t=>t.matcher(u));if(!e)return;let n=r[e.signal];if(!n)return;let o=n.get();setTimeout(()=>{n.set(!o)},10)}})}})}();return a}}}]);