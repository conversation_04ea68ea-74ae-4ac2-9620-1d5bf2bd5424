(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{846:function(e,t,r){var a={"./en/messages.js":[9012,12],"./zh-CN/messages.js":[7645,645],"./zh-HK/messages.js":[9817,817]};function s(e){if(!r.o(a,e))return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],s=t[0];return r.e(t[1]).then(function(){return r.t(s,23)})}s.keys=function(){return Object.keys(a)},s.id=846,e.exports=s},8583:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},7917:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},9218:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8073:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8158:function(e,t,r){Promise.resolve().then(r.t.bind(r,9419,23)),Promise.resolve().then(r.t.bind(r,225,23)),Promise.resolve().then(r.bind(r,7403)),Promise.resolve().then(r.bind(r,4766)),Promise.resolve().then(r.bind(r,8448)),Promise.resolve().then(r.bind(r,5447))},7403:function(e,t,r){"use strict";r.r(t),r.d(t,{ClientNavbar:function(){return ec}});var a=r(7573),s=r(2056),n=r.n(s),i=r(7653),l=r(8255),c=r(273),d=r(8583),o=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,o.Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),m=(0,o.Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),h=(0,o.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var f=r(9218);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,o.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var p=r(8436),g=r(7808),v=r(8344),j=r(4036),y=r(6418),N=r(1469),k=r(8671),E=r(6881);function I(){return()=>{}}var b="Avatar",[w,D]=(0,j.b)(b),[C,A]=w(b),S=i.forwardRef((e,t)=>{let{__scopeAvatar:r,...s}=e,[n,l]=i.useState("idle");return(0,a.jsx)(C,{scope:r,imageLoadingStatus:n,onImageLoadingStatusChange:l,children:(0,a.jsx)(k.WV.span,{...s,ref:t})})});S.displayName=b;var z="AvatarImage",O=i.forwardRef((e,t)=>{let{__scopeAvatar:r,src:s,onLoadingStatusChange:n=()=>{},...l}=e,c=A(z,r),d=function(e,{referrerPolicy:t,crossOrigin:r}){let a=(0,E.useSyncExternalStore)(I,()=>!0,()=>!1),s=i.useRef(null),n=a?(s.current||(s.current=new window.Image),s.current):null,[l,c]=i.useState(()=>M(n,e));return(0,N.b)(()=>{c(M(n,e))},[n,e]),(0,N.b)(()=>{let e=e=>()=>{c(e)};if(!n)return;let a=e("loaded"),s=e("error");return n.addEventListener("load",a),n.addEventListener("error",s),t&&(n.referrerPolicy=t),"string"==typeof r&&(n.crossOrigin=r),()=>{n.removeEventListener("load",a),n.removeEventListener("error",s)}},[n,r,t]),l}(s,l),o=(0,y.W)(e=>{n(e),c.onImageLoadingStatusChange(e)});return(0,N.b)(()=>{"idle"!==d&&o(d)},[d,o]),"loaded"===d?(0,a.jsx)(k.WV.img,{...l,ref:t,src:s}):null});O.displayName=z;var Z="AvatarFallback",L=i.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:s,...n}=e,l=A(Z,r),[c,d]=i.useState(void 0===s);return i.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>d(!0),s);return()=>window.clearTimeout(e)}},[s]),c&&"loaded"!==l.imageLoadingStatus?(0,a.jsx)(k.WV.span,{...n,ref:t}):null});function M(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}L.displayName=Z;var T=r(2741),P=r(605),R=r(3579),V=r(4494);let G={...T.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],default:"3",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"soft"},...P.o3,...R.K,...V.I,fallback:{type:"ReactNode",required:!0}};var U=r(5236),_=r(3589),B=r(5159);let H=i.forwardRef((e,t)=>{let{asChild:r,children:a,className:s,style:n,color:l,radius:c,...d}=(0,U.y)(e,G,B.E);return i.createElement(S,{"data-accent-color":l,"data-radius":c,className:v("rt-reset","rt-AvatarRoot",s),style:n,asChild:r},(0,_.x)({asChild:r,children:a},i.createElement(F,{ref:t,...d})))});H.displayName="Avatar";let F=i.forwardRef(({fallback:e,...t},r)=>{let[a,s]=i.useState("idle");return i.createElement(i.Fragment,null,"idle"===a||"loading"===a?i.createElement("span",{className:"rt-AvatarFallback"}):null,"error"===a?i.createElement(L,{className:v("rt-AvatarFallback",{"rt-one-letter":"string"==typeof e&&1===e.length,"rt-two-letters":"string"==typeof e&&2===e.length}),delayMs:0},e):null,i.createElement(O,{ref:r,className:"rt-AvatarImage",...t,onLoadingStatusChange:e=>{t.onLoadingStatusChange?.(e),s(e)}}))});F.displayName="AvatarImpl";var q=r(8275),K=r(8695);let W={en:"English","zh-CN":"简体中文","zh-HK":"繁體中文"},Y={en:"EN","zh-CN":"简","zh-HK":"繁"};function J(e){let{currentLocale:t}=e,[r,s]=(0,i.useTransition)(),[n,l]=(0,i.useState)(t);return(0,a.jsxs)(K.fC,{value:n,onValueChange:e=>{l(e),s(()=>{document.cookie="locale=".concat(e,";path=/;max-age=").concat(31536e3),window.location.reload()})},disabled:r,children:[(0,a.jsx)(K.xz,{className:"w-[60px] px-2 h-8 text-xs",placeholder:Y[n]}),(0,a.jsx)(K.VY,{children:Object.entries(Y).map(e=>{let[t,r]=e;return(0,a.jsxs)(K.ck,{value:t,children:[(0,a.jsx)("span",{className:"text-xs",children:r}),(0,a.jsx)("span",{className:"ml-2 text-xs text-muted-foreground",children:W[t]})]},t)})})]})}var Q=r(4594),X=r(1874),$=r(1467),ee=r(7917),et=r(6434);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let er=(0,o.Z)("StarOff",[["path",{d:"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43",key:"16m0ql"}],["path",{d:"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91",key:"1vt8nq"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);var ea=r(8073),es=r(5048),en=r(3588);function ei(e){let{open:t,onOpenChange:r}=e,[s,n]=(0,Q.KO)(es.Ym),[l,d]=(0,i.useState)(""),o=Object.entries(es.gA).filter(e=>{let[t]=e;return!s.preferredIDEs.some(e=>e.type===t)}),u=e=>{let t=s.preferredIDEs.find(t=>t.id===e),r=s.preferredIDEs.filter(t=>t.id!==e),a=s.defaultIDE;s.defaultIDE===e&&(a=r.length>0?r[0].id:void 0),n({preferredIDEs:r,defaultIDE:a}),t&&en.Am.success("".concat(t.name," removed from preferences"))},m=e=>{let t=s.preferredIDEs.find(t=>t.id===e);n({...s,defaultIDE:e}),t&&en.Am.success("".concat(t.name," set as default IDE"))};return(0,a.jsx)(X.fC,{open:t,onOpenChange:r,children:(0,a.jsxs)(X.VY,{className:"max-w-2xl",children:[(0,a.jsxs)(X.Dx,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.Z,{className:"h-5 w-5"}),"IDE Preferences"]}),(0,a.jsx)(X.dk,{children:"Manage your preferred IDEs for quick command generation. Set a default IDE and add your favorites."}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium",children:"Add IDE"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(K.fC,{value:l,onValueChange:e=>d(e),children:[(0,a.jsx)(K.xz,{className:"flex-1",placeholder:"Select an IDE to add..."}),(0,a.jsx)(K.VY,{children:o.map(e=>{let[t,r]=e;return(0,a.jsx)(K.ck,{value:t,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.Z,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:r.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:r.description})]})]})},t)})})]}),(0,a.jsxs)(p.z,{onClick:()=>{if(!l)return;let e={id:"".concat(l,"-").concat(Date.now()),name:es.gA[l].name,type:l,isDefault:0===s.preferredIDEs.length,addedAt:new Date().toISOString()},t={...s,preferredIDEs:[...s.preferredIDEs,e],defaultIDE:0===s.preferredIDEs.length?e.id:s.defaultIDE};n(t),d(""),en.Am.success("".concat(es.gA[l].name," added to preferences"))},disabled:!l,size:"2",children:[(0,a.jsx)(ee.Z,{className:"h-4 w-4 mr-1"}),"Add"]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["Preferred IDEs (",s.preferredIDEs.length,")"]}),0===s.preferredIDEs.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(c.Z,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No preferred IDEs added yet"}),(0,a.jsx)("p",{className:"text-xs",children:"Add your favorite IDEs to generate quick commands"})]}):(0,a.jsx)("div",{className:"space-y-2",children:s.preferredIDEs.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(c.Z,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),s.defaultIDE===e.id&&(0,a.jsx)($.C,{variant:"soft",className:"text-xs",children:"Default"})]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:es.gA[e.type].description})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(p.z,{variant:"ghost",size:"2",onClick:()=>m(e.id),disabled:s.defaultIDE===e.id,className:"h-8 w-8 p-0",children:s.defaultIDE===e.id?(0,a.jsx)(et.Z,{className:"h-4 w-4 fill-current"}):(0,a.jsx)(er,{className:"h-4 w-4"})}),(0,a.jsx)(p.z,{variant:"ghost",size:"2",onClick:()=>u(e.id),className:"h-8 w-8 p-0 text-destructive hover:text-destructive",children:(0,a.jsx)(ea.Z,{className:"h-4 w-4"})})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground bg-muted p-3 rounded-lg",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"How it works:"}),(0,a.jsxs)("ul",{className:"space-y-1",children:[(0,a.jsx)("li",{children:"• Add your favorite IDEs to generate quick npx commands"}),(0,a.jsx)("li",{children:"• Set a default IDE for one-click command copying"}),(0,a.jsx)("li",{children:"• Commands will include the --target flag for your selected IDE"})]})]})]})]})})}function el(e){var t,r;let{locale:s}=e,{theme:o,setTheme:v}=(0,l.F)(),{data:j}=(0,q.kP)(),[y,N]=(0,i.useState)(!1),[k,E]=(0,i.useState)(!1),I=async()=>{await (0,q.w7)(),E(!1)},b=()=>{E(!1)};return(0,a.jsxs)("nav",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:[(0,a.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-8",children:[(0,a.jsxs)(n(),{href:"/",className:"flex items-center gap-2",children:[(0,a.jsx)(c.Z,{className:"h-6 w-6 text-primary"}),(0,a.jsx)("span",{className:"font-bold text-xl",children:"OnlyRules"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center gap-6",children:[(0,a.jsx)(n(),{href:"/dashboard",className:"text-sm font-medium transition-colors hover:text-primary",children:"Dashboard"}),(0,a.jsx)(n(),{href:"/templates",className:"text-sm font-medium transition-colors hover:text-primary",children:"Templates"}),(0,a.jsx)(n(),{href:"/ides",className:"text-sm font-medium transition-colors hover:text-primary",children:"IDEs"}),(0,a.jsx)(n(),{href:"/tutorials",className:"text-sm font-medium transition-colors hover:text-primary",children:"Tutorials"}),(0,a.jsx)(n(),{href:"/shared",className:"text-sm font-medium transition-colors hover:text-primary",children:"Community"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(J,{currentLocale:s}),(0,a.jsx)(p.z,{variant:"ghost",size:"2",asChild:!0,children:(0,a.jsxs)(n(),{href:"https://github.com/ranglang/onlyrules",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,a.jsx)(d.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"GitHub"})]})}),(0,a.jsxs)(p.z,{variant:"ghost",size:"2",onClick:()=>v("dark"===o?"light":"dark"),children:[(0,a.jsx)(u,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(m,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}),(null==j?void 0:j.user)?(0,a.jsxs)(g.fC,{open:k,onOpenChange:E,children:[(0,a.jsx)(g.xz,{children:(0,a.jsx)(p.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsx)(H,{className:"h-8 w-8",src:j.user.image||"",fallback:(null===(t=j.user.name)||void 0===t?void 0:t.charAt(0))||(null===(r=j.user.email)||void 0===r?void 0:r.charAt(0))||"U"})})}),(0,a.jsxs)(g.VY,{className:"w-56",align:"end",children:[(0,a.jsx)("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[j.user.name&&(0,a.jsx)("p",{className:"font-medium",children:j.user.name}),j.user.email&&(0,a.jsx)("p",{className:"w-[200px] truncate text-sm text-muted-foreground",children:j.user.email})]})}),(0,a.jsx)(g.Z0,{}),(0,a.jsx)(g.ck,{asChild:!0,children:(0,a.jsxs)(n(),{href:"/dashboard",onClick:b,children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4"}),"Dashboard"]})}),(0,a.jsxs)(g.ck,{onClick:()=>{N(!0),E(!1)},children:[(0,a.jsx)(c.Z,{className:"mr-2 h-4 w-4"}),"IDE Preferences"]}),(0,a.jsx)(g.ck,{asChild:!0,children:(0,a.jsxs)(n(),{href:"/settings",onClick:b,children:[(0,a.jsx)(f.Z,{className:"mr-2 h-4 w-4"}),"Settings"]})}),(0,a.jsx)(g.Z0,{}),(0,a.jsxs)(g.ck,{onClick:I,children:[(0,a.jsx)(x,{className:"mr-2 h-4 w-4"}),"Sign Out"]})]})]}):(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(p.z,{variant:"ghost",size:"2",children:(0,a.jsx)(n(),{href:"/auth/signin",children:"Sign In"})})})]})]}),(0,a.jsx)(ei,{open:y,onOpenChange:N})]})}function ec(e){let{locale:t}=e;return(0,a.jsx)(el,{locale:t})}},4766:function(e,t,r){"use strict";r.r(t),r.d(t,{JotaiProvider:function(){return n}});var a=r(7573),s=r(4594);function n(e){let{children:t}=e;return(0,a.jsx)(s.zt,{children:t})}},8448:function(e,t,r){"use strict";r.r(t),r.d(t,{ThemeProvider:function(){return c}});var a=r(7573),s=r(7653),n=r(8255),i=r(2123);function l(e){let{children:t}=e,{theme:r,systemTheme:l}=(0,n.F)(),[c,d]=s.useState(!1);return(s.useEffect(()=>{d(!0)},[]),c)?(0,a.jsx)(i.Q2,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:("system"===r?l||"dark":r)||"dark",panelBackground:"translucent",hasBackground:!1,children:t}):(0,a.jsx)(i.Q2,{accentColor:"blue",grayColor:"slate",radius:"medium",scaling:"100%",appearance:"dark",panelBackground:"translucent",hasBackground:!1,children:t})}function c(e){let{children:t,...r}=e;return(0,a.jsx)(n.f,{defaultTheme:"dark",...r,children:(0,a.jsx)(l,{children:t})})}},5447:function(e,t,r){"use strict";r.r(t),r.d(t,{Toaster:function(){return i}});var a=r(7573),s=r(8255),n=r(3588);let i=e=>{let{...t}=e,{theme:r="system"}=(0,s.F)();return(0,a.jsx)(n.x7,{theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})}},8275:function(e,t,r){"use strict";r.d(t,{kP:function(){return c},w7:function(){return l},zB:function(){return n}});var a=r(7750);let s=(0,a.XL)({baseURL:"http://localhost:3000"}),{signIn:n,signUp:i,signOut:l,useSession:c,getSession:d}=s},5048:function(e,t,r){"use strict";r.d(t,{Ym:function(){return i},gA:function(){return n}});var a=r(2732),s=r(8935);let n={CURSOR:{name:"Cursor",description:"AI-powered code editor",command:"cursor"},AUGMENT:{name:"Augment",description:"AI coding assistant",command:"augment"},WINDSURF:{name:"Windsurf",description:"AI development environment",command:"windsurf"},CLAUDE:{name:"Claude",description:"Anthropic AI assistant",command:"claude"},GITHUB_COPILOT:{name:"GitHub Copilot",description:"AI pair programmer",command:"github-copilot"},GEMINI:{name:"Gemini",description:"Google AI assistant",command:"gemini"},OPENAI_CODEX:{name:"OpenAI Codex",description:"OpenAI code assistant",command:"openai-codex"},CLINE:{name:"Cline",description:"AI coding assistant",command:"cline"},JUNIE:{name:"Junie",description:"AI development tool",command:"junie"},TRAE:{name:"Trae",description:"AI coding companion",command:"trae"},LINGMA:{name:"Lingma",description:"AI programming assistant",command:"lingma"},KIRO:{name:"Kiro",description:"AI development environment",command:"kiro"},TENCENT_CODEBUDDY:{name:"Tencent CodeBuddy",description:"Tencent AI coding assistant",command:"tencent-codebuddy"},GENERAL:{name:"General",description:"General purpose IDE",command:"general"}};(0,a.cn)([]),(0,a.cn)([]),(0,a.cn)(null),(0,a.cn)(""),(0,a.cn)([]),(0,a.cn)("ALL"),(0,s.O4)("theme","system");let i=(0,s.O4)("ide-preferences",{preferredIDEs:[],defaultIDE:void 0});(0,a.cn)(e=>e(i).preferredIDEs,(e,t,r)=>{let a=e(i);t(i,{...a,preferredIDEs:r})}),(0,a.cn)(e=>{let t=e(i);return t.preferredIDEs.find(e=>e.id===t.defaultIDE)},(e,t,r)=>{let a=e(i);t(i,{...a,defaultIDE:r})})},225:function(){},7716:function(e,t,r){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=r(7653),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=a.useState,i=a.useEffect,l=a.useLayoutEffect,c=a.useDebugValue;function d(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s(e,r)}catch(e){return!0}}var o="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),a=n({inst:{value:r,getSnapshot:t}}),s=a[0].inst,o=a[1];return l(function(){s.value=r,s.getSnapshot=t,d(s)&&o({inst:s})},[e,r,t]),i(function(){return d(s)&&o({inst:s}),e(function(){d(s)&&o({inst:s})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:o},6881:function(e,t,r){"use strict";e.exports=r(7716)}},function(e){e.O(0,[110,612,834,419,67,683,11,293,456,744],function(){return e(e.s=8158)}),_N_E=e.O()}]);