(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[98],{8583:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});var a=s(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},2452:function(e,t,s){Promise.resolve().then(s.bind(s,1575))},1575:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return h},dynamic:function(){return x}});var a=s(7573),r=s(7653),n=s(2859),c=s(2056),l=s.n(c),i=s(5543),u=s(6094),o=s(8436),d=s(8275),f=s(273),m=s(8583);let x="force-dynamic";function h(){let[e,t]=(0,r.useState)(!1),[s,c]=(0,r.useState)("");(0,n.useRouter)();let x=async()=>{t(!0),c("");try{let e=await d.zB.social({provider:"github",callbackURL:"/dashboard"});e.error&&c(e.error.message||"Failed to sign in with GitHub")}catch(e){c("An unexpected error occurred")}finally{t(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(l(),{href:"/",className:"inline-flex items-center gap-2 mb-8",children:[(0,a.jsx)(f.Z,{className:"h-8 w-8 text-primary"}),(0,a.jsx)("span",{className:"font-bold text-2xl",children:"OnlyRules"})]})}),(0,a.jsxs)(i.Z,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-2xl text-center font-semibold",children:"Welcome back"}),(0,a.jsx)("div",{className:"text-center text-muted-foreground",children:"Sign in with your GitHub account to continue"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[s&&(0,a.jsx)(u.fC,{color:"red",children:(0,a.jsx)(u.xv,{children:s})}),(0,a.jsxs)(o.z,{onClick:x,className:"w-full",disabled:e,size:"3",children:[(0,a.jsx)(m.Z,{className:"mr-2 h-5 w-5"}),e?"Signing in...":"Continue with GitHub"]}),(0,a.jsx)("div",{className:"text-center text-sm text-muted-foreground",children:"By signing in, you agree to our terms of service and privacy policy."})]})]})]})})}},8275:function(e,t,s){"use strict";s.d(t,{kP:function(){return i},w7:function(){return l},zB:function(){return n}});var a=s(7750);let r=(0,a.XL)({baseURL:"http://localhost:3000"}),{signIn:n,signUp:c,signOut:l,useSession:i,getSession:u}=r},2859:function(e,t,s){e.exports=s(7892)},6094:function(e,t,s){"use strict";s.d(t,{JO:function(){return v},fC:function(){return h},xv:function(){return N}});var a=s(7653),r=s(8344),n=s(432),c=s(2063),l=s(2741),i=s(605),u=s(3579);let o={...l.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["soft","surface","outline"],default:"soft"},...i.o3,...u.K};var d=s(5236),f=s(2717),m=s(5159);let x=a.createContext({}),h=a.forwardRef((e,t)=>{let{size:s=o.size.default}=e,{asChild:c,children:l,className:i,color:u,...f}=(0,d.y)(e,o,m.E),h=c?n.fC:"div";return a.createElement(h,{"data-accent-color":u,...f,className:r("rt-CalloutRoot",i),ref:t},a.createElement(x.Provider,{value:a.useMemo(()=>({size:s}),[s])},l))});h.displayName="Callout.Root";let v=a.forwardRef(({className:e,...t},s)=>a.createElement("div",{...t,className:r("rt-CalloutIcon",e),ref:s}));v.displayName="Callout.Icon";let N=a.forwardRef(({className:e,...t},s)=>{let{size:n}=a.useContext(x);return a.createElement(c.x,{as:"p",size:(0,f.qz)(n,f.uJ),...t,asChild:!1,ref:s,className:r("rt-CalloutText",e)})});N.displayName="Callout.Text"},5543:function(e,t,s){"use strict";s.d(t,{Z:function(){return o}});var a=s(7653),r=s(8344),n=s(432),c=s(2741);let l={...c.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var i=s(5236),u=s(5159);let o=a.forwardRef((e,t)=>{let{asChild:s,className:c,...o}=(0,i.y)(e,l,u.E),d=s?n.fC:"div";return a.createElement(d,{ref:t,...o,className:r("rt-reset","rt-BaseCard","rt-Card",c)})});o.displayName="Card"}},function(e){e.O(0,[612,419,683,293,456,744],function(){return e(e.s=2452)}),_N_E=e.O()}]);