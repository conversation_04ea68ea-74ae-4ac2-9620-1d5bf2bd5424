(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[271],{8583:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},1873:function(e,t,a){Promise.resolve().then(a.bind(a,5503))},5503:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h},dynamic:function(){return x}});var s=a(7573),r=a(7653),n=a(2859),c=a(2056),l=a.n(c),i=a(5543),u=a(6094),o=a(8436),d=a(8275),f=a(273),m=a(8583);let x="force-dynamic";function h(){let[e,t]=(0,r.useState)(!1),[a,c]=(0,r.useState)("");(0,n.useRouter)();let x=async()=>{t(!0),c("");try{let e=await d.zB.social({provider:"github",callbackURL:"/dashboard"});e.error&&c(e.error.message||"Failed to sign in with GitHub")}catch(e){c("An unexpected error occurred")}finally{t(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(l(),{href:"/",className:"inline-flex items-center gap-2 mb-8",children:[(0,s.jsx)(f.Z,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("span",{className:"font-bold text-2xl",children:"OnlyRules"})]})}),(0,s.jsxs)(i.Z,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("div",{className:"text-2xl text-center font-semibold",children:"Create an account"}),(0,s.jsx)("div",{className:"text-center text-muted-foreground",children:"Sign up with your GitHub account to get started"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[a&&(0,s.jsx)(u.fC,{color:"red",children:(0,s.jsx)(u.xv,{children:a})}),(0,s.jsxs)(o.z,{onClick:x,className:"w-full",disabled:e,size:"3",children:[(0,s.jsx)(m.Z,{className:"mr-2 h-5 w-5"}),e?"Signing up...":"Continue with GitHub"]}),(0,s.jsx)("div",{className:"text-center text-sm text-muted-foreground",children:"By signing up, you agree to our terms of service and privacy policy."}),(0,s.jsxs)("div",{className:"text-center text-sm",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/auth/signin",className:"text-primary hover:underline",children:"Sign in"})]})]})]})]})})}},8275:function(e,t,a){"use strict";a.d(t,{kP:function(){return i},w7:function(){return l},zB:function(){return n}});var s=a(7750);let r=(0,s.XL)({baseURL:"http://localhost:3000"}),{signIn:n,signUp:c,signOut:l,useSession:i,getSession:u}=r},2859:function(e,t,a){e.exports=a(7892)},6094:function(e,t,a){"use strict";a.d(t,{JO:function(){return v},fC:function(){return h},xv:function(){return p}});var s=a(7653),r=a(8344),n=a(432),c=a(2063),l=a(2741),i=a(605),u=a(3579);let o={...l.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["soft","surface","outline"],default:"soft"},...i.o3,...u.K};var d=a(5236),f=a(2717),m=a(5159);let x=s.createContext({}),h=s.forwardRef((e,t)=>{let{size:a=o.size.default}=e,{asChild:c,children:l,className:i,color:u,...f}=(0,d.y)(e,o,m.E),h=c?n.fC:"div";return s.createElement(h,{"data-accent-color":u,...f,className:r("rt-CalloutRoot",i),ref:t},s.createElement(x.Provider,{value:s.useMemo(()=>({size:a}),[a])},l))});h.displayName="Callout.Root";let v=s.forwardRef(({className:e,...t},a)=>s.createElement("div",{...t,className:r("rt-CalloutIcon",e),ref:a}));v.displayName="Callout.Icon";let p=s.forwardRef(({className:e,...t},a)=>{let{size:n}=s.useContext(x);return s.createElement(c.x,{as:"p",size:(0,f.qz)(n,f.uJ),...t,asChild:!1,ref:a,className:r("rt-CalloutText",e)})});p.displayName="Callout.Text"},5543:function(e,t,a){"use strict";a.d(t,{Z:function(){return o}});var s=a(7653),r=a(8344),n=a(432),c=a(2741);let l={...c.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var i=a(5236),u=a(5159);let o=s.forwardRef((e,t)=>{let{asChild:a,className:c,...o}=(0,i.y)(e,l,u.E),d=a?n.fC:"div";return s.createElement(d,{ref:t,...o,className:r("rt-reset","rt-BaseCard","rt-Card",c)})});o.displayName="Card"}},function(e){e.O(0,[612,419,683,293,456,744],function(){return e(e.s=1873)}),_N_E=e.O()}]);