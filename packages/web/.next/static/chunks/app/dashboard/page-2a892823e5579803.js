(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{437:function(e,s,t){Promise.resolve().then(t.bind(t,9716))},9716:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return Q},dynamic:function(){return $}});var l=t(7573),i=t(7653),n=t(8436),a=t(8728),r=t(4045),c=t(4508),o=t(2063),d=t(5543),h=t(8473),u=t(8695),x=t(1467),m=t(4480),g=t(1874),j=t(7917),p=t(273),v=t(2151),f=t(6528),y=t(2480),b=t(9576),w=t(3682),N=t(4213),C=t(6052),k=t(7342),z=t(9218),E=t(7811),S=t(9467),A=t(794),R=t(9654);function I(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,R.m6)((0,A.W)(s))}let T=i.createContext({size:"2",variant:"soft"}),D=i.forwardRef((e,s)=>{let{className:t,variant:i,size:n,children:a,...r}=e;return(0,l.jsx)(S.fC,{ref:s,className:I("flex items-center justify-center gap-1",t),...r,children:(0,l.jsx)(T.Provider,{value:{variant:i,size:n},children:a})})});D.displayName=S.fC.displayName;let Z=i.forwardRef((e,s)=>{let{className:t,children:i,variant:n,size:a,...r}=e;return(0,l.jsx)(S.ck,{ref:s,className:I("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",t),...r,children:i})});Z.displayName=S.ck.displayName;var O=t(3511),L=t(7808),P=t(9290),U=t(8769),F=t(3668),V=t(7216),G=t(7347),_=t(3484),B=t(8073);function M(e){if(!e.trim())return[{id:K(),title:"Section 1",content:""}];let s=e.split(/^---\s*$/m);if(1===s.length){let s=e.match(/^#\s+(.+)/m),t=s?s[1].trim():"Section 1";return[{id:K(),title:t,content:e.trim()}]}let t=[],l={};for(let e=0;e<s.length;e++){let i=s[e].trim();if(!i)continue;let n=i.split("\n"),a=!0,r={};for(let e of n){let s=e.trim();if(!s)continue;let t=s.match(/^(description|name|globs):\s*(.+)$/);if(t){let[,e,s]=t;r[e]=s.trim()}else{a=!1;break}}if(a&&Object.keys(r).length>0)l={...l,...r};else{let e=i,s={...l},a=n.slice(),r=0;for(let e=0;e<n.length;e++){let t=n[e].trim();if(!t){r=e+1;continue}let l=t.match(/^(description|name|globs):\s*(.+)$/);if(l){let[,t,i]=l;s[t]=i.trim(),r=e+1}else break}if(e=a.slice(r).join("\n").trim()){let i=e.match(/^#\s+(.+)/m),n=i?i[1].trim():"Section ".concat(t.length+1);t.push({id:K(),title:n,content:e,...s}),l={}}}}return t.length>0?t:[{id:K(),title:"Section 1",content:e.trim()}]}function Y(e){return 0===e.length?"":1!==e.length||e[0].description||e[0].name||e[0].globs?e.map(s=>{let t=[],l=s.description||s.name||s.globs,i=0===e.indexOf(s);return(!i||l)&&t.push("---"),s.description&&t.push("description: ".concat(s.description)),s.name&&t.push("name: ".concat(s.name)),s.globs&&t.push("globs: ".concat(s.globs)),l&&t.push("---"),t.push(""),t.push(s.content),t.join("\n")}).join("\n\n"):e[0].content}function K(){return"section_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}function W(e){let{section:s,index:t,totalSections:c,isExpanded:u,onUpdate:x,onDelete:m,onDuplicate:g,onMoveUp:j,onMoveDown:p,onToggleExpanded:v}=e,[f,y]=(0,i.useState)([]),b=(e,t)=>{let l={...s,[e]:t};x(l);let i=function(e){let s=[];return e.title.trim()||s.push("Section title is required"),e.content.trim()||s.push("Section content is required"),s}(l);y(i)},w=function(e){let s=e.content.slice(0,100).replace(/\n/g," ");return s.length>100?"".concat(s,"..."):s}(s),N=f.length>0;return(0,l.jsxs)(d.Z,{className:N?"error-border":"",style:{transition:"all 0.2s ease"},children:[(0,l.jsxs)("div",{style:{paddingBottom:"var(--space-3)"},children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",children:[(0,l.jsxs)(r.k,{align:"center",gap:"3",style:{flex:1},children:[(0,l.jsxs)(r.k,{align:"center",gap:"1",children:[(0,l.jsx)(U.Z,{size:16,style:{color:"var(--gray-9)",cursor:"grab"}}),(0,l.jsx)(o.x,{size:"2",weight:"medium",style:{color:"var(--gray-9)"},children:t+1})]}),(0,l.jsxs)(a.x,{style:{flex:1},children:[(0,l.jsx)("div",{className:"font-semibold",style:{fontSize:"var(--font-size-3)"},children:s.title||"Section ".concat(t+1)}),!u&&(0,l.jsx)(o.x,{size:"2",style:{color:"var(--gray-9)",marginTop:"var(--space-1)"},truncate:!0,children:w})]})]}),(0,l.jsxs)(r.k,{align:"center",gap:"2",children:[(0,l.jsxs)(r.k,{align:"center",children:[(0,l.jsx)(n.z,{variant:"ghost",size:"1",onClick:j,disabled:0===t,style:{width:"32px",height:"32px",padding:0},children:(0,l.jsx)(F.Z,{size:16})}),(0,l.jsx)(n.z,{variant:"ghost",size:"1",onClick:p,disabled:t===c-1,style:{width:"32px",height:"32px",padding:0},children:(0,l.jsx)(V.Z,{size:16})})]}),(0,l.jsxs)(L.fC,{children:[(0,l.jsx)(L.xz,{children:(0,l.jsx)(n.z,{variant:"ghost",size:"1",style:{width:"32px",height:"32px",padding:0},children:(0,l.jsx)(G.Z,{size:16})})}),(0,l.jsxs)(L.VY,{align:"end",children:[(0,l.jsxs)(L.ck,{onClick:g,children:[(0,l.jsx)(_.Z,{size:16,style:{marginRight:"var(--space-2)"}}),"Duplicate"]}),(0,l.jsxs)(L.ck,{onClick:m,disabled:1===c,className:"error-text",children:[(0,l.jsx)(B.Z,{size:16,style:{marginRight:"var(--space-2)"}}),"Delete"]})]})]}),(0,l.jsx)(n.z,{variant:"ghost",size:"1",onClick:v,style:{width:"32px",height:"32px",padding:0},children:u?(0,l.jsx)(F.Z,{size:16}):(0,l.jsx)(V.Z,{size:16})})]})]}),N&&(0,l.jsx)(a.x,{style:{marginTop:"var(--space-2)"},children:f.map((e,s)=>(0,l.jsxs)(o.x,{size:"2",className:"error-text",style:{display:"block"},children:["• ",e]},s))})]}),u&&(0,l.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"var(--space-4)"},children:[(0,l.jsxs)(a.x,{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"section-title-".concat(s.id),size:"2",weight:"medium",children:"Section Title"}),(0,l.jsx)(h.f,{id:"section-title-".concat(s.id),value:s.title,onChange:e=>b("title",e.target.value),placeholder:"Enter section title..."})]}),(0,l.jsxs)(P.r,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,l.jsxs)(a.x,{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"section-name-".concat(s.id),size:"2",weight:"medium",children:"Name (Optional)"}),(0,l.jsx)(h.f,{id:"section-name-".concat(s.id),value:s.name||"",onChange:e=>b("name",e.target.value),placeholder:"e.g., global, stylesheet"})]}),(0,l.jsxs)(a.x,{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"section-globs-".concat(s.id),size:"2",weight:"medium",children:"File Patterns (Optional)"}),(0,l.jsx)(h.f,{id:"section-globs-".concat(s.id),value:s.globs||"",onChange:e=>b("globs",e.target.value),placeholder:"e.g., **.css, *.js"})]}),(0,l.jsxs)(a.x,{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"section-description-".concat(s.id),size:"2",weight:"medium",children:"Description (Optional)"}),(0,l.jsx)(h.f,{id:"section-description-".concat(s.id),value:s.description||"",onChange:e=>b("description",e.target.value),placeholder:"Brief description"})]})]}),(0,l.jsxs)(a.x,{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"section-content-".concat(s.id),size:"2",weight:"medium",children:"Content"}),(0,l.jsx)(E.p,{value:s.content,onChange:e=>b("content",e),placeholder:"Enter section content (markdown supported)..."})]})]})]})}function q(e){var s;let{sections:t,onSectionsChange:a}=e,[r,c]=(0,i.useState)(new Set(1===t.length?[null===(s=t[0])||void 0===s?void 0:s.id]:[])),d=(e,s)=>{let l=[...t];l[e]=s,a(l)},h=()=>{var e;let s=(e=t.length,{id:K(),title:"Section ".concat(e+1),content:""}),l=[...t,s];a(l),c(e=>new Set([...e,s.id]))},u=e=>{if(1===t.length)return;let s=t[e],l=t.filter((s,t)=>t!==e);a(l),c(e=>{let t=new Set(e);return t.delete(s.id),t})},x=e=>{let s=t[e],l={...s,id:K(),title:"".concat(s.title," (Copy)")},i=[...t];i.splice(e+1,0,l),a(i),c(e=>new Set([...e,l.id]))},m=(e,s)=>{let l=function(e,s,t){let l=[...e],[i]=l.splice(s,1);return l.splice(t,0,i),l}(t,e,s);a(l)},g=e=>{c(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t})};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(o.x,{className:"text-base font-medium",children:"Rule Sections"}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",t.length," section",1!==t.length?"s":"",")"]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[t.length>1&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.z,{variant:"ghost",size:"1",onClick:()=>{c(new Set([...t.map(e=>e.id)]))},className:"text-xs",children:"Expand All"}),(0,l.jsx)(n.z,{variant:"ghost",size:"1",onClick:()=>{c(new Set([]))},className:"text-xs",children:"Collapse All"})]}),(0,l.jsxs)(n.z,{variant:"outline",size:"1",onClick:h,className:"gap-2",children:[(0,l.jsx)(j.Z,{className:"h-4 w-4"}),"Add Section"]})]})]}),(0,l.jsx)("div",{className:"space-y-3",children:0===t.length?(0,l.jsxs)("div",{className:"text-center py-12 border-2 border-dashed border-muted rounded-lg",children:[(0,l.jsx)(O.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No sections yet"}),(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add your first section to get started"}),(0,l.jsxs)(n.z,{onClick:h,className:"gap-2",children:[(0,l.jsx)(j.Z,{className:"h-4 w-4"}),"Add Section"]})]}):t.map((e,s)=>(0,l.jsx)(W,{section:e,index:s,totalSections:t.length,isExpanded:r.has(e.id),onUpdate:e=>d(s,e),onDelete:()=>u(s),onDuplicate:()=>x(s),onMoveUp:()=>m(s,s-1),onMoveDown:()=>m(s,s+1),onToggleExpanded:()=>g(e.id)},e.id))}),t.length>0&&(0,l.jsxs)("div",{className:"text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg",children:[(0,l.jsx)("p",{className:"font-medium mb-1",children:"Section Format:"}),(0,l.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,l.jsxs)("li",{children:["• Sections are separated by ",(0,l.jsx)("code",{children:"---"})," delimiters"]}),(0,l.jsxs)("li",{children:["• Optional metadata: ",(0,l.jsx)("code",{children:"description"}),", ",(0,l.jsx)("code",{children:"name"}),", ",(0,l.jsx)("code",{children:"globs"})]}),(0,l.jsx)("li",{children:"• Content supports markdown formatting"}),(0,l.jsx)("li",{children:"• Use file patterns (globs) to target specific file types"})]})]})]})}function H(e){let{rule:s,onSave:t,onCancel:a}=e,[c,d]=(0,i.useState)((null==s?void 0:s.title)||""),[m,g]=(0,i.useState)((null==s?void 0:s.description)||""),[v,f]=(0,i.useState)([]),[y,b]=(0,i.useState)((null==s?void 0:s.content)||""),[w,N]=(0,i.useState)("simple"),[S,A]=(0,i.useState)((null==s?void 0:s.ideType)||"GENERAL"),[R,I]=(0,i.useState)((null==s?void 0:s.visibility)||"PRIVATE"),[T,O]=(0,i.useState)((null==s?void 0:s.tags.map(e=>e.tag.name))||[]),[L,P]=(0,i.useState)("");(0,i.useEffect)(()=>{if(null==s?void 0:s.content){let e=M(s.content);f(e),b(s.content)}else{let e=[{id:"section_".concat(Date.now()),title:"Section 1",content:""}];f(e),b("")}},[null==s?void 0:s.content]);let U=()=>{L.trim()&&!T.includes(L.trim())&&(O([...T,L.trim()]),P(""))},F=e=>{O(T.filter(s=>s!==e))},V=e=>{if(e!==w){if("simple"===e){let e=Y(v);b(e)}else{let e=M(y);f(e)}N(e)}};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"title",size:"2",weight:"medium",children:"Rule Title"}),(0,l.jsx)(h.f,{id:"title",value:c,onChange:e=>d(e.target.value),placeholder:"Enter rule title..."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"description",size:"2",weight:"medium",children:"Description"}),(0,l.jsx)(C.K,{id:"description",value:m,onChange:e=>g(e.target.value),placeholder:"Describe what this rule does...",rows:3})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"ide-type",size:"2",weight:"medium",children:"IDE Type"}),(0,l.jsxs)(u.fC,{value:S,onValueChange:e=>A(e),children:[(0,l.jsx)(u.xz,{placeholder:"Select IDE type"}),(0,l.jsxs)(u.VY,{children:[(0,l.jsx)(u.ck,{value:"GENERAL",children:"General"}),(0,l.jsx)(u.ck,{value:"CURSOR",children:"Cursor"}),(0,l.jsx)(u.ck,{value:"AUGMENT",children:"Augment Code"}),(0,l.jsx)(u.ck,{value:"WINDSURF",children:"Windsurf"}),(0,l.jsx)(u.ck,{value:"CLAUDE",children:"Claude"}),(0,l.jsx)(u.ck,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,l.jsx)(u.ck,{value:"GEMINI",children:"Gemini"}),(0,l.jsx)(u.ck,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,l.jsx)(u.ck,{value:"CLINE",children:"Cline"}),(0,l.jsx)(u.ck,{value:"JUNIE",children:"Junie"}),(0,l.jsx)(u.ck,{value:"TRAE",children:"Trae"}),(0,l.jsx)(u.ck,{value:"LINGMA",children:"Lingma"}),(0,l.jsx)(u.ck,{value:"KIRO",children:"Kiro"}),(0,l.jsx)(u.ck,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{as:"label",htmlFor:"visibility",size:"2",weight:"medium",children:"Visibility"}),(0,l.jsxs)(u.fC,{value:R,onValueChange:e=>I(e),children:[(0,l.jsx)(u.xz,{placeholder:"Select visibility"}),(0,l.jsxs)(u.VY,{children:[(0,l.jsx)(u.ck,{value:"PRIVATE",children:"Private"}),(0,l.jsx)(u.ck,{value:"PUBLIC",children:"Public"})]})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{as:"label",size:"2",weight:"medium",children:"Tags"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:T.map(e=>(0,l.jsxs)(x.C,{variant:"soft",className:"gap-1",children:[e,(0,l.jsx)(n.z,{variant:"ghost",size:"1",className:"h-auto p-0 w-4 h-4",onClick:()=>F(e),children:(0,l.jsx)(k.Z,{className:"h-3 w-3"})})]},e))}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(h.f,{value:L,onChange:e=>P(e.target.value),placeholder:"Add a tag...",onKeyPress:e=>"Enter"===e.key&&U()}),(0,l.jsx)(n.z,{onClick:U,size:"1",children:(0,l.jsx)(j.Z,{className:"h-4 w-4"})})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",children:[(0,l.jsx)(o.x,{as:"label",size:"2",weight:"medium",children:"Rule Content"}),(0,l.jsxs)(D,{type:"single",value:w,onValueChange:e=>e&&V(e),className:"bg-gray-50 dark:bg-gray-800 rounded-md p-1",children:[(0,l.jsxs)(Z,{value:"simple",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[(0,l.jsx)(p.Z,{className:"h-4 w-4"}),"Simple"]}),(0,l.jsxs)(Z,{value:"advanced",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[(0,l.jsx)(z.Z,{className:"h-4 w-4"}),"Advanced"]})]})]}),"simple"===w?(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content in a simple code editor format"}),(0,l.jsx)(E.p,{value:y,onChange:e=>{if(b(e),"simple"===w){let s=M(e);f(s)}},placeholder:"Enter your rule content here...",className:"min-h-[300px]"})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)(o.x,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content using structured sections with metadata"}),(0,l.jsx)(q,{sections:v,onSectionsChange:e=>{if(f(e),"advanced"===w){let s=Y(e);b(s)}}})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(n.z,{variant:"outline",onClick:a,children:"Cancel"}),(0,l.jsx)(n.z,{onClick:()=>{let e="simple"===w?y:Y(v);t({title:c,description:m,content:e,ideType:S,visibility:R,tags:T})},children:s?"Update Rule":"Create Rule"})]})]})}var J=t(8275),X=t(3588);let $="force-dynamic";function Q(){let{data:e}=(0,J.kP)(),[s,t]=(0,i.useState)([]),[C,k]=(0,i.useState)([]),[z,E]=(0,i.useState)(!0),[S,A]=(0,i.useState)(""),[R,I]=(0,i.useState)([]),[T,D]=(0,i.useState)("ALL"),[Z,O]=(0,i.useState)(!1),[L,P]=(0,i.useState)(null);(0,i.useEffect)(()=>{console.log("Dialog state changed:",Z)},[Z]),(0,i.useEffect)(()=>{let e=e=>{"Escape"===e.key&&Z&&O(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[Z]),(0,i.useEffect)(()=>{let e=()=>{let e=document.querySelectorAll("*"),s=[];e.forEach(e=>{let t=window.getComputedStyle(e),l=e.getBoundingClientRect();"fixed"===t.position&&l.width>=.9*window.innerWidth&&l.height>=.9*window.innerHeight&&"none"!==t.pointerEvents&&s.push(e)}),s.length>0&&(console.warn("Found blocking elements:",s),s.forEach(e=>{console.log("Blocking element:",{element:e,classes:e.className,id:e.id,zIndex:window.getComputedStyle(e).zIndex,display:window.getComputedStyle(e).display})}))};e();let s=setTimeout(e,1e3);return()=>clearTimeout(s)},[]);let U=(0,i.useCallback)(async()=>{try{let e=new URLSearchParams;S&&e.set("search",S),R.length>0&&e.set("tags",R.join(",")),"ALL"!==T&&e.set("ideType",T);let s=await fetch("/api/rules?".concat(e)),l=await s.json();t(Array.isArray(l)?l:[])}catch(e){console.error("Error fetching rules:",e),X.Am.error("Failed to fetch rules"),t([])}finally{E(!1)}},[S,R,T]),F=async()=>{try{let e=await fetch("/api/tags"),s=await e.json();k(s)}catch(e){console.error("Error fetching tags:",e)}};(0,i.useEffect)(()=>{U(),F()},[U,S,R,T]);let V=()=>{P(null),O(!0)},G=e=>{P(e),O(!0)},_=async e=>{try{let s=L?"PUT":"POST",t=L?"/api/rules/".concat(L.id):"/api/rules",l=await fetch(t,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});l.ok?(X.Am.success(L?"Rule updated":"Rule created"),O(!1),P(null),U()):X.Am.error("Failed to save rule")}catch(e){console.error("Error saving rule:",e),X.Am.error("Failed to save rule")}},B=async e=>{try{let s=await fetch("/api/rules/".concat(e),{method:"DELETE"});s.ok?(X.Am.success("Rule deleted"),U()):X.Am.error("Failed to delete rule")}catch(e){console.error("Error deleting rule:",e),X.Am.error("Failed to delete rule")}},M=e=>{I(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])};if(!(null==e?void 0:e.user))return(0,l.jsx)("div",{className:"container py-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-4",style:{color:"hsl(0 0% 98%)"},children:"Please sign in to continue"}),(0,l.jsx)(n.z,{asChild:!0,children:(0,l.jsx)("a",{href:"/auth/signin",children:"Sign In"})})]})});let Y=e||null,K=Array.isArray(s)?s.filter(e=>{var s;return e.userId===(null==Y?void 0:null===(s=Y.user)||void 0===s?void 0:s.id)}):[],W=Array.isArray(s)?s.filter(e=>"PUBLIC"===e.visibility):[],q={totalRules:K.length,publicRules:K.filter(e=>"PUBLIC"===e.visibility).length,privateRules:K.filter(e=>"PRIVATE"===e.visibility).length,totalViews:0};return(0,l.jsxs)(a.x,{className:"container py-8 space-y-8",children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",children:[(0,l.jsxs)(a.x,{children:[(0,l.jsx)(c.X,{size:"8",weight:"bold",color:"gray",highContrast:!0,children:"Dashboard"}),(0,l.jsx)(o.x,{size:"3",color:"gray",children:"Manage your AI prompt rules and explore the community"})]}),(0,l.jsxs)(n.z,{onClick:V,children:[(0,l.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),"New Rule"]})]}),(0,l.jsxs)(a.x,{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,l.jsxs)(d.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(o.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Total Rules"}),(0,l.jsx)(p.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(o.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:q.totalRules})]}),(0,l.jsxs)(d.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(o.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Public Rules"}),(0,l.jsx)(v.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(o.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:q.publicRules})]}),(0,l.jsxs)(d.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(o.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Private Rules"}),(0,l.jsx)(f.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(o.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:q.privateRules})]}),(0,l.jsxs)(d.Z,{children:[(0,l.jsxs)(r.k,{justify:"between",align:"center",pb:"2",children:[(0,l.jsx)(o.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Total Views"}),(0,l.jsx)(y.Z,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,l.jsx)(o.x,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:q.totalViews})]})]}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,l.jsxs)("div",{className:"relative flex-1",children:[(0,l.jsx)(b.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(h.f,{placeholder:"Search rules...",value:S,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,l.jsxs)(u.fC,{value:T,onValueChange:D,children:[(0,l.jsx)(u.xz,{className:"w-full md:w-48",placeholder:"IDE Type"}),(0,l.jsxs)(u.VY,{children:[(0,l.jsx)(u.ck,{value:"ALL",children:"All IDEs"}),(0,l.jsx)(u.ck,{value:"GENERAL",children:"General"}),(0,l.jsx)(u.ck,{value:"CURSOR",children:"Cursor"}),(0,l.jsx)(u.ck,{value:"AUGMENT",children:"Augment Code"}),(0,l.jsx)(u.ck,{value:"WINDSURF",children:"Windsurf"}),(0,l.jsx)(u.ck,{value:"CLAUDE",children:"Claude"}),(0,l.jsx)(u.ck,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,l.jsx)(u.ck,{value:"GEMINI",children:"Gemini"}),(0,l.jsx)(u.ck,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,l.jsx)(u.ck,{value:"CLINE",children:"Cline"}),(0,l.jsx)(u.ck,{value:"JUNIE",children:"Junie"}),(0,l.jsx)(u.ck,{value:"TRAE",children:"Trae"}),(0,l.jsx)(u.ck,{value:"LINGMA",children:"Lingma"}),(0,l.jsx)(u.ck,{value:"KIRO",children:"Kiro"}),(0,l.jsx)(u.ck,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),C.length>0&&(0,l.jsxs)(a.x,{className:"space-y-2",children:[(0,l.jsxs)(r.k,{align:"center",gap:"2",children:[(0,l.jsx)(w.Z,{className:"h-4 w-4",style:{color:"var(--gray-12)"}}),(0,l.jsx)(o.x,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Filter by tags:"})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:C.map(e=>(0,l.jsx)(x.C,{variant:R.includes(e.name)?"solid":"outline",className:"cursor-pointer",onClick:()=>M(e.name),style:{borderColor:e.color,backgroundColor:R.includes(e.name)?e.color:"transparent"},children:e.name},e.id))})]}),(0,l.jsxs)(m.fC,{defaultValue:"my-rules",className:"space-y-6",children:[(0,l.jsxs)(m.aV,{children:[(0,l.jsxs)(m.xz,{value:"my-rules",children:["My Rules (",K.length,")"]}),(0,l.jsxs)(m.xz,{value:"community",children:["Community (",W.length,")"]})]}),(0,l.jsx)(m.VY,{value:"my-rules",className:"space-y-6",children:0===K.length?(0,l.jsxs)(d.Z,{className:"py-12 text-center",children:[(0,l.jsx)(p.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No rules yet"}),(0,l.jsx)("p",{className:"text-muted-foreground mb-4",children:"Create your first AI prompt rule to get started"}),(0,l.jsxs)(n.z,{onClick:V,children:[(0,l.jsx)(j.Z,{className:"mr-2 h-4 w-4"}),"Create Rule"]})]}):(0,l.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:K.map(e=>(0,l.jsx)(N.q,{rule:e,onEdit:G,onDelete:B,isOwner:!0},e.id))})}),(0,l.jsx)(m.VY,{value:"community",className:"space-y-6",children:0===W.length?(0,l.jsxs)(d.Z,{className:"py-12 text-center",children:[(0,l.jsx)(v.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No community rules found"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:"adjusting your filters or check back later"})]}):(0,l.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:W.map(e=>{var s;return(0,l.jsx)(N.q,{rule:e,isOwner:e.userId===(null==Y?void 0:null===(s=Y.user)||void 0===s?void 0:s.id)},e.id)})})})]}),(0,l.jsx)(g.fC,{open:Z,onOpenChange:O,children:(0,l.jsxs)(g.VY,{className:"max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,l.jsx)(g.Dx,{children:L?"Edit Rule":"Create New Rule"}),(0,l.jsx)("div",{className:"overflow-y-auto max-h-[70vh]",children:(0,l.jsx)(H,{rule:L||void 0,onSave:_,onCancel:()=>{O(!1),P(null)}})})]})})]})}},8275:function(e,s,t){"use strict";t.d(s,{kP:function(){return c},w7:function(){return r},zB:function(){return n}});var l=t(7750);let i=(0,l.XL)({baseURL:"http://localhost:3000"}),{signIn:n,signUp:a,signOut:r,useSession:c,getSession:o}=i}},function(e){e.O(0,[906,612,834,419,67,683,11,396,427,213,293,456,744],function(){return e(e.s=437)}),_N_E=e.O()}]);