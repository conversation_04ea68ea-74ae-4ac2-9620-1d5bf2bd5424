(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[470],{779:function(e,r,n){Promise.resolve().then(n.bind(n,9274))},9274:function(e,r,n){"use strict";n.r(r),n.d(r,{default:function(){return o}});var t=n(7573);function o(e){let{error:r,reset:n}=e;return(0,t.jsx)("html",{children:(0,t.jsx)("body",{children:(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-6xl font-bold mb-4",children:"500"}),(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Something went wrong!"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"An unexpected error occurred. Please try again."}),(0,t.jsx)("button",{onClick:()=>n(),className:"inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-4",children:"Try again"}),(0,t.jsx)("a",{href:"/",className:"inline-block px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors",children:"Go Home"})]})})})})}},8294:function(e,r,n){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t=n(7653),o=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,c=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function a(e,r,n){var t,s={},a=null,u=null;for(t in void 0!==n&&(a=""+n),void 0!==r.key&&(a=""+r.key),void 0!==r.ref&&(u=r.ref),r)l.call(r,t)&&!i.hasOwnProperty(t)&&(s[t]=r[t]);if(e&&e.defaultProps)for(t in r=e.defaultProps)void 0===s[t]&&(s[t]=r[t]);return{$$typeof:o,type:e,key:a,ref:u,props:s,_owner:c.current}}r.Fragment=s,r.jsx=a,r.jsxs=a},7573:function(e,r,n){"use strict";e.exports=n(8294)}},function(e){e.O(0,[293,456,744],function(){return e(e.s=779)}),_N_E=e.O()}]);