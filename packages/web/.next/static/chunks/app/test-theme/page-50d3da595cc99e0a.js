(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[557],{1604:function(e,r,a){Promise.resolve().then(a.bind(a,380))},380:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return M}});var t=a(7573),s=a(7653),n=a(8344),i=a(432),l=a(2741);let c={...l.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"4",responsive:!0},display:{type:"enum",className:"rt-r-display",values:["none","initial"],parseValue:function(e){return"initial"===e?"flex":e},responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["left","center","right"],parseValue:function(e){return"left"===e?"start":"right"===e?"end":e},responsive:!0}};var o=a(5236),d=a(3589),u=a(3230),m=a(1795),h=a(5159),p=a(6306);let x=s.forwardRef(({width:e,minWidth:r,maxWidth:a,height:t,minHeight:l,maxHeight:x,...f},v)=>{let{asChild:g,children:j,className:y,...N}=(0,o.y)(f,c,m.P,h.E),{className:b,style:w}=(0,o.y)({width:e,minWidth:r,maxWidth:a,height:t,minHeight:l,maxHeight:x},p.n,u.F),C=g?i.fC:"div";return s.createElement(C,{...N,ref:v,className:n("rt-Container",y)},(0,d.x)({asChild:g,children:j},e=>s.createElement("div",{className:n("rt-ContainerInner",b),style:w},e)))});x.displayName="Container";var f=a(4045),v=a(8728),g=a(4508),j=a(2063),y=a(9290),N=a(5543),b=a(8436),w=a(8473),C=a(8695),T=a(4480),z=a(605),E=a(3579),R=a(7271);let k={orientation:{type:"enum",className:"rt-r-orientation",values:["horizontal","vertical"],default:"horizontal",responsive:!0},size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},trim:{...R.E.trim,className:"rt-r-trim"}},V={align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0}},D={...p.n,...z.EG,...E.K},I=s.forwardRef((e,r)=>{let{className:a,...t}=(0,o.y)(e,k,h.E);return s.createElement(j.x,{asChild:!0},s.createElement("dl",{...t,ref:r,className:n("rt-DataListRoot",a)}))});I.displayName="DataList.Root";let F=s.forwardRef((e,r)=>{let{className:a,...t}=(0,o.y)(e,V);return s.createElement("div",{...t,ref:r,className:n("rt-DataListItem",a)})});F.displayName="DataList.Item";let L=s.forwardRef((e,r)=>{let{className:a,color:t,...i}=(0,o.y)(e,D);return s.createElement("dt",{...i,"data-accent-color":t,ref:r,className:n("rt-DataListLabel",a)})});L.displayName="DataList.Label";let S=s.forwardRef(({children:e,className:r,...a},t)=>s.createElement("dd",{...a,ref:t,className:n(r,"rt-DataListValue")},e));S.displayName="DataList.Value";var P=a(1467),G=a(1874);function M(){let[e,r]=(0,s.useState)(!1);return(0,t.jsx)(x,{size:"3",py:"8",children:(0,t.jsxs)(f.k,{direction:"column",gap:"8",children:[(0,t.jsxs)(v.x,{children:[(0,t.jsx)(g.X,{size:"8",mb:"4",children:"Radix UI Theme v3 Test Page"}),(0,t.jsx)(j.x,{size:"4",color:"gray",children:"This page demonstrates the proper implementation of Radix UI Theme v3 components with consistent styling and design patterns."})]}),(0,t.jsxs)(y.r,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,t.jsx)(N.Z,{children:(0,t.jsxs)(f.k,{direction:"column",gap:"3",children:[(0,t.jsx)(g.X,{size:"4",children:"Classic Card"}),(0,t.jsx)(j.x,{color:"gray",children:"This is a classic card with default styling from Radix UI Themes."}),(0,t.jsx)(b.z,{variant:"soft",children:"Learn More"})]})}),(0,t.jsx)(N.Z,{variant:"surface",children:(0,t.jsxs)(f.k,{direction:"column",gap:"3",children:[(0,t.jsx)(g.X,{size:"4",children:"Surface Card"}),(0,t.jsx)(j.x,{color:"gray",children:"Surface variant provides a subtle background color."}),(0,t.jsx)(b.z,{variant:"surface",children:"Explore"})]})}),(0,t.jsx)(N.Z,{variant:"ghost",children:(0,t.jsxs)(f.k,{direction:"column",gap:"3",children:[(0,t.jsx)(g.X,{size:"4",children:"Ghost Card"}),(0,t.jsx)(j.x,{color:"gray",children:"Ghost variant has minimal visual styling."}),(0,t.jsx)(b.z,{variant:"outline",children:"Discover"})]})})]}),(0,t.jsxs)(N.Z,{size:"3",children:[(0,t.jsx)(g.X,{size:"5",mb:"4",children:"Form Elements"}),(0,t.jsxs)(f.k,{direction:"column",gap:"4",children:[(0,t.jsxs)(v.x,{children:[(0,t.jsx)(j.x,{as:"label",size:"2",weight:"medium",htmlFor:"name",children:"Name"}),(0,t.jsx)(w.f,{id:"name",placeholder:"Enter your name",mt:"1"})]}),(0,t.jsxs)(v.x,{children:[(0,t.jsx)(j.x,{as:"label",size:"2",weight:"medium",htmlFor:"role",children:"Role"}),(0,t.jsxs)(C.fC,{defaultValue:"developer",children:[(0,t.jsx)(C.xz,{id:"role",placeholder:"Select a role"}),(0,t.jsxs)(C.VY,{children:[(0,t.jsx)(C.ck,{value:"developer",children:"Developer"}),(0,t.jsx)(C.ck,{value:"designer",children:"Designer"}),(0,t.jsx)(C.ck,{value:"manager",children:"Manager"})]})]})]}),(0,t.jsxs)(f.k,{gap:"3",mt:"4",children:[(0,t.jsx)(b.z,{variant:"solid",children:"Submit"}),(0,t.jsx)(b.z,{variant:"soft",color:"gray",children:"Cancel"})]})]})]}),(0,t.jsx)(N.Z,{children:(0,t.jsxs)(T.fC,{defaultValue:"overview",children:[(0,t.jsxs)(T.aV,{children:[(0,t.jsx)(T.xz,{value:"overview",children:"Overview"}),(0,t.jsx)(T.xz,{value:"details",children:"Details"}),(0,t.jsx)(T.xz,{value:"settings",children:"Settings"})]}),(0,t.jsxs)(v.x,{pt:"3",children:[(0,t.jsx)(T.VY,{value:"overview",children:(0,t.jsx)(j.x,{size:"2",children:"This is the overview tab content. It demonstrates how tabs work in Radix UI Themes with proper spacing and typography."})}),(0,t.jsx)(T.VY,{value:"details",children:(0,t.jsxs)(I,{children:[(0,t.jsxs)(F,{children:[(0,t.jsx)(L,{children:"Status"}),(0,t.jsx)(S,{children:(0,t.jsx)(P.C,{color:"green",children:"Active"})})]}),(0,t.jsxs)(F,{children:[(0,t.jsx)(L,{children:"Version"}),(0,t.jsx)(S,{children:"3.0.0"})]}),(0,t.jsxs)(F,{children:[(0,t.jsx)(L,{children:"Theme"}),(0,t.jsx)(S,{children:"Radix UI"})]})]})}),(0,t.jsx)(T.VY,{value:"settings",children:(0,t.jsx)(j.x,{size:"2",children:"Settings configuration would go here."})})]})]})}),(0,t.jsx)(f.k,{gap:"3",children:(0,t.jsxs)(G.fC,{open:e,onOpenChange:r,children:[(0,t.jsx)(G.xz,{children:(0,t.jsx)(b.z,{children:"Open Dialog"})}),(0,t.jsxs)(G.VY,{maxWidth:"450px",children:[(0,t.jsx)(G.Dx,{children:"Example Dialog"}),(0,t.jsx)(G.dk,{size:"2",mb:"4",children:"This dialog demonstrates proper theming with Radix UI v3."}),(0,t.jsxs)(f.k,{direction:"column",gap:"3",children:[(0,t.jsx)(w.f,{placeholder:"Enter some text..."}),(0,t.jsx)(j.x,{size:"2",color:"gray",children:"Dialog content is properly styled and accessible."})]}),(0,t.jsxs)(f.k,{gap:"3",mt:"4",justify:"end",children:[(0,t.jsx)(G.x8,{children:(0,t.jsx)(b.z,{variant:"soft",color:"gray",children:"Cancel"})}),(0,t.jsx)(b.z,{children:"Save Changes"})]})]})]})}),(0,t.jsxs)(v.x,{children:[(0,t.jsx)(g.X,{size:"5",mb:"4",children:"Color System"}),(0,t.jsxs)(f.k,{gap:"2",wrap:"wrap",children:[(0,t.jsx)(P.C,{children:"Default"}),(0,t.jsx)(P.C,{color:"blue",children:"Blue"}),(0,t.jsx)(P.C,{color:"green",children:"Green"}),(0,t.jsx)(P.C,{color:"red",children:"Red"}),(0,t.jsx)(P.C,{color:"orange",children:"Orange"}),(0,t.jsx)(P.C,{color:"purple",children:"Purple"}),(0,t.jsx)(P.C,{color:"pink",children:"Pink"}),(0,t.jsx)(P.C,{color:"yellow",children:"Yellow"})]})]})]})})}},8728:function(e,r,a){"use strict";a.d(r,{x:function(){return u}});var t=a(7653),s=a(8344),n=a(9524),i=a(2741);let l={as:{type:"enum",values:["div","span"],default:"div"},...i.C,display:{type:"enum",className:"rt-r-display",values:["none","inline","inline-block","block","contents"],responsive:!0}};var c=a(5236),o=a(1795),d=a(5159);let u=t.forwardRef((e,r)=>{let{className:a,asChild:i,as:u="div",...m}=(0,c.y)(e,l,o.P,d.E);return t.createElement(i?n.g7:u,{...m,ref:r,className:s("rt-Box",a)})});u.displayName="Box"},5543:function(e,r,a){"use strict";a.d(r,{Z:function(){return d}});var t=a(7653),s=a(8344),n=a(432),i=a(2741);let l={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var c=a(5236),o=a(5159);let d=t.forwardRef((e,r)=>{let{asChild:a,className:i,...d}=(0,c.y)(e,l,o.E),u=a?n.fC:"div";return t.createElement(u,{ref:r,...d,className:s("rt-reset","rt-BaseCard","rt-Card",i)})});d.displayName="Card"},9290:function(e,r,a){"use strict";a.d(r,{r:function(){return h}});var t=a(7653),s=a(8344),n=a(9524),i=a(2741),l=a(6357);let c={as:{type:"enum",values:["div","span"],default:"div"},...i.C,display:{type:"enum",className:"rt-r-display",values:["none","inline-grid","grid"],responsive:!0},areas:{type:"string",className:"rt-r-gta",customProperties:["--grid-template-areas"],responsive:!0},columns:{type:"enum | string",className:"rt-r-gtc",customProperties:["--grid-template-columns"],values:["1","2","3","4","5","6","7","8","9"],parseValue:o,responsive:!0},rows:{type:"enum | string",className:"rt-r-gtr",customProperties:["--grid-template-rows"],values:["1","2","3","4","5","6","7","8","9"],parseValue:o,responsive:!0},flow:{type:"enum",className:"rt-r-gaf",values:["row","column","dense","row-dense","column-dense"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(e){return"between"===e?"space-between":e},responsive:!0},...l.c};function o(e){return c.columns.values.includes(e)?e:e?.match(/^\d+$/)?`repeat(${e}, minmax(0, 1fr))`:e}var d=a(5236),u=a(1795),m=a(5159);let h=t.forwardRef((e,r)=>{let{className:a,asChild:i,as:l="div",...o}=(0,d.y)(e,c,u.P,m.E);return t.createElement(i?n.g7:l,{...o,ref:r,className:s("rt-Grid",a)})});h.displayName="Grid"},4480:function(e,r,a){"use strict";a.d(r,{VY:function(){return G},aV:function(){return S},fC:function(){return L},xz:function(){return P}});var t=a(7653),s=a(8344),n=a(1082),i=a(4036),l=a(306),c=a(7575),o=a(8671),d=a(7205),u=a(7840),m=a(6303),h=a(7573),p="Tabs",[x,f]=(0,i.b)(p,[l.Pc]),v=(0,l.Pc)(),[g,j]=x(p),y=t.forwardRef((e,r)=>{let{__scopeTabs:a,value:t,onValueChange:s,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:c="automatic",...x}=e,f=(0,d.gm)(l),[v,j]=(0,u.T)({prop:t,onChange:s,defaultProp:n??"",caller:p});return(0,h.jsx)(g,{scope:a,baseId:(0,m.M)(),value:v,onValueChange:j,orientation:i,dir:f,activationMode:c,children:(0,h.jsx)(o.WV.div,{dir:f,"data-orientation":i,...x,ref:r})})});y.displayName=p;var N="TabsList",b=t.forwardRef((e,r)=>{let{__scopeTabs:a,loop:t=!0,...s}=e,n=j(N,a),i=v(a);return(0,h.jsx)(l.fC,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:t,children:(0,h.jsx)(o.WV.div,{role:"tablist","aria-orientation":n.orientation,...s,ref:r})})});b.displayName=N;var w="TabsTrigger",C=t.forwardRef((e,r)=>{let{__scopeTabs:a,value:t,disabled:s=!1,...i}=e,c=j(w,a),d=v(a),u=E(c.baseId,t),m=R(c.baseId,t),p=t===c.value;return(0,h.jsx)(l.ck,{asChild:!0,...d,focusable:!s,active:p,children:(0,h.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:u,...i,ref:r,onMouseDown:(0,n.M)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(t)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(t)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||s||!e||c.onValueChange(t)})})})});C.displayName=w;var T="TabsContent",z=t.forwardRef((e,r)=>{let{__scopeTabs:a,value:s,forceMount:n,children:i,...l}=e,d=j(T,a),u=E(d.baseId,s),m=R(d.baseId,s),p=s===d.value,x=t.useRef(p);return t.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(c.z,{present:n||p,children:({present:a})=>(0,h.jsx)(o.WV.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:m,tabIndex:0,...l,ref:r,style:{...e.style,animationDuration:x.current?"0s":void 0},children:a&&i})})});function E(e,r){return`${e}-trigger-${r}`}function R(e,r){return`${e}-content-${r}`}z.displayName=T;var k=a(605),V=a(3579);let D={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end"],responsive:!0},...k.EG,...V.K};var I=a(5236),F=a(5159);let L=t.forwardRef((e,r)=>{let{className:a,...n}=(0,I.y)(e,F.E);return t.createElement(y,{...n,ref:r,className:s("rt-TabsRoot",a)})});L.displayName="Tabs.Root";let S=t.forwardRef((e,r)=>{let{className:a,color:n,...i}=(0,I.y)(e,D,F.E);return t.createElement(b,{"data-accent-color":n,...i,asChild:!1,ref:r,className:s("rt-BaseTabList","rt-TabsList",a)})});S.displayName="Tabs.List";let P=t.forwardRef((e,r)=>{let{className:a,children:n,...i}=e;return t.createElement(C,{...i,asChild:!1,ref:r,className:s("rt-reset","rt-BaseTabListTrigger","rt-TabsTrigger",a)},t.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabsTriggerInner"},n),t.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden"},n))});P.displayName="Tabs.Trigger";let G=t.forwardRef((e,r)=>{let{className:a,...n}=(0,I.y)(e,F.E);return t.createElement(z,{...n,ref:r,className:s("rt-TabsContent",a)})});G.displayName="Tabs.Content"},8473:function(e,r,a){"use strict";a.d(r,{f:function(){return p}});var t=a(7653),s=a(8344),n=a(8556),i=a(605),l=a(6750),c=a(4494),o=a(837);let d={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft"],default:"surface"},...i.EG,...c.I},u={side:{type:"enum",values:["left","right"]},...i.EG,gap:o.l.gap,px:l.i.px,pl:l.i.pl,pr:l.i.pr};var m=a(5236),h=a(5159);let p=t.forwardRef((e,r)=>{let a=t.useRef(null),{children:i,className:l,color:c,radius:o,style:u,...p}=(0,m.y)(e,d,h.E);return t.createElement("div",{"data-accent-color":c,"data-radius":o,style:u,className:s("rt-TextFieldRoot",l),onPointerDown:e=>{let r=e.target;if(r.closest("input, button, a"))return;let t=a.current;if(!t)return;let s=r.closest(`
            .rt-TextFieldSlot[data-side='right'],
            .rt-TextFieldSlot:not([data-side='right']) ~ .rt-TextFieldSlot:not([data-side='left'])
          `)?t.value.length:0;requestAnimationFrame(()=>{try{t.setSelectionRange(s,s)}catch{}t.focus()})}},t.createElement("input",{spellCheck:"false",...p,ref:(0,n.F)(a,r),className:"rt-reset rt-TextFieldInput"}),i)});p.displayName="TextField.Root";let x=t.forwardRef((e,r)=>{let{className:a,color:n,side:i,...l}=(0,m.y)(e,u);return t.createElement("div",{"data-accent-color":n,"data-side":i,...l,ref:r,className:s("rt-TextFieldSlot",a)})});x.displayName="TextField.Slot"}},function(e){e.O(0,[612,834,67,293,456,744],function(){return e(e.s=1604)}),_N_E=e.O()}]);