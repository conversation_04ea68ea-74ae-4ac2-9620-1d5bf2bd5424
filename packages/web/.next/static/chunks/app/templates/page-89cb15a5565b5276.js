(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[0],{9213:function(e,t,r){"use strict";r.d(t,{Z:function(){return c}});var s=r(7653);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:c,iconNode:o,...d},u)=>(0,s.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:a("lucide",l),...d},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},c)=>(0,s.createElement)(l,{ref:c,iconNode:t,className:a(`lucide-${n(e)}`,r),...i}));return r.displayName=`${e}`,r}},8907:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var s=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},273:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var s=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},646:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var s=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s.Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},4850:function(e,t,r){"use strict";var s,n;e.exports=(null==(s=r.g.process)?void 0:s.env)&&"object"==typeof(null==(n=r.g.process)?void 0:n.env)?r.g.process:r(3079)},3198:function(e,t,r){Promise.resolve().then(r.bind(r,6138))},6138:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return N},dynamic:function(){return y}});var s=r(7573),n=r(7653),a=r(5543),i=r(1467),l=r(8473),c=r(8695),o=r(4480),d=r(8436),u=r(273),m=r(8907),h=r(646),f=r(2151),x=r(9576),p=r(3682),g=r(6434),v=r(4213),j=r(3588);let y="force-dynamic";function N(){let[e,t]=(0,n.useState)([]),[r,y]=(0,n.useState)([]),[N,w]=(0,n.useState)(!0),[b,k]=(0,n.useState)(""),[C,T]=(0,n.useState)([]),[A,L]=(0,n.useState)("ALL"),E=(0,n.useCallback)(async()=>{try{let e=new URLSearchParams;e.set("visibility","PUBLIC"),b&&e.set("search",b),C.length>0&&e.set("tags",C.join(",")),"ALL"!==A&&e.set("ideType",A);let r=await fetch("/api/rules?".concat(e)),s=await r.json();t(Array.isArray(s)?s:[])}catch(e){console.error("Error fetching rules:",e),j.Am.error("Failed to fetch templates"),t([])}finally{w(!1)}},[b,C,A]),Z=async()=>{try{let e=await fetch("/api/tags"),t=await e.json();y(Array.isArray(t)?t:[])}catch(e){console.error("Error fetching tags:",e),y([])}};(0,n.useEffect)(()=>{E(),Z()},[E,b,C,A]);let I=e=>{T(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},O=Array.isArray(e)?e.slice(0,6):[],z=Array.isArray(e)?e:[],D=[{name:"Code Generation",description:"Templates for generating code snippets and functions",icon:u.Z,count:z.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("generation"))).length},{name:"Code Review",description:"Templates for automated code review and analysis",icon:m.Z,count:z.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("review"))).length},{name:"Optimization",description:"Templates for code optimization and performance",icon:h.Z,count:z.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("optimization"))).length},{name:"Documentation",description:"Templates for generating documentation",icon:f.Z,count:z.filter(e=>e.tags.some(e=>e.tag.name.toLowerCase().includes("documentation"))).length}];return(0,s.jsxs)("div",{className:"container py-8 space-y-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,s.jsx)(m.Z,{className:"h-8 w-8 text-primary"}),(0,s.jsx)("h1",{className:"text-4xl font-bold",children:"Template Library"})]}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-2xl mx-auto",children:"Discover and use community-created AI prompt rules to boost your coding productivity"})]}),(0,s.jsxs)("section",{className:"space-y-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold",children:"Browse by Category"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:D.map(e=>(0,s.jsxs)(a.Z,{className:"hover:shadow-lg transition-shadow cursor-pointer",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,s.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-lg font-semibold",children:e.name}),(0,s.jsxs)(i.C,{variant:"soft",children:[e.count," templates"]})]})]})}),(0,s.jsx)("div",{children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})})]},e.name))})]}),(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(x.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(l.f,{placeholder:"Search templates...",value:b,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,s.jsxs)(c.fC,{value:A,onValueChange:L,children:[(0,s.jsx)(c.xz,{className:"w-full md:w-48",placeholder:"IDE Type"}),(0,s.jsxs)(c.VY,{children:[(0,s.jsx)(c.ck,{value:"ALL",children:"All IDEs"}),(0,s.jsx)(c.ck,{value:"GENERAL",children:"General"}),(0,s.jsx)(c.ck,{value:"CURSOR",children:"Cursor"}),(0,s.jsx)(c.ck,{value:"AUGMENT",children:"Augment Code"}),(0,s.jsx)(c.ck,{value:"WINDSURF",children:"Windsurf"}),(0,s.jsx)(c.ck,{value:"CLAUDE",children:"Claude"}),(0,s.jsx)(c.ck,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,s.jsx)(c.ck,{value:"GEMINI",children:"Gemini"}),(0,s.jsx)(c.ck,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,s.jsx)(c.ck,{value:"CLINE",children:"Cline"}),(0,s.jsx)(c.ck,{value:"JUNIE",children:"Junie"}),(0,s.jsx)(c.ck,{value:"TRAE",children:"Trae"}),(0,s.jsx)(c.ck,{value:"LINGMA",children:"Lingma"}),(0,s.jsx)(c.ck,{value:"KIRO",children:"Kiro"}),(0,s.jsx)(c.ck,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),r.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Filter by tags:"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:r.map(e=>(0,s.jsx)(i.C,{variant:C.includes(e.name)?"solid":"outline",className:"cursor-pointer",onClick:()=>I(e.name),style:{borderColor:e.color,backgroundColor:C.includes(e.name)?e.color:"transparent"},children:e.name},e.id))})]}),(0,s.jsxs)(o.fC,{defaultValue:"featured",className:"space-y-6",children:[(0,s.jsxs)(o.aV,{children:[(0,s.jsx)(o.xz,{value:"featured",children:"Featured"}),(0,s.jsxs)(o.xz,{value:"all",children:["All Templates (",z.length,")"]})]}),(0,s.jsx)(o.VY,{value:"featured",className:"space-y-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold",children:"Featured Templates"}),0===O.length?(0,s.jsxs)(a.Z,{className:"py-12 text-center",children:[(0,s.jsx)(g.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No featured templates yet"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Check back later for curated templates from the community"})]}):(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:O.map(e=>(0,s.jsx)(v.q,{rule:e},e.id))})]})}),(0,s.jsx)(o.VY,{value:"all",className:"space-y-6",children:N?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-muted-foreground",children:"Loading templates..."})]}):0===z.length?(0,s.jsxs)(a.Z,{className:"py-12 text-center",children:[(0,s.jsx)(m.Z,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No templates found"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Try adjusting your search criteria or check back later"}),(0,s.jsx)(d.z,{variant:"outline",onClick:()=>{k(""),T([]),L("ALL")},children:"Clear Filters"})]}):(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:z.map(e=>(0,s.jsx)(v.q,{rule:e},e.id))})})]})]})}},3079:function(e){!function(){var t={229:function(e){var t,r,s,n=e.exports={};function a(){throw Error("setTimeout has not been defined")}function i(){throw Error("clearTimeout has not been defined")}function l(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(e){r=i}}();var c=[],o=!1,d=-1;function u(){o&&s&&(o=!1,s.length?c=s.concat(c):d=-1,c.length&&m())}function m(){if(!o){var e=l(u);o=!0;for(var t=c.length;t;){for(s=c,c=[];++d<t;)s&&s[d].run();d=-1,t=c.length}s=null,o=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function f(){}n.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new h(e,t)),1!==c.length||o||l(m)},h.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=f,n.addListener=f,n.once=f,n.off=f,n.removeListener=f,n.removeAllListeners=f,n.emit=f,n.prependListener=f,n.prependOnceListener=f,n.listeners=function(e){return[]},n.binding=function(e){throw Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw Error("process.chdir is not supported")},n.umask=function(){return 0}}},r={};function s(e){var n=r[e];if(void 0!==n)return n.exports;var a=r[e]={exports:{}},i=!0;try{t[e](a,a.exports,s),i=!1}finally{i&&delete r[e]}return a.exports}s.ab="//";var n=s(229);e.exports=n}()},2056:function(e,t,r){e.exports=r(9419)}},function(e){e.O(0,[906,612,834,419,67,11,396,213,293,456,744],function(){return e(e.s=3198)}),_N_E=e.O()}]);