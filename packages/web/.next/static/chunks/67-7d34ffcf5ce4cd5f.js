"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[67],{7146:function(e,t,r){r.d(t,{Ry:function(){return c}});var n=new WeakMap,o=new WeakMap,l={},a=0,i=function(e){return e&&(e.host||i(e.parentNode))},s=function(e,t,r,s){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=i(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[r]||(l[r]=new WeakMap);var u=l[r],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(s),l=null!==t&&"false"!==t,a=(n.get(e)||0)+1,i=(u.get(e)||0)+1;n.set(e,a),u.set(e,i),d.push(e),1===a&&l&&o.set(e,!0),1===i&&e.setAttribute(r,"true"),l||e.setAttribute(s,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=n.get(e)-1,l=u.get(e)-1;n.set(e,t),u.set(e,l),t||(o.has(e)||e.removeAttribute(s),o.delete(e)),l||e.removeAttribute(r)}),--a||(n=new WeakMap,n=new WeakMap,o=new WeakMap,l={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),s(n,o,r,"aria-hidden")):function(){return null}}},4397:function(e,t,r){r.d(t,{Z:function(){return Z}});var n,o,l,a,i,s,c=function(){return(c=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function u(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}"function"==typeof SuppressedError&&SuppressedError;var d=r(7653),f="right-scroll-bar-position",p="width-before-scroll-bar";function h(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var m="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,v=new WeakMap,g=(void 0===n&&(n={}),(void 0===o&&(o=function(e){return e}),l=[],a=!1,i={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=o(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var r=l;l=[],r.forEach(e),t=l}var n=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(n)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=c({async:!0,ssr:!1},n),i),y=function(){},w=d.forwardRef(function(e,t){var r,n,o,l,a=d.useRef(null),i=d.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),s=i[0],f=i[1],p=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,S=e.sideCar,R=e.noRelative,T=e.noIsolation,N=e.inert,L=e.allowPinchZoom,P=e.as,D=void 0===P?"div":P,k=e.gapMode,A=u(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(r=[a,t],n=function(e){return r.forEach(function(t){return h(t,e)})},(o=(0,d.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,l=o.facade,m(function(){var e=v.get(l);if(e){var t=new Set(e),n=new Set(r),o=l.current;t.forEach(function(e){n.has(e)||h(e,null)}),n.forEach(function(e){t.has(e)||h(e,o)})}v.set(l,r)},[r]),l),j=c(c({},A),s);return d.createElement(d.Fragment,null,E&&d.createElement(S,{sideCar:g,removeScrollBar:x,shards:C,noRelative:R,noIsolation:T,inert:N,setCallbacks:f,allowPinchZoom:!!L,lockRef:a,gapMode:k}),p?d.cloneElement(d.Children.only(w),c(c({},j),{ref:M})):d.createElement(D,c({},j,{className:b,ref:M}),w))});w.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},w.classNames={fullWidth:p,zeroRight:f};var b=function(e){var t=e.sideCar,r=u(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return d.createElement(n,c({},r))};b.isSideCarExport=!0;var x=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},E=function(){var e=x();return function(t,r){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},C=function(){var e=E();return function(t){return e(t.styles,t.dynamic),null}},S={left:0,top:0,right:0,gap:0},R=function(e){return parseInt(e||"",10)||0},T=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[R(r),R(n),R(o)]},N=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return S;var t=T(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},L=C(),P="data-scroll-locked",D=function(e,t,r,n){var o=e.left,l=e.top,a=e.right,i=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(i,"px ").concat(n,";\n  }\n  body[").concat(P,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(l,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(i,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(f," {\n    right: ").concat(i,"px ").concat(n,";\n  }\n  \n  .").concat(p," {\n    margin-right: ").concat(i,"px ").concat(n,";\n  }\n  \n  .").concat(f," .").concat(f," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(P,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(P)||"0",10);return isFinite(e)?e:0},A=function(){d.useEffect(function(){return document.body.setAttribute(P,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(P):document.body.setAttribute(P,e.toString())}},[])},M=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;A();var l=d.useMemo(function(){return N(o)},[o]);return d.createElement(L,{styles:D(l,!t,o,r?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var W=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",W,W),window.removeEventListener("test",W,W)}catch(e){j=!1}var I=!!j&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},B=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),_(e,n)){var o=z(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},_=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},z=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,r,n,o){var l,a=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),i=a*n,s=r.target,c=t.contains(s),u=!1,d=i>0,f=0,p=0;do{if(!s)break;var h=z(e,s),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&_(e,s)&&(f+=v,p+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&i>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-i>p)&&(u=!0),u},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},V=function(e){return[e.deltaX,e.deltaY]},Y=function(e){return e&&"current"in e?e.current:e},X=0,K=[],U=(g.useMedium(function(e){var t=d.useRef([]),r=d.useRef([0,0]),n=d.useRef(),o=d.useState(X++)[0],l=d.useState(C)[0],a=d.useRef(e);d.useEffect(function(){a.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,l=t.length;o<l;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(Y),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=d.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,l=H(e),i=r.current,s="deltaX"in e?e.deltaX:i[0]-l[0],c="deltaY"in e?e.deltaY:i[1]-l[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=B(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=B(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return F(p,t,e,"h"===p?s:c,!0)},[]),s=d.useCallback(function(e){if(K.length&&K[K.length-1]===l){var r="deltaY"in e?V(e):H(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(Y).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?i(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=d.useCallback(function(e,r,n,o){var l={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),u=d.useCallback(function(e){r.current=H(e),n.current=void 0},[]),f=d.useCallback(function(t){c(t.type,V(t),t.target,i(t,e.lockRef.current))},[]),p=d.useCallback(function(t){c(t.type,H(t),t.target,i(t,e.lockRef.current))},[]);d.useEffect(function(){return K.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,I),document.addEventListener("touchmove",s,I),document.addEventListener("touchstart",u,I),function(){K=K.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,I),document.removeEventListener("touchmove",s,I),document.removeEventListener("touchstart",u,I)}},[]);var h=e.removeScrollBar,m=e.inert;return d.createElement(d.Fragment,null,m?d.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?d.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),b),$=d.forwardRef(function(e,t){return d.createElement(w,c({},e,{ref:t,sideCar:U}))});$.classNames=w.classNames;var Z=$},3202:function(e,t,r){r.d(t,{u:function(){return n}});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},8646:function(e,t,r){r.d(t,{XB:function(){return f}});var n,o=r(7653),l=r(1082),a=r(8671),i=r(8556),s=r(6418),c=r(7573),u="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(d),[x,E]=o.useState(null),C=x?.ownerDocument??globalThis?.document,[,S]=o.useState({}),R=(0,i.e)(t,e=>E(e)),T=Array.from(b.layers),[N]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),L=T.indexOf(N),P=x?T.indexOf(x):-1,D=b.layersWithOutsidePointerEventsDisabled.size>0,k=P>=L,A=function(e,t=globalThis?.document){let r=(0,s.W)(e),n=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",l.current),l.current=n,t.addEventListener("click",l.current,{once:!0})):n()}else t.removeEventListener("click",l.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",l.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...b.branches].some(e=>e.contains(t));!k||r||(m?.(e),g?.(e),e.defaultPrevented||y?.())},C),M=function(e,t=globalThis?.document){let r=(0,s.W)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target,r=[...b.branches].some(e=>e.contains(t));r||(v?.(e),g?.(e),e.defaultPrevented||y?.())},C);return!function(e,t=globalThis?.document){let r=(0,s.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{let t=P===b.layers.size-1;t&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},C),o.useEffect(()=>{if(x)return r&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{r&&1===b.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[x,C,r,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(u,e),()=>document.removeEventListener(u,e)},[]),(0,c.jsx)(a.WV.div,{...w,ref:R,style:{pointerEvents:D?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.M)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,l.M)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,l.M)(e.onPointerDownCapture,A.onPointerDownCapture)})});function p(){let e=new CustomEvent(u);document.dispatchEvent(e)}function h(e,t,r,{discrete:n}){let o=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,a.jH)(o,l):o.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),l=(0,i.e)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(a.WV.div,{...e,ref:l})}).displayName="DismissableLayerBranch"},6010:function(e,t,r){r.d(t,{EW:function(){return l}});var n=r(7653),o=0;function l(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},9555:function(e,t,r){let n;r.d(t,{M:function(){return f}});var o=r(7653),l=r(8556),a=r(8671),i=r(6418),s=r(7573),c="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:f,onUnmountAutoFocus:g,...y}=e,[w,b]=o.useState(null),x=(0,i.W)(f),E=(0,i.W)(g),C=o.useRef(null),S=(0,l.e)(t,e=>b(e)),R=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:m(C.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){let t=document.activeElement;if(t===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&r.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,w,R.paused]),o.useEffect(()=>{if(w){v.add(R);let e=document.activeElement,t=w.contains(e);if(!t){let t=new CustomEvent(c,d);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(m(n,{select:t}),document.activeElement!==r)return}(p(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(u,d);w.addEventListener(u,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(u,E),v.remove(R)},0)}}},[w,x,E,R]);let T=o.useCallback(e=>{if(!r&&!n||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,l]=function(e){let t=p(e),r=h(t,e),n=h(t.reverse(),e);return[r,n]}(t),a=n&&l;a?e.shiftKey||o!==l?e.shiftKey&&o===n&&(e.preventDefault(),r&&m(l,{select:!0})):(e.preventDefault(),r&&m(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,R.paused]);return(0,s.jsx)(a.WV.div,{tabIndex:-1,...y,ref:S,onKeyDown:T})});function p(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function h(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function m(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}f.displayName="FocusScope";var v=(n=[],{add(e){let t=n[0];e!==t&&t?.pause(),(n=g(n,e)).unshift(e)},remove(e){n=g(n,e),n[0]?.resume()}});function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},3184:function(e,t,r){r.d(t,{ee:function(){return e9},Eh:function(){return tt},VY:function(){return te},fC:function(){return e6},D7:function(){return eK}});var n=r(7653);let o=["top","right","bottom","left"],l=Math.min,a=Math.max,i=Math.round,s=Math.floor,c=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let b=["left","right"],x=["right","left"],E=["top","bottom"],C=["bottom","top"];function S(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function R(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function T(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function N(e,t,r){let n,{reference:o,floating:l}=e,a=y(t),i=m(y(t)),s=v(i),c=p(t),u="y"===a,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,g=o[s]/2-l[s]/2;switch(c){case"top":n={x:d,y:o.y-l.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-l.width,y:f};break;default:n={x:o.x,y:o.y}}switch(h(t)){case"start":n[i]-=g*(r&&u?-1:1);break;case"end":n[i]+=g*(r&&u?-1:1)}return n}let L=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:l=[],platform:a}=r,i=l.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=N(c,n,s),f=n,p={},h=0;for(let r=0;r<i.length;r++){let{name:l,fn:m}=i[r],{x:v,y:g,data:y,reset:w}=await m({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:u,y:d}=N(c,f,s)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function P(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:l,rects:a,elements:i,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=R(h),v=i[p?"floating"===d?"reference":"floating":d],g=T(await l.getClippingRect({element:null==(r=await (null==l.isElement?void 0:l.isElement(v)))||r?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(i.floating)),boundary:c,rootBoundary:u,strategy:s})),y="floating"===d?{x:n,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(i.floating)),b=await (null==l.isElement?void 0:l.isElement(w))&&await (null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},x=T(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:y,offsetParent:w,strategy:s}):y);return{top:(g.top-x.top+m.top)/b.y,bottom:(x.bottom-g.bottom+m.bottom)/b.y,left:(g.left-x.left+m.left)/b.x,right:(x.right-g.right+m.right)/b.x}}function D(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return o.some(t=>e[t]>=0)}let A=new Set(["left","top"]);async function M(e,t){let{placement:r,platform:n,elements:o}=e,l=await (null==n.isRTL?void 0:n.isRTL(o.floating)),a=p(r),i=h(r),s="y"===y(r),c=A.has(a)?-1:1,u=l&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return i&&"number"==typeof g&&(v="end"===i?-1*g:g),s?{x:v*u,y:m*c}:{x:m*c,y:v*u}}function j(){return"undefined"!=typeof window}function W(e){return B(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(B(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function B(e){return!!j()&&(e instanceof Node||e instanceof I(e).Node)}function _(e){return!!j()&&(e instanceof Element||e instanceof I(e).Element)}function z(e){return!!j()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function F(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let H=new Set(["inline","contents"]);function V(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!H.has(o)}let Y=new Set(["table","td","th"]),X=[":popover-open",":modal"];function K(e){return X.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let U=["transform","translate","scale","rotate","perspective"],$=["transform","translate","scale","rotate","perspective","filter"],Z=["paint","layout","strict","content"];function G(e){let t=q(),r=_(e)?ee(e):e;return U.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||$.some(e=>(r.willChange||"").includes(e))||Z.some(e=>(r.contain||"").includes(e))}function q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(W(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return _(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===W(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||O(e);return F(t)?t.host:t}function en(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=er(t);return J(r)?t.ownerDocument?t.ownerDocument.body:t.body:z(r)&&V(r)?r:e(r)}(e),l=o===(null==(n=e.ownerDocument)?void 0:n.body),a=I(o);if(l){let e=eo(a);return t.concat(a,a.visualViewport||[],V(o)?o:[],e&&r?en(e):[])}return t.concat(o,en(o,[],r))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function el(e){let t=ee(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=z(e),l=o?e.offsetWidth:r,a=o?e.offsetHeight:n,s=i(r)!==l||i(n)!==a;return s&&(r=l,n=a),{width:r,height:n,$:s}}function ea(e){return _(e)?e:e.contextElement}function ei(e){let t=ea(e);if(!z(t))return c(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:l}=el(t),a=(l?i(r.width):r.width)/n,s=(l?i(r.height):r.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let es=c(0);function ec(e){let t=I(e);return q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function eu(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let l=e.getBoundingClientRect(),a=ea(e),i=c(1);t&&(n?_(n)&&(i=ei(n)):i=ei(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===I(a))&&o)?ec(a):c(0),u=(l.left+s.x)/i.x,d=(l.top+s.y)/i.y,f=l.width/i.x,p=l.height/i.y;if(a){let e=I(a),t=n&&_(n)?I(n):n,r=e,o=eo(r);for(;o&&n&&t!==r;){let e=ei(o),t=o.getBoundingClientRect(),n=ee(o),l=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;u*=e.x,d*=e.y,f*=e.x,p*=e.y,u+=l,d+=a,o=eo(r=I(o))}}return T({width:f,height:p,x:u,y:d})}function ed(e,t){let r=et(e).scrollLeft;return t?t.left+r:eu(O(e)).left+r}function ef(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect(),o=n.left+t.scrollLeft-(r?0:ed(e,n)),l=n.top+t.scrollTop;return{x:o,y:l}}let ep=new Set(["absolute","fixed"]);function eh(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=I(e),n=O(e),o=r.visualViewport,l=n.clientWidth,a=n.clientHeight,i=0,s=0;if(o){l=o.width,a=o.height;let e=q();(!e||e&&"fixed"===t)&&(i=o.offsetLeft,s=o.offsetTop)}return{width:l,height:a,x:i,y:s}}(e,r);else if("document"===t)n=function(e){let t=O(e),r=et(e),n=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),l=a(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+ed(e),s=-r.scrollTop;return"rtl"===ee(n).direction&&(i+=a(t.clientWidth,n.clientWidth)-o),{width:o,height:l,x:i,y:s}}(O(e));else if(_(t))n=function(e,t){let r=eu(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,l=z(e)?ei(e):c(1),a=e.clientWidth*l.x,i=e.clientHeight*l.y,s=o*l.x,u=n*l.y;return{width:a,height:i,x:s,y:u}}(t,r);else{let r=ec(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return T(n)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!z(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let r=e.offsetParent;return O(e)===r&&(r=r.ownerDocument.body),r}function eg(e,t){var r;let n=I(e);if(K(e))return n;if(!z(e)){let t=er(e);for(;t&&!J(t);){if(_(t)&&!em(t))return t;t=er(t)}return n}let o=ev(e,t);for(;o&&(r=o,Y.has(W(r)))&&em(o);)o=ev(o,t);return o&&J(o)&&em(o)&&!G(o)?n:o||function(e){let t=er(e);for(;z(t)&&!J(t);){if(G(t))return t;if(K(t))break;t=er(t)}return null}(e)||n}let ey=async function(e){let t=this.getOffsetParent||eg,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=z(t),o=O(t),l="fixed"===r,a=eu(e,!0,l,t),i={scrollLeft:0,scrollTop:0},s=c(0);if(n||!n&&!l){if(("body"!==W(t)||V(o))&&(i=et(t)),n){let e=eu(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ed(o))}l&&!n&&o&&(s.x=ed(o));let u=!o||n||l?c(0):ef(o,i),d=a.left+i.scrollLeft-s.x-u.x,f=a.top+i.scrollTop-s.y-u.y;return{x:d,y:f,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,l="fixed"===o,a=O(n),i=!!t&&K(t.floating);if(n===a||i&&l)return r;let s={scrollLeft:0,scrollTop:0},u=c(1),d=c(0),f=z(n);if((f||!f&&!l)&&(("body"!==W(n)||V(a))&&(s=et(n)),z(n))){let e=eu(n);u=ei(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!a||f||l?c(0):ef(a,s,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-s.scrollLeft*u.x+d.x+p.x,y:r.y*u.y-s.scrollTop*u.y+d.y+p.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i="clippingAncestors"===r?K(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=en(e,[],!1).filter(e=>_(e)&&"body"!==W(e)),o=null,l="fixed"===ee(e).position,a=l?er(e):e;for(;_(a)&&!J(a);){let t=ee(a),r=G(a);r||"fixed"!==t.position||(o=null);let i=l?!r&&!o:!r&&"static"===t.position&&!!o&&ep.has(o.position)||V(a)&&!r&&function e(t,r){let n=er(t);return!(n===r||!_(n)||J(n))&&("fixed"===ee(n).position||e(n,r))}(e,a);i?n=n.filter(e=>e!==a):o=t,a=er(a)}return t.set(e,n),n}(t,this._c):[].concat(r),s=[...i,n],c=s[0],u=s.reduce((e,r)=>{let n=eh(t,r,o);return e.top=a(n.top,e.top),e.right=l(n.right,e.right),e.bottom=l(n.bottom,e.bottom),e.left=a(n.left,e.left),e},eh(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=el(e);return{width:t,height:r}},getScale:ei,isElement:_,isRTL:function(e){return"rtl"===ee(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:s,elements:c,middlewareData:u}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=R(p),w={x:r,y:n},b=m(y(o)),x=v(b),E=await s.getDimensions(d),C="y"===b,S=C?"clientHeight":"clientWidth",T=i.reference[x]+i.reference[b]-w[b]-i.floating[x],N=w[b]-i.reference[b],L=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),P=L?L[S]:0;P&&await (null==s.isElement?void 0:s.isElement(L))||(P=c.floating[S]||i.floating[x]);let D=P/2-E[x]/2-1,k=l(g[C?"top":"left"],D),A=l(g[C?"bottom":"right"],D),M=P-E[x]-A,j=P/2-E[x]/2+(T/2-N/2),W=a(k,l(j,M)),I=!u.arrow&&null!=h(o)&&j!==W&&i.reference[x]/2-(j<k?k:A)-E[x]/2<0,O=I?j<k?j-k:j-M:0;return{[b]:w[b]+O,data:{[b]:W,centerOffset:j-W-O,...I&&{alignmentOffset:O}},reset:I}}}),eE=(e,t,r)=>{let n=new Map,o={platform:ew,...r},l={...o.platform,_c:n};return L(e,t,{...o,platform:l})};var eC=r(1036),eS="undefined"!=typeof document?n.useLayoutEffect:function(){};function eR(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eR(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eR(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eT(e){if("undefined"==typeof window)return 1;let t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function eN(e,t){let r=eT(e);return Math.round(t*r)/r}function eL(e){let t=n.useRef(e);return eS(()=>{t.current=e}),t}let eP=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ex({element:r.current,padding:n}).fn(t):{}:r?ex({element:r,padding:n}).fn(t):{}}}),eD=(e,t)=>{var r;return{...(void 0===(r=e)&&(r=0),{name:"offset",options:r,async fn(e){var t,n;let{x:o,y:l,placement:a,middlewareData:i}=e,s=await M(e,r);return a===(null==(t=i.offset)?void 0:t.placement)&&null!=(n=i.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:a}}}}),options:[e,t]}},ek=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"shift",options:r,async fn(e){let{x:t,y:n,placement:o}=e,{mainAxis:i=!0,crossAxis:s=!1,limiter:c={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...u}=f(r,e),d={x:t,y:n},h=await P(e,u),v=y(p(o)),g=m(v),w=d[g],b=d[v];if(i){let e=w+h["y"===g?"top":"left"],t=w-h["y"===g?"bottom":"right"];w=a(e,l(w,t))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=b+h[e],n=b-h[t];b=a(r,l(b,n))}let x=c.fn({...e,[g]:w,[v]:b});return{...x,data:{x:x.x-t,y:x.y-n,enabled:{[g]:i,[v]:s}}}}}),options:[e,t]}},eA=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{options:r,fn(e){let{x:t,y:n,placement:o,rects:l,middlewareData:a}=e,{offset:i=0,mainAxis:s=!0,crossAxis:c=!0}=f(r,e),u={x:t,y:n},d=y(o),h=m(d),v=u[h],g=u[d],w=f(i,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+b.mainAxis,r=l.reference[h]+l.reference[e]-b.mainAxis;v<t?v=t:v>r&&(v=r)}if(c){var x,E;let e="y"===h?"width":"height",t=A.has(p(o)),r=l.reference[d]-l.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),n=l.reference[d]+l.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);g<r?g=r:g>n&&(g=n)}return{[h]:v,[d]:g}}}),options:[e,t]}},eM=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"flip",options:r,async fn(e){var t,n,o,l,a;let{placement:i,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:g}=e,{mainAxis:R=!0,crossAxis:T=!0,fallbackPlacements:N,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:D="none",flipAlignment:k=!0,...A}=f(r,e);if(null!=(t=s.arrow)&&t.alignmentOffset)return{};let M=p(i),j=y(u),W=p(u)===u,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),O=N||(W||!k?[S(u)]:function(e){let t=S(e);return[w(e),t,w(t)]}(u)),B="none"!==D;!N&&B&&O.push(...function(e,t,r,n){let o=h(e),l=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?x:b;return t?b:x;case"left":case"right":return t?E:C;default:return[]}}(p(e),"start"===r,n);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(w)))),l}(u,k,D,I));let _=[u,...O],z=await P(e,A),F=[],H=(null==(n=s.flip)?void 0:n.overflows)||[];if(R&&F.push(z[M]),T){let e=function(e,t,r){void 0===r&&(r=!1);let n=h(e),o=m(y(e)),l=v(o),a="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[l]>t.floating[l]&&(a=S(a)),[a,S(a)]}(i,c,I);F.push(z[e[0]],z[e[1]])}if(H=[...H,{placement:i,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=_[e];if(t){let r="alignment"===T&&j!==y(t);if(!r||H.every(e=>y(e.placement)!==j||e.overflows[0]>0))return{data:{index:e,overflows:H},reset:{placement:t}}}let r=null==(l=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!r)switch(L){case"bestFit":{let e=null==(a=H.filter(e=>{if(B){let t=y(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=u}if(i!==r)return{reset:{placement:r}}}return{}}}),options:[e,t]}},ej=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"size",options:r,async fn(e){var t,n;let o,i;let{placement:s,rects:c,platform:u,elements:d}=e,{apply:m=()=>{},...v}=f(r,e),g=await P(e,v),w=p(s),b=h(s),x="y"===y(s),{width:E,height:C}=c.floating;"top"===w||"bottom"===w?(o=w,i=b===(await (null==u.isRTL?void 0:u.isRTL(d.floating))?"start":"end")?"left":"right"):(i=w,o="end"===b?"top":"bottom");let S=C-g.top-g.bottom,R=E-g.left-g.right,T=l(C-g[o],S),N=l(E-g[i],R),L=!e.middlewareData.shift,D=T,k=N;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(k=R),null!=(n=e.middlewareData.shift)&&n.enabled.y&&(D=S),L&&!b){let e=a(g.left,0),t=a(g.right,0),r=a(g.top,0),n=a(g.bottom,0);x?k=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):D=C-2*(0!==r||0!==n?r+n:a(g.top,g.bottom))}await m({...e,availableWidth:k,availableHeight:D});let A=await u.getDimensions(d.floating);return E!==A.width||C!==A.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eW=(e,t)=>{var r;return{...(void 0===(r=e)&&(r={}),{name:"hide",options:r,async fn(e){let{rects:t}=e,{strategy:n="referenceHidden",...o}=f(r,e);switch(n){case"referenceHidden":{let r=await P(e,{...o,elementContext:"reference"}),n=D(r,t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:k(n)}}}case"escaped":{let r=await P(e,{...o,altBoundary:!0}),n=D(r,t.floating);return{data:{escapedOffsets:n,escaped:k(n)}}}default:return{}}}}),options:[e,t]}},eI=(e,t)=>({...eP(e),options:[e,t]});var eO=r(8671),eB=r(7573),e_=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...l}=e;return(0,eB.jsx)(eO.WV.svg,{...l,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eB.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e_.displayName="Arrow";var ez=r(8556),eF=r(4036),eH=r(6418),eV=r(1469),eY="Popper",[eX,eK]=(0,eF.b)(eY),[eU,e$]=eX(eY),eZ=e=>{let{__scopePopper:t,children:r}=e,[o,l]=n.useState(null);return(0,eB.jsx)(eU,{scope:t,anchor:o,onAnchorChange:l,children:r})};eZ.displayName=eY;var eG="PopperAnchor",eq=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...l}=e,a=e$(eG,r),i=n.useRef(null),s=(0,ez.e)(t,i);return n.useEffect(()=>{a.onAnchorChange(o?.current||i.current)}),o?null:(0,eB.jsx)(eO.WV.div,{...l,ref:s})});eq.displayName=eG;var eQ="PopperContent",[eJ,e0]=eX(eQ),e1=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:i=0,align:c="center",alignOffset:u=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=e$(eQ,r),[x,E]=n.useState(null),C=(0,ez.e)(t,e=>E(e)),[S,R]=n.useState(null),T=function(e){let[t,r]=n.useState(void 0);return(0,eV.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(S),N=T?.width??0,L=T?.height??0,P="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},D=Array.isArray(p)?p:[p],k=D.length>0,A={padding:P,boundary:D.filter(e4),altBoundary:k},{refs:M,floatingStyles:j,placement:W,isPositioned:I,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:l,elements:{reference:a,floating:i}={},transform:s=!0,whileElementsMounted:c,open:u}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(o);eR(p,o)||h(o);let[m,v]=n.useState(null),[g,y]=n.useState(null),w=n.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=n.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=a||m,E=i||g,C=n.useRef(null),S=n.useRef(null),R=n.useRef(d),T=null!=c,N=eL(c),L=eL(l),P=eL(u),D=n.useCallback(()=>{if(!C.current||!S.current)return;let e={placement:t,strategy:r,middleware:p};L.current&&(e.platform=L.current),eE(C.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};k.current&&!eR(R.current,t)&&(R.current=t,eC.flushSync(()=>{f(t)}))})},[p,t,r,L,P]);eS(()=>{!1===u&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[u]);let k=n.useRef(!1);eS(()=>(k.current=!0,()=>{k.current=!1}),[]),eS(()=>{if(x&&(C.current=x),E&&(S.current=E),x&&E){if(N.current)return N.current(x,E,D);D()}},[x,E,D,N,T]);let A=n.useMemo(()=>({reference:C,floating:S,setReference:w,setFloating:b}),[w,b]),M=n.useMemo(()=>({reference:x,floating:E}),[x,E]),j=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!M.floating)return e;let t=eN(M.floating,d.x),n=eN(M.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...eT(M.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,M.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:D,refs:A,elements:M,floatingStyles:j}),[d,D,A,M,j])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>{let t=function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:c=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=ea(e),h=i||c?[...p?en(p):[],...en(t)]:[];h.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),c&&e.addEventListener("resize",r)});let m=p&&d?function(e,t){let r,n=null,o=O(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function c(u,d){void 0===u&&(u=!1),void 0===d&&(d=1),i();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(u||t(),!m||!v)return;let g=s(h),y=s(o.clientWidth-(p+m)),w=s(o.clientHeight-(h+v)),b=s(p),x={rootMargin:-g+"px "+-y+"px "+-w+"px "+-b+"px",threshold:a(0,l(1,d))||1},E=!0;function C(t){let n=t[0].intersectionRatio;if(n!==d){if(!E)return c();n?c(!1,n):r=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==n||eb(f,e.getBoundingClientRect())||c(),E=!1}try{n=new IntersectionObserver(C,{...x,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(C,x)}n.observe(e)}(!0),i}(p,r):null,v=-1,g=null;u&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!f&&g.observe(p),g.observe(t));let y=f?eu(e):null;return f&&function t(){let n=eu(e);y&&!eb(y,n)&&r(),y=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{i&&e.removeEventListener("scroll",r),c&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...e,{animationFrame:"always"===g});return t},elements:{reference:b.anchor},middleware:[eD({mainAxis:i+L,alignmentAxis:u}),f&&ek({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eA():void 0,...A}),f&&eM({...A}),ej({...A,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:l}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${l}px`)}}),S&&eI({element:S,padding:d}),e7({arrowWidth:N,arrowHeight:L}),v&&eW({strategy:"referenceHidden",...A})]}),[_,z]=e8(W),F=(0,eH.W)(y);(0,eV.b)(()=>{I&&F?.()},[I,F]);let H=B.arrow?.x,V=B.arrow?.y,Y=B.arrow?.centerOffset!==0,[X,K]=n.useState();return(0,eV.b)(()=>{x&&K(window.getComputedStyle(x).zIndex)},[x]),(0,eB.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...j,transform:I?j.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eB.jsx)(eJ,{scope:r,placedSide:_,onArrowChange:R,arrowX:H,arrowY:V,shouldHideArrow:Y,children:(0,eB.jsx)(eO.WV.div,{"data-side":_,"data-align":z,...w,ref:C,style:{...w.style,animation:I?void 0:"none"}})})})});e1.displayName=eQ;var e5="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e2=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e0(e5,r),l=e3[o.placedSide];return(0,eB.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eB.jsx)(e_,{...n,ref:t,style:{...n.style,display:"block"}})})});function e4(e){return null!==e}e2.displayName=e5;var e7=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,l=o.arrow?.centerOffset!==0,a=l?0:e.arrowWidth,i=l?0:e.arrowHeight,[s,c]=e8(r),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+i/2,p="",h="";return"bottom"===s?(p=l?u:`${d}px`,h=`${-i}px`):"top"===s?(p=l?u:`${d}px`,h=`${n.floating.height+i}px`):"right"===s?(p=`${-i}px`,h=l?u:`${f}px`):"left"===s&&(p=`${n.floating.width+i}px`,h=l?u:`${f}px`),{data:{x:p,y:h}}}});function e8(e){let[t,r="center"]=e.split("-");return[t,r]}var e6=eZ,e9=eq,te=e1,tt=e2},2268:function(e,t,r){r.d(t,{h:function(){return s}});var n=r(7653),o=r(1036),l=r(8671),a=r(1469),i=r(7573),s=n.forwardRef((e,t)=>{let{container:r,...s}=e,[c,u]=n.useState(!1);(0,a.b)(()=>u(!0),[]);let d=r||c&&globalThis?.document?.body;return d?o.createPortal((0,i.jsx)(l.WV.div,{...s,ref:t}),d):null});s.displayName="Portal"},9148:function(e,t,r){r.d(t,{LW:function(){return Z},Ns:function(){return q},bU:function(){return G},fC:function(){return U},l_:function(){return $}});var n=r(7653),o=r(8671),l=r(7575),a=r(4036),i=r(8556),s=r(6418),c=r(7205),u=r(1469),d=r(3202),f=r(1082),p=r(7573),h="ScrollArea",[m,v]=(0,a.b)(h),[g,y]=m(h),w=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:a,scrollHideDelay:s=600,...u}=e,[d,f]=n.useState(null),[h,m]=n.useState(null),[v,y]=n.useState(null),[w,b]=n.useState(null),[x,E]=n.useState(null),[C,S]=n.useState(0),[R,T]=n.useState(0),[N,L]=n.useState(!1),[P,D]=n.useState(!1),k=(0,i.e)(t,e=>f(e)),A=(0,c.gm)(a);return(0,p.jsx)(g,{scope:r,type:l,dir:A,scrollHideDelay:s,scrollArea:d,viewport:h,onViewportChange:m,content:v,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:N,onScrollbarXEnabledChange:L,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:P,onScrollbarYEnabledChange:D,onCornerWidthChange:S,onCornerHeightChange:T,children:(0,p.jsx)(o.WV.div,{dir:A,...u,ref:k,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});w.displayName=h;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:a,...s}=e,c=y(b,r),u=n.useRef(null),d=(0,i.e)(t,u,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,p.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});x.displayName=b;var E="ScrollAreaScrollbar",C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?a(!0):i(!0),()=>{s?a(!1):i(!1)}),[s,a,i]),"hover"===l.type?(0,p.jsx)(S,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,p.jsx)(T,{...o,ref:t,forceMount:r}):"always"===l.type?(0,p.jsx)(N,{...o,ref:t}):null});C.displayName=E;var S=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=y(E,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),(0,p.jsx)(l.z,{present:r||i,children:(0,p.jsx)(T,{"data-state":i?"visible":"hidden",...o,ref:t})})}),R=n.forwardRef((e,t)=>{var r;let{forceMount:o,...a}=e,i=y(E,e.__scopeScrollArea),s="horizontal"===e.orientation,c=X(()=>d("SCROLL_END"),100),[u,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return n??e},"hidden"));return n.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,i.scrollHideDelay,d]),n.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t],o=r!==n;o&&(d("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,d,c]),(0,p.jsx)(l.z,{present:o||"hidden"!==u,children:(0,p.jsx)(N,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:(0,f.M)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.M)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),T=n.forwardRef((e,t)=>{let r=y(E,e.__scopeScrollArea),{forceMount:o,...a}=e,[i,s]=n.useState(!1),c="horizontal"===e.orientation,u=X(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(c?e:t)}},10);return K(r.viewport,u),K(r.content,u),(0,p.jsx)(l.z,{present:o||i,children:(0,p.jsx)(N,{"data-state":i?"visible":"hidden",...a,ref:t})})}),N=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=y(E,e.__scopeScrollArea),a=n.useRef(null),i=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=z(s.viewport,s.content),d={...o,sizes:s,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=F(r),l=t||o/2,a=r.scrollbar.paddingStart+l,i=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport,c=V([a,i],"ltr"===n?[0,s]:[-1*s,0]);return c(e)}(e,i.current,s,t)}return"horizontal"===r?(0,p.jsx)(L,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=l.viewport.scrollLeft,t=H(e,s,l.dir);a.current.style.transform=`translate3d(${t}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===r?(0,p.jsx)(P,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=l.viewport.scrollTop,t=H(e,s);a.current.style.transform=`translate3d(0, ${t}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,a=y(E,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,i.e)(t,u,a.onScrollbarXChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(A,{"data-orientation":"horizontal",...l,ref:d,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":F(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&a.viewport&&s&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:_(s.paddingLeft),paddingEnd:_(s.paddingRight)}})}})}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,a=y(E,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,i.e)(t,u,a.onScrollbarYChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(A,{"data-orientation":"vertical",...l,ref:d,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":F(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{u.current&&a.viewport&&s&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:_(s.paddingTop),paddingEnd:_(s.paddingBottom)}})}})}),[D,k]=m(E),A=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:a,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:m,onWheelScroll:v,onResize:g,...w}=e,b=y(E,r),[x,C]=n.useState(null),S=(0,i.e)(t,e=>C(e)),R=n.useRef(null),T=n.useRef(""),N=b.viewport,L=l.content-l.viewport,P=(0,s.W)(v),k=(0,s.W)(h),A=X(g,10);function M(e){if(R.current){let t=e.clientX-R.current.left,r=e.clientY-R.current.top;m({x:t,y:r})}}return n.useEffect(()=>{let e=e=>{let t=e.target,r=x?.contains(t);r&&P(e,L)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,x,L,P]),n.useEffect(k,[l,k]),K(x,A),K(b.content,A),(0,p.jsx)(D,{scope:r,scrollbar:x,hasThumb:a,onThumbChange:(0,s.W)(c),onThumbPointerUp:(0,s.W)(u),onThumbPositionChange:k,onThumbPointerDown:(0,s.W)(d),children:(0,p.jsx)(o.WV.div,{...w,ref:S,style:{position:"absolute",...w.style},onPointerDown:(0,f.M)(e.onPointerDown,e=>{if(0===e.button){let t=e.target;t.setPointerCapture(e.pointerId),R.current=x.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),M(e)}}),onPointerMove:(0,f.M)(e.onPointerMove,M),onPointerUp:(0,f.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),M="ScrollAreaThumb",j=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=k(M,e.__scopeScrollArea);return(0,p.jsx)(l.z,{present:r||o.hasThumb,children:(0,p.jsx)(W,{ref:t,...n})})}),W=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...a}=e,s=y(M,r),c=k(M,r),{onThumbPositionChange:u}=c,d=(0,i.e)(t,e=>c.onThumbChange(e)),h=n.useRef(void 0),m=X(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(m(),!h.current){let t=Y(e,u);h.current=t,u()}};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,u]),(0,p.jsx)(o.WV.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,f.M)(e.onPointerDownCapture,e=>{let t=e.target,r=t.getBoundingClientRect(),n=e.clientX-r.left,o=e.clientY-r.top;c.onThumbPointerDown({x:n,y:o})}),onPointerUp:(0,f.M)(e.onPointerUp,c.onThumbPointerUp)})});j.displayName=M;var I="ScrollAreaCorner",O=n.forwardRef((e,t)=>{let r=y(I,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY),o="scroll"!==r.type&&n;return o?(0,p.jsx)(B,{...e,ref:t}):null});O.displayName=I;var B=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,a=y(I,r),[i,s]=n.useState(0),[c,u]=n.useState(0);return K(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),u(e)}),K(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),s(e)}),i&&c?(0,p.jsx)(o.WV.div,{...l,ref:t,style:{width:i,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function _(e){return e?parseInt(e,10):0}function z(e,t){let r=e/t;return isNaN(r)?0:r}function F(e){let t=z(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-r)*t;return Math.max(n,18)}function H(e,t,r="ltr"){let n=F(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,a=t.content-t.viewport,i="ltr"===r?[0,a]:[-1*a,0],s=(0,d.u)(e,i),c=V([0,a],[0,l-n]);return c(s)}function V(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var Y=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},a=r.left!==l.left,i=r.top!==l.top;(a||i)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function X(e,t){let r=(0,s.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function K(e,t){let r=(0,s.W)(t);(0,u.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var U=w,$=x,Z=C,G=j,q=O},1874:function(e,t,r){r.d(t,{x8:function(){return ep},VY:function(){return eu},dk:function(){return ef},fC:function(){return es},Dx:function(){return ed},xz:function(){return ec}});var n=r(7653),o=r(8344),l=r(1082),a=r(8556),i=r(4036),s=r(6303),c=r(7840),u=r(8646),d=r(9555),f=r(2268),p=r(7575),h=r(8671),m=r(6010),v=r(4397),g=r(7146),y=r(432),w=r(7573),b="Dialog",[x,E]=(0,i.b)(b),[C,S]=x(b),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:l,onOpenChange:a,modal:i=!0}=e,u=n.useRef(null),d=n.useRef(null),[f,p]=(0,c.T)({prop:o,defaultProp:l??!1,onChange:a,caller:b});return(0,w.jsx)(C,{scope:t,triggerRef:u,contentRef:d,contentId:(0,s.M)(),titleId:(0,s.M)(),descriptionId:(0,s.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:i,children:r})};R.displayName=b;var T="DialogTrigger",N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(T,r),i=(0,a.e)(t,o.triggerRef);return(0,w.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":U(o.open),...n,ref:i,onClick:(0,l.M)(e.onClick,o.onOpenToggle)})});N.displayName=T;var L="DialogPortal",[P,D]=x(L,{forceMount:void 0}),k=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:l}=e,a=S(L,t);return(0,w.jsx)(P,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,w.jsx)(p.z,{present:r||a.open,children:(0,w.jsx)(f.h,{asChild:!0,container:l,children:e})}))})};k.displayName=L;var A="DialogOverlay",M=n.forwardRef((e,t)=>{let r=D(A,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=S(A,e.__scopeDialog);return l.modal?(0,w.jsx)(p.z,{present:n||l.open,children:(0,w.jsx)(W,{...o,ref:t})}):null});M.displayName=A;var j=(0,y.Z8)("DialogOverlay.RemoveScroll"),W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(A,r);return(0,w.jsx)(v.Z,{as:j,allowPinchZoom:!0,shards:[o.contentRef],children:(0,w.jsx)(h.WV.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",O=n.forwardRef((e,t)=>{let r=D(I,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=S(I,e.__scopeDialog);return(0,w.jsx)(p.z,{present:n||l.open,children:l.modal?(0,w.jsx)(B,{...o,ref:t}):(0,w.jsx)(_,{...o,ref:t})})});O.displayName=I;var B=n.forwardRef((e,t)=>{let r=S(I,e.__scopeDialog),o=n.useRef(null),i=(0,a.e)(t,r.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,g.Ry)(e)},[]),(0,w.jsx)(z,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,l.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;n&&e.preventDefault()}),onFocusOutside:(0,l.M)(e.onFocusOutside,e=>e.preventDefault())})}),_=n.forwardRef((e,t)=>{let r=S(I,e.__scopeDialog),o=n.useRef(!1),l=n.useRef(!1);return(0,w.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let n=t.target,a=r.triggerRef.current?.contains(n);a&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),z=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=S(I,r),f=n.useRef(null),p=(0,a.e)(t,f);return(0,m.EW)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(d.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,w.jsx)(u.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(q,{titleId:c.titleId}),(0,w.jsx)(Q,{contentRef:f,descriptionId:c.descriptionId})]})]})}),F="DialogTitle",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(F,r);return(0,w.jsx)(h.WV.h2,{id:o.titleId,...n,ref:t})});H.displayName=F;var V="DialogDescription",Y=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(V,r);return(0,w.jsx)(h.WV.p,{id:o.descriptionId,...n,ref:t})});Y.displayName=V;var X="DialogClose",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=S(X,r);return(0,w.jsx)(h.WV.button,{type:"button",...n,ref:t,onClick:(0,l.M)(e.onClick,()=>o.onOpenChange(!1))})});function U(e){return e?"open":"closed"}K.displayName=X;var $="DialogTitleWarning",[Z,G]=(0,i.k)($,{contentName:I,titleName:F,docsSlug:"dialog"}),q=({titleId:e})=>{let t=G($),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{if(e){let t=document.getElementById(e);t||console.error(r)}},[r,e]),null},Q=({contentRef:e,descriptionId:t})=>{let r=G("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");if(t&&r){let e=document.getElementById(t);e||console.warn(o)}},[o,e,t]),null},J=r(2741),ee=r(6306),et=r(3230);let er={...J.C,align:{type:"enum",className:"rt-r-align",values:["start","center"],default:"center"},size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"3",responsive:!0},width:ee.n.width,minWidth:ee.n.minWidth,maxWidth:{...ee.n.maxWidth,default:"600px"},...et.F};var en=r(4508),eo=r(2063),el=r(2123),ea=r(5236),ei=r(8373);let es=e=>n.createElement(R,{...e,modal:!0});es.displayName="Dialog.Root";let ec=n.forwardRef(({children:e,...t},r)=>n.createElement(N,{...t,ref:r,asChild:!0},(0,ei.O)(e)));ec.displayName="Dialog.Trigger";let eu=n.forwardRef(({align:e,...t},r)=>{let{align:l,...a}=er,{className:i}=(0,ea.y)({align:e},{align:l}),{className:s,forceMount:c,container:u,...d}=(0,ea.y)(t,a);return n.createElement(k,{container:u,forceMount:c},n.createElement(el.Q2,{asChild:!0},n.createElement(M,{className:"rt-BaseDialogOverlay rt-DialogOverlay"},n.createElement("div",{className:"rt-BaseDialogScroll rt-DialogScroll"},n.createElement("div",{className:`rt-BaseDialogScrollPadding rt-DialogScrollPadding ${i}`},n.createElement(O,{...d,ref:r,className:o("rt-BaseDialogContent","rt-DialogContent",s)}))))))});eu.displayName="Dialog.Content";let ed=n.forwardRef((e,t)=>n.createElement(H,{asChild:!0},n.createElement(en.X,{size:"5",mb:"3",trim:"start",...e,asChild:!1,ref:t})));ed.displayName="Dialog.Title";let ef=n.forwardRef((e,t)=>n.createElement(Y,{asChild:!0},n.createElement(eo.x,{as:"p",size:"3",...e,asChild:!1,ref:t})));ef.displayName="Dialog.Description";let ep=n.forwardRef(({children:e,...t},r)=>n.createElement(K,{...t,ref:r,asChild:!0},(0,ei.O)(e)));ep.displayName="Dialog.Close"},4508:function(e,t,r){r.d(t,{X:function(){return g}});var n=r(7653),o=r(8344),l=r(432),a=r(2741),i=r(605),s=r(3579),c=r(7271),u=r(8296),d=r(8132),f=r(2034),p=r(5597);let h={as:{type:"enum",values:["h1","h2","h3","h4","h5","h6"],default:"h1"},...a.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],default:"6",responsive:!0},...p.x,...u.O,...c.E,...f.w,...d.u,...i.EG,...s.K};var m=r(5236),v=r(5159);let g=n.forwardRef((e,t)=>{let{children:r,className:a,asChild:i,as:s="h1",color:c,...u}=(0,m.y)(e,h,v.E);return n.createElement(l.fC,{"data-accent-color":c,...u,ref:t,className:o("rt-Heading",a)},i?r:n.createElement(s,null,r))});g.displayName="Heading"},1511:function(e,t,r){r.d(t,{OW:function(){return i},dc:function(){return l},v4:function(){return a}});var n=r(7653);let o=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.75 4.5C0.75 4.08579 1.08579 3.75 1.5 3.75H7.5C7.91421 3.75 8.25 4.08579 8.25 4.5C8.25 4.91421 7.91421 5.25 7.5 5.25H1.5C1.08579 5.25 0.75 4.91421 0.75 4.5Z"})));o.displayName="ThickDividerHorizontalIcon";let l=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.53547 0.62293C8.88226 0.849446 8.97976 1.3142 8.75325 1.66099L4.5083 8.1599C4.38833 8.34356 4.19397 8.4655 3.9764 8.49358C3.75883 8.52167 3.53987 8.45309 3.3772 8.30591L0.616113 5.80777C0.308959 5.52987 0.285246 5.05559 0.563148 4.74844C0.84105 4.44128 1.31533 4.41757 1.62249 4.69547L3.73256 6.60459L7.49741 0.840706C7.72393 0.493916 8.18868 0.396414 8.53547 0.62293Z"})));l.displayName="ThickCheckIcon";let a=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{d:"M0.135232 3.15803C0.324102 2.95657 0.640521 2.94637 0.841971 3.13523L4.5 6.56464L8.158 3.13523C8.3595 2.94637 8.6759 2.95657 8.8648 3.15803C9.0536 3.35949 9.0434 3.67591 8.842 3.86477L4.84197 7.6148C4.64964 7.7951 4.35036 7.7951 4.15803 7.6148L0.158031 3.86477C-0.0434285 3.67591 -0.0536285 3.35949 0.135232 3.15803Z"})));a.displayName="ChevronDownIcon";let i=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.23826 0.201711C3.54108 -0.0809141 4.01567 -0.0645489 4.29829 0.238264L7.79829 3.98826C8.06724 4.27642 8.06724 4.72359 7.79829 5.01174L4.29829 8.76174C4.01567 9.06455 3.54108 9.08092 3.23826 8.79829C2.93545 8.51567 2.91909 8.04108 3.20171 7.73826L6.22409 4.5L3.20171 1.26174C2.91909 0.958928 2.93545 0.484337 3.23826 0.201711Z"})));i.displayName="ThickChevronRightIcon"},8695:function(e,t,r){r.d(t,{VY:function(){return eH},ck:function(){return eV},fC:function(){return ez},xz:function(){return eF}});var n=r(7653),o=r(8344),l=r(1036),a=r(3202),i=r(1082),s=r(1171),c=r(8556),u=r(4036),d=r(7205),f=r(8646),p=r(6010),h=r(9555),m=r(6303),v=r(3184),g=r(2268),y=r(8671),w=r(432),b=r(6418),x=r(7840),E=r(1469),C=r(8412),S=r(7146),R=r(4397),T=r(7573),N=[" ","Enter","ArrowUp","ArrowDown"],L=[" ","Enter"],P="Select",[D,k,A]=(0,s.B)(P),[M,j]=(0,u.b)(P,[A,v.D7]),W=(0,v.D7)(),[I,O]=M(P),[B,_]=M(P),z=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:l,onOpenChange:a,value:i,defaultValue:s,onValueChange:c,dir:u,name:f,autoComplete:p,disabled:h,required:g,form:y}=e,w=W(t),[b,E]=n.useState(null),[C,S]=n.useState(null),[R,N]=n.useState(!1),L=(0,d.gm)(u),[k,A]=(0,x.T)({prop:o,defaultProp:l??!1,onChange:a,caller:P}),[M,j]=(0,x.T)({prop:i,defaultProp:s,onChange:c,caller:P}),O=n.useRef(null),_=!b||y||!!b.closest("form"),[z,F]=n.useState(new Set),H=Array.from(z).map(e=>e.props.value).join(";");return(0,T.jsx)(v.fC,{...w,children:(0,T.jsxs)(I,{required:g,scope:t,trigger:b,onTriggerChange:E,valueNode:C,onValueNodeChange:S,valueNodeHasChildren:R,onValueNodeHasChildrenChange:N,contentId:(0,m.M)(),value:M,onValueChange:j,open:k,onOpenChange:A,dir:L,triggerPointerDownPosRef:O,disabled:h,children:[(0,T.jsx)(D.Provider,{scope:t,children:(0,T.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),_?(0,T.jsxs)(eS,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:M,onChange:e=>j(e.target.value),disabled:h,form:y,children:[void 0===M?(0,T.jsx)("option",{value:""}):null,Array.from(z)]},H):null]})})};z.displayName=P;var F="SelectTrigger",H=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...l}=e,a=W(r),s=O(F,r),u=s.disabled||o,d=(0,c.e)(t,s.onTriggerChange),f=k(r),p=n.useRef("touch"),[h,m,g]=eT(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=eN(t,e,r);void 0!==n&&s.onValueChange(n.value)}),w=e=>{u||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(v.ee,{asChild:!0,...a,children:(0,T.jsx)(y.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eR(s.value)?"":void 0,...l,ref:d,onClick:(0,i.M)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,i.M)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,i.M)(l.onKeyDown,e=>{let t=""!==h.current,r=e.ctrlKey||e.altKey||e.metaKey;r||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&N.includes(e.key)&&(w(),e.preventDefault())})})})});H.displayName=F;var V="SelectValue",Y=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:l,placeholder:a="",...i}=e,s=O(V,r),{onValueNodeHasChildrenChange:u}=s,d=void 0!==l,f=(0,c.e)(t,s.onValueNodeChange);return(0,E.b)(()=>{u(d)},[u,d]),(0,T.jsx)(y.WV.span,{...i,ref:f,style:{pointerEvents:"none"},children:eR(s.value)?(0,T.jsx)(T.Fragment,{children:a}):l})});Y.displayName=V;var X=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,T.jsx)(y.WV.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});X.displayName="SelectIcon";var K=e=>(0,T.jsx)(g.h,{asChild:!0,...e});K.displayName="SelectPortal";var U="SelectContent",$=n.forwardRef((e,t)=>{let r=O(U,e.__scopeSelect),[o,a]=n.useState();return((0,E.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,T.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,T.jsx)(Z,{scope:e.__scopeSelect,children:(0,T.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});$.displayName=U;var[Z,G]=M(U),q=(0,w.Z8)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:s,side:u,sideOffset:d,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E,...C}=e,N=O(U,r),[L,P]=n.useState(null),[D,A]=n.useState(null),M=(0,c.e)(t,e=>P(e)),[j,W]=n.useState(null),[I,B]=n.useState(null),_=k(r),[z,F]=n.useState(!1),H=n.useRef(!1);n.useEffect(()=>{if(L)return(0,S.Ry)(L)},[L]),(0,p.EW)();let V=n.useCallback(e=>{let[t,...r]=_().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&D&&(D.scrollTop=0),r===n&&D&&(D.scrollTop=D.scrollHeight),r?.focus(),document.activeElement!==o))return},[_,D]),Y=n.useCallback(()=>V([j,L]),[V,j,L]);n.useEffect(()=>{z&&Y()},[z,Y]);let{onOpenChange:X,triggerPointerDownPosRef:K}=N;n.useEffect(()=>{if(L){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(K.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(K.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():L.contains(r.target)||X(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[L,X,K]),n.useEffect(()=>{let e=()=>X(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[X]);let[$,G]=eT(e=>{let t=_().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eN(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!H.current&&!r,o=void 0!==N.value&&N.value===t;(o||n)&&(W(e),n&&(H.current=!0))},[N.value]),et=n.useCallback(()=>L?.focus(),[L]),er=n.useCallback((e,t,r)=>{let n=!H.current&&!r,o=void 0!==N.value&&N.value===t;(o||n)&&B(e)},[N.value]),en="popper"===o?ee:J,eo=en===ee?{side:u,sideOffset:d,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E}:{};return(0,T.jsx)(Z,{scope:r,content:L,viewport:D,onViewportChange:A,itemRefCallback:Q,selectedItem:j,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:Y,selectedItemText:I,position:o,isPositioned:z,searchRef:$,children:(0,T.jsx)(R.Z,{as:q,allowPinchZoom:!0,children:(0,T.jsx)(h.M,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.M)(l,e=>{N.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,T.jsx)(en,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...C,...eo,onPlaced:()=>F(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,i.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=_().filter(e=>!e.disabled),r=t.map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(r=r.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let t=e.target,n=r.indexOf(t);r=r.slice(n+1)}setTimeout(()=>V(r)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var J=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...l}=e,i=O(U,r),s=G(U,r),[u,d]=n.useState(null),[f,p]=n.useState(null),h=(0,c.e)(t,e=>p(e)),m=k(r),v=n.useRef(!1),g=n.useRef(!0),{viewport:w,selectedItem:b,selectedItemText:x,focusSelectedItem:C}=s,S=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&f&&w&&b&&x){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=x.getBoundingClientRect();if("rtl"!==i.dir){let o=n.left-t.left,l=r.left-o,i=e.left-l,s=e.width+i,c=Math.max(s,t.width),d=window.innerWidth-10,f=(0,a.u)(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.left=f+"px"}else{let o=t.right-n.right,l=window.innerWidth-r.right-o,i=window.innerWidth-e.right-l,s=e.width+i,c=Math.max(s,t.width),d=window.innerWidth-10,f=(0,a.u)(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.right=f+"px"}let l=m(),s=window.innerHeight-20,c=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=parseInt(d.paddingBottom,10),E=p+h+c+y+g,C=Math.min(5*b.offsetHeight,E),S=window.getComputedStyle(w),R=parseInt(S.paddingTop,10),T=parseInt(S.paddingBottom,10),N=e.top+e.height/2-10,L=b.offsetHeight/2,P=b.offsetTop+L,D=p+h+P;if(D<=N){let e=l.length>0&&b===l[l.length-1].ref.current;u.style.bottom="0px";let t=f.clientHeight-w.offsetTop-w.offsetHeight;u.style.height=D+Math.max(s-N,L+(e?T:0)+t+g)+"px"}else{let e=l.length>0&&b===l[0].ref.current;u.style.top="0px";let t=Math.max(N,p+w.offsetTop+(e?R:0)+L);u.style.height=t+(E-D)+"px",w.scrollTop=D-N+w.offsetTop}u.style.margin="10px 0",u.style.minHeight=C+"px",u.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,i.trigger,i.valueNode,u,f,w,b,x,i.dir,o]);(0,E.b)(()=>S(),[S]);let[R,N]=n.useState();(0,E.b)(()=>{f&&N(window.getComputedStyle(f).zIndex)},[f]);let L=n.useCallback(e=>{e&&!0===g.current&&(S(),C?.(),g.current=!1)},[S,C]);return(0,T.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:v,onScrollButtonChange:L,children:(0,T.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,T.jsx)(y.WV.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});J.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...l}=e,a=W(r);return(0,T.jsx)(v.VY,{...a,...l,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=M(U,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...l}=e,a=G(en,r),s=er(en,r),u=(0,c.e)(t,a.onViewportChange),d=n.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,T.jsx)(D.Slot,{scope:r,children:(0,T.jsx)(y.WV.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.M)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=parseFloat(r.style.minHeight),l=parseFloat(r.style.height),a=Math.max(o,l);if(a<n){let o=a+e,l=Math.min(n,o),i=o-l;r.style.height=l+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var el="SelectGroup",[ea,ei]=M(el),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,m.M)();return(0,T.jsx)(ea,{scope:r,id:o,children:(0,T.jsx)(y.WV.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=el;var ec="SelectLabel",eu=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ei(ec,r);return(0,T.jsx)(y.WV.div,{id:o.id,...n,ref:t})});eu.displayName=ec;var ed="SelectItem",[ef,ep]=M(ed),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:l=!1,textValue:a,...s}=e,u=O(ed,r),d=G(ed,r),f=u.value===o,[p,h]=n.useState(a??""),[v,g]=n.useState(!1),w=(0,c.e)(t,e=>d.itemRefCallback?.(e,o,l)),b=(0,m.M)(),x=n.useRef("touch"),E=()=>{l||(u.onValueChange(o),u.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ef,{scope:r,value:o,disabled:l,textId:b,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,T.jsx)(D.ItemSlot,{scope:r,value:o,disabled:l,textValue:p,children:(0,T.jsx)(y.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...s,ref:w,onFocus:(0,i.M)(s.onFocus,()=>g(!0)),onBlur:(0,i.M)(s.onBlur,()=>g(!1)),onClick:(0,i.M)(s.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,i.M)(s.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,i.M)(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,i.M)(s.onPointerMove,e=>{x.current=e.pointerType,l?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.M)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,i.M)(s.onKeyDown,e=>{let t=d.searchRef?.current!=="";t&&" "===e.key||(L.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var em="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,s=O(em,r),u=G(em,r),d=ep(em,r),f=_(em,r),[p,h]=n.useState(null),m=(0,c.e)(t,e=>h(e),d.onItemTextChange,e=>u.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,g=n.useMemo(()=>(0,T.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:b}=f;return(0,E.b)(()=>(w(g),()=>b(g)),[w,b,g]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(y.WV.span,{id:d.textId,...i,ref:m}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?l.createPortal(i.children,s.valueNode):null]})});ev.displayName=em;var eg="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ep(eg,r);return o.isSelected?(0,T.jsx)(y.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=eg;var ew="SelectScrollUpButton";n.forwardRef((e,t)=>{let r=G(ew,e.__scopeSelect),o=er(ew,e.__scopeSelect),[l,a]=n.useState(!1),i=(0,c.e)(t,o.onScrollButtonChange);return(0,E.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollTop>0;a(e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,T.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=ew;var eb="SelectScrollDownButton";n.forwardRef((e,t)=>{let r=G(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[l,a]=n.useState(!1),i=(0,c.e)(t,o.onScrollButtonChange);return(0,E.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight,r=Math.ceil(t.scrollTop)<e;a(r)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,T.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=eb;var ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...l}=e,a=G("SelectScrollButton",r),s=n.useRef(null),c=k(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,E.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,T.jsx)(y.WV.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.M)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.M)(l.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.M)(l.onPointerLeave,()=>{u()})})}),eE=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,T.jsx)(y.WV.div,{"aria-hidden":!0,...n,ref:t})});eE.displayName="SelectSeparator";var eC="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=W(r),l=O(eC,r),a=G(eC,r);return l.open&&"popper"===a.position?(0,T.jsx)(v.Eh,{...o,...n,ref:t}):null}).displayName=eC;var eS=n.forwardRef(({__scopeSelect:e,value:t,...r},o)=>{let l=n.useRef(null),a=(0,c.e)(o,l),i=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return n.useEffect(()=>{let e=l.current;if(!e)return;let r=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(r,"value"),o=n.set;if(i!==t&&o){let r=new Event("change",{bubbles:!0});o.call(e,t),e.dispatchEvent(r)}},[i,t]),(0,T.jsx)(y.WV.select,{...r,style:{...C.C2,...r.style},ref:a,defaultValue:t})});function eR(e){return""===e||void 0===e}function eT(e){let t=(0,b.W)(e),r=n.useRef(""),o=n.useRef(0),l=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,l,a]}function eN(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0]),l=o?t[0]:t,a=r?e.indexOf(r):-1,i=(n=Math.max(a,0),e.map((t,r)=>e[(n+r)%e.length])),s=1===l.length;s&&(i=i.filter(e=>e!==r));let c=i.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return c!==r?c:void 0}eS.displayName="SelectBubbleInput";var eL=r(9148),eP=r(5236),eD=r(5159),ek=r(1511),eA=r(605),eM=r(3579),ej=r(4494);let eW={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0}},eI={variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft","ghost"],default:"surface"},...eA.EG,...ej.I,placeholder:{type:"string"}},eO={variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"solid"},...eA.EG,...eM.K};var eB=r(2123);let e_=n.createContext({}),ez=e=>{let{children:t,size:r=eW.size.default,...o}=e;return n.createElement(z,{...o},n.createElement(e_.Provider,{value:n.useMemo(()=>({size:r}),[r])},t))};ez.displayName="Select.Root";let eF=n.forwardRef((e,t)=>{let r=n.useContext(e_),{children:l,className:a,color:i,radius:s,placeholder:c,...u}=(0,eP.y)({size:r?.size,...e},{size:eW.size},eI,eD.E);return n.createElement(H,{asChild:!0},n.createElement("button",{"data-accent-color":i,"data-radius":s,...u,ref:t,className:o("rt-reset","rt-SelectTrigger",a)},n.createElement("span",{className:"rt-SelectTriggerInner"},n.createElement(Y,{placeholder:c},l)),n.createElement(X,{asChild:!0},n.createElement(ek.v4,{className:"rt-SelectIcon"}))))});eF.displayName="Select.Trigger";let eH=n.forwardRef((e,t)=>{let r=n.useContext(e_),{className:l,children:a,color:i,container:s,...c}=(0,eP.y)({size:r?.size,...e},{size:eW.size},eO),u=(0,eB.TC)(),d=i||u.accentColor;return n.createElement(K,{container:s},n.createElement(eB.Q2,{asChild:!0},n.createElement($,{"data-accent-color":d,sideOffset:4,...c,asChild:!1,ref:t,className:o({"rt-PopperContent":"popper"===c.position},"rt-SelectContent",l)},n.createElement(eL.fC,{type:"auto",className:"rt-ScrollAreaRoot"},n.createElement(eo,{asChild:!0,className:"rt-SelectViewport"},n.createElement(eL.l_,{className:"rt-ScrollAreaViewport",style:{overflowY:void 0}},a)),n.createElement(eL.LW,{className:"rt-ScrollAreaScrollbar rt-r-size-1",orientation:"vertical"},n.createElement(eL.bU,{className:"rt-ScrollAreaThumb"}))))))});eH.displayName="Select.Content";let eV=n.forwardRef((e,t)=>{let{className:r,children:l,...a}=e;return n.createElement(eh,{...a,asChild:!1,ref:t,className:o("rt-SelectItem",r)},n.createElement(ey,{className:"rt-SelectItemIndicator"},n.createElement(ek.dc,{className:"rt-SelectItemIndicatorIcon"})),n.createElement(ev,null,l))});eV.displayName="Select.Item";let eY=n.forwardRef(({className:e,...t},r)=>n.createElement(es,{...t,asChild:!1,ref:r,className:o("rt-SelectGroup",e)}));eY.displayName="Select.Group";let eX=n.forwardRef(({className:e,...t},r)=>n.createElement(eu,{...t,asChild:!1,ref:r,className:o("rt-SelectLabel",e)}));eX.displayName="Select.Label";let eK=n.forwardRef(({className:e,...t},r)=>n.createElement(eE,{...t,asChild:!1,ref:r,className:o("rt-SelectSeparator",e)}));eK.displayName="Select.Separator"},2123:function(e,t,r){r.d(t,{Q2:function(){return X},TC:function(){return Y}});var n=r(7653),o=r(8344),l=r(1082),a=r(8556),i=r(4036),s=r(8646),c=(r(6303),r(3184)),u=(r(2268),r(7575)),d=r(8671),f=r(432),p=(r(7840),r(8412)),h=r(7573),[m,v]=(0,i.b)("Tooltip",[c.D7]),g=(0,c.D7)(),y="TooltipProvider",w="tooltip.open",[b,x]=m(y),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:a}=e,i=n.useRef(!0),s=n.useRef(!1),c=n.useRef(0);return n.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(b,{scope:t,isOpenDelayedRef:i,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(c.current),i.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:a})};E.displayName=y;var C="Tooltip",[S,R]=m(C),T="TooltipTrigger";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...o}=e,i=R(T,r),s=x(T,r),u=g(r),f=n.useRef(null),p=(0,a.e)(t,f,i.onTriggerChange),m=n.useRef(!1),v=n.useRef(!1),y=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,h.jsx)(c.ee,{asChild:!0,...u,children:(0,h.jsx)(d.WV.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...o,ref:p,onPointerMove:(0,l.M)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(i.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,l.M)(e.onPointerLeave,()=>{i.onTriggerLeave(),v.current=!1}),onPointerDown:(0,l.M)(e.onPointerDown,()=>{i.open&&i.onClose(),m.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,l.M)(e.onFocus,()=>{m.current||i.onOpen()}),onBlur:(0,l.M)(e.onBlur,i.onClose),onClick:(0,l.M)(e.onClick,i.onClose)})})}).displayName=T;var[N,L]=m("TooltipPortal",{forceMount:void 0}),P="TooltipContent",D=n.forwardRef((e,t)=>{let r=L(P,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,a=R(P,e.__scopeTooltip);return(0,h.jsx)(u.z,{present:n||a.open,children:a.disableHoverableContent?(0,h.jsx)(W,{side:o,...l,ref:t}):(0,h.jsx)(k,{side:o,...l,ref:t})})}),k=n.forwardRef((e,t)=>{let r=R(P,e.__scopeTooltip),o=x(P,e.__scopeTooltip),l=n.useRef(null),i=(0,a.e)(t,l),[s,c]=n.useState(null),{trigger:u,onClose:d}=r,f=l.current,{onPointerInTransitChange:p}=o,m=n.useCallback(()=>{c(null),p(!1)},[p]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect()),l=function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),a=function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect()),i=function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...l,...a]);c(i),p(!0)},[p]);return n.useEffect(()=>()=>m(),[m]),n.useEffect(()=>{if(u&&f){let e=e=>v(e,f),t=e=>v(e,u);return u.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[u,f,v,m]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=u?.contains(t)||f?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let a=t[e],i=t[l],s=a.x,c=a.y,u=i.x,d=i.y,f=c>n!=d>n&&r<(u-s)*(n-c)/(d-c)+s;f&&(o=!o)}return o}(r,s);n?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,f,s,d,m]),(0,h.jsx)(W,{...e,ref:i})}),[A,M]=m(C,{isInside:!1}),j=(0,f.sA)("TooltipContent"),W=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:a,onPointerDownOutside:i,...u}=e,d=R(P,r),f=g(r),{onClose:m}=d;return n.useEffect(()=>(document.addEventListener(w,m),()=>document.removeEventListener(w,m)),[m]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&m()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,m]),(0,h.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,h.jsxs)(c.VY,{"data-state":d.stateAttribute,...f,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(j,{children:o}),(0,h.jsx)(A,{scope:r,isInside:!0,children:(0,h.jsx)(p.fC,{id:d.contentId,role:"tooltip",children:l||o})})]})})});D.displayName=P;var I="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=g(r),l=M(I,r);return l.isInside?null:(0,h.jsx)(c.Eh,{...o,...n,ref:t})}).displayName=I;var O=r(7205),B=r(2741),_=r(605),z=r(4494);let F={...B.C,hasBackground:{type:"boolean",default:!0},appearance:{type:"enum",values:["inherit","light","dark"],default:"inherit"},accentColor:{type:"enum",values:_.FN,default:"indigo"},grayColor:{type:"enum",values:_.ab,default:"auto"},panelBackground:{type:"enum",values:["solid","translucent"],default:"translucent"},radius:{type:"enum",values:z.p,default:"medium"},scaling:{type:"enum",values:["90%","95%","100%","105%","110%"],default:"100%"}},H=()=>{},V=n.createContext(void 0);function Y(){let e=n.useContext(V);if(void 0===e)throw Error("`useThemeContext` must be used within a `Theme`");return e}let X=n.forwardRef((e,t)=>void 0===n.useContext(V)?n.createElement(E,{delayDuration:200},n.createElement(O.zt,{dir:"ltr"},n.createElement(K,{...e,ref:t}))):n.createElement(U,{...e,ref:t}));X.displayName="Theme";let K=n.forwardRef((e,t)=>{let{appearance:r=F.appearance.default,accentColor:o=F.accentColor.default,grayColor:l=F.grayColor.default,panelBackground:a=F.panelBackground.default,radius:i=F.radius.default,scaling:s=F.scaling.default,hasBackground:c=F.hasBackground.default,...u}=e,[d,f]=n.useState(r);n.useEffect(()=>f(r),[r]);let[p,h]=n.useState(o);n.useEffect(()=>h(o),[o]);let[m,v]=n.useState(l);n.useEffect(()=>v(l),[l]);let[g,y]=n.useState(a);n.useEffect(()=>y(a),[a]);let[w,b]=n.useState(i);n.useEffect(()=>b(i),[i]);let[x,E]=n.useState(s);return n.useEffect(()=>E(s),[s]),n.createElement(U,{...u,ref:t,isRoot:!0,hasBackground:c,appearance:d,accentColor:p,grayColor:m,panelBackground:g,radius:w,scaling:x,onAppearanceChange:f,onAccentColorChange:h,onGrayColorChange:v,onPanelBackgroundChange:y,onRadiusChange:b,onScalingChange:E})});K.displayName="ThemeRoot";let U=n.forwardRef((e,t)=>{let r=n.useContext(V),{asChild:l,isRoot:a,hasBackground:i,appearance:s=r?.appearance??F.appearance.default,accentColor:c=r?.accentColor??F.accentColor.default,grayColor:u=r?.resolvedGrayColor??F.grayColor.default,panelBackground:d=r?.panelBackground??F.panelBackground.default,radius:p=r?.radius??F.radius.default,scaling:h=r?.scaling??F.scaling.default,onAppearanceChange:m=H,onAccentColorChange:v=H,onGrayColorChange:g=H,onPanelBackgroundChange:y=H,onRadiusChange:w=H,onScalingChange:b=H,...x}=e,E=l?f.fC:"div",C="auto"===u?function(e){switch(e){case"tomato":case"red":case"ruby":case"crimson":case"pink":case"plum":case"purple":case"violet":return"mauve";case"iris":case"indigo":case"blue":case"sky":case"cyan":return"slate";case"teal":case"jade":case"mint":case"green":return"sage";case"grass":case"lime":return"olive";case"yellow":case"amber":case"orange":case"brown":case"gold":case"bronze":return"sand";case"gray":return"gray"}}(c):u,S="light"===e.appearance||"dark"===e.appearance;return n.createElement(V.Provider,{value:n.useMemo(()=>({appearance:s,accentColor:c,grayColor:u,resolvedGrayColor:C,panelBackground:d,radius:p,scaling:h,onAppearanceChange:m,onAccentColorChange:v,onGrayColorChange:g,onPanelBackgroundChange:y,onRadiusChange:w,onScalingChange:b}),[s,c,u,C,d,p,h,m,v,g,y,w,b])},n.createElement(E,{"data-is-root-theme":a?"true":"false","data-accent-color":c,"data-gray-color":C,"data-has-background":(void 0===i?a||S:i)?"true":"false","data-panel-background":d,"data-radius":p,"data-scaling":h,ref:t,...x,className:o("radix-themes",{light:"light"===s,dark:"dark"===s},x.className)}))});U.displayName="ThemeImpl"},3589:function(e,t,r){r.d(t,{x:function(){return o}});var n=r(7653);function o(e,t){let{asChild:r,children:o}=e;if(!r)return"function"==typeof t?t(o):t;let l=n.Children.only(o);return n.cloneElement(l,{children:"function"==typeof t?t(l.props.children):t})}},8373:function(e,t,r){r.d(t,{O:function(){return o}});var n=r(7653);let o=e=>{if(!n.isValidElement(e))throw Error(`Expected a single React Element child, but got: ${n.Children.toArray(e).map(e=>"object"==typeof e&&"type"in e&&"string"==typeof e.type?e.type:typeof e).join(", ")}`);return e}}}]);