(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[612],{8294:function(e,t,r){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(7653),s=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,r){var n,i={},u=null,c=null;for(n in void 0!==r&&(u=""+r),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)a.call(t,n)&&!o.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===i[n]&&(i[n]=t[n]);return{$$typeof:s,type:e,key:u,ref:c,props:i,_owner:l.current}}t.Fragment=i,t.jsx=u,t.jsxs=u},7573:function(e,t,r){"use strict";e.exports=r(8294)},8344:function(e,t){var r;/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function s(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=i(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return s.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=i(t,r));return t}(r)))}return e}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(s.default=s,e.exports=s):void 0!==(r=(function(){return s}).apply(t,[]))&&(e.exports=r)}()},8556:function(e,t,r){"use strict";r.d(t,{F:function(){return i},e:function(){return a}});var n=r(7653);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function a(...e){return n.useCallback(i(...e),e)}},8671:function(e,t,r){"use strict";r.d(t,{WV:function(){return l},jH:function(){return o}});var n=r(7653),s=r(1036),i=r(432),a=r(7573),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.Z8)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{let{asChild:s,...i}=e,l=s?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...i,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function o(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},432:function(e,t,r){"use strict";r.d(t,{A4:function(){return c},Z8:function(){return a},fC:function(){return l},sA:function(){return u}});var n=r(7653),s=r(8556),i=r(7573);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,a;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let n in t){let s=e[n],i=t[n],a=/^on[A-Z]/.test(n);a?s&&i?r[n]=(...e)=>{let t=i(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...i}:"className"===n&&(r[n]=[s,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,s.F)(t,l):l),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...a}=e,l=n.Children.toArray(s),o=l.find(p);if(o){let e=o.props.children,s=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,i.jsx)(t,{...a,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=a("Slot"),o=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}var c=u("Slottable");function p(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},8412:function(e,t,r){"use strict";r.d(t,{C2:function(){return a},fC:function(){return o}});var n=r(7653),s=r(8671),i=r(7573),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=n.forwardRef((e,t)=>(0,i.jsx)(s.WV.span,{...e,ref:t,style:{...a,...e.style}}));l.displayName="VisuallyHidden";var o=l},8436:function(e,t,r){"use strict";r.d(t,{z:function(){return b}});var n=r(7653),s=r(8344),i=r(432),a=r(2741),l=r(605),o=r(3579),u=r(4494);let c={...a.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","solid","soft","surface","outline","ghost"],default:"solid"},...l.o3,...o.K,...u.I,loading:{type:"boolean",className:"rt-loading",default:!1}};var p=r(4045);let m={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},loading:{type:"boolean",default:!0}};var f=r(5236),d=r(5159);let v=n.forwardRef((e,t)=>{let{className:r,children:i,loading:a,...l}=(0,f.y)(e,m,d.E);if(!a)return i;let o=n.createElement("span",{...l,ref:t,className:s("rt-Spinner",r)},n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}));return void 0===i?o:n.createElement(p.k,{asChild:!0,position:"relative",align:"center",justify:"center"},n.createElement("span",null,n.createElement("span",{"aria-hidden":!0,style:{display:"contents",visibility:"hidden"},inert:void 0},i),n.createElement(p.k,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},n.createElement("span",null,o))))});v.displayName="Spinner";var y=r(8412);let g=y.fC;y.fC;var N=r(2717);let h=n.forwardRef((e,t)=>{let{size:r=c.size.default}=e,{className:a,children:l,asChild:o,color:u,radius:m,disabled:y=e.loading,...h}=(0,f.y)(e,c,d.E),b=o?i.fC:"button";return n.createElement(b,{"data-disabled":y||void 0,"data-accent-color":u,"data-radius":m,...h,ref:t,className:s("rt-reset","rt-BaseButton",a),disabled:y},e.loading?n.createElement(n.Fragment,null,n.createElement("span",{style:{display:"contents",visibility:"hidden"},"aria-hidden":!0},l),n.createElement(g,null,l),n.createElement(p.k,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},n.createElement("span",null,n.createElement(v,{size:(0,N.qz)(r,N.AG)})))):l)});h.displayName="BaseButton";let b=n.forwardRef(({className:e,...t},r)=>n.createElement(h,{...t,ref:r,className:s("rt-Button",e)}));b.displayName="Button"},4045:function(e,t,r){"use strict";r.d(t,{k:function(){return c}});var n=r(7653),s=r(8344),i=r(5236),a=r(1795),l=r(5159),o=r(9524),u=r(837);let c=n.forwardRef((e,t)=>{let{className:r,asChild:c,as:p="div",...m}=(0,i.y)(e,u.l,a.P,l.E);return n.createElement(c?o.g7:p,{...m,ref:t,className:s("rt-Flex",r)})});c.displayName="Flex"},837:function(e,t,r){"use strict";r.d(t,{l:function(){return i}});var n=r(2741),s=r(6357);let i={as:{type:"enum",values:["div","span"],default:"div"},...n.C,display:{type:"enum",className:"rt-r-display",values:["none","inline-flex","flex"],responsive:!0},direction:{type:"enum",className:"rt-r-fd",values:["row","column","row-reverse","column-reverse"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(e){return"between"===e?"space-between":e},responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},...s.c}},9524:function(e,t,r){"use strict";r.d(t,{g7:function(){return s}});var n=r(432);n.fC;let s=n.fC;n.A4},2063:function(e,t,r){"use strict";r.d(t,{x:function(){return g}});var n=r(7653),s=r(8344),i=r(432),a=r(5236),l=r(5159),o=r(2741),u=r(605),c=r(3579),p=r(7271),m=r(8296),f=r(8132),d=r(2034),v=r(5597);let y={as:{type:"enum",values:["span","div","label","p"],default:"span"},...o.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],responsive:!0},...v.x,...m.O,...p.E,...d.w,...f.u,...u.EG,...c.K},g=n.forwardRef((e,t)=>{let{children:r,className:o,asChild:u,as:c="span",color:p,...m}=(0,a.y)(e,y,l.E);return n.createElement(i.fC,{"data-accent-color":p,...m,ref:t,className:s("rt-Text",o)},u?r:n.createElement(c,null,r))});g.displayName="Text"},5236:function(e,t,r){"use strict";r.d(t,{y:function(){return l}});var n=r(8344),s=r(1032),i=r(3027),a=r(2808);function l(e,...t){let r,o;let u={...e},c=function(...e){return Object.assign({},...e)}(...t);for(let e in c){let t=u[e],l=c[e];if(void 0!==l.default&&void 0===t&&(t=l.default),"enum"!==l.type||[l.default,...l.values].includes(t)||(0,i.d)(t)||(t=l.default),u[e]=t,"className"in l&&l.className){delete u[e];let c="responsive"in l;if(!t||(0,i.d)(t)&&!c)continue;if((0,i.d)(t)&&(void 0!==l.default&&void 0===t.initial&&(t.initial=l.default),"enum"===l.type&&([l.default,...l.values].includes(t.initial)||(t.initial=l.default))),"enum"===l.type){let e=(0,s.RE)({allowArbitraryValues:!1,value:t,className:l.className,propValues:l.values,parseValue:l.parseValue});r=n(r,e);continue}if("string"===l.type||"enum | string"===l.type){let e="string"===l.type?[]:l.values,[i,u]=(0,s.uq)({className:l.className,customProperties:l.customProperties,propValues:e,parseValue:l.parseValue,value:t});o=(0,a.y)(o,u),r=n(r,i);continue}if("boolean"===l.type&&t){r=n(r,l.className);continue}}}return u.className=n(r,e.className),u.style=(0,a.y)(o,e.style),u}},1032:function(e,t,r){"use strict";r.d(t,{RE:function(){return l},uq:function(){return a}});var n=r(9910);function s(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var i=r(3027);function a({className:e,customProperties:t,...r}){let a=l({allowArbitraryValues:!0,className:e,...r}),o=function({customProperties:e,value:t,propValues:r,parseValue:a=e=>e}){let l={};if(!(!t||"string"==typeof t&&r.includes(t))){if("string"==typeof t&&(l=Object.fromEntries(e.map(e=>[e,t]))),(0,i.d)(t))for(let i in t){if(!s(t,i)||!n.A.includes(i))continue;let a=t[i];if(!r.includes(a))for(let t of e)l={["initial"===i?t:`${t}-${i}`]:a,...l}}for(let e in l){let t=l[e];void 0!==t&&(l[e]=a(t))}return l}}({customProperties:t,...r});return[a,o]}function l({allowArbitraryValues:e,value:t,className:r,propValues:a,parseValue:l=e=>e}){let u=[];if(t){if("string"==typeof t&&a.includes(t))return o(r,t,l);if((0,i.d)(t)){for(let i in t){if(!s(t,i)||!n.A.includes(i))continue;let c=t[i];if(void 0!==c){if(a.includes(c)){let e=o(r,c,l),t="initial"===i?e:`${i}:${e}`;u.push(t)}else if(e){let e="initial"===i?r:`${i}:${r}`;u.push(e)}}}return u.join(" ")}if(e)return r}}function o(e,t,r){let n=r(t),s=n?.startsWith("-"),i=s?n?.substring(1):n;return`${s?"-":""}${e}${e?"-":""}${i}`}},3027:function(e,t,r){"use strict";r.d(t,{d:function(){return s}});var n=r(9910);function s(e){return"object"==typeof e&&Object.keys(e).some(e=>n.A.includes(e))}},2717:function(e,t,r){"use strict";function n(e,t){if(void 0!==e)return"string"==typeof e?t(e):Object.fromEntries(Object.entries(e).map(([e,r])=>[e,t(r)]))}function s(e){return"3"===e?"3":"2"}function i(e){switch(e){case"1":return"1";case"2":case"3":return"2";case"4":return"3"}}r.d(t,{AG:function(){return i},qz:function(){return n},uJ:function(){return s}})},2808:function(e,t,r){"use strict";function n(...e){let t={};for(let r of e)r&&(t={...t,...r});return Object.keys(t).length?t:void 0}r.d(t,{y:function(){return n}})},2741:function(e,t,r){"use strict";r.d(t,{C:function(){return n}});let n={asChild:{type:"boolean"}}},605:function(e,t,r){"use strict";r.d(t,{EG:function(){return i},FN:function(){return n},ab:function(){return s},o3:function(){return a}});let n=["gray","gold","bronze","brown","yellow","amber","orange","tomato","red","ruby","crimson","pink","plum","purple","violet","iris","indigo","blue","cyan","teal","jade","green","grass","lime","mint","sky"],s=["auto","gray","mauve","slate","sage","olive","sand"],i={color:{type:"enum",values:n,default:void 0}},a={color:{type:"enum",values:n,default:""}}},6357:function(e,t,r){"use strict";r.d(t,{c:function(){return s}});let n=["0","1","2","3","4","5","6","7","8","9"],s={gap:{type:"enum | string",className:"rt-r-gap",customProperties:["--gap"],values:n,responsive:!0},gapX:{type:"enum | string",className:"rt-r-cg",customProperties:["--column-gap"],values:n,responsive:!0},gapY:{type:"enum | string",className:"rt-r-rg",customProperties:["--row-gap"],values:n,responsive:!0}}},3230:function(e,t,r){"use strict";r.d(t,{F:function(){return n}});let n={height:{type:"string",className:"rt-r-h",customProperties:["--height"],responsive:!0},minHeight:{type:"string",className:"rt-r-min-h",customProperties:["--min-height"],responsive:!0},maxHeight:{type:"string",className:"rt-r-max-h",customProperties:["--max-height"],responsive:!0}}},3579:function(e,t,r){"use strict";r.d(t,{K:function(){return n}});let n={highContrast:{type:"boolean",className:"rt-high-contrast",default:void 0}}},1795:function(e,t,r){"use strict";r.d(t,{P:function(){return o}});var n=r(6750),s=r(3230),i=r(6306);let a=["visible","hidden","clip","scroll","auto"],l=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],o={...n.i,...i.n,...s.F,position:{type:"enum",className:"rt-r-position",values:["static","relative","absolute","fixed","sticky"],responsive:!0},inset:{type:"enum | string",className:"rt-r-inset",customProperties:["--inset"],values:l,responsive:!0},top:{type:"enum | string",className:"rt-r-top",customProperties:["--top"],values:l,responsive:!0},right:{type:"enum | string",className:"rt-r-right",customProperties:["--right"],values:l,responsive:!0},bottom:{type:"enum | string",className:"rt-r-bottom",customProperties:["--bottom"],values:l,responsive:!0},left:{type:"enum | string",className:"rt-r-left",customProperties:["--left"],values:l,responsive:!0},overflow:{type:"enum",className:"rt-r-overflow",values:a,responsive:!0},overflowX:{type:"enum",className:"rt-r-ox",values:a,responsive:!0},overflowY:{type:"enum",className:"rt-r-oy",values:a,responsive:!0},flexBasis:{type:"string",className:"rt-r-fb",customProperties:["--flex-basis"],responsive:!0},flexShrink:{type:"enum | string",className:"rt-r-fs",customProperties:["--flex-shrink"],values:["0","1"],responsive:!0},flexGrow:{type:"enum | string",className:"rt-r-fg",customProperties:["--flex-grow"],values:["0","1"],responsive:!0},gridArea:{type:"string",className:"rt-r-ga",customProperties:["--grid-area"],responsive:!0},gridColumn:{type:"string",className:"rt-r-gc",customProperties:["--grid-column"],responsive:!0},gridColumnStart:{type:"string",className:"rt-r-gcs",customProperties:["--grid-column-start"],responsive:!0},gridColumnEnd:{type:"string",className:"rt-r-gce",customProperties:["--grid-column-end"],responsive:!0},gridRow:{type:"string",className:"rt-r-gr",customProperties:["--grid-row"],responsive:!0},gridRowStart:{type:"string",className:"rt-r-grs",customProperties:["--grid-row-start"],responsive:!0},gridRowEnd:{type:"string",className:"rt-r-gre",customProperties:["--grid-row-end"],responsive:!0}}},7271:function(e,t,r){"use strict";r.d(t,{E:function(){return n}});let n={trim:{type:"enum",className:"rt-r-lt",values:["normal","start","end","both"],responsive:!0}}},5159:function(e,t,r){"use strict";r.d(t,{E:function(){return s}});let n=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],s={m:{type:"enum | string",values:n,responsive:!0,className:"rt-r-m",customProperties:["--m"]},mx:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mx",customProperties:["--ml","--mr"]},my:{type:"enum | string",values:n,responsive:!0,className:"rt-r-my",customProperties:["--mt","--mb"]},mt:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mt",customProperties:["--mt"]},mr:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mr",customProperties:["--mr"]},mb:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mb",customProperties:["--mb"]},ml:{type:"enum | string",values:n,responsive:!0,className:"rt-r-ml",customProperties:["--ml"]}}},6750:function(e,t,r){"use strict";r.d(t,{i:function(){return s}});let n=["0","1","2","3","4","5","6","7","8","9"],s={p:{type:"enum | string",className:"rt-r-p",customProperties:["--p"],values:n,responsive:!0},px:{type:"enum | string",className:"rt-r-px",customProperties:["--pl","--pr"],values:n,responsive:!0},py:{type:"enum | string",className:"rt-r-py",customProperties:["--pt","--pb"],values:n,responsive:!0},pt:{type:"enum | string",className:"rt-r-pt",customProperties:["--pt"],values:n,responsive:!0},pr:{type:"enum | string",className:"rt-r-pr",customProperties:["--pr"],values:n,responsive:!0},pb:{type:"enum | string",className:"rt-r-pb",customProperties:["--pb"],values:n,responsive:!0},pl:{type:"enum | string",className:"rt-r-pl",customProperties:["--pl"],values:n,responsive:!0}}},9910:function(e,t,r){"use strict";r.d(t,{A:function(){return n}});let n=["initial","xs","sm","md","lg","xl"]},4494:function(e,t,r){"use strict";r.d(t,{I:function(){return s},p:function(){return n}});let n=["none","small","medium","large","full"],s={radius:{type:"enum",values:n,default:void 0}}},8296:function(e,t,r){"use strict";r.d(t,{O:function(){return n}});let n={align:{type:"enum",className:"rt-r-ta",values:["left","center","right"],responsive:!0}}},8132:function(e,t,r){"use strict";r.d(t,{u:function(){return n}});let n={wrap:{type:"enum",className:"rt-r-tw",values:["wrap","nowrap","pretty","balance"],responsive:!0}}},2034:function(e,t,r){"use strict";r.d(t,{w:function(){return n}});let n={truncate:{type:"boolean",className:"rt-truncate"}}},5597:function(e,t,r){"use strict";r.d(t,{x:function(){return n}});let n={weight:{type:"enum",className:"rt-r-weight",values:["light","regular","medium","bold"],responsive:!0}}},6306:function(e,t,r){"use strict";r.d(t,{n:function(){return n}});let n={width:{type:"string",className:"rt-r-w",customProperties:["--width"],responsive:!0},minWidth:{type:"string",className:"rt-r-min-w",customProperties:["--min-width"],responsive:!0},maxWidth:{type:"string",className:"rt-r-max-w",customProperties:["--max-width"],responsive:!0}}}}]);