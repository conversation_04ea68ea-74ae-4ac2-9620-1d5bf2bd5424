"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[280],{9213:function(e,a,t){t.d(a,{Z:function(){return o}});var r=t(7653);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,a,t)=>!!e&&t.indexOf(e)===a).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r.forwardRef)(({color:e="currentColor",size:a=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:o,iconNode:c,...u},d)=>(0,r.createElement)("svg",{ref:d,...s,width:a,height:a,stroke:e,strokeWidth:n?24*Number(t)/Number(a):t,className:l("lucide",i),...u},[...c.map(([e,a])=>(0,r.createElement)(e,a)),...Array.isArray(o)?o:[o]])),o=(e,a)=>{let t=(0,r.forwardRef)(({className:t,...s},o)=>(0,r.createElement)(i,{ref:o,iconNode:a,className:l(`lucide-${n(e)}`,t),...s}));return t.displayName=`${e}`,t}},8121:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},8907:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},1887:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},820:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("CodeXml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]])},3484:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},214:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},3511:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},5204:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},9218:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},966:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},646:function(e,a,t){t.d(a,{Z:function(){return n}});var r=t(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},6094:function(e,a,t){t.d(a,{JO:function(){return h},fC:function(){return v},xv:function(){return p}});var r=t(7653),n=t(8344),l=t(432),s=t(2063),i=t(2741),o=t(605),c=t(3579);let u={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["soft","surface","outline"],default:"soft"},...o.o3,...c.K};var d=t(5236),f=t(2717),m=t(5159);let y=r.createContext({}),v=r.forwardRef((e,a)=>{let{size:t=u.size.default}=e,{asChild:s,children:i,className:o,color:c,...f}=(0,d.y)(e,u,m.E),v=s?l.fC:"div";return r.createElement(v,{"data-accent-color":c,...f,className:n("rt-CalloutRoot",o),ref:a},r.createElement(y.Provider,{value:r.useMemo(()=>({size:t}),[t])},i))});v.displayName="Callout.Root";let h=r.forwardRef(({className:e,...a},t)=>r.createElement("div",{...a,className:n("rt-CalloutIcon",e),ref:t}));h.displayName="Callout.Icon";let p=r.forwardRef(({className:e,...a},t)=>{let{size:l}=r.useContext(y);return r.createElement(s.x,{as:"p",size:(0,f.qz)(l,f.uJ),...a,asChild:!1,ref:t,className:n("rt-CalloutText",e)})});p.displayName="Callout.Text"},5543:function(e,a,t){t.d(a,{Z:function(){return u}});var r=t(7653),n=t(8344),l=t(432),s=t(2741);let i={...s.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var o=t(5236),c=t(5159);let u=r.forwardRef((e,a)=>{let{asChild:t,className:s,...u}=(0,o.y)(e,i,c.E),d=t?l.fC:"div";return r.createElement(d,{ref:a,...u,className:n("rt-reset","rt-BaseCard","rt-Card",s)})});u.displayName="Card"},4480:function(e,a,t){t.d(a,{VY:function(){return $},aV:function(){return L},fC:function(){return I},xz:function(){return A}});var r=t(7653),n=t(8344),l=t(1082),s=t(4036),i=t(306),o=t(7575),c=t(8671),u=t(7205),d=t(7840),f=t(6303),m=t(7573),y="Tabs",[v,h]=(0,s.b)(y,[i.Pc]),p=(0,i.Pc)(),[b,k]=v(y),N=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:n,defaultValue:l,orientation:s="horizontal",dir:i,activationMode:o="automatic",...v}=e,h=(0,u.gm)(i),[p,k]=(0,d.T)({prop:r,onChange:n,defaultProp:l??"",caller:y});return(0,m.jsx)(b,{scope:t,baseId:(0,f.M)(),value:p,onValueChange:k,orientation:s,dir:h,activationMode:o,children:(0,m.jsx)(c.WV.div,{dir:h,"data-orientation":s,...v,ref:a})})});N.displayName=y;var C="TabsList",g=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...n}=e,l=k(C,t),s=p(t);return(0,m.jsx)(i.fC,{asChild:!0,...s,orientation:l.orientation,dir:l.dir,loop:r,children:(0,m.jsx)(c.WV.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:a})})});g.displayName=C;var w="TabsTrigger",x=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:n=!1,...s}=e,o=k(w,t),u=p(t),d=Z(o.baseId,r),f=E(o.baseId,r),y=r===o.value;return(0,m.jsx)(i.ck,{asChild:!0,...u,focusable:!n,active:y,children:(0,m.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":f,"data-state":y?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:d,...s,ref:a,onMouseDown:(0,l.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,l.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,l.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;y||n||!e||o.onValueChange(r)})})})});x.displayName=w;var T="TabsContent",z=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:n,forceMount:l,children:s,...i}=e,u=k(T,t),d=Z(u.baseId,n),f=E(u.baseId,n),y=n===u.value,v=r.useRef(y);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(o.z,{present:l||y,children:({present:t})=>(0,m.jsx)(c.WV.div,{"data-state":y?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!t,id:f,tabIndex:0,...i,ref:a,style:{...e.style,animationDuration:v.current?"0s":void 0},children:t&&s})})});function Z(e,a){return`${e}-trigger-${a}`}function E(e,a){return`${e}-content-${a}`}z.displayName=T;var M=t(605),R=t(3579);let j={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end"],responsive:!0},...M.EG,...R.K};var V=t(5236),q=t(5159);let I=r.forwardRef((e,a)=>{let{className:t,...l}=(0,V.y)(e,q.E);return r.createElement(N,{...l,ref:a,className:n("rt-TabsRoot",t)})});I.displayName="Tabs.Root";let L=r.forwardRef((e,a)=>{let{className:t,color:l,...s}=(0,V.y)(e,j,q.E);return r.createElement(g,{"data-accent-color":l,...s,asChild:!1,ref:a,className:n("rt-BaseTabList","rt-TabsList",t)})});L.displayName="Tabs.List";let A=r.forwardRef((e,a)=>{let{className:t,children:l,...s}=e;return r.createElement(x,{...s,asChild:!1,ref:a,className:n("rt-reset","rt-BaseTabListTrigger","rt-TabsTrigger",t)},r.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabsTriggerInner"},l),r.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden"},l))});A.displayName="Tabs.Trigger";let $=r.forwardRef((e,a)=>{let{className:t,...l}=(0,V.y)(e,q.E);return r.createElement(z,{...l,ref:a,className:n("rt-TabsContent",t)})});$.displayName="Tabs.Content"}}]);