"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[834],{1082:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},1171:function(e,t,n){n.d(t,{B:function(){return a}});var r=n(7653),o=n(4036),u=n(8556),i=n(432),l=n(7573);function a(e){let t=e+"CollectionProvider",[n,a]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:u,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",m=(0,i.Z8)(d),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),i=(0,u.e)(t,o.collectionRef);return(0,l.jsx)(m,{ref:i,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",w="data-radix-collection-item",N=(0,i.Z8)(v),g=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,a=r.useRef(null),c=(0,u.e)(t,a),f=s(v,n);return r.useEffect(()=>(f.itemMap.set(a,{ref:a,...i}),()=>void f.itemMap.delete(a))),(0,l.jsx)(N,{[w]:"",ref:c,children:o})});return g.displayName=v,[{Provider:f,Slot:p,ItemSlot:g},function(t){let n=s(e+"CollectionConsumer",t),o=r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${w}]`)),r=Array.from(n.itemMap.values()),o=r.sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current));return o},[n.collectionRef,n.itemMap]);return o},a]}},4036:function(e,t,n){n.d(t,{b:function(){return i},k:function(){return u}});var r=n(7653),o=n(7573);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),l=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,c=n?.[e]?.[l]||i,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:u})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[l]||i,c=r.useContext(a);if(c)return c;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e),u=o[`__scope${r}`];return{...t,...u}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},7205:function(e,t,n){n.d(t,{gm:function(){return i},zt:function(){return l}});var r=n(7653),o=n(7573),u=r.createContext(void 0);function i(e){let t=r.useContext(u);return e||t||"ltr"}var l=e=>{let{dir:t,children:n}=e;return(0,o.jsx)(u.Provider,{value:t,children:n})}},6303:function(e,t,n){n.d(t,{M:function(){return a}});var r,o=n(7653),u=n(1469),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function a(e){let[t,n]=o.useState(i());return(0,u.b)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},7575:function(e,t,n){n.d(t,{z:function(){return i}});var r=n(7653),o=n(8556),u=n(1469),i=e=>{let t,n;let{present:i,children:a}=e,c=function(e){var t;let[n,o]=r.useState(),i=r.useRef(null),a=r.useRef(e),c=r.useRef("none"),s=e?"mounted":"unmounted",[f,d]=(t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return r??e},s));return r.useEffect(()=>{let e=l(i.current);c.current="mounted"===f?e:"none"},[f]),(0,u.b)(()=>{let t=i.current,n=a.current,r=n!==e;if(r){let r=c.current,o=l(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,u.b)(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let o=l(i.current),u=o.includes(r.animationName);if(r.target===n&&u&&(d("ANIMATION_END"),!a.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},o=e=>{e.target===n&&(c.current=l(i.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(i),s="function"==typeof a?a({present:c.isPresent}):r.Children.only(a),f=(0,o.e)(c.ref,(t=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.ref:(t=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.props.ref:s.props.ref||s.ref),d="function"==typeof a;return d||c.isPresent?r.cloneElement(s,{ref:f}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},306:function(e,t,n){n.d(t,{Pc:function(){return M},ck:function(){return O},fC:function(){return S}});var r=n(7653),o=n(1082),u=n(1171),i=n(8556),l=n(4036),a=n(6303),c=n(8671),s=n(6418),f=n(7840),d=n(7205),m=n(7573),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[N,g,b]=(0,u.B)(w),[y,M]=(0,l.b)(w,[b]),[R,h]=y(w),E=r.forwardRef((e,t)=>(0,m.jsx)(N.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(N.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(C,{...e,ref:t})})}));E.displayName=w;var C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:u,loop:l=!1,dir:a,currentTabStopId:N,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:y,onEntryFocus:M,preventScrollOnEntryFocus:h=!1,...E}=e,C=r.useRef(null),T=(0,i.e)(t,C),x=(0,d.gm)(a),[I,S]=(0,f.T)({prop:N,defaultProp:b??null,onChange:y,caller:w}),[O,P]=r.useState(!1),_=(0,s.W)(M),D=g(n),F=r.useRef(!1),[j,k]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(p,_),()=>e.removeEventListener(p,_)},[_]),(0,m.jsx)(R,{scope:n,orientation:u,dir:x,loop:l,currentTabStopId:I,onItemFocus:r.useCallback(e=>S(e),[S]),onItemShiftTab:r.useCallback(()=>P(!0),[]),onFocusableItemAdd:r.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>k(e=>e-1),[]),children:(0,m.jsx)(c.WV.div,{tabIndex:O||0===j?-1:0,"data-orientation":u,...E,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!O){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable),t=e.find(e=>e.active),n=e.find(e=>e.id===I),r=[t,n,...e].filter(Boolean),o=r.map(e=>e.ref.current);A(o,h)}}F.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>P(!1))})})}),T="RovingFocusGroupItem",x=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:u=!0,active:i=!1,tabStopId:l,children:s,...f}=e,d=(0,a.M)(),p=l||d,v=h(T,n),w=v.currentTabStopId===p,b=g(n),{onFocusableItemAdd:y,onFocusableItemRemove:M,currentTabStopId:R}=v;return r.useEffect(()=>{if(u)return y(),()=>M()},[u,y,M]),(0,m.jsx)(N.ItemSlot,{scope:n,id:p,focusable:u,active:i,children:(0,m.jsx)(c.WV.span,{tabIndex:w?0:-1,"data-orientation":v.orientation,...f,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=b().filter(e=>e.focusable),u=o.map(e=>e.ref.current);if("last"===t)u.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&u.reverse();let o=u.indexOf(e.currentTarget);u=v.loop?(n=u,r=o+1,n.map((e,t)=>n[(r+t)%n.length])):u.slice(o+1)}setTimeout(()=>A(u))}}),children:"function"==typeof s?s({isCurrentTabStop:w,hasTabStop:null!=R}):s})})});x.displayName=T;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var S=E,O=x},6418:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(7653);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},7840:function(e,t,n){n.d(t,{T:function(){return l}});var r,o=n(7653),u=n(1469),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==n&&(l.current?.(n),u.current=n)},[n,u]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}let f=o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[c,e,l,a]);return[s,f]}Symbol("RADIX:SYNC_STATE")},1469:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(7653),o=globalThis?.document?r.useLayoutEffect:()=>{}},1467:function(e,t,n){n.d(t,{C:function(){return m}});var r=n(7653),o=n(8344),u=n(432),i=n(2741),l=n(605),a=n(3579),c=n(4494);let s={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft","surface","outline"],default:"soft"},...l.o3,...a.K,...c.I};var f=n(5236),d=n(5159);let m=r.forwardRef((e,t)=>{let{asChild:n,className:i,color:l,radius:a,...c}=(0,f.y)(e,s,d.E),m=n?u.fC:"span";return r.createElement(m,{"data-accent-color":l,"data-radius":a,...c,ref:t,className:o("rt-reset","rt-Badge",i)})});m.displayName="Badge"}}]);