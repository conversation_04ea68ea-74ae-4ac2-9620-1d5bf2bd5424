"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[396],{3484:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},214:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1233:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5438:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},2480:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3696:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("FileJson",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 12a1 1 0 0 0-1 1v1a1 1 0 0 1-1 1 1 1 0 0 1 1 1v1a1 1 0 0 0 1 1",key:"1oajmo"}],["path",{d:"M14 18a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1 1 1 0 0 1-1-1v-1a1 1 0 0 0-1-1",key:"mpwhp6"}]])},3511:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},3682:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},9576:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},6017:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]])},248:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},966:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},5105:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},2151:function(e,t,i){i.d(t,{Z:function(){return n}});var r=i(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},8881:function(e,t,i){i.d(t,{eJ:function(){return rx}});let r=0;class n{constructor(e,t){this.from=e,this.to=t}}class s{constructor(e={}){this.id=r++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=l.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}s.closedBy=new s({deserialize:e=>e.split(" ")}),s.openedBy=new s({deserialize:e=>e.split(" ")}),s.group=new s({deserialize:e=>e.split(" ")}),s.isolate=new s({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw RangeError("Invalid value for isolate: "+e);return e||"auto"}}),s.contextHash=new s({perNode:!0}),s.lookAhead=new s({perNode:!0}),s.mounted=new s({perNode:!0});class o{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[s.mounted.id]}}let a=Object.create(null);class l{constructor(e,t,i,r=0){this.name=e,this.props=t,this.id=i,this.flags=r}static define(e){let t=e.props&&e.props.length?Object.create(null):a,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),r=new l(e.name||"",t,e.id,i);if(e.props){for(let i of e.props)if(Array.isArray(i)||(i=i(r)),i){if(i[0].perNode)throw RangeError("Can't store a per-node prop on a node type");t[i[0].id]=i[1]}}return r}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(s.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let r of i.split(" "))t[r]=e[i];return e=>{for(let i=e.prop(s.group),r=-1;r<(i?i.length:0);r++){let n=t[r<0?e.name:i[r]];if(n)return n}}}}l.none=new l("",Object.create(null),0,8);class h{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let r=null;for(let t of e){let e=t(i);e&&(r||(r=Object.assign({},i.props)),r[e[0].id]=e[1])}t.push(r?new l(i.name,r,i.id,i.flags):i)}return new h(t)}}let c=new WeakMap,f=new WeakMap;(A=R||(R={}))[A.ExcludeBuffers=1]="ExcludeBuffers",A[A.IncludeAnonymous=2]="IncludeAnonymous",A[A.IgnoreMounts=4]="IgnoreMounts",A[A.IgnoreOverlays=8]="IgnoreOverlays";class u{constructor(e,t,i,r,n){if(this.type=e,this.children=t,this.positions=i,this.length=r,this.props=null,n&&n.length)for(let[e,t]of(this.props=Object.create(null),n))this.props["number"==typeof e?e:e.id]=t}toString(){let e=o.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let i=e.toString();i&&(t&&(t+=","),t+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new w(this.topNode,e)}cursorAt(e,t=0,i=0){let r=c.get(this)||this.topNode,n=new w(r);return n.moveTo(e,t),c.set(this,n._tree),n}get topNode(){return new y(this,0,0,null)}resolve(e,t=0){let i=g(c.get(this)||this.topNode,e,t,!1);return c.set(this,i),i}resolveInner(e,t=0){let i=g(f.get(this)||this.topNode,e,t,!0);return f.set(this,i),i}resolveStack(e,t=0){return function(e,t,i){let r=e.resolveInner(t,i),n=null;for(let e=r instanceof y?r:r.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(n||(n=[r])).push(s.resolve(t,i)),e=s}else{let s=o.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new y(s.tree,s.overlay[0].from+e.from,-1,e);(n||(n=[r])).push(g(o,t,i,!1))}}return n?v(n):r}(this,e,t)}iterate(e){let{enter:t,leave:i,from:r=0,to:n=this.length}=e,s=e.mode||0,o=(s&R.IncludeAnonymous)>0;for(let e=this.cursor(s|R.IncludeAnonymous);;){let s=!1;if(e.from<=n&&e.to>=r&&(!o&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&i&&(o||!e.type.isAnonymous)&&i(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:T(l.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new u(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new u(l.none,e,t,i)))}static build(e){return function(e){var t;let{buffer:i,nodeSet:r,maxBufferLength:n=1024,reused:o=[],minRepeatType:a=r.types.length}=e,l=Array.isArray(i)?new p(i,i.length):i,h=r.types,c=0,f=0;function d(e,t,i,n,s,o,a,l,h){let c=[],f=[];for(;e.length>n;)c.push(e.pop()),f.push(t.pop()+i-s);e.push(g(r.types[a],c,f,o-s,l-o,h)),t.push(s-i)}function g(e,t,i,r,n,o,a){if(o){let e=[s.contextHash,o];a=a?[e].concat(a):[e]}if(n>25){let e=[s.lookAhead,n];a=a?[e].concat(a):[e]}return new u(e,t,i,r,a)}let m=[],y=[];for(;l.pos>0;)!function e(t,i,p,m,y,x){let{id:k,start:Q,end:b,size:v}=l,S=f,w=c;for(;v<0;){if(l.next(),-1==v){let e=o[k];p.push(e),m.push(Q-t);return}if(-3==v){c=k;return}if(-4==v){f=k;return}throw RangeError(`Unrecognized record size: ${v}`)}let $=h[k],P,Z,_=Q-t;if(b-Q<=n&&(Z=function(e,t){let i=l.fork(),r=0,s=0,o=0,h=i.end-n,c={size:0,start:0,skip:0};e:for(let n=i.pos-e;i.pos>n;){let e=i.size;if(i.id==t&&e>=0){c.size=r,c.start=s,c.skip=o,o+=4,r+=4,i.next();continue}let l=i.pos-e;if(e<0||l<n||i.start<h)break;let f=i.id>=a?4:0,u=i.start;for(i.next();i.pos>l;){if(i.size<0){if(-3==i.size)f+=4;else break e}else i.id>=a&&(f+=4);i.next()}s=u,r+=e,o+=f}return(t<0||r==e)&&(c.size=r,c.start=s,c.skip=o),c.size>4?c:void 0}(l.pos-i,y))){let e=new Uint16Array(Z.size-Z.skip),i=l.pos-Z.size,n=e.length;for(;l.pos>i;)n=function e(t,i,r){let{id:n,start:s,end:o,size:h}=l;if(l.next(),h>=0&&n<a){let a=r;if(h>4){let n=l.pos-(h-4);for(;l.pos>n;)r=e(t,i,r)}i[--r]=a,i[--r]=o-t,i[--r]=s-t,i[--r]=n}else -3==h?c=n:-4==h&&(f=n);return r}(Z.start,e,n);P=new O(e,b-Z.start,r),_=Z.start-t}else{let t=l.pos-v;l.next();let i=[],o=[],h=k>=a?k:-1,c=0,f=b;for(;l.pos>t;)h>=0&&l.id==h&&l.size>=0?(l.end<=f-n&&(d(i,o,Q,c,l.end,f,h,S,w),c=i.length,f=l.end),l.next()):x>2500?function(e,t,i,s){let o=[],a=0,h=-1;for(;l.pos>t;){let{id:e,start:t,end:i,size:r}=l;if(r>4)l.next();else if(h>-1&&t<h)break;else h<0&&(h=i-n),o.push(e,t,i),a++,l.next()}if(a){let t=new Uint16Array(4*a),n=o[o.length-2];for(let e=o.length-3,i=0;e>=0;e-=3)t[i++]=o[e],t[i++]=o[e+1]-n,t[i++]=o[e+2]-n,t[i++]=i;i.push(new O(t,o[2]-n,r)),s.push(n-e)}}(Q,t,i,o):e(Q,t,i,o,h,x+1);if(h>=0&&c>0&&c<i.length&&d(i,o,Q,c,Q,f,h,S,w),i.reverse(),o.reverse(),h>-1&&c>0){let e=function(e,t){return(i,r,n)=>{let o=0,a=i.length-1,l,h;if(a>=0&&(l=i[a])instanceof u){if(!a&&l.type==e&&l.length==n)return l;(h=l.prop(s.lookAhead))&&(o=r[a]+l.length+h)}return g(e,i,r,n,o,t)}}($,w);P=T($,i,o,0,i.length,0,b-Q,e,e)}else P=g($,i,o,b-Q,S-b,w)}p.push(P),m.push(_)}(e.start||0,e.bufferStart||0,m,y,-1,0);let x=null!==(t=e.length)&&void 0!==t?t:m.length?y[0]+m[0].length:0;return new u(h[e.topID],m.reverse(),y.reverse(),x)}(e)}}u.empty=new u(l.none,[],[],0);class p{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new p(this.buffer,this.index)}}class O{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return l.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],r=this.set.types[t],n=r.name;if(/\W/.test(n)&&!r.isError&&(n=JSON.stringify(n)),i==(e+=4))return n;let s=[];for(;e<i;)s.push(this.childString(e)),e=this.buffer[e+3];return n+"("+s.join(",")+")"}findChild(e,t,i,r,n){let{buffer:s}=this,o=-1;for(let a=e;a!=t&&(!d(n,r,s[a+1],s[a+2])||(o=a,!(i>0)));a=s[a+3]);return o}slice(e,t,i){let r=this.buffer,n=new Uint16Array(t-e),s=0;for(let o=e,a=0;o<t;){n[a++]=r[o++],n[a++]=r[o++]-i;let t=n[a++]=r[o++]-i;n[a++]=r[o++]-e,s=Math.max(s,t)}return new O(n,s,this.set)}}function d(e,t,i,r){switch(e){case -2:return i<t;case -1:return r>=t&&i<t;case 0:return i<t&&r>t;case 1:return i<=t&&r>t;case 2:return r>t;case 4:return!0}}function g(e,t,i,r){for(var n;e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t);){let t=!r&&e instanceof y&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=r?0:R.IgnoreOverlays;if(r)for(let r=e,o=r.parent;o;o=(r=o).parent)r instanceof y&&r.index<0&&(null===(n=o.enter(t,i,s))||void 0===n?void 0:n.from)!=r.from&&(e=o);for(;;){let r=e.enter(t,i,s);if(!r)return e;e=r}}class m{cursor(e=0){return new w(this,e)}getChild(e,t=null,i=null){let r=x(this,e,t,i);return r.length?r[0]:null}getChildren(e,t=null,i=null){return x(this,e,t,i)}resolve(e,t=0){return g(this,e,t,!1)}resolveInner(e,t=0){return g(this,e,t,!0)}matchContext(e){return k(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(i=t,t=e.prevSibling):t=e}return i}get node(){return this}get next(){return this.parent}}class y extends m{constructor(e,t,i,r){super(),this._tree=e,this.from=t,this.index=i,this._parent=r}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,r,n=0){for(let s=this;;){for(let{children:a,positions:l}=s._tree,h=t>0?a.length:-1;e!=h;e+=t){let h=a[e],c=l[e]+s.from;if(d(r,i,c,c+h.length)){if(h instanceof O){if(n&R.ExcludeBuffers)continue;let o=h.findChild(0,h.buffer.length,t,i-c,r);if(o>-1)return new b(new Q(s,h,e,c),null,o)}else if(n&R.IncludeAnonymous||!h.type.isAnonymous||$(h)){let a;if(!(n&R.IgnoreMounts)&&(a=o.get(h))&&!a.overlay)return new y(a.tree,c,e,s);let l=new y(h,c,e,s);return n&R.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(t<0?h.children.length-1:0,t,i,r)}}}if(n&R.IncludeAnonymous||!s.type.isAnonymous||(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,!(s=s._parent)))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let r;if(!(i&R.IgnoreOverlays)&&(r=o.get(this._tree))&&r.overlay){let i=e-this.from;for(let{from:e,to:n}of r.overlay)if((t>0?e<=i:e<i)&&(t<0?n>=i:n>i))return new y(r.tree,r.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function x(e,t,i,r){let n=e.cursor(),s=[];if(!n.firstChild())return s;if(null!=i){for(let e=!1;!e;)if(e=n.type.is(i),!n.nextSibling())return s}for(;;){if(null!=r&&n.type.is(r))return s;if(n.type.is(t)&&s.push(n.node),!n.nextSibling())return null==r?s:[]}}function k(e,t,i=t.length-1){for(let r=e;i>=0;r=r.parent){if(!r)return!1;if(!r.type.isAnonymous){if(t[i]&&t[i]!=r.name)return!1;i--}}return!0}class Q{constructor(e,t,i,r){this.parent=e,this.buffer=t,this.index=i,this.start=r}}class b extends m{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.context.start,i);return n<0?null:new b(this.context,this,n)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&R.ExcludeBuffers)return null;let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return n<0?null:new b(this.context,this,n)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new b(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new b(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,r=this.index+4,n=i.buffer[this.index+3];if(n>r){let s=i.buffer[this.index+1];e.push(i.slice(r,n,s)),t.push(0)}return new u(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function v(e){if(!e.length)return null;let t=0,i=e[0];for(let r=1;r<e.length;r++){let n=e[r];(n.from>i.from||n.to<i.to)&&(i=n,t=r)}let r=i instanceof y&&i.index<0?null:i.parent,n=e.slice();return r?n[t]=r:n.splice(t,1),new S(n,i)}class S{constructor(e,t){this.heads=e,this.node=t}get next(){return v(this.heads)}}class w{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof y)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:r}=this.buffer;return this.type=t||r.set.types[r.buffer[e]],this.from=i+r.buffer[e+1],this.to=i+r.buffer[e+2],!0}yield(e){return!!e&&(e instanceof y?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:r}=this.buffer,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.buffer.start,i);return!(n<0)&&(this.stack.push(this.index),this.yieldBuf(n))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&R.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&R.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&R.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:r}=this;if(r){if(e>0){if(this.index<r.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(r.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=r)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,n=e<0?-1:i._tree.children.length;r!=n;r+=e){let e=i._tree.children[r];if(this.mode&R.IncludeAnonymous||e instanceof O||!e.type.isAnonymous||$(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let r=this.index,n=this.stack.length;n>=0;){for(let s=e;s;s=s._parent)if(s.index==r){if(r==this.index)return s;t=s,i=n+1;break e}r=this.stack[--n]}for(let e=i;e<this.stack.length;e++)t=new b(this.buffer,t,this.stack[e]);return this.bufferNode=new b(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let r=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(r=!0)}for(;;){if(r&&t&&t(this),r=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,r=!0}}}matchContext(e){if(!this.buffer)return k(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let r=e.length-1,n=this.stack.length-1;r>=0;n--){if(n<0)return k(this._tree,e,r);let s=i[t.buffer[this.stack[n]]];if(!s.isAnonymous){if(e[r]&&e[r]!=s.name)return!1;r--}}return!0}}function $(e){return e.children.some(e=>e instanceof O||!e.type.isAnonymous||$(e))}let P=new WeakMap;function Z(e,t){if(!e.isAnonymous||t instanceof O||t.type!=e)return 1;let i=P.get(t);if(null==i){for(let r of(i=1,t.children)){if(r.type!=e||!(r instanceof u)){i=1;break}i+=Z(e,r)}P.set(t,i)}return i}function T(e,t,i,r,n,s,o,a,l){let h=0;for(let i=r;i<n;i++)h+=Z(e,t[i]);let c=Math.ceil(1.5*h/8),f=[],u=[];return!function t(i,r,n,o,a){for(let h=n;h<o;){let n=h,p=r[h],O=Z(e,i[h]);for(h++;h<o;h++){let t=Z(e,i[h]);if(O+t>=c)break;O+=t}if(h==n+1){if(O>c){let e=i[n];t(e.children,e.positions,0,e.children.length,r[n]+a);continue}f.push(i[n])}else{let t=r[h-1]+i[h-1].length-p;f.push(T(e,i,r,n,h,p,t,null,l))}u.push(p+a-s)}}(t,i,r,n,0),(a||l)(f,u,o)}class _{startParse(e,t,i){return"string"==typeof e&&(e=new X(e)),i=i?i.length?i.map(e=>new n(e.from,e.to)):[new n(0,0)]:[new n(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let r=this.startParse(e,t,i);for(;;){let e=r.advance();if(e)return e}}}class X{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new s({perNode:!0});var C,A,R,q,M,j=i(4850);class Y{constructor(e,t,i,r,n,s,o,a,l,h=0,c){this.p=e,this.stack=t,this.state=i,this.reducePos=r,this.pos=n,this.score=s,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=h,this.parent=c}toString(){return`[${this.stack.filter((e,t)=>t%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(e,t,i=0){let r=e.parser.context;return new Y(e,[],t,i,i,0,[],0,r?new N(r,r.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(e,t){this.stack.push(this.state,t,this.bufferBase+this.buffer.length),this.state=e}reduce(e){var t;let i=e>>19,r=65535&e,{parser:n}=this.p,s=this.reducePos<this.pos-25;s&&this.setLookAhead(this.pos);let o=n.dynamicPrecedence(r);if(o&&(this.score+=o),0==i){this.pushState(n.getGoto(this.state,r,!0),this.reducePos),r<n.minRepeatTerm&&this.storeNode(r,this.reducePos,this.reducePos,s?8:4,!0),this.reduceContext(r,this.reducePos);return}let a=this.stack.length-(i-1)*3-(262144&e?6:0),l=a?this.stack[a-2]:this.p.ranges[0].from,h=this.reducePos-l;h>=2e3&&!(null===(t=this.p.parser.nodeSet.types[r])||void 0===t?void 0:t.isAnonymous)&&(l==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=h):this.p.lastBigReductionSize<h&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=l,this.p.lastBigReductionSize=h));let c=a?this.stack[a-1]:0,f=this.bufferBase+this.buffer.length-c;if(r<n.minRepeatTerm||131072&e){let e=n.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(r,l,e,f+4,!0)}if(262144&e)this.state=this.stack[a];else{let e=this.stack[a-3];this.state=n.getGoto(e,r,!0)}for(;this.stack.length>a;)this.stack.pop();this.reduceContext(r,l)}storeNode(e,t,i,r=4,n=!1){if(0==e&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let e=this,r=this.buffer.length;if(0==r&&e.parent&&(r=e.bufferBase-e.parent.bufferBase,e=e.parent),r>0&&0==e.buffer[r-4]&&e.buffer[r-1]>-1){if(t==i)return;if(e.buffer[r-2]>=t){e.buffer[r-2]=i;return}}}if(n&&this.pos!=i){let n=this.buffer.length;if(n>0&&0!=this.buffer[n-4]){let e=!1;for(let t=n;t>0&&this.buffer[t-2]>i;t-=4)if(this.buffer[t-1]>=0){e=!0;break}if(e)for(;n>0&&this.buffer[n-2]>i;)this.buffer[n]=this.buffer[n-4],this.buffer[n+1]=this.buffer[n-3],this.buffer[n+2]=this.buffer[n-2],this.buffer[n+3]=this.buffer[n-1],n-=4,r>4&&(r-=4)}this.buffer[n]=e,this.buffer[n+1]=t,this.buffer[n+2]=i,this.buffer[n+3]=r}else this.buffer.push(e,t,i,r)}shift(e,t,i,r){if(131072&e)this.pushState(65535&e,this.pos);else if((262144&e)==0){let{parser:n}=this.p;(r>this.pos||t<=n.maxNode)&&(this.pos=r,n.stateFlag(e,1)||(this.reducePos=r)),this.pushState(e,i),this.shiftContext(t,i),t<=n.maxNode&&this.buffer.push(t,i,r,4)}else this.pos=r,this.shiftContext(t,i),t<=this.p.parser.maxNode&&this.buffer.push(t,i,r,4)}apply(e,t,i,r){65536&e?this.reduce(e):this.shift(e,t,i,r)}useNode(e,t){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=e)&&(this.p.reused.push(e),i++);let r=this.pos;this.reducePos=this.pos=r+e.length,this.pushState(t,r),this.buffer.push(i,r,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,e,this,this.p.stream.reset(this.pos-e.length)))}split(){let e=this,t=e.buffer.length;for(;t>0&&e.buffer[t-2]>e.reducePos;)t-=4;let i=e.buffer.slice(t),r=e.bufferBase+t;for(;e&&r==e.bufferBase;)e=e.parent;return new Y(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,r,this.curContext,this.lookAhead,e)}recoverByDelete(e,t){let i=e<=this.p.parser.maxNode;i&&this.storeNode(e,this.pos,t,4),this.storeNode(0,this.pos,t,i?8:4),this.pos=this.reducePos=t,this.score-=190}canShift(e){for(let t=new I(this);;){let i=this.p.parser.stateSlot(t.state,4)||this.p.parser.hasAction(t.state,e);if(0==i)return!1;if((65536&i)==0)return!0;t.reduce(i)}}recoverByInsert(e){if(this.stack.length>=300)return[];let t=this.p.parser.nextStates(this.state);if(t.length>8||this.stack.length>=120){let i=[];for(let r=0,n;r<t.length;r+=2)(n=t[r+1])!=this.state&&this.p.parser.hasAction(n,e)&&i.push(t[r],n);if(this.stack.length<120)for(let e=0;i.length<8&&e<t.length;e+=2){let r=t[e+1];i.some((e,t)=>1&t&&e==r)||i.push(t[e],r)}t=i}let i=[];for(let e=0;e<t.length&&i.length<4;e+=2){let r=t[e+1];if(r==this.state)continue;let n=this.split();n.pushState(r,this.pos),n.storeNode(0,n.pos,n.pos,4,!0),n.shiftContext(t[e],this.pos),n.reducePos=this.pos,n.score-=200,i.push(n)}return i}forceReduce(){let{parser:e}=this.p,t=e.stateSlot(this.state,5);if((65536&t)==0)return!1;if(!e.validAction(this.state,t)){let i=t>>19,r=65535&t,n=this.stack.length-3*i;if(n<0||0>e.getGoto(this.stack[n],r,!1)){let e=this.findForcedReduction();if(null==e)return!1;t=e}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(t),!0}findForcedReduction(){let{parser:e}=this.p,t=[],i=(r,n)=>{if(!t.includes(r))return t.push(r),e.allActions(r,t=>{if(393216&t);else if(65536&t){let i=(t>>19)-n;if(i>1){let r=65535&t,n=this.stack.length-3*i;if(n>=0&&e.getGoto(this.stack[n],r,!1)>=0)return i<<19|65536|r}}else{let e=i(t,n+1);if(null!=e)return e}})};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:e}=this.p;return 65535==e.data[e.stateSlot(this.state,1)]&&!e.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(e){if(this.state!=e.state||this.stack.length!=e.stack.length)return!1;for(let t=0;t<this.stack.length;t+=3)if(this.stack[t]!=e.stack[t])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(e){return this.p.parser.dialect.flags[e]}shiftContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,e,this,this.p.stream.reset(t)))}reduceContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,e,this,this.p.stream.reset(t)))}emitContext(){let e=this.buffer.length-1;(e<0||-3!=this.buffer[e])&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let e=this.buffer.length-1;(e<0||-4!=this.buffer[e])&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(e){if(e!=this.curContext.context){let t=new N(this.curContext.tracker,e);t.hash!=this.curContext.hash&&this.emitContext(),this.curContext=t}}setLookAhead(e){e>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=e)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class N{constructor(e,t){this.tracker=e,this.context=t,this.hash=e.strict?e.hash(t):0}}class I{constructor(e){this.start=e,this.state=e.state,this.stack=e.stack,this.base=this.stack.length}reduce(e){let t=e>>19;0==t?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=(t-1)*3;let i=this.start.p.parser.getGoto(this.stack[this.base-3],65535&e,!0);this.state=i}}class E{constructor(e,t,i){this.stack=e,this.pos=t,this.index=i,this.buffer=e.buffer,0==this.index&&this.maybeNext()}static create(e,t=e.bufferBase+e.buffer.length){return new E(e,t,t-e.bufferBase)}maybeNext(){let e=this.stack.parent;null!=e&&(this.index=this.stack.bufferBase-e.bufferBase,this.stack=e,this.buffer=e.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new E(this.stack,this.pos,this.index)}}function z(e,t=Uint16Array){if("string"!=typeof e)return e;let i=null;for(let r=0,n=0;r<e.length;){let s=0;for(;;){let t=e.charCodeAt(r++),i=!1;if(126==t){s=65535;break}t>=92&&t--,t>=34&&t--;let n=t-32;if(n>=46&&(n-=46,i=!0),s+=n,i)break;s*=46}i?i[n++]=s:i=new t(s)}return i}class V{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}let L=new V;class W{constructor(e,t){this.input=e,this.ranges=t,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=L,this.rangeIndex=0,this.pos=this.chunkPos=t[0].from,this.range=t[0],this.end=t[t.length-1].to,this.readNext()}resolveOffset(e,t){let i=this.range,r=this.rangeIndex,n=this.pos+e;for(;n<i.from;){if(!r)return null;let e=this.ranges[--r];n-=i.from-e.to,i=e}for(;t<0?n>i.to:n>=i.to;){if(r==this.ranges.length-1)return null;let e=this.ranges[++r];n+=e.from-i.to,i=e}return n}clipPos(e){if(e>=this.range.from&&e<this.range.to)return e;for(let t of this.ranges)if(t.to>e)return Math.max(e,t.from);return this.end}peek(e){let t=this.chunkOff+e,i,r;if(t>=0&&t<this.chunk.length)i=this.pos+e,r=this.chunk.charCodeAt(t);else{let t=this.resolveOffset(e,1);if(null==t)return -1;if((i=t)>=this.chunk2Pos&&i<this.chunk2Pos+this.chunk2.length)r=this.chunk2.charCodeAt(i-this.chunk2Pos);else{let e=this.rangeIndex,t=this.range;for(;t.to<=i;)t=this.ranges[++e];this.chunk2=this.input.chunk(this.chunk2Pos=i),i+this.chunk2.length>t.to&&(this.chunk2=this.chunk2.slice(0,t.to-i)),r=this.chunk2.charCodeAt(0)}}return i>=this.token.lookAhead&&(this.token.lookAhead=i+1),r}acceptToken(e,t=0){let i=t?this.resolveOffset(t,-1):this.pos;if(null==i||i<this.token.start)throw RangeError("Token end out of bounds");this.token.value=e,this.token.end=i}acceptTokenTo(e,t){this.token.value=e,this.token.end=t}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:e,chunkPos:t}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=e,this.chunk2Pos=t,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let e=this.input.chunk(this.pos),t=this.pos+e.length;this.chunk=t>this.range.to?e.slice(0,this.range.to-this.pos):e,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(e=1){for(this.chunkOff+=e;this.pos+e>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();e-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=e,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(e,t){if(t?(this.token=t,t.start=e,t.lookAhead=e+1,t.value=t.extended=-1):this.token=L,this.pos!=e){if(this.pos=e,e==this.end)return this.setDone(),this;for(;e<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;e>=this.range.to;)this.range=this.ranges[++this.rangeIndex];e>=this.chunkPos&&e<this.chunkPos+this.chunk.length?this.chunkOff=e-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(e,t){if(e>=this.chunkPos&&t<=this.chunkPos+this.chunk.length)return this.chunk.slice(e-this.chunkPos,t-this.chunkPos);if(e>=this.chunk2Pos&&t<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(e-this.chunk2Pos,t-this.chunk2Pos);if(e>=this.range.from&&t<=this.range.to)return this.input.read(e,t);let i="";for(let r of this.ranges){if(r.from>=t)break;r.to>e&&(i+=this.input.read(Math.max(r.from,e),Math.min(r.to,t)))}return i}}class G{constructor(e,t){this.data=e,this.id=t}token(e,t){let{parser:i}=t.p;D(this.data,e,t,this.id,i.data,i.tokenPrecTable)}}G.prototype.contextual=G.prototype.fallback=G.prototype.extend=!1;class B{constructor(e,t,i){this.precTable=t,this.elseToken=i,this.data="string"==typeof e?z(e):e}token(e,t){let i=e.pos,r=0;for(;;){let i=e.next<0,n=e.resolveOffset(1,1);if(D(this.data,e,t,0,this.data,this.precTable),e.token.value>-1)break;if(null==this.elseToken)return;if(!i&&r++,null==n)break;e.reset(n,e.token)}r&&(e.reset(i,e.token),e.acceptToken(this.elseToken,r))}}B.prototype.contextual=G.prototype.fallback=G.prototype.extend=!1;class U{constructor(e,t={}){this.token=e,this.contextual=!!t.contextual,this.fallback=!!t.fallback,this.extend=!!t.extend}}function D(e,t,i,r,n,s){let o=0,a=1<<r,{dialect:l}=i.p.parser;e:for(;(a&e[o])!=0;){let i=e[o+1];for(let r=o+3;r<i;r+=2)if((e[r+1]&a)>0){let i=e[r];if(l.allows(i)&&(-1==t.token.value||t.token.value==i||function(e,t,i,r){let n=J(i,r,t);return n<0||J(i,r,e)<n}(i,t.token.value,n,s))){t.acceptToken(i);break}}let r=t.next,h=0,c=e[o+2];if(t.next<0&&c>h&&65535==e[i+3*c-3]){o=e[i+3*c-1];continue}for(;h<c;){let n=h+c>>1,s=i+n+(n<<1),a=e[s],l=e[s+1]||65536;if(r<a)c=n;else if(r>=l)h=n+1;else{o=e[s+2],t.advance();continue e}}break}}function J(e,t,i){for(let r=t,n;65535!=(n=e[r]);r++)if(n==i)return r-t;return -1}let F=void 0!==j&&j.env&&/\bparse\b/.test(j.env.LOG),K=null;function H(e,t,i){let r=e.cursor(R.IncludeAnonymous);for(r.moveTo(t);;)if(!(i<0?r.childBefore(t):r.childAfter(t)))for(;;){if((i<0?r.to<t:r.from>t)&&!r.type.isError)return i<0?Math.max(0,Math.min(r.to-1,t-25)):Math.min(e.length,Math.max(r.from+1,t+25));if(i<0?r.prevSibling():r.nextSibling())break;if(!r.parent())return i<0?0:e.length}}class ee{constructor(e,t){this.fragments=e,this.nodeSet=t,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let e=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(e){for(this.safeFrom=e.openStart?H(e.tree,e.from+e.offset,1)-e.offset:e.from,this.safeTo=e.openEnd?H(e.tree,e.to+e.offset,-1)-e.offset:e.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(e.tree),this.start.push(-e.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(e){if(e<this.nextStart)return null;for(;this.fragment&&this.safeTo<=e;)this.nextFragment();if(!this.fragment)return null;for(;;){let t=this.trees.length-1;if(t<0)return this.nextFragment(),null;let i=this.trees[t],r=this.index[t];if(r==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let n=i.children[r],o=this.start[t]+i.positions[r];if(o>e)return this.nextStart=o,null;if(n instanceof u){if(o==e){if(o<this.safeFrom)return null;let e=o+n.length;if(e<=this.safeTo){let t=n.prop(s.lookAhead);if(!t||e+t<this.fragment.to)return n}}this.index[t]++,o+n.length>=Math.max(this.safeFrom,e)&&(this.trees.push(n),this.start.push(o),this.index.push(0))}else this.index[t]++,this.nextStart=o+n.length}}}class et{constructor(e,t){this.stream=t,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=e.tokenizers.map(e=>new V)}getActions(e){let t=0,i=null,{parser:r}=e.p,{tokenizers:n}=r,s=r.stateSlot(e.state,3),o=e.curContext?e.curContext.hash:0,a=0;for(let r=0;r<n.length;r++){if((1<<r&s)==0)continue;let l=n[r],h=this.tokens[r];if((!i||l.fallback)&&((l.contextual||h.start!=e.pos||h.mask!=s||h.context!=o)&&(this.updateCachedToken(h,l,e),h.mask=s,h.context=o),h.lookAhead>h.end+25&&(a=Math.max(h.lookAhead,a)),0!=h.value)){let r=t;if(h.extended>-1&&(t=this.addActions(e,h.extended,h.end,t)),t=this.addActions(e,h.value,h.end,t),!l.extend&&(i=h,t>r))break}}for(;this.actions.length>t;)this.actions.pop();return a&&e.setLookAhead(a),i||e.pos!=this.stream.end||((i=new V).value=e.p.parser.eofTerm,i.start=i.end=e.pos,t=this.addActions(e,i.value,i.end,t)),this.mainToken=i,this.actions}getMainToken(e){if(this.mainToken)return this.mainToken;let t=new V,{pos:i,p:r}=e;return t.start=i,t.end=Math.min(i+1,r.stream.end),t.value=i==r.stream.end?r.parser.eofTerm:0,t}updateCachedToken(e,t,i){let r=this.stream.clipPos(i.pos);if(t.token(this.stream.reset(r,e),i),e.value>-1){let{parser:t}=i.p;for(let r=0;r<t.specialized.length;r++)if(t.specialized[r]==e.value){let n=t.specializers[r](this.stream.read(e.start,e.end),i);if(n>=0&&i.p.parser.dialect.allows(n>>1)){(1&n)==0?e.value=n>>1:e.extended=n>>1;break}}}else e.value=0,e.end=this.stream.clipPos(r+1)}putAction(e,t,i,r){for(let t=0;t<r;t+=3)if(this.actions[t]==e)return r;return this.actions[r++]=e,this.actions[r++]=t,this.actions[r++]=i,r}addActions(e,t,i,r){let{state:n}=e,{parser:s}=e.p,{data:o}=s;for(let e=0;e<2;e++)for(let a=s.stateSlot(n,e?2:1);;a+=3){if(65535==o[a]){if(1==o[a+1])a=ea(o,a+2);else{0==r&&2==o[a+1]&&(r=this.putAction(ea(o,a+2),t,i,r));break}}o[a]==t&&(r=this.putAction(ea(o,a+1),t,i,r))}return r}}class ei{constructor(e,t,i,r){this.parser=e,this.input=t,this.ranges=r,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new W(t,r),this.tokens=new et(e,this.stream),this.topTerm=e.top[1];let{from:n}=r[0];this.stacks=[Y.start(this,e.top[0],n)],this.fragments=i.length&&this.stream.end-n>4*e.bufferLength?new ee(i,e.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let e,t,i=this.stacks,r=this.minStackPos,n=this.stacks=[];if(this.bigReductionCount>300&&1==i.length){let[e]=i;for(;e.forceReduce()&&e.stack.length&&e.stack[e.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let s=0;s<i.length;s++){let o=i[s];for(;;){if(this.tokens.mainToken=null,o.pos>r)n.push(o);else{if(this.advanceStack(o,n,i))continue;e||(e=[],t=[]),e.push(o);let r=this.tokens.getMainToken(o);t.push(r.value,r.end)}break}}if(!n.length){let t=e&&function(e){let t=null;for(let i of e){let e=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=e&&i.pos>e)&&i.p.parser.stateFlag(i.state,2)&&(!t||t.score<i.score)&&(t=i)}return t}(e);if(t)return F&&console.log("Finish with "+this.stackID(t)),this.stackToTree(t);if(this.parser.strict)throw F&&e&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),SyntaxError("No parse at "+r);this.recovering||(this.recovering=5)}if(this.recovering&&e){let i=null!=this.stoppedAt&&e[0].pos>this.stoppedAt?e[0]:this.runRecovery(e,t,n);if(i)return F&&console.log("Force-finish "+this.stackID(i)),this.stackToTree(i.forceAll())}if(this.recovering){let e=1==this.recovering?1:3*this.recovering;if(n.length>e)for(n.sort((e,t)=>t.score-e.score);n.length>e;)n.pop();n.some(e=>e.reducePos>r)&&this.recovering--}else if(n.length>1){t:for(let e=0;e<n.length-1;e++){let t=n[e];for(let i=e+1;i<n.length;i++){let r=n[i];if(t.sameState(r)||t.buffer.length>500&&r.buffer.length>500){if((t.score-r.score||t.buffer.length-r.buffer.length)>0)n.splice(i--,1);else{n.splice(e--,1);continue t}}}}n.length>12&&n.splice(12,n.length-12)}this.minStackPos=n[0].pos;for(let e=1;e<n.length;e++)n[e].pos<this.minStackPos&&(this.minStackPos=n[e].pos);return null}stopAt(e){if(null!=this.stoppedAt&&this.stoppedAt<e)throw RangeError("Can't move stoppedAt forward");this.stoppedAt=e}advanceStack(e,t,i){let r=e.pos,{parser:n}=this,o=F?this.stackID(e)+" -> ":"";if(null!=this.stoppedAt&&r>this.stoppedAt)return e.forceReduce()?e:null;if(this.fragments){let t=e.curContext&&e.curContext.tracker.strict,i=t?e.curContext.hash:0;for(let a=this.fragments.nodeAt(r);a;){let r=this.parser.nodeSet.types[a.type.id]==a.type?n.getGoto(e.state,a.type.id):-1;if(r>-1&&a.length&&(!t||(a.prop(s.contextHash)||0)==i))return e.useNode(a,r),F&&console.log(o+this.stackID(e)+` (via reuse of ${n.getName(a.type.id)})`),!0;if(!(a instanceof u)||0==a.children.length||a.positions[0]>0)break;let l=a.children[0];if(l instanceof u&&0==a.positions[0])a=l;else break}}let a=n.stateSlot(e.state,4);if(a>0)return e.reduce(a),F&&console.log(o+this.stackID(e)+` (via always-reduce ${n.getName(65535&a)})`),!0;if(e.stack.length>=8400)for(;e.stack.length>6e3&&e.forceReduce(););let l=this.tokens.getActions(e);for(let s=0;s<l.length;){let a=l[s++],h=l[s++],c=l[s++],f=s==l.length||!i,u=f?e:e.split(),p=this.tokens.mainToken;if(u.apply(a,h,p?p.start:u.pos,c),F&&console.log(o+this.stackID(u)+` (via ${(65536&a)==0?"shift":`reduce of ${n.getName(65535&a)}`} for ${n.getName(h)} @ ${r}${u==e?"":", split"})`),f)return!0;u.pos>r?t.push(u):i.push(u)}return!1}advanceFully(e,t){let i=e.pos;for(;;){if(!this.advanceStack(e,null,null))return!1;if(e.pos>i)return er(e,t),!0}}runRecovery(e,t,i){let r=null,n=!1;for(let s=0;s<e.length;s++){let o=e[s],a=t[s<<1],l=t[(s<<1)+1],h=F?this.stackID(o)+" -> ":"";if(o.deadEnd&&(n||(n=!0,o.restart(),F&&console.log(h+this.stackID(o)+" (restarted)"),this.advanceFully(o,i))))continue;let c=o.split(),f=h;for(let e=0;c.forceReduce()&&e<10&&(F&&console.log(f+this.stackID(c)+" (via force-reduce)"),!this.advanceFully(c,i));e++)F&&(f=this.stackID(c)+" -> ");for(let e of o.recoverByInsert(a))F&&console.log(h+this.stackID(e)+" (via recover-insert)"),this.advanceFully(e,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),F&&console.log(h+this.stackID(o)+` (via recover-delete ${this.parser.getName(a)})`),er(o,i)):(!r||r.score<o.score)&&(r=o)}return r}stackToTree(e){return e.close(),u.build({buffer:E.create(e),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:e.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(e){let t=(K||(K=new WeakMap)).get(e);return t||K.set(e,t=String.fromCodePoint(this.nextStackID++)),t+e}}function er(e,t){for(let i=0;i<t.length;i++){let r=t[i];if(r.pos==e.pos&&r.sameState(e)){t[i].score<e.score&&(t[i]=e);return}}t.push(e)}class en{constructor(e,t,i){this.source=e,this.flags=t,this.disabled=i}allows(e){return!this.disabled||0==this.disabled[e]}}let es=e=>e;class eo extends _{constructor(e){if(super(),this.wrappers=[],14!=e.version)throw RangeError(`Parser version (${e.version}) doesn't match runtime version (14)`);let t=e.nodeNames.split(" ");this.minRepeatTerm=t.length;for(let i=0;i<e.repeatNodeCount;i++)t.push("");let i=Object.keys(e.topRules).map(t=>e.topRules[t][1]),r=[];for(let e=0;e<t.length;e++)r.push([]);function n(e,t,i){r[e].push([t,t.deserialize(String(i))])}if(e.nodeProps)for(let t of e.nodeProps){let e=t[0];"string"==typeof e&&(e=s[e]);for(let i=1;i<t.length;){let r=t[i++];if(r>=0)n(r,e,t[i++]);else{let s=t[i+-r];for(let o=-r;o>0;o--)n(t[i++],e,s);i++}}}this.nodeSet=new h(t.map((t,n)=>l.define({name:n>=this.minRepeatTerm?void 0:t,id:n,props:r[n],top:i.indexOf(n)>-1,error:0==n,skipped:e.skippedNodes&&e.skippedNodes.indexOf(n)>-1}))),e.propSources&&(this.nodeSet=this.nodeSet.extend(...e.propSources)),this.strict=!1,this.bufferLength=1024;let o=z(e.tokenData);this.context=e.context,this.specializerSpecs=e.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let e=0;e<this.specializerSpecs.length;e++)this.specialized[e]=this.specializerSpecs[e].term;this.specializers=this.specializerSpecs.map(el),this.states=z(e.states,Uint32Array),this.data=z(e.stateData),this.goto=z(e.goto),this.maxTerm=e.maxTerm,this.tokenizers=e.tokenizers.map(e=>"number"==typeof e?new G(o,e):e),this.topRules=e.topRules,this.dialects=e.dialects||{},this.dynamicPrecedences=e.dynamicPrecedences||null,this.tokenPrecTable=e.tokenPrec,this.termNames=e.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(e,t,i){let r=new ei(this,e,t,i);for(let n of this.wrappers)r=n(r,e,t,i);return r}getGoto(e,t,i=!1){let r=this.goto;if(t>=r[0])return -1;for(let n=r[t+1];;){let t=r[n++],s=1&t,o=r[n++];if(s&&i)return o;for(let i=n+(t>>1);n<i;n++)if(r[n]==e)return o;if(s)return -1}}hasAction(e,t){let i=this.data;for(let r=0;r<2;r++)for(let n=this.stateSlot(e,r?2:1),s;;n+=3){if(65535==(s=i[n])){if(1==i[n+1])s=i[n=ea(i,n+2)];else if(2==i[n+1])return ea(i,n+2);else break}if(s==t||0==s)return ea(i,n+1)}return 0}stateSlot(e,t){return this.states[6*e+t]}stateFlag(e,t){return(this.stateSlot(e,0)&t)>0}validAction(e,t){return!!this.allActions(e,e=>e==t||null)}allActions(e,t){let i=this.stateSlot(e,4),r=i?t(i):void 0;for(let i=this.stateSlot(e,1);null==r;i+=3){if(65535==this.data[i]){if(1==this.data[i+1])i=ea(this.data,i+2);else break}r=t(ea(this.data,i+1))}return r}nextStates(e){let t=[];for(let i=this.stateSlot(e,1);;i+=3){if(65535==this.data[i]){if(1==this.data[i+1])i=ea(this.data,i+2);else break}if((1&this.data[i+2])==0){let e=this.data[i+1];t.some((t,i)=>1&i&&t==e)||t.push(this.data[i],e)}}return t}configure(e){let t=Object.assign(Object.create(eo.prototype),this);if(e.props&&(t.nodeSet=this.nodeSet.extend(...e.props)),e.top){let i=this.topRules[e.top];if(!i)throw RangeError(`Invalid top rule name ${e.top}`);t.top=i}return e.tokenizers&&(t.tokenizers=this.tokenizers.map(t=>{let i=e.tokenizers.find(e=>e.from==t);return i?i.to:t})),e.specializers&&(t.specializers=this.specializers.slice(),t.specializerSpecs=this.specializerSpecs.map((i,r)=>{let n=e.specializers.find(e=>e.from==i.external);if(!n)return i;let s=Object.assign(Object.assign({},i),{external:n.to});return t.specializers[r]=el(s),s})),e.contextTracker&&(t.context=e.contextTracker),e.dialect&&(t.dialect=this.parseDialect(e.dialect)),null!=e.strict&&(t.strict=e.strict),e.wrap&&(t.wrappers=t.wrappers.concat(e.wrap)),null!=e.bufferLength&&(t.bufferLength=e.bufferLength),t}hasWrappers(){return this.wrappers.length>0}getName(e){return this.termNames?this.termNames[e]:String(e<=this.maxNode&&this.nodeSet.types[e].name||e)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(e){let t=this.dynamicPrecedences;return null==t?0:t[e]||0}parseDialect(e){let t=Object.keys(this.dialects),i=t.map(()=>!1);if(e)for(let r of e.split(" ")){let e=t.indexOf(r);e>=0&&(i[e]=!0)}let r=null;for(let e=0;e<t.length;e++)if(!i[e])for(let i=this.dialects[t[e]],n;65535!=(n=this.data[i++]);)(r||(r=new Uint8Array(this.maxTerm+1)))[n]=1;return new en(e,i,r)}static deserialize(e){return new eo(e)}}function ea(e,t){return e[t]|e[t+1]<<16}function el(e){if(e.external){let t=e.extend?1:0;return(i,r)=>e.external(i,r)<<1|t}return e.get}let eh=0;class ec{constructor(e,t,i,r){this.name=e,this.set=t,this.base=i,this.modified=r,this.id=eh++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i="string"==typeof e?e:"?";if(e instanceof ec&&(t=e),null==t?void 0:t.base)throw Error("Can not derive from a modified tag");let r=new ec(i,[],null,[]);if(r.set.push(r),t)for(let e of t.set)r.set.push(e);return r}static defineModifier(e){let t=new eu(e);return e=>e.modified.indexOf(t)>-1?e:eu.get(e.base||e,e.modified.concat(t).sort((e,t)=>e.id-t.id))}}let ef=0;class eu{constructor(e){this.name=e,this.instances=[],this.id=ef++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>{var r;return i.base==e&&(r=i.modified,t.length==r.length&&t.every((e,t)=>e==r[t]))});if(i)return i;let r=[],n=new ec(e.name,r,e,t);for(let e of t)e.instances.push(n);let s=function(e){let t=[[]];for(let i=0;i<e.length;i++)for(let r=0,n=t.length;r<n;r++)t.push(t[r].concat(e[i]));return t.sort((e,t)=>t.length-e.length)}(t);for(let t of e.set)if(!t.modified.length)for(let e of s)r.push(eu.get(t,e));return n}}let ep=new s;class eO{constructor(e,t,i,r){this.tags=e,this.mode=t,this.context=i,this.next=r}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}eO.empty=new eO([],2,null);let ed=ec.define,eg=ed(),em=ed(),ey=ed(em),ex=ed(em),ek=ed(),eQ=ed(ek),eb=ed(ek),ev=ed(),eS=ed(ev),ew=ed(),e$=ed(),eP=ed(),eZ=ed(eP),eT=ed(),e_={comment:eg,lineComment:ed(eg),blockComment:ed(eg),docComment:ed(eg),name:em,variableName:ed(em),typeName:ey,tagName:ed(ey),propertyName:ex,attributeName:ed(ex),className:ed(em),labelName:ed(em),namespace:ed(em),macroName:ed(em),literal:ek,string:eQ,docString:ed(eQ),character:ed(eQ),attributeValue:ed(eQ),number:eb,integer:ed(eb),float:ed(eb),bool:ed(ek),regexp:ed(ek),escape:ed(ek),color:ed(ek),url:ed(ek),keyword:ew,self:ed(ew),null:ed(ew),atom:ed(ew),unit:ed(ew),modifier:ed(ew),operatorKeyword:ed(ew),controlKeyword:ed(ew),definitionKeyword:ed(ew),moduleKeyword:ed(ew),operator:e$,derefOperator:ed(e$),arithmeticOperator:ed(e$),logicOperator:ed(e$),bitwiseOperator:ed(e$),compareOperator:ed(e$),updateOperator:ed(e$),definitionOperator:ed(e$),typeOperator:ed(e$),controlOperator:ed(e$),punctuation:eP,separator:ed(eP),bracket:eZ,angleBracket:ed(eZ),squareBracket:ed(eZ),paren:ed(eZ),brace:ed(eZ),content:ev,heading:eS,heading1:ed(eS),heading2:ed(eS),heading3:ed(eS),heading4:ed(eS),heading5:ed(eS),heading6:ed(eS),contentSeparator:ed(ev),list:ed(ev),quote:ed(ev),emphasis:ed(ev),strong:ed(ev),link:ed(ev),monospace:ed(ev),strikethrough:ed(ev),inserted:ed(),deleted:ed(),changed:ed(),invalid:ed(),meta:eT,documentMeta:ed(eT),annotation:ed(eT),processingInstruction:ed(eT),definition:ec.defineModifier("definition"),constant:ec.defineModifier("constant"),function:ec.defineModifier("function"),standard:ec.defineModifier("standard"),local:ec.defineModifier("local"),special:ec.defineModifier("special")};for(let e in e_){let t=e_[e];t instanceof ec&&(t.name=e)}!function(e,t){let i=Object.create(null);for(let t of e)if(Array.isArray(t.tag))for(let e of t.tag)i[e.id]=t.class;else i[t.tag.id]=t.class;let{scope:r,all:n=null}={}}([{tag:e_.link,class:"tok-link"},{tag:e_.heading,class:"tok-heading"},{tag:e_.emphasis,class:"tok-emphasis"},{tag:e_.strong,class:"tok-strong"},{tag:e_.keyword,class:"tok-keyword"},{tag:e_.atom,class:"tok-atom"},{tag:e_.bool,class:"tok-bool"},{tag:e_.url,class:"tok-url"},{tag:e_.labelName,class:"tok-labelName"},{tag:e_.inserted,class:"tok-inserted"},{tag:e_.deleted,class:"tok-deleted"},{tag:e_.literal,class:"tok-literal"},{tag:e_.string,class:"tok-string"},{tag:e_.number,class:"tok-number"},{tag:[e_.regexp,e_.escape,e_.special(e_.string)],class:"tok-string2"},{tag:e_.variableName,class:"tok-variableName"},{tag:e_.local(e_.variableName),class:"tok-variableName tok-local"},{tag:e_.definition(e_.variableName),class:"tok-variableName tok-definition"},{tag:e_.special(e_.variableName),class:"tok-variableName2"},{tag:e_.definition(e_.propertyName),class:"tok-propertyName tok-definition"},{tag:e_.typeName,class:"tok-typeName"},{tag:e_.namespace,class:"tok-namespace"},{tag:e_.className,class:"tok-className"},{tag:e_.macroName,class:"tok-macroName"},{tag:e_.propertyName,class:"tok-propertyName"},{tag:e_.operator,class:"tok-operator"},{tag:e_.comment,class:"tok-comment"},{tag:e_.meta,class:"tok-meta"},{tag:e_.invalid,class:"tok-invalid"},{tag:e_.punctuation,class:"tok-punctuation"}]);let eX=[9,10,11,12,13,32,133,160,5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8232,8233,8239,8287,12288],eC=new class{constructor(e){this.start=e.start,this.shift=e.shift||es,this.reduce=e.reduce||es,this.reuse=e.reuse||es,this.hash=e.hash||(()=>0),this.strict=!1!==e.strict}}({start:!1,shift:(e,t)=>5==t||6==t||319==t?e:320==t,strict:!1}),eA=new U((e,t)=>{let{next:i}=e;(125==i||-1==i||t.context)&&e.acceptToken(317)},{contextual:!0,fallback:!0}),eR=new U((e,t)=>{let{next:i}=e,r;!(eX.indexOf(i)>-1)&&(47==i&&(47==(r=e.peek(1))||42==r)||125==i||59==i||-1==i||t.context||e.acceptToken(315))},{contextual:!0}),eq=new U((e,t)=>{91!=e.next||t.context||e.acceptToken(316)},{contextual:!0}),eM=new U((e,t)=>{let{next:i}=e;if(43==i||45==i){if(e.advance(),i==e.next){e.advance();let i=!t.context&&t.canShift(1);e.acceptToken(i?1:2)}}else 63==i&&46==e.peek(1)&&(e.advance(),e.advance(),(e.next<48||e.next>57)&&e.acceptToken(3))},{contextual:!0});function ej(e,t){return e>=65&&e<=90||e>=97&&e<=122||95==e||e>=192||!t&&e>=48&&e<=57}let eY=new U((e,t)=>{if(60!=e.next||!t.dialectEnabled(0)||(e.advance(),47==e.next))return;let i=0;for(;eX.indexOf(e.next)>-1;)e.advance(),i++;if(ej(e.next,!0)){for(e.advance(),i++;ej(e.next,!1);)e.advance(),i++;for(;eX.indexOf(e.next)>-1;)e.advance(),i++;if(44==e.next)return;for(let t=0;;t++){if(7==t){if(!ej(e.next,!0))return;break}if(e.next!="extends".charCodeAt(t))break;e.advance(),i++}}e.acceptToken(4,-i)}),eN=function(e){let t=Object.create(null);for(let i in e){let r=e[i];for(let e of(Array.isArray(r)||(r=[r]),i.split(" ")))if(e){let i=[],n=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){n=1;break}let r=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!r)throw RangeError("Invalid path: "+e);if(i.push("*"==r[0]?"":'"'==r[0][0]?JSON.parse(r[0]):r[0]),(t+=r[0].length)==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){n=0;break}if("/"!=o)throw RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,a=i[o];if(!a)throw RangeError("Invalid path: "+e);let l=new eO(r,n,o>0?i.slice(0,o):null);t[a]=l.sort(t[a])}}return ep.add(t)}({"get set async static":e_.modifier,"for while do if else switch try catch finally return throw break continue default case":e_.controlKeyword,"in of await yield void typeof delete instanceof as satisfies":e_.operatorKeyword,"let var const using function class extends":e_.definitionKeyword,"import export from":e_.moduleKeyword,"with debugger new":e_.keyword,TemplateString:e_.special(e_.string),super:e_.atom,BooleanLiteral:e_.bool,this:e_.self,null:e_.null,Star:e_.modifier,VariableName:e_.variableName,"CallExpression/VariableName TaggedTemplateExpression/VariableName":e_.function(e_.variableName),VariableDefinition:e_.definition(e_.variableName),Label:e_.labelName,PropertyName:e_.propertyName,PrivatePropertyName:e_.special(e_.propertyName),"CallExpression/MemberExpression/PropertyName":e_.function(e_.propertyName),"FunctionDeclaration/VariableDefinition":e_.function(e_.definition(e_.variableName)),"ClassDeclaration/VariableDefinition":e_.definition(e_.className),"NewExpression/VariableName":e_.className,PropertyDefinition:e_.definition(e_.propertyName),PrivatePropertyDefinition:e_.definition(e_.special(e_.propertyName)),UpdateOp:e_.updateOperator,"LineComment Hashbang":e_.lineComment,BlockComment:e_.blockComment,Number:e_.number,String:e_.string,Escape:e_.escape,ArithOp:e_.arithmeticOperator,LogicOp:e_.logicOperator,BitOp:e_.bitwiseOperator,CompareOp:e_.compareOperator,RegExp:e_.regexp,Equals:e_.definitionOperator,Arrow:e_.function(e_.punctuation),": Spread":e_.punctuation,"( )":e_.paren,"[ ]":e_.squareBracket,"{ }":e_.brace,"InterpolationStart InterpolationEnd":e_.special(e_.brace),".":e_.derefOperator,", ;":e_.separator,"@":e_.meta,TypeName:e_.typeName,TypeDefinition:e_.definition(e_.typeName),"type enum interface implements namespace module declare":e_.definitionKeyword,"abstract global Privacy readonly override":e_.modifier,"is keyof unique infer asserts":e_.operatorKeyword,JSXAttributeValue:e_.attributeValue,JSXText:e_.content,"JSXStartTag JSXStartCloseTag JSXSelfCloseEndTag JSXEndTag":e_.angleBracket,"JSXIdentifier JSXNameSpacedName":e_.tagName,"JSXAttribute/JSXIdentifier JSXAttribute/JSXNameSpacedName":e_.attributeName,"JSXBuiltin/JSXIdentifier":e_.standard(e_.tagName)}),eI={__proto__:null,export:20,as:25,from:33,default:36,async:41,function:42,in:52,out:55,const:56,extends:60,this:64,true:72,false:72,null:84,void:88,typeof:92,super:108,new:142,delete:154,yield:163,await:167,class:172,public:235,private:235,protected:235,readonly:237,instanceof:256,satisfies:259,import:292,keyof:349,unique:353,infer:359,asserts:395,is:397,abstract:417,implements:419,type:421,let:424,var:426,using:429,interface:435,enum:439,namespace:445,module:447,declare:451,global:455,for:474,of:483,while:486,with:490,do:494,if:498,else:500,switch:504,case:510,try:516,catch:520,finally:524,return:528,throw:532,break:536,continue:540,debugger:544},eE={__proto__:null,async:129,get:131,set:133,declare:195,public:197,private:197,protected:197,static:199,abstract:201,override:203,readonly:209,accessor:211,new:401},ez={__proto__:null,"<":193},eV=eo.deserialize({version:14,states:"$EOQ%TQlOOO%[QlOOO'_QpOOP(lO`OOO*zQ!0MxO'#CiO+RO#tO'#CjO+aO&jO'#CjO+oO#@ItO'#DaO.QQlO'#DgO.bQlO'#DrO%[QlO'#DzO0fQlO'#ESOOQ!0Lf'#E['#E[O1PQ`O'#EXOOQO'#Ep'#EpOOQO'#Ik'#IkO1XQ`O'#GsO1dQ`O'#EoO1iQ`O'#EoO3hQ!0MxO'#JqO6[Q!0MxO'#JrO6uQ`O'#F]O6zQ,UO'#FtOOQ!0Lf'#Ff'#FfO7VO7dO'#FfO7eQMhO'#F|O9[Q`O'#F{OOQ!0Lf'#Jr'#JrOOQ!0Lb'#Jq'#JqO9aQ`O'#GwOOQ['#K^'#K^O9lQ`O'#IXO9qQ!0LrO'#IYOOQ['#J_'#J_OOQ['#I^'#I^Q`QlOOQ`QlOOO9yQ!L^O'#DvO:QQlO'#EOO:XQlO'#EQO9gQ`O'#GsO:`QMhO'#CoO:nQ`O'#EnO:yQ`O'#EyO;OQMhO'#FeO;mQ`O'#GsOOQO'#K_'#K_O;rQ`O'#K_O<QQ`O'#G{O<QQ`O'#G|O<QQ`O'#HOO9gQ`O'#HRO<wQ`O'#HUO>`Q`O'#CeO>pQ`O'#HbO>xQ`O'#HhO>xQ`O'#HjO`QlO'#HlO>xQ`O'#HnO>xQ`O'#HqO>}Q`O'#HwO?SQ!0LsO'#H}O%[QlO'#IPO?_Q!0LsO'#IRO?jQ!0LsO'#ITO9qQ!0LrO'#IVO?uQ!0MxO'#CiO@wQpO'#DlQOQ`OOO%[QlO'#EQOA_Q`O'#ETO:`QMhO'#EnOAjQ`O'#EnOAuQ!bO'#FeOOQ['#Cg'#CgOOQ!0Lb'#Dq'#DqOOQ!0Lb'#Ju'#JuO%[QlO'#JuOOQO'#Jx'#JxOOQO'#Ig'#IgOBuQpO'#EgOOQ!0Lb'#Ef'#EfOOQ!0Lb'#J|'#J|OCqQ!0MSO'#EgOC{QpO'#EWOOQO'#Jw'#JwODaQpO'#JxOEnQpO'#EWOC{QpO'#EgPE{O&2DjO'#CbPOOO)CD|)CD|OOOO'#I_'#I_OFWO#tO,59UOOQ!0Lh,59U,59UOOOO'#I`'#I`OFfO&jO,59UOFtQ!L^O'#DcOOOO'#Ib'#IbOF{O#@ItO,59{OOQ!0Lf,59{,59{OGZQlO'#IcOGnQ`O'#JsOImQ!fO'#JsO+}QlO'#JsOItQ`O,5:ROJ[Q`O'#EpOJiQ`O'#KSOJtQ`O'#KROJtQ`O'#KROJ|Q`O,5;^OKRQ`O'#KQOOQ!0Ln,5:^,5:^OKYQlO,5:^OMWQ!0MxO,5:fOMwQ`O,5:nONbQ!0LrO'#KPONiQ`O'#KOO9aQ`O'#KOON}Q`O'#KOO! VQ`O,5;]O! [Q`O'#KOO!#aQ!fO'#JrOOQ!0Lh'#Ci'#CiO%[QlO'#ESO!$PQ!fO,5:sOOQS'#Jy'#JyOOQO-E<i-E<iO9gQ`O,5=_O!$gQ`O,5=_O!$lQlO,5;ZO!&oQMhO'#EkO!(YQ`O,5;ZO!(_QlO'#DyO!(iQpO,5;dO!(qQpO,5;dO%[QlO,5;dOOQ['#FT'#FTOOQ['#FV'#FVO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eO%[QlO,5;eOOQ['#FZ'#FZO!)PQlO,5;tOOQ!0Lf,5;y,5;yOOQ!0Lf,5;z,5;zOOQ!0Lf,5;|,5;|O%[QlO'#IoO!+SQ!0LrO,5<iO%[QlO,5;eO!&oQMhO,5;eO!+qQMhO,5;eO!-cQMhO'#E^O%[QlO,5;wOOQ!0Lf,5;{,5;{O!-jQ,UO'#FjO!.gQ,UO'#KWO!.RQ,UO'#KWO!.nQ,UO'#KWOOQO'#KW'#KWO!/SQ,UO,5<SOOOW,5<`,5<`O!/eQlO'#FvOOOW'#In'#InO7VO7dO,5<QO!/lQ,UO'#FxOOQ!0Lf,5<Q,5<QO!0]Q$IUO'#CyOOQ!0Lh'#C}'#C}O!0pO#@ItO'#DRO!1^QMjO,5<eO!1eQ`O,5<hO!3QQ(CWO'#GXO!3_Q`O'#GYO!3dQ`O'#GYO!5SQ(CWO'#G^O!6XQpO'#GbOOQO'#Gn'#GnO!+xQMhO'#GmOOQO'#Gp'#GpO!+xQMhO'#GoO!6zQ$IUO'#JkOOQ!0Lh'#Jk'#JkO!7UQ`O'#JjO!7dQ`O'#JiO!7lQ`O'#CuOOQ!0Lh'#C{'#C{O!7}Q`O'#C}OOQ!0Lh'#DV'#DVOOQ!0Lh'#DX'#DXO1SQ`O'#DZO!+xQMhO'#GPO!+xQMhO'#GRO!8SQ`O'#GTO!8XQ`O'#GUO!3dQ`O'#G[O!+xQMhO'#GaO<QQ`O'#JjO!8^Q`O'#EqO!8{Q`O,5<gOOQ!0Lb'#Cr'#CrO!9TQ`O'#ErO!9}QpO'#EsOOQ!0Lb'#KQ'#KQO!:UQ!0LrO'#K`O9qQ!0LrO,5=cO`QlO,5>sOOQ['#Jg'#JgOOQ[,5>t,5>tOOQ[-E<[-E<[O!<TQ!0MxO,5:bO!9xQpO,5:`O!>nQ!0MxO,5:jO%[QlO,5:jO!AUQ!0MxO,5:lOOQO,5@y,5@yO!AuQMhO,5=_O!BTQ!0LrO'#JhO9[Q`O'#JhO!BfQ!0LrO,59ZO!BqQpO,59ZO!ByQMhO,59ZO:`QMhO,59ZO!CUQ`O,5;ZO!C^Q`O'#HaO!CrQ`O'#KcO%[QlO,5;}O!9xQpO,5<PO!CzQ`O,5=zO!DPQ`O,5=zO!DUQ`O,5=zO9qQ!0LrO,5=zO<QQ`O,5=jOOQO'#Cy'#CyO!DdQpO,5=gO!DlQMhO,5=hO!DwQ`O,5=jO!D|Q!bO,5=mO!EUQ`O'#K_O>}Q`O'#HWO9gQ`O'#HYO!EZQ`O'#HYO:`QMhO'#H[O!E`Q`O'#H[OOQ[,5=p,5=pO!EeQ`O'#H]O!EvQ`O'#CoO!E{Q`O,59PO!FVQ`O,59PO!H[QlO,59POOQ[,59P,59PO!HlQ!0LrO,59PO%[QlO,59PO!JwQlO'#HdOOQ['#He'#HeOOQ['#Hf'#HfO`QlO,5=|O!K_Q`O,5=|O`QlO,5>SO`QlO,5>UO!KdQ`O,5>WO`QlO,5>YO!KiQ`O,5>]O!KnQlO,5>cOOQ[,5>i,5>iO%[QlO,5>iO9qQ!0LrO,5>kOOQ[,5>m,5>mO# xQ`O,5>mOOQ[,5>o,5>oO# xQ`O,5>oOOQ[,5>q,5>qO#!fQpO'#D_O%[QlO'#JuO##XQpO'#JuO##cQpO'#DmO##tQpO'#DmO#&VQlO'#DmO#&^Q`O'#JtO#&fQ`O,5:WO#&kQ`O'#EtO#&yQ`O'#KTO#'RQ`O,5;_O#'WQpO'#DmO#'eQpO'#EVOOQ!0Lf,5:o,5:oO%[QlO,5:oO#'lQ`O,5:oO>}Q`O,5;YO!BqQpO,5;YO!ByQMhO,5;YO:`QMhO,5;YO#'tQ`O,5@aO#'yQ07dO,5:sOOQO-E<e-E<eO#)PQ!0MSO,5;ROC{QpO,5:rO#)ZQpO,5:rOC{QpO,5;RO!BfQ!0LrO,5:rOOQ!0Lb'#Ej'#EjOOQO,5;R,5;RO%[QlO,5;RO#)hQ!0LrO,5;RO#)sQ!0LrO,5;RO!BqQpO,5:rOOQO,5;X,5;XO#*RQ!0LrO,5;RPOOO'#I]'#I]P#*gO&2DjO,58|POOO,58|,58|OOOO-E<]-E<]OOQ!0Lh1G.p1G.pOOOO-E<^-E<^OOOO,59},59}O#*rQ!bO,59}OOOO-E<`-E<`OOQ!0Lf1G/g1G/gO#*wQ!fO,5>}O+}QlO,5>}OOQO,5?T,5?TO#+RQlO'#IcOOQO-E<a-E<aO#+`Q`O,5@_O#+hQ!fO,5@_O#+oQ`O,5@mOOQ!0Lf1G/m1G/mO%[QlO,5@nO#+wQ`O'#IiOOQO-E<g-E<gO#+oQ`O,5@mOOQ!0Lb1G0x1G0xOOQ!0Ln1G/x1G/xOOQ!0Ln1G0Y1G0YO%[QlO,5@kO#,]Q!0LrO,5@kO#,nQ!0LrO,5@kO#,uQ`O,5@jO9aQ`O,5@jO#,}Q`O,5@jO#-]Q`O'#IlO#,uQ`O,5@jOOQ!0Lb1G0w1G0wO!(iQpO,5:uO!(tQpO,5:uOOQS,5:w,5:wO#-}QdO,5:wO#.VQMhO1G2yO9gQ`O1G2yOOQ!0Lf1G0u1G0uO#.eQ!0MxO1G0uO#/jQ!0MvO,5;VOOQ!0Lh'#GW'#GWO#0WQ!0MzO'#JkO!$lQlO1G0uO#2cQ!fO'#JvO%[QlO'#JvO#2mQ`O,5:eOOQ!0Lh'#D_'#D_OOQ!0Lf1G1O1G1OO%[QlO1G1OOOQ!0Lf1G1f1G1fO#2rQ`O1G1OO#5WQ!0MxO1G1PO#5_Q!0MxO1G1PO#7uQ!0MxO1G1PO#7|Q!0MxO1G1PO#:dQ!0MxO1G1PO#<zQ!0MxO1G1PO#=RQ!0MxO1G1PO#=YQ!0MxO1G1PO#?pQ!0MxO1G1PO#?wQ!0MxO1G1PO#BUQ?MtO'#CiO#DPQ?MtO1G1`O#DWQ?MtO'#JrO#DkQ!0MxO,5?ZOOQ!0Lb-E<m-E<mO#FxQ!0MxO1G1PO#GuQ!0MzO1G1POOQ!0Lf1G1P1G1PO#HxQMjO'#J{O#ISQ`O,5:xO#IXQ!0MxO1G1cO#I{Q,UO,5<WO#JTQ,UO,5<XO#J]Q,UO'#FoO#JtQ`O'#FnOOQO'#KX'#KXOOQO'#Im'#ImO#JyQ,UO1G1nOOQ!0Lf1G1n1G1nOOOW1G1y1G1yO#K[Q?MtO'#JqO#KfQ`O,5<bO!)PQlO,5<bOOOW-E<l-E<lOOQ!0Lf1G1l1G1lO#KkQpO'#KWOOQ!0Lf,5<d,5<dO#KsQpO,5<dO#KxQMhO'#DTOOOO'#Ia'#IaO#LPO#@ItO,59mOOQ!0Lh,59m,59mO%[QlO1G2PO!8XQ`O'#IqO#L[Q`O,5<zOOQ!0Lh,5<w,5<wO!+xQMhO'#ItO#LxQMjO,5=XO!+xQMhO'#IvO#MkQMjO,5=ZO!&oQMhO,5=]OOQO1G2S1G2SO#MuQ!dO'#CrO#NYQ(CWO'#ErO$ _QpO'#GbO$ uQ!dO,5<sO$ |Q`O'#KZO9aQ`O'#KZO$![Q`O,5<uO!+xQMhO,5<tO$!aQ`O'#GZO$!rQ`O,5<tO$!wQ!dO'#GWO$#UQ!dO'#K[O$#`Q`O'#K[O!&oQMhO'#K[O$#eQ`O,5<xO$#jQlO'#JuO$#tQpO'#GcO##tQpO'#GcO$$VQ`O'#GgO!3dQ`O'#GkO$$[Q!0LrO'#IsO$$gQpO,5<|OOQ!0Lp,5<|,5<|O$$nQpO'#GcO$${QpO'#GdO$%^QpO'#GdO$%cQMjO,5=XO$%sQMjO,5=ZOOQ!0Lh,5=^,5=^O!+xQMhO,5@UO!+xQMhO,5@UO$&TQ`O'#IxO$&iQ`O,5@TO$&qQ`O,59aOOQ!0Lh,59i,59iO$'hQ$IYO,59uOOQ!0Lh'#Jo'#JoO$(ZQMjO,5<kO$(|QMjO,5<mO@oQ`O,5<oOOQ!0Lh,5<p,5<pO$)WQ`O,5<vO$)]QMjO,5<{O$)mQ`O,5@UO$){Q`O'#KOO!$lQlO1G2RO$*QQ`O1G2RO9aQ`O'#KRO9aQ`O'#EtO%[QlO'#EtO9aQ`O'#IzO$*VQ!0LrO,5@zOOQ[1G2}1G2}OOQ[1G4_1G4_OOQ!0Lf1G/|1G/|OOQ!0Lf1G/z1G/zO$,XQ!0MxO1G0UOOQ[1G2y1G2yO!&oQMhO1G2yO%[QlO1G2yO#.YQ`O1G2yO$.]QMhO'#EkOOQ!0Lb,5@S,5@SO$.jQ!0LrO,5@SOOQ[1G.u1G.uO!BfQ!0LrO1G.uO!BqQpO1G.uO!ByQMhO1G.uO$.{Q`O1G0uO$/QQ`O'#CiO$/]Q`O'#KdO$/eQ`O,5={O$/jQ`O'#KdO$/oQ`O'#KdO$/}Q`O'#JQO$0]Q`O,5@}O$0eQ!fO1G1iOOQ!0Lf1G1k1G1kO9gQ`O1G3fO@oQ`O1G3fO$0lQ`O1G3fO$0qQ`O1G3fOOQ[1G3f1G3fO!DwQ`O1G3UO!&oQMhO1G3RO$0vQ`O1G3ROOQ[1G3S1G3SO!&oQMhO1G3SO$0{Q`O1G3SO$1TQpO'#HQOOQ[1G3U1G3UO!6SQpO'#I|O!D|Q!bO1G3XOOQ[1G3X1G3XOOQ[,5=r,5=rO$1]QMhO,5=tO9gQ`O,5=tO$$VQ`O,5=vO9[Q`O,5=vO!BqQpO,5=vO!ByQMhO,5=vO:`QMhO,5=vO$1kQ`O'#KbO$1vQ`O,5=wOOQ[1G.k1G.kO$1{Q!0LrO1G.kO@oQ`O1G.kO$2WQ`O1G.kO9qQ!0LrO1G.kO$4`Q!fO,5APO$4mQ`O,5APO9aQ`O,5APO$4xQlO,5>OO$5PQ`O,5>OOOQ[1G3h1G3hO`QlO1G3hOOQ[1G3n1G3nOOQ[1G3p1G3pO>xQ`O1G3rO$5UQlO1G3tO$9YQlO'#HsOOQ[1G3w1G3wO$9gQ`O'#HyO>}Q`O'#H{OOQ[1G3}1G3}O$9oQlO1G3}O9qQ!0LrO1G4TOOQ[1G4V1G4VOOQ!0Lb'#G_'#G_O9qQ!0LrO1G4XO9qQ!0LrO1G4ZO$=vQ`O,5@aO!)PQlO,5;`O9aQ`O,5;`O>}Q`O,5:XO!)PQlO,5:XO!BqQpO,5:XO$={Q?MtO,5:XOOQO,5;`,5;`O$>VQpO'#IdO$>mQ`O,5@`OOQ!0Lf1G/r1G/rO$>uQpO'#IjO$?PQ`O,5@oOOQ!0Lb1G0y1G0yO##tQpO,5:XOOQO'#If'#IfO$?XQpO,5:qOOQ!0Ln,5:q,5:qO#'oQ`O1G0ZOOQ!0Lf1G0Z1G0ZO%[QlO1G0ZOOQ!0Lf1G0t1G0tO>}Q`O1G0tO!BqQpO1G0tO!ByQMhO1G0tOOQ!0Lb1G5{1G5{O!BfQ!0LrO1G0^OOQO1G0m1G0mO%[QlO1G0mO$?`Q!0LrO1G0mO$?kQ!0LrO1G0mO!BqQpO1G0^OC{QpO1G0^O$?yQ!0LrO1G0mOOQO1G0^1G0^O$@_Q!0MxO1G0mPOOO-E<Z-E<ZPOOO1G.h1G.hOOOO1G/i1G/iO$@iQ!bO,5<iO$@qQ!fO1G4iOOQO1G4o1G4oO%[QlO,5>}O$@{Q`O1G5yO$ATQ`O1G6XO$A]Q!fO1G6YO9aQ`O,5?TO$AgQ!0MxO1G6VO%[QlO1G6VO$AwQ!0LrO1G6VO$BYQ`O1G6UO$BYQ`O1G6UO9aQ`O1G6UO$BbQ`O,5?WO9aQ`O,5?WOOQO,5?W,5?WO$BvQ`O,5?WO$){Q`O,5?WOOQO-E<j-E<jOOQS1G0a1G0aOOQS1G0c1G0cO#.QQ`O1G0cOOQ[7+(e7+(eO!&oQMhO7+(eO%[QlO7+(eO$CUQ`O7+(eO$CaQMhO7+(eO$CoQ!0MzO,5=XO$EzQ!0MzO,5=ZO$HVQ!0MzO,5=XO$JhQ!0MzO,5=ZO$LyQ!0MzO,59uO% OQ!0MzO,5<kO%#ZQ!0MzO,5<mO%%fQ!0MzO,5<{OOQ!0Lf7+&a7+&aO%'wQ!0MxO7+&aO%(kQlO'#IeO%(xQ`O,5@bO%)QQ!fO,5@bOOQ!0Lf1G0P1G0PO%)[Q`O7+&jOOQ!0Lf7+&j7+&jO%)aQ?MtO,5:fO%[QlO7+&zO%)kQ?MtO,5:bO%)xQ?MtO,5:jO%*SQ?MtO,5:lO%*^QMhO'#IhO%*hQ`O,5@gOOQ!0Lh1G0d1G0dOOQO1G1r1G1rOOQO1G1s1G1sO%*pQ!jO,5<ZO!)PQlO,5<YOOQO-E<k-E<kOOQ!0Lf7+'Y7+'YOOOW7+'e7+'eOOOW1G1|1G1|O%*{Q`O1G1|OOQ!0Lf1G2O1G2OOOOO,59o,59oO%+QQ!dO,59oOOOO-E<_-E<_OOQ!0Lh1G/X1G/XO%+XQ!0MxO7+'kOOQ!0Lh,5?],5?]O%+{QMhO1G2fP%,SQ`O'#IqPOQ!0Lh-E<o-E<oO%,pQMjO,5?`OOQ!0Lh-E<r-E<rO%-cQMjO,5?bOOQ!0Lh-E<t-E<tO%-mQ!dO1G2wO%-tQ!dO'#CrO%.[QMhO'#KRO$#jQlO'#JuOOQ!0Lh1G2_1G2_O%.cQ`O'#IpO%.wQ`O,5@uO%.wQ`O,5@uO%/PQ`O,5@uO%/[Q`O,5@uOOQO1G2a1G2aO%/jQMjO1G2`O!+xQMhO1G2`O%/zQ(CWO'#IrO%0XQ`O,5@vO!&oQMhO,5@vO%0aQ!dO,5@vOOQ!0Lh1G2d1G2dO%2qQ!fO'#CiO%2{Q`O,5=POOQ!0Lb,5<},5<}O%3TQpO,5<}OOQ!0Lb,5=O,5=OOClQ`O,5<}O%3`QpO,5<}OOQ!0Lb,5=R,5=RO$){Q`O,5=VOOQO,5?_,5?_OOQO-E<q-E<qOOQ!0Lp1G2h1G2hO##tQpO,5<}O$#jQlO,5=PO%3nQ`O,5=OO%3yQpO,5=OO!+xQMhO'#ItO%4sQMjO1G2sO!+xQMhO'#IvO%5fQMjO1G2uO%5pQMjO1G5pO%5zQMjO1G5pOOQO,5?d,5?dOOQO-E<v-E<vOOQO1G.{1G.{O!9xQpO,59wO%[QlO,59wOOQ!0Lh,5<j,5<jO%6XQ`O1G2ZO!+xQMhO1G2bO!+xQMhO1G5pO!+xQMhO1G5pO%6^Q!0MxO7+'mOOQ!0Lf7+'m7+'mO!$lQlO7+'mO%7QQ`O,5;`OOQ!0Lb,5?f,5?fOOQ!0Lb-E<x-E<xO%7VQ!dO'#K]O#'oQ`O7+(eO4UQ!fO7+(eO$CXQ`O7+(eO%7aQ!0MvO'#CiO%7tQ!0MvO,5=SO%8fQ`O,5=SO%8nQ`O,5=SOOQ!0Lb1G5n1G5nOOQ[7+$a7+$aO!BfQ!0LrO7+$aO!BqQpO7+$aO!$lQlO7+&aO%8sQ`O'#JPO%9[Q`O,5AOOOQO1G3g1G3gO9gQ`O,5AOO%9[Q`O,5AOO%9dQ`O,5AOOOQO,5?l,5?lOOQO-E=O-E=OOOQ!0Lf7+'T7+'TO%9iQ`O7+)QO9qQ!0LrO7+)QO9gQ`O7+)QO@oQ`O7+)QOOQ[7+(p7+(pO%9nQ!0MvO7+(mO!&oQMhO7+(mO!DrQ`O7+(nOOQ[7+(n7+(nO!&oQMhO7+(nO%9xQ`O'#KaO%:TQ`O,5=lOOQO,5?h,5?hOOQO-E<z-E<zOOQ[7+(s7+(sO%;gQpO'#HZOOQ[1G3`1G3`O!&oQMhO1G3`O%[QlO1G3`O%;nQ`O1G3`O%;yQMhO1G3`O9qQ!0LrO1G3bO$$VQ`O1G3bO9[Q`O1G3bO!BqQpO1G3bO!ByQMhO1G3bO%<XQ`O'#JOO%<mQ`O,5@|O%<uQpO,5@|OOQ!0Lb1G3c1G3cOOQ[7+$V7+$VO@oQ`O7+$VO9qQ!0LrO7+$VO%=QQ`O7+$VO%[QlO1G6kO%[QlO1G6lO%=VQ!0LrO1G6kO%=aQlO1G3jO%=hQ`O1G3jO%=mQlO1G3jOOQ[7+)S7+)SO9qQ!0LrO7+)^O`QlO7+)`OOQ['#Kg'#KgOOQ['#JR'#JRO%=tQlO,5>_OOQ[,5>_,5>_O%[QlO'#HtO%>RQ`O'#HvOOQ[,5>e,5>eO9aQ`O,5>eOOQ[,5>g,5>gOOQ[7+)i7+)iOOQ[7+)o7+)oOOQ[7+)s7+)sOOQ[7+)u7+)uO%>WQpO1G5{O%>rQ?MtO1G0zO%>|Q`O1G0zOOQO1G/s1G/sO%?XQ?MtO1G/sO>}Q`O1G/sO!)PQlO'#DmOOQO,5?O,5?OOOQO-E<b-E<bOOQO,5?U,5?UOOQO-E<h-E<hO!BqQpO1G/sOOQO-E<d-E<dOOQ!0Ln1G0]1G0]OOQ!0Lf7+%u7+%uO#'oQ`O7+%uOOQ!0Lf7+&`7+&`O>}Q`O7+&`O!BqQpO7+&`OOQO7+%x7+%xO$@_Q!0MxO7+&XOOQO7+&X7+&XO%[QlO7+&XO%?cQ!0LrO7+&XO!BfQ!0LrO7+%xO!BqQpO7+%xO%?nQ!0LrO7+&XO%?|Q!0MxO7++qO%[QlO7++qO%@^Q`O7++pO%@^Q`O7++pOOQO1G4r1G4rO9aQ`O1G4rO%@fQ`O1G4rOOQS7+%}7+%}O#'oQ`O<<LPO4UQ!fO<<LPO%@tQ`O<<LPOOQ[<<LP<<LPO!&oQMhO<<LPO%[QlO<<LPO%@|Q`O<<LPO%AXQ!0MzO,5?`O%CdQ!0MzO,5?bO%EoQ!0MzO1G2`O%HQQ!0MzO1G2sO%J]Q!0MzO1G2uO%LhQ!fO,5?PO%[QlO,5?POOQO-E<c-E<cO%LrQ`O1G5|OOQ!0Lf<<JU<<JUO%LzQ?MtO1G0uO& RQ?MtO1G1PO& YQ?MtO1G1PO&#ZQ?MtO1G1PO&#bQ?MtO1G1PO&%cQ?MtO1G1PO&'dQ?MtO1G1PO&'kQ?MtO1G1PO&'rQ?MtO1G1PO&)sQ?MtO1G1PO&)zQ?MtO1G1PO&*RQ!0MxO<<JfO&+yQ?MtO1G1PO&,vQ?MvO1G1PO&-yQ?MvO'#JkO&0PQ?MtO1G1cO&0^Q?MtO1G0UO&0hQMjO,5?SOOQO-E<f-E<fO!)PQlO'#FqOOQO'#KY'#KYOOQO1G1u1G1uO&0rQ`O1G1tO&0wQ?MtO,5?ZOOOW7+'h7+'hOOOO1G/Z1G/ZO&1RQ!dO1G4wOOQ!0Lh7+(Q7+(QP!&oQMhO,5?]O!+xQMhO7+(cO&1YQ`O,5?[O9aQ`O,5?[OOQO-E<n-E<nO&1hQ`O1G6aO&1hQ`O1G6aO&1pQ`O1G6aO&1{QMjO7+'zO&2]Q!dO,5?^O&2gQ`O,5?^O!&oQMhO,5?^OOQO-E<p-E<pO&2lQ!dO1G6bO&2vQ`O1G6bO&3OQ`O1G2kO!&oQMhO1G2kOOQ!0Lb1G2i1G2iOOQ!0Lb1G2j1G2jO%3TQpO1G2iO!BqQpO1G2iOClQ`O1G2iOOQ!0Lb1G2q1G2qO&3TQpO1G2iO&3cQ`O1G2kO$){Q`O1G2jOClQ`O1G2jO$#jQlO1G2kO&3kQ`O1G2jO&4_QMjO,5?`OOQ!0Lh-E<s-E<sO&5QQMjO,5?bOOQ!0Lh-E<u-E<uO!+xQMhO7++[OOQ!0Lh1G/c1G/cO&5[Q`O1G/cOOQ!0Lh7+'u7+'uO&5aQMjO7+'|O&5qQMjO7++[O&5{QMjO7++[O&6YQ!0MxO<<KXOOQ!0Lf<<KX<<KXO&6|Q`O1G0zO!&oQMhO'#IyO&7RQ`O,5@wO&9TQ!fO<<LPO!&oQMhO1G2nO&9[Q!0LrO1G2nOOQ[<<G{<<G{O!BfQ!0LrO<<G{O&9mQ!0MxO<<I{OOQ!0Lf<<I{<<I{OOQO,5?k,5?kO&:aQ`O,5?kO&:fQ`O,5?kOOQO-E<}-E<}O&:tQ`O1G6jO&:tQ`O1G6jO9gQ`O1G6jO@oQ`O<<LlOOQ[<<Ll<<LlO&:|Q`O<<LlO9qQ!0LrO<<LlOOQ[<<LX<<LXO%9nQ!0MvO<<LXOOQ[<<LY<<LYO!DrQ`O<<LYO&;RQpO'#I{O&;^Q`O,5@{O!)PQlO,5@{OOQ[1G3W1G3WOOQO'#I}'#I}O9qQ!0LrO'#I}O&;fQpO,5=uOOQ[,5=u,5=uO&;mQpO'#EgO&;tQpO'#GeO&;yQ`O7+(zO&<OQ`O7+(zOOQ[7+(z7+(zO!&oQMhO7+(zO%[QlO7+(zO&<WQ`O7+(zOOQ[7+(|7+(|O9qQ!0LrO7+(|O$$VQ`O7+(|O9[Q`O7+(|O!BqQpO7+(|O&<cQ`O,5?jOOQO-E<|-E<|OOQO'#H^'#H^O&<nQ`O1G6hO9qQ!0LrO<<GqOOQ[<<Gq<<GqO@oQ`O<<GqO&<vQ`O7+,VO&<{Q`O7+,WO%[QlO7+,VO%[QlO7+,WOOQ[7+)U7+)UO&=QQ`O7+)UO&=VQlO7+)UO&=^Q`O7+)UOOQ[<<Lx<<LxOOQ[<<Lz<<LzOOQ[-E=P-E=POOQ[1G3y1G3yO&=cQ`O,5>`OOQ[,5>b,5>bO&=hQ`O1G4PO9aQ`O7+&fO!)PQlO7+&fOOQO7+%_7+%_O&=mQ?MtO1G6YO>}Q`O7+%_OOQ!0Lf<<Ia<<IaOOQ!0Lf<<Iz<<IzO>}Q`O<<IzOOQO<<Is<<IsO$@_Q!0MxO<<IsO%[QlO<<IsOOQO<<Id<<IdO!BfQ!0LrO<<IdO&=wQ!0LrO<<IsO&>SQ!0MxO<= ]O&>dQ`O<= [OOQO7+*^7+*^O9aQ`O7+*^OOQ[ANAkANAkO&>lQ!fOANAkO!&oQMhOANAkO#'oQ`OANAkO4UQ!fOANAkO&>sQ`OANAkO%[QlOANAkO&>{Q!0MzO7+'zO&A^Q!0MzO,5?`O&CiQ!0MzO,5?bO&EtQ!0MzO7+'|O&HVQ!fO1G4kO&HaQ?MtO7+&aO&JeQ?MvO,5=XO&LlQ?MvO,5=ZO&L|Q?MvO,5=XO&M^Q?MvO,5=ZO&MnQ?MvO,59uO' tQ?MvO,5<kO'#wQ?MvO,5<mO'&]Q?MvO,5<{O'(RQ?MtO7+'kO'(`Q?MtO7+'mO'(mQ`O,5<]OOQO7+'`7+'`OOQ!0Lh7+*c7+*cO'(rQMjO<<K}OOQO1G4v1G4vO'(yQ`O1G4vO')UQ`O1G4vO')dQ`O7++{O')dQ`O7++{O!&oQMhO1G4xO')lQ!dO1G4xO')vQ`O7++|O'*OQ`O7+(VO'*ZQ!dO7+(VOOQ!0Lb7+(T7+(TOOQ!0Lb7+(U7+(UO!BqQpO7+(TOClQ`O7+(TO'*eQ`O7+(VO!&oQMhO7+(VO$){Q`O7+(UO'*jQ`O7+(VOClQ`O7+(UO'*rQMjO<<NvOOQ!0Lh7+$}7+$}O!+xQMhO<<NvO'*|Q!dO,5?eOOQO-E<w-E<wO'+WQ!0MvO7+(YO!&oQMhO7+(YOOQ[AN=gAN=gO9gQ`O1G5VOOQO1G5V1G5VO'+hQ`O1G5VO'+mQ`O7+,UO'+mQ`O7+,UO9qQ!0LrOANBWO@oQ`OANBWOOQ[ANBWANBWOOQ[ANAsANAsOOQ[ANAtANAtO'+uQ`O,5?gOOQO-E<y-E<yO',QQ?MtO1G6gOOQO,5?i,5?iOOQO-E<{-E<{OOQ[1G3a1G3aO',[Q`O,5=POOQ[<<Lf<<LfO!&oQMhO<<LfO&;yQ`O<<LfO',aQ`O<<LfO%[QlO<<LfOOQ[<<Lh<<LhO9qQ!0LrO<<LhO$$VQ`O<<LhO9[Q`O<<LhO',iQpO1G5UO',tQ`O7+,SOOQ[AN=]AN=]O9qQ!0LrOAN=]OOQ[<= q<= qOOQ[<= r<= rO',|Q`O<= qO'-RQ`O<= rOOQ[<<Lp<<LpO'-WQ`O<<LpO'-]QlO<<LpOOQ[1G3z1G3zO>}Q`O7+)kO'-dQ`O<<JQO'-oQ?MtO<<JQOOQO<<Hy<<HyOOQ!0LfAN?fAN?fOOQOAN?_AN?_O$@_Q!0MxOAN?_OOQOAN?OAN?OO%[QlOAN?_OOQO<<Mx<<MxOOQ[G27VG27VO!&oQMhOG27VO#'oQ`OG27VO'-yQ!fOG27VO4UQ!fOG27VO'.QQ`OG27VO'.YQ?MtO<<JfO'.gQ?MvO1G2`O'0]Q?MvO,5?`O'2`Q?MvO,5?bO'4cQ?MvO1G2sO'6fQ?MvO1G2uO'8iQ?MtO<<KXO'8vQ?MtO<<I{OOQO1G1w1G1wO!+xQMhOANAiOOQO7+*b7+*bO'9TQ`O7+*bO'9`Q`O<= gO'9hQ!dO7+*dOOQ!0Lb<<Kq<<KqO$){Q`O<<KqOClQ`O<<KqO'9rQ`O<<KqO!&oQMhO<<KqOOQ!0Lb<<Ko<<KoO!BqQpO<<KoO'9}Q!dO<<KqOOQ!0Lb<<Kp<<KpO':XQ`O<<KqO!&oQMhO<<KqO$){Q`O<<KpO':^QMjOANDbO':hQ!0MvO<<KtOOQO7+*q7+*qO9gQ`O7+*qO':xQ`O<= pOOQ[G27rG27rO9qQ!0LrOG27rO!)PQlO1G5RO';QQ`O7+,RO';YQ`O1G2kO&;yQ`OANBQOOQ[ANBQANBQO!&oQMhOANBQO';_Q`OANBQOOQ[ANBSANBSO9qQ!0LrOANBSO$$VQ`OANBSOOQO'#H_'#H_OOQO7+*p7+*pOOQ[G22wG22wOOQ[ANE]ANE]OOQ[ANE^ANE^OOQ[ANB[ANB[O';gQ`OANB[OOQ[<<MV<<MVO!)PQlOAN?lOOQOG24yG24yO$@_Q!0MxOG24yO#'oQ`OLD,qOOQ[LD,qLD,qO!&oQMhOLD,qO';lQ!fOLD,qO';sQ?MvO7+'zO'=iQ?MvO,5?`O'?lQ?MvO,5?bO'AoQ?MvO7+'|O'CeQMjOG27TOOQO<<M|<<M|OOQ!0LbANA]ANA]O$){Q`OANA]OClQ`OANA]O'CuQ!dOANA]OOQ!0LbANAZANAZO'C|Q`OANA]O!&oQMhOANA]O'DXQ!dOANA]OOQ!0LbANA[ANA[OOQO<<N]<<N]OOQ[LD-^LD-^O'DcQ?MtO7+*mOOQO'#Gf'#GfOOQ[G27lG27lO&;yQ`OG27lO!&oQMhOG27lOOQ[G27nG27nO9qQ!0LrOG27nOOQ[G27vG27vO'DmQ?MtOG25WOOQOLD*eLD*eOOQ[!$(!]!$(!]O#'oQ`O!$(!]O!&oQMhO!$(!]O'DwQ!0MzOG27TOOQ!0LbG26wG26wO$){Q`OG26wO'GYQ`OG26wOClQ`OG26wO'GeQ!dOG26wO!&oQMhOG26wOOQ[LD-WLD-WO&;yQ`OLD-WOOQ[LD-YLD-YOOQ[!)9Ew!)9EwO#'oQ`O!)9EwOOQ!0LbLD,cLD,cO$){Q`OLD,cOClQ`OLD,cO'GlQ`OLD,cO'GwQ!dOLD,cOOQ[!$(!r!$(!rOOQ[!.K;c!.K;cO'HOQ?MvOG27TOOQ!0Lb!$( }!$( }O$){Q`O!$( }OClQ`O!$( }O'ItQ`O!$( }OOQ!0Lb!)9Ei!)9EiO$){Q`O!)9EiOClQ`O!)9EiOOQ!0Lb!.K;T!.K;TO$){Q`O!.K;TOOQ!0Lb!4/0o!4/0oO!)PQlO'#DzO1PQ`O'#EXO'JPQ!fO'#JqO'JWQ!L^O'#DvO'J_QlO'#EOO'JfQ!fO'#CiO'L|Q!fO'#CiO!)PQlO'#EQO'M^QlO,5;ZO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO,5;eO!)PQlO'#IoO( aQ`O,5<iO!)PQlO,5;eO( iQMhO,5;eO(#SQMhO,5;eO!)PQlO,5;wO!&oQMhO'#GmO( iQMhO'#GmO!&oQMhO'#GoO( iQMhO'#GoO1SQ`O'#DZO1SQ`O'#DZO!&oQMhO'#GPO( iQMhO'#GPO!&oQMhO'#GRO( iQMhO'#GRO!&oQMhO'#GaO( iQMhO'#GaO!)PQlO,5:jO(#ZQpO'#D_O(#eQpO'#JuO!)PQlO,5@nO'M^QlO1G0uO(#oQ?MtO'#CiO!)PQlO1G2PO!&oQMhO'#ItO( iQMhO'#ItO!&oQMhO'#IvO( iQMhO'#IvO(#yQ!dO'#CrO!&oQMhO,5<tO( iQMhO,5<tO'M^QlO1G2RO!)PQlO7+&zO!&oQMhO1G2`O( iQMhO1G2`O!&oQMhO'#ItO( iQMhO'#ItO!&oQMhO'#IvO( iQMhO'#IvO!&oQMhO1G2bO( iQMhO1G2bO'M^QlO7+'mO'M^QlO7+&aO!&oQMhOANAiO( iQMhOANAiO($^Q`O'#EoO($cQ`O'#EoO($kQ`O'#F]O($pQ`O'#EyO($uQ`O'#KSO(%QQ`O'#KQO(%]Q`O,5;ZO(%bQMjO,5<eO(%iQ`O'#GYO(%nQ`O'#GYO(%sQ`O,5<gO(%{Q`O,5;ZO(&TQ?MtO1G1`O(&[Q`O,5<tO(&aQ`O,5<tO(&fQ`O,5<vO(&kQ`O,5<vO(&pQ`O1G2RO(&uQ`O1G0uO(&zQMjO<<K}O('RQMjO<<K}O7eQMhO'#F|O9[Q`O'#F{OAjQ`O'#EnO!)PQlO,5;tO!3dQ`O'#GYO!3dQ`O'#GYO!3dQ`O'#G[O!3dQ`O'#G[O!+xQMhO7+(cO!+xQMhO7+(cO%-mQ!dO1G2wO%-mQ!dO1G2wO!&oQMhO,5=]O!&oQMhO,5=]",stateData:"((X~O'{OS'|OSTOS'}RQ~OPYOQYOSfOY!VOaqOdzOeyOl!POpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!uwO!xxO!|]O$W|O$niO%h}O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO&V!WO&]!XO&_!YO&a!ZO&c![O&f!]O&l!^O&r!_O&t!`O&v!aO&x!bO&z!cO(SSO(UTO(XUO(`VO(n[O~OWtO~P`OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(S!dO(UTO(XUO(`VO(n[O~Oa!wOs!nO!S!oO!b!yO!c!vO!d!vO!|;wO#T!pO#U!pO#V!xO#W!pO#X!pO#[!zO#]!zO(T!lO(UTO(XUO(d!mO(n!sO~O'}!{O~OP]XR]X[]Xa]Xj]Xr]X!Q]X!S]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X'y]X(`]X(q]X(x]X(y]X~O!g%RX~P(qO_!}O(U#PO(V!}O(W#PO~O_#QO(W#PO(X#PO(Y#QO~Ox#SO!U#TO(a#TO(b#VO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(S;{O(UTO(XUO(`VO(n[O~O![#ZO!]#WO!Y(gP!Y(uP~P+}O!^#cO~P`OPYOQYOSfOd!jOe!iOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(UTO(XUO(`VO(n[O~Op#mO![#iO!|]O#i#lO#j#iO(S;|O!k(rP~P.iO!l#oO(S#nO~O!x#sO!|]O%h#tO~O#k#uO~O!g#vO#k#uO~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!]$_O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(`VO(q$YO(x#|O(y#}O~Oa(eX'y(eX'v(eX!k(eX!Y(eX!_(eX%i(eX!g(eX~P1qO#S$dO#`$eO$Q$eOP(fXR(fX[(fXj(fXr(fX!Q(fX!S(fX!](fX!l(fX!p(fX#R(fX#n(fX#o(fX#p(fX#q(fX#r(fX#s(fX#t(fX#u(fX#v(fX#x(fX#z(fX#{(fX(`(fX(q(fX(x(fX(y(fX!_(fX%i(fX~Oa(fX'y(fX'v(fX!Y(fX!k(fXv(fX!g(fX~P4UO#`$eO~O$]$hO$_$gO$f$mO~OSfO!_$nO$i$oO$k$qO~Oh%VOj%cOk%cOl%cOp%WOr%XOs$tOt$tOz%YO|%ZO!O%[O!S${O!_$|O!i%aO!l$xO#j%bO$W%_O$t%]O$v%^O$y%`O(S$sO(UTO(XUO(`$uO(x$}O(y%POg(]P~O!l%dO~O!S%gO!_%hO(S%fO~O!g%lO~Oa%mO'y%mO~O!Q%qO~P%[O(T!lO~P%[O%n%uO~P%[Oh%VO!l%dO(S%fO(T!lO~Oe%|O!l%dO(S%fO~Oj$RO~O!Q&RO!_&OO!l&QO%j&UO(S%fO(T!lO(UTO(XUO`)VP~O!x#sO~O%s&WO!S)RX!_)RX(S)RX~O(S&XO~Ol!PO!u&^O%j!QO%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO~Od&cOe&bO!x&`O%h&aO%{&_O~P<VOd&fOeyOl!PO!_&eO!u&^O!xxO!|]O%h}O%l!OO%m!OO%n!OO%q!RO%s!SO%v!TO%w!TO%y!UO~Ob&iO#`&lO%j&gO(T!lO~P=[O!l&mO!u&qO~O!l#oO~O!_XO~Oa%mO'w&yO'y%mO~Oa%mO'w&|O'y%mO~Oa%mO'w'OO'y%mO~O'v]X!Y]Xv]X!k]X&Z]X!_]X%i]X!g]X~P(qO!b']O!c'UO!d'UO(T!lO(UTO(XUO~Os'SO!S'RO!['VO(d'QO!^(hP!^(wP~P@cOn'`O!_'^O(S%fO~Oe'eO!l%dO(S%fO~O!Q&RO!l&QO~Os!nO!S!oO!|;wO#T!pO#U!pO#W!pO#X!pO(T!lO(UTO(XUO(d!mO(n!sO~O!b'kO!c'jO!d'jO#V!pO#['lO#]'lO~PA}Oa%mOh%VO!g#vO!l%dO'y%mO(q'nO~O!p'rO#`'pO~PC]Os!nO!S!oO(UTO(XUO(d!mO(n!sO~O!_XOs(lX!S(lX!b(lX!c(lX!d(lX!|(lX#T(lX#U(lX#V(lX#W(lX#X(lX#[(lX#](lX(T(lX(U(lX(X(lX(d(lX(n(lX~O!c'jO!d'jO(T!lO~PC{O(O'vO(P'vO(Q'xO~O_!}O(U'zO(V!}O(W'zO~O_#QO(W'zO(X'zO(Y#QO~Ov'|O~P%[Ox#SO!U#TO(a#TO(b(PO~O![(RO!Y'VX!Y']X!]'VX!]']X~P+}O!](TO!Y(gX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!](TO!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(`VO(q$YO(x#|O(y#}O~O!Y(gX~PGvO!Y(YO~O!Y(tX!](tX!g(tX!k(tX(q(tX~O#`(tX#k#dX!^(tX~PIyO#`(ZO!Y(vX!](vX~O!]([O!Y(uX~O!Y(_O~O#`$eO~PIyO!^(`O~P`OR#zO!Q#yO!S#{O!l#xO(`VOP!na[!naj!nar!na!]!na!p!na#R!na#n!na#o!na#p!na#q!na#r!na#s!na#t!na#u!na#v!na#x!na#z!na#{!na(q!na(x!na(y!na~Oa!na'y!na'v!na!Y!na!k!nav!na!_!na%i!na!g!na~PKaO!k(aO~O!g#vO#`(bO(q'nO!](sXa(sX'y(sX~O!k(sX~PM|O!S%gO!_%hO!|]O#i(gO#j(fO(S%fO~O!](hO!k(rX~O!k(jO~O!S%gO!_%hO#j(fO(S%fO~OP(fXR(fX[(fXj(fXr(fX!Q(fX!S(fX!](fX!l(fX!p(fX#R(fX#n(fX#o(fX#p(fX#q(fX#r(fX#s(fX#t(fX#u(fX#v(fX#x(fX#z(fX#{(fX(`(fX(q(fX(x(fX(y(fX~O!g#vO!k(fX~P! jOR(lO!Q(kO!l#xO#S$dO!|!{a!S!{a~O!x!{a%h!{a!_!{a#i!{a#j!{a(S!{a~P!#kO!x(pO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_XO!iuO!lZO!oYO!pYO!qYO!svO!u!gO!x!hO$W!kO$niO(S!dO(UTO(XUO(`VO(n[O~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<eO!S${O!_$|O!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(S(tO(UTO(XUO(`$uO(x$}O(y%PO~O#k(vO~O![(xO!k(jP~P%[O(d(zO(n[O~O!S(|O!l#xO(d(zO(n[O~OP;vOQ;vOSfOd=rOe!iOpkOr;vOskOtkOzkO|;vO!O;vO!SWO!WkO!XkO!_!eO!i;yO!lZO!o;vO!p;vO!q;vO!s;zO!u;}O!x!hO$W!kO$n=pO(S)ZO(UTO(XUO(`VO(n[O~O!]$_Oa$qa'y$qa'v$qa!k$qa!Y$qa!_$qa%i$qa!g$qa~Ol)bO~P!&oOh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O%[O!S${O!_$|O!i%aO!l$xO#j%bO$W%_O$t%]O$v%^O$y%`O(S(tO(UTO(XUO(`$uO(x$}O(y%PO~Og(oP~P!+xO!Q)gO!g)fO!_$^X$Z$^X$]$^X$_$^X$f$^X~O!g)fO!_(zX$Z(zX$](zX$_(zX$f(zX~O!Q)gO~P!.RO!Q)gO!_(zX$Z(zX$](zX$_(zX$f(zX~O!_)iO$Z)mO$])hO$_)hO$f)nO~O![)qO~P!)PO$]$hO$_$gO$f)uO~On$zX!Q$zX#S$zX'x$zX(x$zX(y$zX~OgmXg$zXnmX!]mX#`mX~P!/wOx)wO(a)xO(b)zO~On*TO!Q)|O'x)}O(x$}O(y%PO~Og){O~P!0{Og*UO~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<eO!S*WO!_*XO!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(UTO(XUO(`$uO(x$}O(y%PO~O![*[O(S*VO!k(}P~P!1jO#k*^O~O!l*_O~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<eO!S${O!_$|O!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(S*aO(UTO(XUO(`$uO(x$}O(y%PO~O![*dO!Y)OP~P!3iOr*pOs!nO!S*fO!b*nO!c*hO!d*hO!l*_O#[*oO%`*jO(T!lO(UTO(XUO(d!mO~O!^*mO~P!5^O#S$dOn(_X!Q(_X'x(_X(x(_X(y(_X!](_X#`(_X~Og(_X$O(_X~P!6`On*uO#`*tOg(^X!](^X~O!]*vOg(]X~Oj%cOk%cOl%cO(S&XOg(]P~Os*yO~O!l+OO~O(S(tO~Op+TO!S%gO![#iO!_%hO!|]O#i#lO#j#iO(S%fO!k(rP~O!g#vO#k+UO~O!S%gO![+WO!]([O!_%hO(S%fO!Y(uP~Os'YO!S+YO![+XO(UTO(XUO(d(zO~O!^(wP~P!9iO!]+ZOa)SX'y)SX~OP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO#z$WO#{$XO(`VO(q$YO(x#|O(y#}O~Oa!ja!]!ja'y!ja'v!ja!Y!ja!k!jav!ja!_!ja%i!ja!g!ja~P!:aOR#zO!Q#yO!S#{O!l#xO(`VOP!ra[!raj!rar!ra!]!ra!p!ra#R!ra#n!ra#o!ra#p!ra#q!ra#r!ra#s!ra#t!ra#u!ra#v!ra#x!ra#z!ra#{!ra(q!ra(x!ra(y!ra~Oa!ra'y!ra'v!ra!Y!ra!k!rav!ra!_!ra%i!ra!g!ra~P!<wOR#zO!Q#yO!S#{O!l#xO(`VOP!ta[!taj!tar!ta!]!ta!p!ta#R!ta#n!ta#o!ta#p!ta#q!ta#r!ta#s!ta#t!ta#u!ta#v!ta#x!ta#z!ta#{!ta(q!ta(x!ta(y!ta~Oa!ta'y!ta'v!ta!Y!ta!k!tav!ta!_!ta%i!ta!g!ta~P!?_Oh%VOn+dO!_'^O%i+cO~O!g+fOa([X!_([X'y([X!]([X~Oa%mO!_XO'y%mO~Oh%VO!l%dO~Oh%VO!l%dO(S%fO~O!g#vO#k(vO~Ob+qO%j+rO(S+nO(UTO(XUO!^)WP~O!]+sO`)VX~O[+wO~O`+xO~O!_&OO(S%fO(T!lO`)VP~Oh%VO#`+}O~Oh%VOn,QO!_$|O~O!_,SO~O!Q,UO!_XO~O%n%uO~O!x,ZO~Oe,`O~Ob,aO(S#nO(UTO(XUO!^)UP~Oe%|O~O%j!QO(S&XO~P=[O[,fO`,eO~OPYOQYOSfOdzOeyOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!iuO!lZO!oYO!pYO!qYO!svO!xxO!|]O$niO%h}O(UTO(XUO(`VO(n[O~O!_!eO!u!gO$W!kO(S!dO~P!F_O`,eOa%mO'y%mO~OPYOQYOSfOd!jOe!iOpkOrYOskOtkOzkO|YO!OYO!SWO!WkO!XkO!_!eO!iuO!lZO!oYO!pYO!qYO!svO!x!hO$W!kO$niO(S!dO(UTO(XUO(`VO(n[O~Oa,kOl!OO!uwO%l!OO%m!OO%n!OO~P!HwO!l&mO~O&],qO~O!_,sO~O&n,uO&p,vOP&kaQ&kaS&kaY&kaa&kad&kae&kal&kap&kar&kas&kat&kaz&ka|&ka!O&ka!S&ka!W&ka!X&ka!_&ka!i&ka!l&ka!o&ka!p&ka!q&ka!s&ka!u&ka!x&ka!|&ka$W&ka$n&ka%h&ka%j&ka%l&ka%m&ka%n&ka%q&ka%s&ka%v&ka%w&ka%y&ka&V&ka&]&ka&_&ka&a&ka&c&ka&f&ka&l&ka&r&ka&t&ka&v&ka&x&ka&z&ka'v&ka(S&ka(U&ka(X&ka(`&ka(n&ka!^&ka&d&kab&ka&i&ka~O(S,{O~Oh!eX!]!RX!^!RX!g!RX!g!eX!l!eX#`!RX~O!]!eX!^!eX~P# }O!g-QO#`-POh(iX!]#hX!^#hX!g(iX!l(iX~O!](iX!^(iX~P#!pOh%VO!g-SO!l%dO!]!aX!^!aX~Os!nO!S!oO(UTO(XUO(d!mO~OP;vOQ;vOSfOd=rOe!iOpkOr;vOskOtkOzkO|;vO!O;vO!SWO!WkO!XkO!_!eO!i;yO!lZO!o;vO!p;vO!q;vO!s;zO!u;}O!x!hO$W!kO$n=pO(UTO(XUO(`VO(n[O~O(S<rO~P#$VO!]-WO!^(hX~O!^-YO~O!g-QO#`-PO!]#hX!^#hX~O!]-ZO!^(wX~O!^-]O~O!c-^O!d-^O(T!lO~P##tO!^-aO~P'_On-dO!_'^O~O!Y-iO~Os!{a!b!{a!c!{a!d!{a#T!{a#U!{a#V!{a#W!{a#X!{a#[!{a#]!{a(T!{a(U!{a(X!{a(d!{a(n!{a~P!#kO!p-nO#`-lO~PC]O!c-pO!d-pO(T!lO~PC{Oa%mO#`-lO'y%mO~Oa%mO!g#vO#`-lO'y%mO~Oa%mO!g#vO!p-nO#`-lO'y%mO(q'nO~O(O'vO(P'vO(Q-uO~Ov-vO~O!Y'Va!]'Va~P!:aO![-zO!Y'VX!]'VX~P%[O!](TO!Y(ga~O!Y(ga~PGvO!]([O!Y(ua~O!S%gO![.OO!_%hO(S%fO!Y']X!]']X~O#`.QO!](sa!k(saa(sa'y(sa~O!g#vO~P#,]O!](hO!k(ra~O!S%gO!_%hO#j.UO(S%fO~Op.ZO!S%gO![.WO!_%hO!|]O#i.YO#j.WO(S%fO!]'`X!k'`X~OR._O!l#xO~Oh%VOn.bO!_'^O%i.aO~Oa#ci!]#ci'y#ci'v#ci!Y#ci!k#civ#ci!_#ci%i#ci!g#ci~P!:aOn=|O!Q)|O'x)}O(x$}O(y%PO~O#k#_aa#_a#`#_a'y#_a!]#_a!k#_a!_#_a!Y#_a~P#/XO#k(_XP(_XR(_X[(_Xa(_Xj(_Xr(_X!S(_X!l(_X!p(_X#R(_X#n(_X#o(_X#p(_X#q(_X#r(_X#s(_X#t(_X#u(_X#v(_X#x(_X#z(_X#{(_X'y(_X(`(_X(q(_X!k(_X!Y(_X'v(_Xv(_X!_(_X%i(_X!g(_X~P!6`O!].oO!k(jX~P!:aO!k.rO~O!Y.tO~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(`VO[#mia#mij#mir#mi!]#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'y#mi(q#mi(x#mi(y#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#n#mi~P#2wO#n$OO~P#2wOP$[OR#zOr$aO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(`VO[#mia#mij#mi!]#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'y#mi(q#mi(x#mi(y#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#r#mi~P#5fO#r$QO~P#5fOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO(`VOa#mi!]#mi#x#mi#z#mi#{#mi'y#mi(q#mi(x#mi(y#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#v#mi~P#8TOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO(`VO(y#}Oa#mi!]#mi#z#mi#{#mi'y#mi(q#mi(x#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#x$UO~P#:kO#x#mi~P#:kO#v$SO~P#8TOP$[OR#zO[$cOj$ROr$aO!Q#yO!S#{O!l#xO!p$[O#R$RO#n$OO#o$PO#p$PO#q$PO#r$QO#s$RO#t$RO#u$bO#v$SO#x$UO(`VO(x#|O(y#}Oa#mi!]#mi#{#mi'y#mi(q#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~O#z#mi~P#=aO#z$WO~P#=aOP]XR]X[]Xj]Xr]X!Q]X!S]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(`]X(q]X(x]X(y]X!]]X!^]X~O$O]X~P#@OOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O#v<TO#x<VO#z<XO#{<YO(`VO(q$YO(x#|O(y#}O~O$O.vO~P#B]O#S$dO#`<`O$Q<`O$O(fX!^(fX~P! jOa'ca!]'ca'y'ca'v'ca!k'ca!Y'cav'ca!_'ca%i'ca!g'ca~P!:aO[#mia#mij#mir#mi!]#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi'y#mi(q#mi'v#mi!Y#mi!k#miv#mi!_#mi%i#mi!g#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n$OO#o$PO#p$PO#q$PO(`VO(x#mi(y#mi~P#E_On=|O!Q)|O'x)}O(x$}O(y%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(`#mi~P#E_O!].zOg(oX~P!0{Og.|O~Oa$Pi!]$Pi'y$Pi'v$Pi!Y$Pi!k$Piv$Pi!_$Pi%i$Pi!g$Pi~P!:aO$].}O$_.}O~O$]/OO$_/OO~O!g)fO#`/PO!_$cX$Z$cX$]$cX$_$cX$f$cX~O![/QO~O!_)iO$Z/SO$])hO$_)hO$f/TO~O!]<ZO!^(eX~P#B]O!^/UO~O!g)fO$f(zX~O$f/WO~Ov/XO~P!&oOx)wO(a)xO(b/[O~O!S/_O~O(x$}On%aa!Q%aa'x%aa(y%aa!]%aa#`%aa~Og%aa$O%aa~P#LaO(y%POn%ca!Q%ca'x%ca(x%ca!]%ca#`%ca~Og%ca$O%ca~P#MSO!]fX!gfX!kfX!k$zX(qfX~P!/wO![/hO!]([O(S/gO!Y(uP!Y)OP~P!1jOr*pO!b*nO!c*hO!d*hO!l*_O#[*oO%`*jO(T!lO(UTO(XUO~Os<oO!S/iO![+XO!^*mO(d<nO!^(wP~P#NmO!k/jO~P#/XO!]/kO!g#vO(q'nO!k(}X~O!k/pO~O!S%gO![*[O!_%hO(S%fO!k(}P~O#k/rO~O!Y$zX!]$zX!g%RX~P!/wO!]/sO!Y)OX~P#/XO!g/uO~O!Y/wO~OpkO(S/xO~P.iOh%VOr/}O!g#vO!l%dO(q'nO~O!g+fO~Oa%mO!]0RO'y%mO~O!^0TO~P!5^O!c0UO!d0UO(T!lO~P##tOs!nO!S0VO(UTO(XUO(d!mO~O#[0XO~Og%aa!]%aa#`%aa$O%aa~P!0{Og%ca!]%ca#`%ca$O%ca~P!0{Oj%cOk%cOl%cO(S&XOg'lX!]'lX~O!]*vOg(]a~Og0bO~OR0cO!Q0cO!S0dO#S$dOn}a'x}a(x}a(y}a!]}a#`}a~Og}a$O}a~P$&vO!Q)|O'x)}On$sa(x$sa(y$sa!]$sa#`$sa~Og$sa$O$sa~P$'rO!Q)|O'x)}On$ua(x$ua(y$ua!]$ua#`$ua~Og$ua$O$ua~P$(eO#k0gO~Og%Ta!]%Ta#`%Ta$O%Ta~P!0{On0iO#`0hOg(^a!](^a~O!g#vO~O#k0lO~O!]+ZOa)Sa'y)Sa~OR#zO!Q#yO!S#{O!l#xO(`VOP!ri[!rij!rir!ri!]!ri!p!ri#R!ri#n!ri#o!ri#p!ri#q!ri#r!ri#s!ri#t!ri#u!ri#v!ri#x!ri#z!ri#{!ri(q!ri(x!ri(y!ri~Oa!ri'y!ri'v!ri!Y!ri!k!riv!ri!_!ri%i!ri!g!ri~P$*bOh%VOr%XOs$tOt$tOz%YO|%ZO!O<eO!S${O!_$|O!i=vO!l$xO#j<kO$W%_O$t<gO$v<iO$y%`O(UTO(XUO(`$uO(x$}O(y%PO~Op0uO%]0vO(S0tO~P$,xO!g+fOa([a!_([a'y([a!]([a~O#k0|O~O[]X!]fX!^fX~O!]0}O!^)WX~O!^1PO~O[1QO~Ob1SO(S+nO(UTO(XUO~O!_&OO(S%fO`'tX!]'tX~O!]+sO`)Va~O!k1VO~P!:aO[1YO~O`1ZO~O#`1^O~On1aO!_$|O~O(d(zO!^)TP~Oh%VOn1jO!_1gO%i1iO~O[1tO!]1rO!^)UX~O!^1uO~O`1wOa%mO'y%mO~O(S#nO(UTO(XUO~O#S$dO#`$eO$Q$eOP(fXR(fX[(fXr(fX!Q(fX!S(fX!](fX!l(fX!p(fX#R(fX#n(fX#o(fX#p(fX#q(fX#r(fX#s(fX#t(fX#u(fX#v(fX#x(fX#z(fX#{(fX(`(fX(q(fX(x(fX(y(fX~Oj1zO&Z1{Oa(fX~P$2cOj1zO#`$eO&Z1{O~Oa1}O~P%[Oa2PO~O&d2SOP&biQ&biS&biY&bia&bid&bie&bil&bip&bir&bis&bit&biz&bi|&bi!O&bi!S&bi!W&bi!X&bi!_&bi!i&bi!l&bi!o&bi!p&bi!q&bi!s&bi!u&bi!x&bi!|&bi$W&bi$n&bi%h&bi%j&bi%l&bi%m&bi%n&bi%q&bi%s&bi%v&bi%w&bi%y&bi&V&bi&]&bi&_&bi&a&bi&c&bi&f&bi&l&bi&r&bi&t&bi&v&bi&x&bi&z&bi'v&bi(S&bi(U&bi(X&bi(`&bi(n&bi!^&bib&bi&i&bi~Ob2YO!^2WO&i2XO~P`O!_XO!l2[O~O&p,vOP&kiQ&kiS&kiY&kia&kid&kie&kil&kip&kir&kis&kit&kiz&ki|&ki!O&ki!S&ki!W&ki!X&ki!_&ki!i&ki!l&ki!o&ki!p&ki!q&ki!s&ki!u&ki!x&ki!|&ki$W&ki$n&ki%h&ki%j&ki%l&ki%m&ki%n&ki%q&ki%s&ki%v&ki%w&ki%y&ki&V&ki&]&ki&_&ki&a&ki&c&ki&f&ki&l&ki&r&ki&t&ki&v&ki&x&ki&z&ki'v&ki(S&ki(U&ki(X&ki(`&ki(n&ki!^&ki&d&kib&ki&i&ki~O!Y2bO~O!]!aa!^!aa~P#B]Os!nO!S!oO![2hO(d!mO!]'WX!^'WX~P@cO!]-WO!^(ha~O!]'^X!^'^X~P!9iO!]-ZO!^(wa~O!^2oO~P'_Oa%mO#`2xO'y%mO~Oa%mO!g#vO#`2xO'y%mO~Oa%mO!g#vO!p2|O#`2xO'y%mO(q'nO~Oa%mO'y%mO~P!:aO!]$_Ov$qa~O!Y'Vi!]'Vi~P!:aO!](TO!Y(gi~O!]([O!Y(ui~O!Y(vi!](vi~P!:aO!](si!k(sia(si'y(si~P!:aO#`3OO!](si!k(sia(si'y(si~O!](hO!k(ri~O!S%gO!_%hO!|]O#i3TO#j3SO(S%fO~O!S%gO!_%hO#j3SO(S%fO~On3[O!_'^O%i3ZO~Oh%VOn3[O!_'^O%i3ZO~O#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'y%aa(`%aa(q%aa!k%aa!Y%aa'v%aav%aa!_%aa%i%aa!g%aa~P#LaO#k%caP%caR%ca[%caa%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'y%ca(`%ca(q%ca!k%ca!Y%ca'v%cav%ca!_%ca%i%ca!g%ca~P#MSO#k%aaP%aaR%aa[%aaa%aaj%aar%aa!S%aa!]%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa'y%aa(`%aa(q%aa!k%aa!Y%aa'v%aa#`%aav%aa!_%aa%i%aa!g%aa~P#/XO#k%caP%caR%ca[%caa%caj%car%ca!S%ca!]%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca'y%ca(`%ca(q%ca!k%ca!Y%ca'v%ca#`%cav%ca!_%ca%i%ca!g%ca~P#/XO#k}aP}a[}aa}aj}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a'y}a(`}a(q}a!k}a!Y}a'v}av}a!_}a%i}a!g}a~P$&vO#k$saP$saR$sa[$saa$saj$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa'y$sa(`$sa(q$sa!k$sa!Y$sa'v$sav$sa!_$sa%i$sa!g$sa~P$'rO#k$uaP$uaR$ua[$uaa$uaj$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua'y$ua(`$ua(q$ua!k$ua!Y$ua'v$uav$ua!_$ua%i$ua!g$ua~P$(eO#k%TaP%TaR%Ta[%Taa%Taj%Tar%Ta!S%Ta!]%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta'y%Ta(`%Ta(q%Ta!k%Ta!Y%Ta'v%Ta#`%Tav%Ta!_%Ta%i%Ta!g%Ta~P#/XOa#cq!]#cq'y#cq'v#cq!Y#cq!k#cqv#cq!_#cq%i#cq!g#cq~P!:aO![3dO!]'XX!k'XX~P%[O!].oO!k(ja~O!].oO!k(ja~P!:aO!Y3gO~O$O!na!^!na~PKaO$O!ja!]!ja!^!ja~P#B]O$O!ra!^!ra~P!<wO$O!ta!^!ta~P!?_Og'[X!]'[X~P!+xO!].zOg(oa~OSfO!_3{O$d3|O~O!^4QO~Ov4RO~P#/XOa$mq!]$mq'y$mq'v$mq!Y$mq!k$mqv$mq!_$mq%i$mq!g$mq~P!:aO!Y4TO~P!&oO!S4UO~O!Q)|O'x)}O(y%POn'ha(x'ha!]'ha#`'ha~Og'ha$O'ha~P%,XO!Q)|O'x)}On'ja(x'ja(y'ja!]'ja#`'ja~Og'ja$O'ja~P%,zO(q$YO~P#/XO!YfX!Y$zX!]fX!]$zX!g%RX#`fX~P!/wO(S<xO~P!1jO!S%gO![4XO!_%hO(S%fO!]'dX!k'dX~O!]/kO!k(}a~O!]/kO!g#vO!k(}a~O!]/kO!g#vO(q'nO!k(}a~Og$|i!]$|i#`$|i$O$|i~P!0{O![4aO!Y'fX!]'fX~P!3iO!]/sO!Y)Oa~O!]/sO!Y)Oa~P#/XOP]XR]X[]Xj]Xr]X!Q]X!S]X!Y]X!]]X!l]X!p]X#R]X#S]X#`]X#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(`]X(q]X(x]X(y]X~Oj%YX!g%YX~P%0kOj4fO!g#vO~Oh%VO!g#vO!l%dO~Oh%VOr4kO!l%dO(q'nO~Or4pO!g#vO(q'nO~Os!nO!S4qO(UTO(XUO(d!mO~O(x$}On%ai!Q%ai'x%ai(y%ai!]%ai#`%ai~Og%ai$O%ai~P%4[O(y%POn%ci!Q%ci'x%ci(x%ci!]%ci#`%ci~Og%ci$O%ci~P%4}Og(^i!](^i~P!0{O#`4wOg(^i!](^i~P!0{O!k4zO~Oa$oq!]$oq'y$oq'v$oq!Y$oq!k$oqv$oq!_$oq%i$oq!g$oq~P!:aO!Y5QO~O!]5RO!_)PX~P#/XOa$zX!_$zX%^]X'y$zX!]$zX~P!/wO%^5UOaoXnoX!QoX!_oX'xoX'yoX(xoX(yoX!]oX~Op5VO(S#nO~O%^5UO~Ob5]O%j5^O(S+nO(UTO(XUO!]'sX!^'sX~O!]0}O!^)Wa~O[5bO~O`5cO~Oa%mO'y%mO~P#/XO!]5kO#`5mO!^)TX~O!^5nO~Or5tOs!nO!S*fO!b!yO!c!vO!d!vO!|;wO#T!pO#U!pO#V!pO#W!pO#X!pO#[5sO#]!zO(T!lO(UTO(XUO(d!mO(n!sO~O!^5rO~P%:YOn5yO!_1gO%i5xO~Oh%VOn5yO!_1gO%i5xO~Ob6QO(S#nO(UTO(XUO!]'rX!^'rX~O!]1rO!^)Ua~O(UTO(XUO(d6SO~O`6WO~Oj6ZO&Z6[O~PM|O!k6]O~P%[Oa6_O~Oa6_O~P%[Ob2YO!^6dO&i2XO~P`O!g6fO~O!g6hOh(ii!](ii!^(ii!g(ii!l(iir(ii(q(ii~O!]#hi!^#hi~P#B]O#`6iO!]#hi!^#hi~O!]!ai!^!ai~P#B]Oa%mO#`6rO'y%mO~Oa%mO!g#vO#`6rO'y%mO~O!](sq!k(sqa(sq'y(sq~P!:aO!](hO!k(rq~O!S%gO!_%hO#j6yO(S%fO~O!_'^O%i6|O~On7QO!_'^O%i6|O~O#k'haP'haR'ha['haa'haj'har'ha!S'ha!l'ha!p'ha#R'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#t'ha#u'ha#v'ha#x'ha#z'ha#{'ha'y'ha(`'ha(q'ha!k'ha!Y'ha'v'hav'ha!_'ha%i'ha!g'ha~P%,XO#k'jaP'jaR'ja['jaa'jaj'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja'y'ja(`'ja(q'ja!k'ja!Y'ja'v'jav'ja!_'ja%i'ja!g'ja~P%,zO#k$|iP$|iR$|i[$|ia$|ij$|ir$|i!S$|i!]$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i'y$|i(`$|i(q$|i!k$|i!Y$|i'v$|i#`$|iv$|i!_$|i%i$|i!g$|i~P#/XO#k%aiP%aiR%ai[%aia%aij%air%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai'y%ai(`%ai(q%ai!k%ai!Y%ai'v%aiv%ai!_%ai%i%ai!g%ai~P%4[O#k%ciP%ciR%ci[%cia%cij%cir%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci'y%ci(`%ci(q%ci!k%ci!Y%ci'v%civ%ci!_%ci%i%ci!g%ci~P%4}O!]'Xa!k'Xa~P!:aO!].oO!k(ji~O$O#ci!]#ci!^#ci~P#B]OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O(`VO[#mij#mir#mi#R#mi#o#mi#p#mi#q#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(q#mi(x#mi(y#mi!]#mi!^#mi~O#n#mi~P%MXO#n<PO~P%MXOP$[OR#zOr<]O!Q#yO!S#{O!l#xO!p$[O#n<PO#o<QO#p<QO#q<QO(`VO[#mij#mi#R#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(q#mi(x#mi(y#mi!]#mi!^#mi~O#r#mi~P& aO#r<RO~P& aOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O(`VO#x#mi#z#mi#{#mi$O#mi(q#mi(x#mi(y#mi!]#mi!^#mi~O#v#mi~P&#iOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O#v<TO(`VO(y#}O#z#mi#{#mi$O#mi(q#mi(x#mi!]#mi!^#mi~O#x<VO~P&%jO#x#mi~P&%jO#v<TO~P&#iOP$[OR#zO[<_Oj<SOr<]O!Q#yO!S#{O!l#xO!p$[O#R<SO#n<PO#o<QO#p<QO#q<QO#r<RO#s<SO#t<SO#u<^O#v<TO#x<VO(`VO(x#|O(y#}O#{#mi$O#mi(q#mi!]#mi!^#mi~O#z#mi~P&'yO#z<XO~P&'yOa#|y!]#|y'y#|y'v#|y!Y#|y!k#|yv#|y!_#|y%i#|y!g#|y~P!:aO[#mij#mir#mi#R#mi#r#mi#s#mi#t#mi#u#mi#v#mi#x#mi#z#mi#{#mi$O#mi(q#mi!]#mi!^#mi~OP$[OR#zO!Q#yO!S#{O!l#xO!p$[O#n<PO#o<QO#p<QO#q<QO(`VO(x#mi(y#mi~P&*uOn=}O!Q)|O'x)}O(x$}O(y%POP#miR#mi!S#mi!l#mi!p#mi#n#mi#o#mi#p#mi#q#mi(`#mi~P&*uO#S$dOP(_XR(_X[(_Xj(_Xn(_Xr(_X!Q(_X!S(_X!l(_X!p(_X#R(_X#n(_X#o(_X#p(_X#q(_X#r(_X#s(_X#t(_X#u(_X#v(_X#x(_X#z(_X#{(_X$O(_X'x(_X(`(_X(q(_X(x(_X(y(_X!](_X!^(_X~O$O$Pi!]$Pi!^$Pi~P#B]O$O!ri!^!ri~P$*bOg'[a!]'[a~P!0{O!^7dO~O!]'ca!^'ca~P#B]O!Y7eO~P#/XO!g#vO(q'nO!]'da!k'da~O!]/kO!k(}i~O!]/kO!g#vO!k(}i~Og$|q!]$|q#`$|q$O$|q~P!0{O!Y'fa!]'fa~P#/XO!g7lO~O!]/sO!Y)Oi~P#/XO!]/sO!Y)Oi~O!Y7oO~Oh%VOr7tO!l%dO(q'nO~Oj7vO!g#vO~Or7yO!g#vO(q'nO~O!Q)|O'x)}O(y%POn'ia(x'ia!]'ia#`'ia~Og'ia$O'ia~P&3vO!Q)|O'x)}On'ka(x'ka(y'ka!]'ka#`'ka~Og'ka$O'ka~P&4iO!Y7{O~Og%Oq!]%Oq#`%Oq$O%Oq~P!0{Og(^q!](^q~P!0{O#`7|Og(^q!](^q~P!0{Oa$oy!]$oy'y$oy'v$oy!Y$oy!k$oyv$oy!_$oy%i$oy!g$oy~P!:aO!g6hO~O!]5RO!_)Pa~O!_'^OP$TaR$Ta[$Taj$Tar$Ta!Q$Ta!S$Ta!]$Ta!l$Ta!p$Ta#R$Ta#n$Ta#o$Ta#p$Ta#q$Ta#r$Ta#s$Ta#t$Ta#u$Ta#v$Ta#x$Ta#z$Ta#{$Ta(`$Ta(q$Ta(x$Ta(y$Ta~O%i6|O~P&7ZO%^8QOa%[i!_%[i'y%[i!]%[i~Oa#cy!]#cy'y#cy'v#cy!Y#cy!k#cyv#cy!_#cy%i#cy!g#cy~P!:aO[8SO~Ob8UO(S+nO(UTO(XUO~O!]0}O!^)Wi~O`8YO~O(d(zO!]'oX!^'oX~O!]5kO!^)Ta~O!^8cO~P%:YO(n!sO~P$${O#[8dO~O!_1gO~O!_1gO%i8fO~On8iO!_1gO%i8fO~O[8nO!]'ra!^'ra~O!]1rO!^)Ui~O!k8rO~O!k8sO~O!k8vO~O!k8vO~P%[Oa8xO~O!g8yO~O!k8zO~O!](vi!^(vi~P#B]Oa%mO#`9SO'y%mO~O!](sy!k(sya(sy'y(sy~P!:aO!](hO!k(ry~O%i9VO~P&7ZO!_'^O%i9VO~O#k$|qP$|qR$|q[$|qa$|qj$|qr$|q!S$|q!]$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q'y$|q(`$|q(q$|q!k$|q!Y$|q'v$|q#`$|qv$|q!_$|q%i$|q!g$|q~P#/XO#k'iaP'iaR'ia['iaa'iaj'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia'y'ia(`'ia(q'ia!k'ia!Y'ia'v'iav'ia!_'ia%i'ia!g'ia~P&3vO#k'kaP'kaR'ka['kaa'kaj'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka'y'ka(`'ka(q'ka!k'ka!Y'ka'v'kav'ka!_'ka%i'ka!g'ka~P&4iO#k%OqP%OqR%Oq[%Oqa%Oqj%Oqr%Oq!S%Oq!]%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq'y%Oq(`%Oq(q%Oq!k%Oq!Y%Oq'v%Oq#`%Oqv%Oq!_%Oq%i%Oq!g%Oq~P#/XO!]'Xi!k'Xi~P!:aO$O#cq!]#cq!^#cq~P#B]O(x$}OP%aaR%aa[%aaj%aar%aa!S%aa!l%aa!p%aa#R%aa#n%aa#o%aa#p%aa#q%aa#r%aa#s%aa#t%aa#u%aa#v%aa#x%aa#z%aa#{%aa$O%aa(`%aa(q%aa!]%aa!^%aa~On%aa!Q%aa'x%aa(y%aa~P&HnO(y%POP%caR%ca[%caj%car%ca!S%ca!l%ca!p%ca#R%ca#n%ca#o%ca#p%ca#q%ca#r%ca#s%ca#t%ca#u%ca#v%ca#x%ca#z%ca#{%ca$O%ca(`%ca(q%ca!]%ca!^%ca~On%ca!Q%ca'x%ca(x%ca~P&JuOn=}O!Q)|O'x)}O(y%PO~P&HnOn=}O!Q)|O'x)}O(x$}O~P&JuOR0cO!Q0cO!S0dO#S$dOP}a[}aj}an}ar}a!l}a!p}a#R}a#n}a#o}a#p}a#q}a#r}a#s}a#t}a#u}a#v}a#x}a#z}a#{}a$O}a'x}a(`}a(q}a(x}a(y}a!]}a!^}a~O!Q)|O'x)}OP$saR$sa[$saj$san$sar$sa!S$sa!l$sa!p$sa#R$sa#n$sa#o$sa#p$sa#q$sa#r$sa#s$sa#t$sa#u$sa#v$sa#x$sa#z$sa#{$sa$O$sa(`$sa(q$sa(x$sa(y$sa!]$sa!^$sa~O!Q)|O'x)}OP$uaR$ua[$uaj$uan$uar$ua!S$ua!l$ua!p$ua#R$ua#n$ua#o$ua#p$ua#q$ua#r$ua#s$ua#t$ua#u$ua#v$ua#x$ua#z$ua#{$ua$O$ua(`$ua(q$ua(x$ua(y$ua!]$ua!^$ua~On=}O!Q)|O'x)}O(x$}O(y%PO~OP%TaR%Ta[%Taj%Tar%Ta!S%Ta!l%Ta!p%Ta#R%Ta#n%Ta#o%Ta#p%Ta#q%Ta#r%Ta#s%Ta#t%Ta#u%Ta#v%Ta#x%Ta#z%Ta#{%Ta$O%Ta(`%Ta(q%Ta!]%Ta!^%Ta~P'%zO$O$mq!]$mq!^$mq~P#B]O$O$oq!]$oq!^$oq~P#B]O!^9dO~O$O9eO~P!0{O!g#vO!]'di!k'di~O!g#vO(q'nO!]'di!k'di~O!]/kO!k(}q~O!Y'fi!]'fi~P#/XO!]/sO!Y)Oq~Or9lO!g#vO(q'nO~O[9nO!Y9mO~P#/XO!Y9mO~Oj9tO!g#vO~Og(^y!](^y~P!0{O!]'ma!_'ma~P#/XOa%[q!_%[q'y%[q!]%[q~P#/XO[9yO~O!]0}O!^)Wq~O#`9}O!]'oa!^'oa~O!]5kO!^)Ti~P#B]O!S:PO~O!_1gO%i:SO~O(UTO(XUO(d:XO~O!]1rO!^)Uq~O!k:[O~O!k:]O~O!k:^O~O!k:^O~P%[O#`:aO!]#hy!^#hy~O!]#hy!^#hy~P#B]O%i:fO~P&7ZO!_'^O%i:fO~O$O#|y!]#|y!^#|y~P#B]OP$|iR$|i[$|ij$|ir$|i!S$|i!l$|i!p$|i#R$|i#n$|i#o$|i#p$|i#q$|i#r$|i#s$|i#t$|i#u$|i#v$|i#x$|i#z$|i#{$|i$O$|i(`$|i(q$|i!]$|i!^$|i~P'%zO!Q)|O'x)}O(y%POP'haR'ha['haj'han'har'ha!S'ha!l'ha!p'ha#R'ha#n'ha#o'ha#p'ha#q'ha#r'ha#s'ha#t'ha#u'ha#v'ha#x'ha#z'ha#{'ha$O'ha(`'ha(q'ha(x'ha!]'ha!^'ha~O!Q)|O'x)}OP'jaR'ja['jaj'jan'jar'ja!S'ja!l'ja!p'ja#R'ja#n'ja#o'ja#p'ja#q'ja#r'ja#s'ja#t'ja#u'ja#v'ja#x'ja#z'ja#{'ja$O'ja(`'ja(q'ja(x'ja(y'ja!]'ja!^'ja~O(x$}OP%aiR%ai[%aij%ain%air%ai!Q%ai!S%ai!l%ai!p%ai#R%ai#n%ai#o%ai#p%ai#q%ai#r%ai#s%ai#t%ai#u%ai#v%ai#x%ai#z%ai#{%ai$O%ai'x%ai(`%ai(q%ai(y%ai!]%ai!^%ai~O(y%POP%ciR%ci[%cij%cin%cir%ci!Q%ci!S%ci!l%ci!p%ci#R%ci#n%ci#o%ci#p%ci#q%ci#r%ci#s%ci#t%ci#u%ci#v%ci#x%ci#z%ci#{%ci$O%ci'x%ci(`%ci(q%ci(x%ci!]%ci!^%ci~O$O$oy!]$oy!^$oy~P#B]O$O#cy!]#cy!^#cy~P#B]O!g#vO!]'dq!k'dq~O!]/kO!k(}y~O!Y'fq!]'fq~P#/XOr:pO!g#vO(q'nO~O[:tO!Y:sO~P#/XO!Y:sO~Og(^!R!](^!R~P!0{Oa%[y!_%[y'y%[y!]%[y~P#/XO!]0}O!^)Wy~O!]5kO!^)Tq~O(S:zO~O!_1gO%i:}O~O!k;QO~O%i;VO~P&7ZOP$|qR$|q[$|qj$|qr$|q!S$|q!l$|q!p$|q#R$|q#n$|q#o$|q#p$|q#q$|q#r$|q#s$|q#t$|q#u$|q#v$|q#x$|q#z$|q#{$|q$O$|q(`$|q(q$|q!]$|q!^$|q~P'%zO!Q)|O'x)}O(y%POP'iaR'ia['iaj'ian'iar'ia!S'ia!l'ia!p'ia#R'ia#n'ia#o'ia#p'ia#q'ia#r'ia#s'ia#t'ia#u'ia#v'ia#x'ia#z'ia#{'ia$O'ia(`'ia(q'ia(x'ia!]'ia!^'ia~O!Q)|O'x)}OP'kaR'ka['kaj'kan'kar'ka!S'ka!l'ka!p'ka#R'ka#n'ka#o'ka#p'ka#q'ka#r'ka#s'ka#t'ka#u'ka#v'ka#x'ka#z'ka#{'ka$O'ka(`'ka(q'ka(x'ka(y'ka!]'ka!^'ka~OP%OqR%Oq[%Oqj%Oqr%Oq!S%Oq!l%Oq!p%Oq#R%Oq#n%Oq#o%Oq#p%Oq#q%Oq#r%Oq#s%Oq#t%Oq#u%Oq#v%Oq#x%Oq#z%Oq#{%Oq$O%Oq(`%Oq(q%Oq!]%Oq!^%Oq~P'%zOg%e!Z!]%e!Z#`%e!Z$O%e!Z~P!0{O!Y;ZO~P#/XOr;[O!g#vO(q'nO~O[;^O!Y;ZO~P#/XO!]'oq!^'oq~P#B]O!]#h!Z!^#h!Z~P#B]O#k%e!ZP%e!ZR%e!Z[%e!Za%e!Zj%e!Zr%e!Z!S%e!Z!]%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z'y%e!Z(`%e!Z(q%e!Z!k%e!Z!Y%e!Z'v%e!Z#`%e!Zv%e!Z!_%e!Z%i%e!Z!g%e!Z~P#/XOr;fO!g#vO(q'nO~O!Y;gO~P#/XOr;nO!g#vO(q'nO~O!Y;oO~P#/XOP%e!ZR%e!Z[%e!Zj%e!Zr%e!Z!S%e!Z!l%e!Z!p%e!Z#R%e!Z#n%e!Z#o%e!Z#p%e!Z#q%e!Z#r%e!Z#s%e!Z#t%e!Z#u%e!Z#v%e!Z#x%e!Z#z%e!Z#{%e!Z$O%e!Z(`%e!Z(q%e!Z!]%e!Z!^%e!Z~P'%zOr;rO!g#vO(q'nO~Ov(eX~P1qO!Q%qO~P!)PO(T!lO~P!)PO!YfX!]fX#`fX~P%0kOP]XR]X[]Xj]Xr]X!Q]X!S]X!]]X!]fX!l]X!p]X#R]X#S]X#`]X#`fX#kfX#n]X#o]X#p]X#q]X#r]X#s]X#t]X#u]X#v]X#x]X#z]X#{]X$Q]X(`]X(q]X(x]X(y]X~O!gfX!k]X!kfX(qfX~P'JsOP;vOQ;vOSfOd=rOe!iOpkOr;vOskOtkOzkO|;vO!O;vO!SWO!WkO!XkO!_XO!i;yO!lZO!o;vO!p;vO!q;vO!s;zO!u;}O!x!hO$W!kO$n=pO(S)ZO(UTO(XUO(`VO(n[O~O!]<ZO!^$qa~Oh%VOp%WOr%XOs$tOt$tOz%YO|%ZO!O<fO!S${O!_$|O!i=wO!l$xO#j<lO$W%_O$t<hO$v<jO$y%`O(S(tO(UTO(XUO(`$uO(x$}O(y%PO~Ol)bO~P( iOr!eX(q!eX~P# }Or(iX(q(iX~P#!pO!^]X!^fX~P'JsO!YfX!Y$zX!]fX!]$zX#`fX~P!/wO#k<OO~O!g#vO#k<OO~O#`<`O~Oj<SO~O#`<pO!](vX!^(vX~O#`<`O!](tX!^(tX~O#k<qO~Og<sO~P!0{O#k<yO~O#k<zO~O!g#vO#k<{O~O!g#vO#k<qO~O$O<|O~P#B]O#k<}O~O#k=OO~O#k=TO~O#k=UO~O#k=VO~O#k=WO~O$O=XO~P!0{O$O=YO~P!0{Ok#S#T#U#W#X#[#i#j#u$n$t$v$y%]%^%h%i%j%q%s%v%w%y%{~'}T#o!X'{(T#ps#n#qr!Q'|$]'|(S$_(d~",goto:"$8g)[PPPPPP)]PP)`P)qP+R/WPPPP6bPP6xPP<pPPP@dP@zP@zPPP@zPCSP@zP@zP@zPCWPC]PCzPHtPPPHxPPPPHxK{PPPLRLsPHxPHxPP! RHxPPPHxPHxP!#YHxP!&p!'u!(OP!(r!(v!(r!,TPPPPPPP!,t!'uPP!-U!.vP!2SHxHx!2X!5e!:R!:R!>QPPP!>YHxPPPPPPPPP!AiP!BvPPHx!DXPHxPHxHxHxHxHxPHx!EkP!HuP!K{P!LP!LZ!L_!L_P!HrP!Lc!LcP# iP# mHxPHx# s#$xCW@zP@zP@z@zP#&V@z@z#(i@z#+a@z#-m@z@z#.]#0q#0q#0v#1P#0q#1[PP#0qP@z#1t@z#5s@z@z6bPPP#9xPPP#:c#:cP#:cP#:y#:cPP#;PP#:vP#:v#;d#:v#<O#<U#<X)`#<[)`P#<c#<c#<cP)`P)`P)`P)`PP)`P#<i#<lP#<l)`P#<pP#<sP)`P)`P)`P)`P)`P)`)`PP#<y#=P#=[#=b#=h#=n#=t#>S#>Y#>d#>j#>t#>z#?[#?b#@S#@f#@l#@r#AQ#Ag#C[#Cj#Cq#E]#Ek#G]#Gk#Gq#Gw#G}#HX#H_#He#Ho#IR#IXPPPPPPPPPPP#I_PPPPPPP#JS#MZ#Ns#Nz$ SPPP$&nP$&w$)p$0Z$0^$0a$1`$1c$1j$1rP$1x$1{P$2i$2m$3e$4s$4x$5`PP$5e$5k$5o$5r$5v$5z$6v$7_$7v$7z$7}$8Q$8W$8Z$8_$8cR!|RoqOXst!Z#d%l&p&r&s&u,n,s2S2VY!vQ'^-`1g5qQ%svQ%{yQ&S|Q&h!VS'U!e-WQ'd!iS'j!r!yU*h$|*X*lQ+l%|Q+y&UQ,_&bQ-^']Q-h'eQ-p'kQ0U*nQ1q,`R<m;z%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y,k,n,s-d-l-z.Q.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3d4q5y6Z6[6_6r8i8x9SS#q];w!r)]$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sU*{%[<e<fQ+q&OQ,a&eQ,h&mQ0r+dQ0w+fQ1S+rQ1y,fQ3W.bQ5V0vQ5]0}Q6Q1rQ7O3[Q8U5^R9Y7Q'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=s!S!nQ!r!v!y!z$|'U']'^'j'k'l*h*l*n*o-W-^-`-p0U0X1g5q5s%[$ti#v$b$c$d$x${%O%Q%]%^%b)w*P*R*T*W*^*d*t*u+c+f+},Q.a.z/_/h/r/s/u0Y0[0g0h0i1^1a1i3Z4U4V4a4f4w5R5U5x6|7l7v7|8Q8f9V9e9n9t:S:f:t:};V;^<^<_<a<b<c<d<g<h<i<j<k<l<t<u<v<w<y<z<}=O=P=Q=R=S=T=U=X=Y=p=x=y=|=}Q&V|Q'S!eS'Y%h-ZQ+q&OQ,a&eQ0f+OQ1S+rQ1X+xQ1x,eQ1y,fQ5]0}Q5f1ZQ6Q1rQ6T1tQ6U1wQ8U5^Q8X5cQ8q6WQ9|8YQ:Y8nR<o*XrnOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VR,c&i&z^OPXYstuvwz!Z!`!g!j!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=r=s[#]WZ#W#Z'V(R!b%im#h#i#l$x%d%g([(f(g(h*W*[*_+W+X+Z,j-Q.O.U.V.W.Y/h/k2[3S3T4X6h6yQ%vxQ%zyS&P|&UQ&]!TQ'a!hQ'c!iQ(o#sS+k%{%|Q+o&OQ,Y&`Q,^&bS-g'd'eQ.d(pQ0{+lQ1R+rQ1T+sQ1W+wQ1l,ZS1p,_,`Q2t-hQ5[0}Q5`1QQ5e1YQ6P1qQ8T5^Q8W5bQ9x8SR:w9y!U$zi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y!^%xy!i!u%z%{%|'T'c'd'e'i's*g+k+l-T-g-h-o/{0O0{2m2t2{4i4j4m7s9pQ+e%vQ,O&YQ,R&ZQ,]&bQ.c(oQ1k,YU1o,^,_,`Q3].dQ5z1lS6O1p1qQ8m6P#f=t#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}o=u<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=YW%Ti%V*v=pS&Y!Q&gQ&Z!RQ&[!SQ+S%cR+|&W%]%Si#v$b$c$d$x${%O%Q%]%^%b)w*P*R*T*W*^*d*t*u+c+f+},Q.a.z/_/h/r/s/u0Y0[0g0h0i1^1a1i3Z4U4V4a4f4w5R5U5x6|7l7v7|8Q8f9V9e9n9t:S:f:t:};V;^<^<_<a<b<c<d<g<h<i<j<k<l<t<u<v<w<y<z<}=O=P=Q=R=S=T=U=X=Y=p=x=y=|=}T)x$u)yV*{%[<e<fW'Y!e%h*X-ZS({#y#zQ+`%qQ+v&RS.](k(lQ1b,SQ4x0cR8^5k'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=s$i$^c#Y#e%p%r%t(Q(W(r(w)P)Q)R)S)T)U)V)W)X)Y)[)^)`)e)o+a+u-U-s-x-}.P.n.q.u.w.x.y/]0j2c2f2v2}3c3h3i3j3k3l3m3n3o3p3q3r3s3t3w3x4P5O5Y6k6q6v7V7W7a7b8`8|9Q9[9b9c:c:y;R;x=gT#TV#U'RkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sQ'W!eR2i-W!W!nQ!e!r!v!y!z$|'U']'^'j'k'l*X*h*l*n*o-W-^-`-p0U0X1g5q5sR1d,UnqOXst!Z#d%l&p&r&s&u,n,s2S2VQ&w!^Q't!xS(q#u<OQ+i%yQ,W&]Q,X&_Q-e'bQ-r'mS.m(v<qS0k+U<{Q0y+jQ1f,VQ2Z,uQ2],vQ2e-RQ2r-fQ2u-jS5P0l=VQ5W0zS5Z0|=WQ6j2gQ6n2sQ6s2zQ8R5XQ8}6lQ9O6oQ9R6tR:`8z$d$]c#Y#e%r%t(Q(W(r(w)P)Q)R)S)T)U)V)W)X)Y)[)^)`)e)o+a+u-U-s-x-}.P.n.q.u.x.y/]0j2c2f2v2}3c3h3i3j3k3l3m3n3o3p3q3r3s3t3w3x4P5O5Y6k6q6v7V7W7a7b8`8|9Q9[9b9c:c:y;R;x=gS(m#p'gQ(}#zS+_%p.wS.^(l(nR3U._'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sS#q];wQ&r!XQ&s!YQ&u![Q&v!]R2R,qQ'_!hQ+b%vQ-c'aS.`(o+eQ2p-bW3Y.c.d0q0sQ6m2qW6z3V3X3]5TU9U6{6}7PU:e9W9X9ZS;T:d:gQ;b;UR;j;cU!wQ'^-`T5o1g5q!Q_OXZ`st!V!Z#d#h%d%l&g&i&p&r&s&u(h,n,s.V2S2V]!pQ!r'^-`1g5qT#q];w%^{OPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SS({#y#zS.](k(l!s=^$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sU$fd)],hS(n#p'gU*s%R(u3vU0e*z.i7]Q5T0rQ6{3WQ9X7OR:g9Ym!tQ!r!v!y!z'^'j'k'l-`-p1g5q5sQ'r!uS(d#g1|S-n'i'uQ/n*ZQ/{*gQ2|-qQ4]/oQ4i/}Q4j0OQ4o0WQ7h4WS7s4k4mS7w4p4rQ9g7iQ9k7oQ9p7tQ9u7yS:o9l9mS;Y:p:sS;e;Z;[S;m;f;gS;q;n;oR;t;rQ#wbQ'q!uS(c#g1|S(e#m+TQ+V%eQ+g%wQ+m%}U-m'i'r'uQ.R(dQ/m*ZQ/|*gQ0P*iQ0x+hQ1m,[S2y-n-qQ3R.ZS4[/n/oQ4e/yS4h/{0WQ4l0QQ5|1nQ6u2|Q7g4WQ7k4]U7r4i4o4rQ7u4nQ8k5}S9f7h7iQ9j7oQ9r7wQ9s7xQ:V8lQ:m9gS:n9k9mQ:v9uQ;P:WS;X:o:sS;d;Y;ZS;l;e;gS;p;m;oQ;s;qQ;u;tQ=a=[Q=l=eR=m=fV!wQ'^-`%^aOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SS#wz!j!r=Z$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sR=a=r%^bOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SQ%ej!^%wy!i!u%z%{%|'T'c'd'e'i's*g+k+l-T-g-h-o/{0O0{2m2t2{4i4j4m7s9pS%}z!jQ+h%xQ,[&bW1n,],^,_,`U5}1o1p1qS8l6O6PQ:W8m!r=[$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sQ=e=qR=f=r%QeOPXYstuvw!Z!`!g!o#S#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&p&r&s&u&y'R'`'p(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9SY#bWZ#W#Z(R!b%im#h#i#l$x%d%g([(f(g(h*W*[*_+W+X+Z,j-Q.O.U.V.W.Y/h/k2[3S3T4X6h6yQ,i&m!p=]$Z$n)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sR=`'VU'Z!e%h*XR2k-Z%SdOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y,k,n,s-d-l-z.Q.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3d4q5y6Z6[6_6r8i8x9S!r)]$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sQ,h&mQ0r+dQ3W.bQ7O3[R9Y7Q!b$Tc#Y%p(Q(W(r(w)X)Y)^)e+u-s-x-}.P.n.q/]0j2v2}3c3s5O5Y6q6v7V9Q:c;x!P<U)[)o-U.w2c2f3h3q3r3w4P6k7W7a7b8`8|9[9b9c:y;R=g!f$Vc#Y%p(Q(W(r(w)U)V)X)Y)^)e+u-s-x-}.P.n.q/]0j2v2}3c3s5O5Y6q6v7V9Q:c;x!T<W)[)o-U.w2c2f3h3n3o3q3r3w4P6k7W7a7b8`8|9[9b9c:y;R=g!^$Zc#Y%p(Q(W(r(w)^)e+u-s-x-}.P.n.q/]0j2v2}3c3s5O5Y6q6v7V9Q:c;xQ4V/fz=s)[)o-U.w2c2f3h3w4P6k7W7a7b8`8|9[9b9c:y;R=gQ=x=zR=y={'QkOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sS$oh$pR3|/P'XgOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/P/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sT$kf$qQ$ifS)h$l)lR)t$qT$jf$qT)j$l)l'XhOPWXYZhstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$Z$_$a$e$n$p%l%s&Q&i&l&m&p&r&s&u&y'R'V'`'p(R(T(Z(b(v(x(|)q){*f+U+Y+d,k,n,s-P-S-d-l-z.Q.b.o.v/P/Q/i0V0d0l0|1j1z1{1}2P2S2V2X2h2x3O3[3d3{4q5m5y6Z6[6_6i6r7Q8i8x9S9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=sT$oh$pQ$rhR)s$p%^jOPWXYZstuvw!Z!`!g!o#S#W#Z#d#o#u#x#{$O$P$Q$R$S$T$U$V$W$X$_$a$e%l%s&Q&i&l&m&p&r&s&u&y'R'`'p(R(T(Z(b(v(x(|){*f+U+Y+d,k,n,s-d-l-z.Q.b.o.v/i0V0d0l0|1j1z1{1}2P2S2V2X2x3O3[3d4q5y6Z6[6_6r7Q8i8x9S!s=q$Z$n'V)q-P-S/Q2h3{5m6i9}:a;v;y;z;}<O<P<Q<R<S<T<U<V<W<X<Y<Z<]<`<m<p<q<s<{<|=V=W=s#glOPXZst!Z!`!o#S#d#o#{$n%l&i&l&m&p&r&s&u&y'R'`(|)q*f+Y+d,k,n,s-d.b/Q/i0V0d1j1z1{1}2P2S2V2X3[3{4q5y6Z6[6_7Q8i8x!U%Ri$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y#f(u#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}Q+P%`Q/^)|o3v<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=Y!U$yi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=yQ*`$zU*i$|*X*lQ+Q%aQ0Q*j#f=c#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}n=d<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=YQ=h=tQ=i=uQ=j=vR=k=w!U%Ri$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y#f(u#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}o3v<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=YnoOXst!Z#d%l&p&r&s&u,n,s2S2VS*c${*WQ,|&|Q,}'OR4`/s%[%Si#v$b$c$d$x${%O%Q%]%^%b)w*P*R*T*W*^*d*t*u+c+f+},Q.a.z/_/h/r/s/u0Y0[0g0h0i1^1a1i3Z4U4V4a4f4w5R5U5x6|7l7v7|8Q8f9V9e9n9t:S:f:t:};V;^<^<_<a<b<c<d<g<h<i<j<k<l<t<u<v<w<y<z<}=O=P=Q=R=S=T=U=X=Y=p=x=y=|=}Q,P&ZQ1`,RQ5i1_R8]5jV*k$|*X*lU*k$|*X*lT5p1g5qS/y*f/iQ4n0VT7x4q:PQ+g%wQ0P*iQ0x+hQ1m,[Q5|1nQ8k5}Q:V8lR;P:W!U%Oi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=yx*P$v)c*Q*r+R/q0^0_3y4^4{4|4}7f7z9v:l=b=n=oS0Y*q0Z#f<a#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}n<b<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=Y!d<t(s)a*Y*b.e.h.l/Y/f/v0p1]3`4S4_4c5h7R7U7m7p7}8P9i9q9w:q:u;W;];h=z={`<u3u7X7[7`9]:h:k;kS=P.g3aT=Q7Z9`!U%Qi$d%O%Q%]%^%b*P*R*^*t*u.z/r0Y0[0g0h0i4V4w7|9e=p=x=y|*R$v)c*S*q+R/b/q0^0_3y4^4s4{4|4}7f7z9v:l=b=n=oS0[*r0]#f<c#v$b$c$x${)w*T*W*d+c+f+},Q.a/_/h/s/u1^1a1i3Z4U4a4f5R5U5x6|7l7v8Q8f9V9n9t:S:f:t:};V;^<a<c<g<i<k<t<v<y<}=P=R=T=X=|=}n<d<^<_<b<d<h<j<l<u<w<z=O=Q=S=U=Y!h<v(s)a*Y*b.f.g.l/Y/f/v0p1]3^3`4S4_4c5h7R7S7U7m7p7}8P9i9q9w:q:u;W;];h=z={d<w3u7Y7Z7`9]9^:h:i:k;kS=R.h3bT=S7[9arnOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VQ&d!UR,k&mrnOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VR&d!UQ,T&[R1[+|snOXst!V!Z#d%l&g&p&r&s&u,n,s2S2VQ1h,YS5w1k1lU8e5u5v5zS:R8g8hS:{:Q:TQ;_:|R;i;`Q&k!VR,d&gR6T1tR:Y8nS&P|&UR1T+sQ&p!WR,n&qR,t&vT2T,s2VR,x&wQ,w&wR2^,xQ'w!{R-t'wSsOtQ#dXT%os#dQ#OTR'y#OQ#RUR'{#RQ)y$uR/Z)yQ#UVR(O#UQ#XWU(U#X(V-{Q(V#YR-{(WQ-X'WR2j-XQ.p(wS3e.p3fR3f.qQ-`'^R2n-`Y!rQ'^-`1g5qR'h!rQ.{)cR3z.{U#_W%g*WU(]#_(^-|Q(^#`R-|(XQ-['ZR2l-[t`OXst!V!Z#d%l&g&i&p&r&s&u,n,s2S2VS#hZ%dU#r`#h.VR.V(hQ(i#jQ.S(eW.[(i.S3P6wQ3P.TR6w3QQ)l$lR/R)lQ$phR)r$pQ$`cU)_$`-w<[Q-w;xR<[)oQ/l*ZW4Y/l4Z7j9hU4Z/m/n/oS7j4[4]R9h7k$e*O$v(s)a)c*Y*b*q*r*|*}+R.g.h.j.k.l/Y/b/d/f/q/v0^0_0p1]3^3_3`3u3y4S4^4_4c4s4u4{4|4}5h7R7S7T7U7Z7[7^7_7`7f7m7p7z7}8P9]9^9_9i9q9v9w:h:i:j:k:l:q:u;W;];h;k=b=n=o=z={Q/t*bU4b/t4d7nQ4d/vR7n4cS*l$|*XR0S*lx*Q$v)c*q*r+R/q0^0_3y4^4{4|4}7f7z9v:l=b=n=o!d.e(s)a*Y*b.g.h.l/Y/f/v0p1]3`4S4_4c5h7R7U7m7p7}8P9i9q9w:q:u;W;];h=z={U/c*Q.e7Xa7X3u7Z7[7`9]:h:k;kQ0Z*qQ3a.gU4t0Z3a9`R9`7Z|*S$v)c*q*r+R/b/q0^0_3y4^4s4{4|4}7f7z9v:l=b=n=o!h.f(s)a*Y*b.g.h.l/Y/f/v0p1]3^3`4S4_4c5h7R7S7U7m7p7}8P9i9q9w:q:u;W;];h=z={U/e*S.f7Ye7Y3u7Z7[7`9]9^:h:i:k;kQ0]*rQ3b.hU4v0]3b9aR9a7[Q*w%UR0a*wQ5S0pR8O5SQ+[%jR0o+[Q5l1bS8_5l:OR:O8`Q,V&]R1e,VQ5q1gR8b5qQ1s,aS6R1s8oR8o6TQ1O+oW5_1O5a8V9zQ5a1RQ8V5`R9z8WQ+t&PR1U+tQ2V,sR6c2VYrOXst#dQ&t!ZQ+^%lQ,m&pQ,o&rQ,p&sQ,r&uQ2Q,nS2T,s2VR6b2SQ%npQ&x!_Q&{!aQ&}!bQ'P!cQ'o!uQ+]%kQ+i%yQ+{&VQ,c&kQ,z&zW-k'i'q'r'uQ-r'mQ0R*kQ0y+jS1v,d,gQ2_,yQ2`,|Q2a,}Q2u-jW2w-m-n-q-sQ5W0zQ5d1XQ5g1]Q5{1mQ6V1xQ6a2RU6p2v2y2|Q6s2zQ8R5XQ8Z5fQ8[5hQ8a5pQ8j5|Q8p6US9P6q6uQ9R6tQ9{8XQ:U8kQ:Z8qQ:b9QQ:x9|Q;O:VQ;S:cR;a;PQ%yyQ'b!iQ'm!uU+j%z%{%|Q-R'TU-f'c'd'eS-j'i'sQ/z*gS0z+k+lQ2g-TS2s-g-hQ2z-oS4g/{0OQ5X0{Q6l2mQ6o2tQ6t2{U7q4i4j4mQ9o7sR:r9pS$wi=pR*x%VU%Ui%V=pR0`*vQ$viS(s#v+fS)a$b$cQ)c$dQ*Y$xS*b${*WQ*q%OQ*r%QQ*|%]Q*}%^Q+R%bQ.g<aQ.h<cQ.j<gQ.k<iQ.l<kQ/Y)wQ/b*PQ/d*RQ/f*TQ/q*^S/v*d/hQ0^*tQ0_*ul0p+c,Q.a1a1i3Z5x6|8f9V:S:f:};VQ1]+}Q3^<tQ3_<vQ3`<yS3u<^<_Q3y.zS4S/_4UQ4^/rQ4_/sQ4c/uQ4s0YQ4u0[Q4{0gQ4|0hQ4}0iQ5h1^Q7R<}Q7S=PQ7T=RQ7U=TQ7Z<bQ7[<dQ7^<hQ7_<jQ7`<lQ7f4VQ7m4aQ7p4fQ7z4wQ7}5RQ8P5UQ9]<zQ9^<uQ9_<wQ9i7lQ9q7vQ9v7|Q9w8QQ:h=OQ:i=QQ:j=SQ:k=UQ:l9eQ:q9nQ:u9tQ;W=XQ;]:tQ;h;^Q;k=YQ=b=pQ=n=xQ=o=yQ=z=|R={=}Q*z%[Q.i<eR7]<fnpOXst!Z#d%l&p&r&s&u,n,s2S2VQ!fPS#fZ#oQ&z!`W'f!o*f0V4qQ'}#SQ)O#{Q)p$nS,g&i&lQ,l&mQ,y&yS-O'R/iQ-b'`Q.s(|Q/V)qQ0m+YQ0s+dQ2O,kQ2q-dQ3X.bQ4O/QQ4y0dQ5v1jQ6X1zQ6Y1{Q6^1}Q6`2PQ6e2XQ7P3[Q7c3{Q8h5yQ8t6ZQ8u6[Q8w6_Q9Z7QQ:T8iR:_8x#[cOPXZst!Z!`!o#d#o#{%l&i&l&m&p&r&s&u&y'R'`(|*f+Y+d,k,n,s-d.b/i0V0d1j1z1{1}2P2S2V2X3[4q5y6Z6[6_7Q8i8xQ#YWQ#eYQ%puQ%rvS%tw!gS(Q#W(TQ(W#ZQ(r#uQ(w#xQ)P$OQ)Q$PQ)R$QQ)S$RQ)T$SQ)U$TQ)V$UQ)W$VQ)X$WQ)Y$XQ)[$ZQ)^$_Q)`$aQ)e$eW)o$n)q/Q3{Q+a%sQ+u&QS-U'V2hQ-s'pS-x(R-zQ-}(ZQ.P(bQ.n(vQ.q(xQ.u;vQ.w;yQ.x;zQ.y;}Q/]){Q0j+UQ2c-PQ2f-SQ2v-lQ2}.QQ3c.oQ3h<OQ3i<PQ3j<QQ3k<RQ3l<SQ3m<TQ3n<UQ3o<VQ3p<WQ3q<XQ3r<YQ3s.vQ3t<]Q3w<`Q3x<mQ4P<ZQ5O0lQ5Y0|Q6k<pQ6q2xQ6v3OQ7V3dQ7W<qQ7a<sQ7b<{Q8`5mQ8|6iQ9Q6rQ9[<|Q9b=VQ9c=WQ:c9SQ:y9}Q;R:aQ;x#SR=g=sR#[WR'X!el!tQ!r!v!y!z'^'j'k'l-`-p1g5q5sS'T!e-WU*g$|*X*lS-T'U']S0O*h*nQ0W*oQ2m-^Q4m0UR4r0XR(y#xQ!fQT-_'^-`]!qQ!r'^-`1g5qQ#p]R'g;wR)d$dY!uQ'^-`1g5qQ'i!rS's!v!yS'u!z5sS-o'j'kQ-q'lR2{-pT#kZ%dS#jZ%dS%jm,jU(e#h#i#lS.T(f(gQ.X(hQ0n+ZQ3Q.UU3R.V.W.YS6x3S3TR9T6yd#^W#W#Z%g(R([*W+W.O/hr#gZm#h#i#l%d(f(g(h+Z.U.V.W.Y3S3T6yS*Z$x*_Q/o*[Q1|,jQ2d-QQ4W/kQ6g2[Q7i4XQ8{6hT=_'V+XV#aW%g*WU#`W%g*WS(S#W([U(X#Z+W/hS-V'V+XT-y(R.OV'[!e%h*XQ$lfR)v$qT)k$l)lR3}/PT*]$x*_T*e${*WQ0q+cQ1_,QQ3V.aQ5j1aQ5u1iQ6}3ZQ8g5xQ9W6|Q:Q8fQ:d9VQ:|:SQ;U:fQ;`:}R;c;VnqOXst!Z#d%l&p&r&s&u,n,s2S2VQ&j!VR,c&gtmOXst!U!V!Z#d%l&g&p&r&s&u,n,s2S2VR,j&mT%km,jR1c,SR,b&eQ&T|R+z&UR+p&OT&n!W&qT&o!W&qT2U,s2V",nodeNames:"⚠ ArithOp ArithOp ?. JSXStartTag LineComment BlockComment Script Hashbang ExportDeclaration export Star as VariableName String Escape from ; default FunctionDeclaration async function VariableDefinition > < TypeParamList in out const TypeDefinition extends ThisType this LiteralType ArithOp Number BooleanLiteral TemplateType InterpolationEnd Interpolation InterpolationStart NullType null VoidType void TypeofType typeof MemberExpression . PropertyName [ TemplateString Escape Interpolation super RegExp ] ArrayExpression Spread , } { ObjectExpression Property async get set PropertyDefinition Block : NewTarget new NewExpression ) ( ArgList UnaryExpression delete LogicOp BitOp YieldExpression yield AwaitExpression await ParenthesizedExpression ClassExpression class ClassBody MethodDeclaration Decorator @ MemberExpression PrivatePropertyName CallExpression TypeArgList CompareOp < declare Privacy static abstract override PrivatePropertyDefinition PropertyDeclaration readonly accessor Optional TypeAnnotation Equals StaticBlock FunctionExpression ArrowFunction ParamList ParamList ArrayPattern ObjectPattern PatternProperty Privacy readonly Arrow MemberExpression BinaryExpression ArithOp ArithOp ArithOp ArithOp BitOp CompareOp instanceof satisfies CompareOp BitOp BitOp BitOp LogicOp LogicOp ConditionalExpression LogicOp LogicOp AssignmentExpression UpdateOp PostfixExpression CallExpression InstantiationExpression TaggedTemplateExpression DynamicImport import ImportMeta JSXElement JSXSelfCloseEndTag JSXSelfClosingTag JSXIdentifier JSXBuiltin JSXIdentifier JSXNamespacedName JSXMemberExpression JSXSpreadAttribute JSXAttribute JSXAttributeValue JSXEscape JSXEndTag JSXOpenTag JSXFragmentTag JSXText JSXEscape JSXStartCloseTag JSXCloseTag PrefixCast < ArrowFunction TypeParamList SequenceExpression InstantiationExpression KeyofType keyof UniqueType unique ImportType InferredType infer TypeName ParenthesizedType FunctionSignature ParamList NewSignature IndexedType TupleType Label ArrayType ReadonlyType ObjectType MethodType PropertyType IndexSignature PropertyDefinition CallSignature TypePredicate asserts is NewSignature new UnionType LogicOp IntersectionType LogicOp ConditionalType ParameterizedType ClassDeclaration abstract implements type VariableDeclaration let var using TypeAliasDeclaration InterfaceDeclaration interface EnumDeclaration enum EnumBody NamespaceDeclaration namespace module AmbientDeclaration declare GlobalDeclaration global ClassDeclaration ClassBody AmbientFunctionDeclaration ExportGroup VariableName VariableName ImportDeclaration ImportGroup ForStatement for ForSpec ForInSpec ForOfSpec of WhileStatement while WithStatement with DoStatement do IfStatement if else SwitchStatement switch SwitchBody CaseLabel case DefaultLabel TryStatement try CatchClause catch FinallyClause finally ReturnStatement return ThrowStatement throw BreakStatement break ContinueStatement continue DebuggerStatement debugger LabeledStatement ExpressionStatement SingleExpression SingleClassItem",maxTerm:379,context:eC,nodeProps:[["isolate",-8,5,6,14,37,39,51,53,55,""],["group",-26,9,17,19,68,207,211,215,216,218,221,224,234,236,242,244,246,248,251,257,263,265,267,269,271,273,274,"Statement",-34,13,14,32,35,36,42,51,54,55,57,62,70,72,76,80,82,84,85,110,111,120,121,136,139,141,142,143,144,145,147,148,167,169,171,"Expression",-23,31,33,37,41,43,45,173,175,177,178,180,181,182,184,185,186,188,189,190,201,203,205,206,"Type",-3,88,103,109,"ClassItem"],["openedBy",23,"<",38,"InterpolationStart",56,"[",60,"{",73,"(",160,"JSXStartCloseTag"],["closedBy",-2,24,168,">",40,"InterpolationEnd",50,"]",61,"}",74,")",165,"JSXEndTag"]],propSources:[eN],skippedNodes:[0,5,6,277],repeatNodeCount:37,tokenData:"$Fq07[R!bOX%ZXY+gYZ-yZ[+g[]%Z]^.c^p%Zpq+gqr/mrs3cst:_tuEruvJSvwLkwx! Yxy!'iyz!(sz{!)}{|!,q|}!.O}!O!,q!O!P!/Y!P!Q!9j!Q!R#:O!R![#<_![!]#I_!]!^#Jk!^!_#Ku!_!`$![!`!a$$v!a!b$*T!b!c$,r!c!}Er!}#O$-|#O#P$/W#P#Q$4o#Q#R$5y#R#SEr#S#T$7W#T#o$8b#o#p$<r#p#q$=h#q#r$>x#r#s$@U#s$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$I|Er$I|$I}$Dk$I}$JO$Dk$JO$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr(n%d_$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z&j&hT$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c&j&zP;=`<%l&c'|'U]$i&j(Y!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!b(SU(Y!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!b(iP;=`<%l'}'|(oP;=`<%l&}'[(y]$i&j(VpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(rp)wU(VpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)rp*^P;=`<%l)r'[*dP;=`<%l(r#S*nX(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g#S+^P;=`<%l*g(n+dP;=`<%l%Z07[+rq$i&j(Vp(Y!b'{0/lOX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p$f%Z$f$g+g$g#BY%Z#BY#BZ+g#BZ$IS%Z$IS$I_+g$I_$JT%Z$JT$JU+g$JU$KV%Z$KV$KW+g$KW&FU%Z&FU&FV+g&FV;'S%Z;'S;=`+a<%l?HT%Z?HT?HU+g?HUO%Z07[.ST(W#S$i&j'|0/lO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c07[.n_$i&j(Vp(Y!b'|0/lOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)3p/x`$i&j!p),Q(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW1V`#v(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`2X!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW2d_#v(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At3l_(U':f$i&j(Y!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k(^4r_$i&j(Y!bOY4kYZ5qZr4krs7nsw4kwx5qx!^4k!^!_8p!_#O4k#O#P5q#P#o4k#o#p8p#p;'S4k;'S;=`:X<%lO4k&z5vX$i&jOr5qrs6cs!^5q!^!_6y!_#o5q#o#p6y#p;'S5q;'S;=`7h<%lO5q&z6jT$d`$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c`6|TOr6yrs7]s;'S6y;'S;=`7b<%lO6y`7bO$d``7eP;=`<%l6y&z7kP;=`<%l5q(^7w]$d`$i&j(Y!bOY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}!r8uZ(Y!bOY8pYZ6yZr8prs9hsw8pwx6yx#O8p#O#P6y#P;'S8p;'S;=`:R<%lO8p!r9oU$d`(Y!bOY'}Zw'}x#O'}#P;'S'};'S;=`(f<%lO'}!r:UP;=`<%l8p(^:[P;=`<%l4k%9[:hh$i&j(Vp(Y!bOY%ZYZ&cZq%Zqr<Srs&}st%ZtuCruw%Zwx(rx!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr(r<__WS$i&j(Vp(Y!bOY<SYZ&cZr<Srs=^sw<Swx@nx!^<S!^!_Bm!_#O<S#O#P>`#P#o<S#o#pBm#p;'S<S;'S;=`Cl<%lO<S(Q=g]WS$i&j(Y!bOY=^YZ&cZw=^wx>`x!^=^!^!_?q!_#O=^#O#P>`#P#o=^#o#p?q#p;'S=^;'S;=`@h<%lO=^&n>gXWS$i&jOY>`YZ&cZ!^>`!^!_?S!_#o>`#o#p?S#p;'S>`;'S;=`?k<%lO>`S?XSWSOY?SZ;'S?S;'S;=`?e<%lO?SS?hP;=`<%l?S&n?nP;=`<%l>`!f?xWWS(Y!bOY?qZw?qwx?Sx#O?q#O#P?S#P;'S?q;'S;=`@b<%lO?q!f@eP;=`<%l?q(Q@kP;=`<%l=^'`@w]WS$i&j(VpOY@nYZ&cZr@nrs>`s!^@n!^!_Ap!_#O@n#O#P>`#P#o@n#o#pAp#p;'S@n;'S;=`Bg<%lO@ntAwWWS(VpOYApZrAprs?Ss#OAp#O#P?S#P;'SAp;'S;=`Ba<%lOAptBdP;=`<%lAp'`BjP;=`<%l@n#WBvYWS(Vp(Y!bOYBmZrBmrs?qswBmwxApx#OBm#O#P?S#P;'SBm;'S;=`Cf<%lOBm#WCiP;=`<%lBm(rCoP;=`<%l<S%9[C}i$i&j(n%1l(Vp(Y!bOY%ZYZ&cZr%Zrs&}st%ZtuCruw%Zwx(rx!Q%Z!Q![Cr![!^%Z!^!_*g!_!c%Z!c!}Cr!}#O%Z#O#P&c#P#R%Z#R#SCr#S#T%Z#T#oCr#o#p*g#p$g%Z$g;'SCr;'S;=`El<%lOCr%9[EoP;=`<%lCr07[FRk$i&j(Vp(Y!b$]#t(S,2j(d$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr+dHRk$i&j(Vp(Y!b$]#tOY%ZYZ&cZr%Zrs&}st%ZtuGvuw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Gv![!^%Z!^!_*g!_!c%Z!c!}Gv!}#O%Z#O#P&c#P#R%Z#R#SGv#S#T%Z#T#oGv#o#p*g#p$g%Z$g;'SGv;'S;=`Iv<%lOGv+dIyP;=`<%lGv07[JPP;=`<%lEr(KWJ_`$i&j(Vp(Y!b#p(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWKl_$i&j$Q(Ch(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,#xLva(y+JY$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sv%ZvwM{wx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KWNW`$i&j#z(Ch(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'At! c_(X';W$i&j(VpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b'l!!i_$i&j(VpOY!!bYZ!#hZr!!brs!#hsw!!bwx!$xx!^!!b!^!_!%z!_#O!!b#O#P!#h#P#o!!b#o#p!%z#p;'S!!b;'S;=`!'c<%lO!!b&z!#mX$i&jOw!#hwx6cx!^!#h!^!_!$Y!_#o!#h#o#p!$Y#p;'S!#h;'S;=`!$r<%lO!#h`!$]TOw!$Ywx7]x;'S!$Y;'S;=`!$l<%lO!$Y`!$oP;=`<%l!$Y&z!$uP;=`<%l!#h'l!%R]$d`$i&j(VpOY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r!Q!&PZ(VpOY!%zYZ!$YZr!%zrs!$Ysw!%zwx!&rx#O!%z#O#P!$Y#P;'S!%z;'S;=`!']<%lO!%z!Q!&yU$d`(VpOY)rZr)rs#O)r#P;'S)r;'S;=`*Z<%lO)r!Q!'`P;=`<%l!%z'l!'fP;=`<%l!!b/5|!'t_!l/.^$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#&U!)O_!k!Lf$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z-!n!*[b$i&j(Vp(Y!b(T%&f#q(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rxz%Zz{!+d{!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW!+o`$i&j(Vp(Y!b#n(ChOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;x!,|`$i&j(Vp(Y!br+4YOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z,$U!.Z_!]+Jf$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!/ec$i&j(Vp(Y!b!Q.2^OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!0p!P!Q%Z!Q![!3Y![!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!0ya$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!2O!P!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z#%|!2Z_![!L^$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!3eg$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!3Y![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S!3Y#S#X%Z#X#Y!4|#Y#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!5Vg$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx{%Z{|!6n|}%Z}!O!6n!O!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!6wc$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad!8_c$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![!8S![!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S!8S#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[!9uf$i&j(Vp(Y!b#o(ChOY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcxz!;Zz{#-}{!P!;Z!P!Q#/d!Q!^!;Z!^!_#(i!_!`#7S!`!a#8i!a!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z?O!;fb$i&j(Vp(Y!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z>^!<w`$i&j(Y!b!X7`OY!<nYZ&cZw!<nwx!=yx!P!<n!P!Q!Eq!Q!^!<n!^!_!Gr!_!}!<n!}#O!KS#O#P!Dy#P#o!<n#o#p!Gr#p;'S!<n;'S;=`!L]<%lO!<n<z!>Q^$i&j!X7`OY!=yYZ&cZ!P!=y!P!Q!>|!Q!^!=y!^!_!@c!_!}!=y!}#O!CW#O#P!Dy#P#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!?Td$i&j!X7`O!^&c!_#W&c#W#X!>|#X#Z&c#Z#[!>|#[#]&c#]#^!>|#^#a&c#a#b!>|#b#g&c#g#h!>|#h#i&c#i#j!>|#j#k!>|#k#m&c#m#n!>|#n#o&c#p;'S&c;'S;=`&w<%lO&c7`!@hX!X7`OY!@cZ!P!@c!P!Q!AT!Q!}!@c!}#O!Ar#O#P!Bq#P;'S!@c;'S;=`!CQ<%lO!@c7`!AYW!X7`#W#X!AT#Z#[!AT#]#^!AT#a#b!AT#g#h!AT#i#j!AT#j#k!AT#m#n!AT7`!AuVOY!ArZ#O!Ar#O#P!B[#P#Q!@c#Q;'S!Ar;'S;=`!Bk<%lO!Ar7`!B_SOY!ArZ;'S!Ar;'S;=`!Bk<%lO!Ar7`!BnP;=`<%l!Ar7`!BtSOY!@cZ;'S!@c;'S;=`!CQ<%lO!@c7`!CTP;=`<%l!@c<z!C][$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#O!CW#O#P!DR#P#Q!=y#Q#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DWX$i&jOY!CWYZ&cZ!^!CW!^!_!Ar!_#o!CW#o#p!Ar#p;'S!CW;'S;=`!Ds<%lO!CW<z!DvP;=`<%l!CW<z!EOX$i&jOY!=yYZ&cZ!^!=y!^!_!@c!_#o!=y#o#p!@c#p;'S!=y;'S;=`!Ek<%lO!=y<z!EnP;=`<%l!=y>^!Ezl$i&j(Y!b!X7`OY&}YZ&cZw&}wx&cx!^&}!^!_'}!_#O&}#O#P&c#P#W&}#W#X!Eq#X#Z&}#Z#[!Eq#[#]&}#]#^!Eq#^#a&}#a#b!Eq#b#g&}#g#h!Eq#h#i&}#i#j!Eq#j#k!Eq#k#m&}#m#n!Eq#n#o&}#o#p'}#p;'S&};'S;=`(l<%lO&}8r!GyZ(Y!b!X7`OY!GrZw!Grwx!@cx!P!Gr!P!Q!Hl!Q!}!Gr!}#O!JU#O#P!Bq#P;'S!Gr;'S;=`!J|<%lO!Gr8r!Hse(Y!b!X7`OY'}Zw'}x#O'}#P#W'}#W#X!Hl#X#Z'}#Z#[!Hl#[#]'}#]#^!Hl#^#a'}#a#b!Hl#b#g'}#g#h!Hl#h#i'}#i#j!Hl#j#k!Hl#k#m'}#m#n!Hl#n;'S'};'S;=`(f<%lO'}8r!JZX(Y!bOY!JUZw!JUwx!Arx#O!JU#O#P!B[#P#Q!Gr#Q;'S!JU;'S;=`!Jv<%lO!JU8r!JyP;=`<%l!JU8r!KPP;=`<%l!Gr>^!KZ^$i&j(Y!bOY!KSYZ&cZw!KSwx!CWx!^!KS!^!_!JU!_#O!KS#O#P!DR#P#Q!<n#Q#o!KS#o#p!JU#p;'S!KS;'S;=`!LV<%lO!KS>^!LYP;=`<%l!KS>^!L`P;=`<%l!<n=l!Ll`$i&j(Vp!X7`OY!LcYZ&cZr!Lcrs!=ys!P!Lc!P!Q!Mn!Q!^!Lc!^!_# o!_!}!Lc!}#O#%P#O#P!Dy#P#o!Lc#o#p# o#p;'S!Lc;'S;=`#&Y<%lO!Lc=l!Mwl$i&j(Vp!X7`OY(rYZ&cZr(rrs&cs!^(r!^!_)r!_#O(r#O#P&c#P#W(r#W#X!Mn#X#Z(r#Z#[!Mn#[#](r#]#^!Mn#^#a(r#a#b!Mn#b#g(r#g#h!Mn#h#i(r#i#j!Mn#j#k!Mn#k#m(r#m#n!Mn#n#o(r#o#p)r#p;'S(r;'S;=`*a<%lO(r8Q# vZ(Vp!X7`OY# oZr# ors!@cs!P# o!P!Q#!i!Q!}# o!}#O#$R#O#P!Bq#P;'S# o;'S;=`#$y<%lO# o8Q#!pe(Vp!X7`OY)rZr)rs#O)r#P#W)r#W#X#!i#X#Z)r#Z#[#!i#[#])r#]#^#!i#^#a)r#a#b#!i#b#g)r#g#h#!i#h#i)r#i#j#!i#j#k#!i#k#m)r#m#n#!i#n;'S)r;'S;=`*Z<%lO)r8Q#$WX(VpOY#$RZr#$Rrs!Ars#O#$R#O#P!B[#P#Q# o#Q;'S#$R;'S;=`#$s<%lO#$R8Q#$vP;=`<%l#$R8Q#$|P;=`<%l# o=l#%W^$i&j(VpOY#%PYZ&cZr#%Prs!CWs!^#%P!^!_#$R!_#O#%P#O#P!DR#P#Q!Lc#Q#o#%P#o#p#$R#p;'S#%P;'S;=`#&S<%lO#%P=l#&VP;=`<%l#%P=l#&]P;=`<%l!Lc?O#&kn$i&j(Vp(Y!b!X7`OY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#W%Z#W#X#&`#X#Z%Z#Z#[#&`#[#]%Z#]#^#&`#^#a%Z#a#b#&`#b#g%Z#g#h#&`#h#i%Z#i#j#&`#j#k#&`#k#m%Z#m#n#&`#n#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z9d#(r](Vp(Y!b!X7`OY#(iZr#(irs!Grsw#(iwx# ox!P#(i!P!Q#)k!Q!}#(i!}#O#+`#O#P!Bq#P;'S#(i;'S;=`#,`<%lO#(i9d#)th(Vp(Y!b!X7`OY*gZr*grs'}sw*gwx)rx#O*g#P#W*g#W#X#)k#X#Z*g#Z#[#)k#[#]*g#]#^#)k#^#a*g#a#b#)k#b#g*g#g#h#)k#h#i*g#i#j#)k#j#k#)k#k#m*g#m#n#)k#n;'S*g;'S;=`+Z<%lO*g9d#+gZ(Vp(Y!bOY#+`Zr#+`rs!JUsw#+`wx#$Rx#O#+`#O#P!B[#P#Q#(i#Q;'S#+`;'S;=`#,Y<%lO#+`9d#,]P;=`<%l#+`9d#,cP;=`<%l#(i?O#,o`$i&j(Vp(Y!bOY#,fYZ&cZr#,frs!KSsw#,fwx#%Px!^#,f!^!_#+`!_#O#,f#O#P!DR#P#Q!;Z#Q#o#,f#o#p#+`#p;'S#,f;'S;=`#-q<%lO#,f?O#-tP;=`<%l#,f?O#-zP;=`<%l!;Z07[#.[b$i&j(Vp(Y!b'}0/l!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z07[#/o_$i&j(Vp(Y!bT0/lOY#/dYZ&cZr#/drs#0nsw#/dwx#4Ox!^#/d!^!_#5}!_#O#/d#O#P#1p#P#o#/d#o#p#5}#p;'S#/d;'S;=`#6|<%lO#/d06j#0w]$i&j(Y!bT0/lOY#0nYZ&cZw#0nwx#1px!^#0n!^!_#3R!_#O#0n#O#P#1p#P#o#0n#o#p#3R#p;'S#0n;'S;=`#3x<%lO#0n05W#1wX$i&jT0/lOY#1pYZ&cZ!^#1p!^!_#2d!_#o#1p#o#p#2d#p;'S#1p;'S;=`#2{<%lO#1p0/l#2iST0/lOY#2dZ;'S#2d;'S;=`#2u<%lO#2d0/l#2xP;=`<%l#2d05W#3OP;=`<%l#1p01O#3YW(Y!bT0/lOY#3RZw#3Rwx#2dx#O#3R#O#P#2d#P;'S#3R;'S;=`#3r<%lO#3R01O#3uP;=`<%l#3R06j#3{P;=`<%l#0n05x#4X]$i&j(VpT0/lOY#4OYZ&cZr#4Ors#1ps!^#4O!^!_#5Q!_#O#4O#O#P#1p#P#o#4O#o#p#5Q#p;'S#4O;'S;=`#5w<%lO#4O00^#5XW(VpT0/lOY#5QZr#5Qrs#2ds#O#5Q#O#P#2d#P;'S#5Q;'S;=`#5q<%lO#5Q00^#5tP;=`<%l#5Q05x#5zP;=`<%l#4O01p#6WY(Vp(Y!bT0/lOY#5}Zr#5}rs#3Rsw#5}wx#5Qx#O#5}#O#P#2d#P;'S#5};'S;=`#6v<%lO#5}01p#6yP;=`<%l#5}07[#7PP;=`<%l#/d)3h#7ab$i&j$Q(Ch(Vp(Y!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;ZAt#8vb$Z#t$i&j(Vp(Y!b!X7`OY!;ZYZ&cZr!;Zrs!<nsw!;Zwx!Lcx!P!;Z!P!Q#&`!Q!^!;Z!^!_#(i!_!}!;Z!}#O#,f#O#P!Dy#P#o!;Z#o#p#(i#p;'S!;Z;'S;=`#-w<%lO!;Z'Ad#:Zp$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#U%Z#U#V#?i#V#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#d#Bq#d#l%Z#l#m#Es#m#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#<jk$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!O%Z!O!P!3Y!P!Q%Z!Q![#<_![!^%Z!^!_*g!_!g%Z!g!h!4|!h#O%Z#O#P&c#P#R%Z#R#S#<_#S#X%Z#X#Y!4|#Y#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#>j_$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#?rd$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#A]f$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!R#AQ!R!S#AQ!S!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#AQ#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Bzc$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Dbe$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q!Y#DV!Y!^%Z!^!_*g!_#O%Z#O#P&c#P#R%Z#R#S#DV#S#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#E|g$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z'Ad#Gpi$i&j(Vp(Y!bs'9tOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!Q%Z!Q![#Ge![!^%Z!^!_*g!_!c%Z!c!i#Ge!i#O%Z#O#P&c#P#R%Z#R#S#Ge#S#T%Z#T#Z#Ge#Z#b%Z#b#c#>_#c#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x#Il_!g$b$i&j$O)Lv(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z)[#Jv_al$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f#LS^h#)`#R-<U(Vp(Y!b$n7`OY*gZr*grs'}sw*gwx)rx!P*g!P!Q#MO!Q!^*g!^!_#Mt!_!`$ f!`#O*g#P;'S*g;'S;=`+Z<%lO*g(n#MXX$k&j(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El#M}Z#r(Ch(Vp(Y!bOY*gZr*grs'}sw*gwx)rx!_*g!_!`#Np!`#O*g#P;'S*g;'S;=`+Z<%lO*g(El#NyX$Q(Ch(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g(El$ oX#s(Ch(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g*)x$!ga#`*!Y$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`0z!`!a$#l!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(K[$#w_#k(Cl$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z*)x$%Vag!*r#s(Ch$f#|$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`$&[!`!a$'f!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$&g_#s(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$'qa#r(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`!a$(v!a#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$)R`#r(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(Kd$*`a(q(Ct$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!a%Z!a!b$+e!b#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$+p`$i&j#{(Ch(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z%#`$,}_!|$Ip$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z04f$.X_!S0,v$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(n$/]Z$i&jO!^$0O!^!_$0f!_#i$0O#i#j$0k#j#l$0O#l#m$2^#m#o$0O#o#p$0f#p;'S$0O;'S;=`$4i<%lO$0O(n$0VT_#S$i&jO!^&c!_#o&c#p;'S&c;'S;=`&w<%lO&c#S$0kO_#S(n$0p[$i&jO!Q&c!Q![$1f![!^&c!_!c&c!c!i$1f!i#T&c#T#Z$1f#Z#o&c#o#p$3|#p;'S&c;'S;=`&w<%lO&c(n$1kZ$i&jO!Q&c!Q![$2^![!^&c!_!c&c!c!i$2^!i#T&c#T#Z$2^#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$2cZ$i&jO!Q&c!Q![$3U![!^&c!_!c&c!c!i$3U!i#T&c#T#Z$3U#Z#o&c#p;'S&c;'S;=`&w<%lO&c(n$3ZZ$i&jO!Q&c!Q![$0O![!^&c!_!c&c!c!i$0O!i#T&c#T#Z$0O#Z#o&c#p;'S&c;'S;=`&w<%lO&c#S$4PR!Q![$4Y!c!i$4Y#T#Z$4Y#S$4]S!Q![$4Y!c!i$4Y#T#Z$4Y#q#r$0f(n$4lP;=`<%l$0O#1[$4z_!Y#)l$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z(KW$6U`#x(Ch$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z+;p$7c_$i&j(Vp(Y!b(`+4QOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$8qk$i&j(Vp(Y!b(S,2j$_#t(d$I[OY%ZYZ&cZr%Zrs&}st%Ztu$8buw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$8b![!^%Z!^!_*g!_!c%Z!c!}$8b!}#O%Z#O#P&c#P#R%Z#R#S$8b#S#T%Z#T#o$8b#o#p*g#p$g%Z$g;'S$8b;'S;=`$<l<%lO$8b+d$:qk$i&j(Vp(Y!b$_#tOY%ZYZ&cZr%Zrs&}st%Ztu$:fuw%Zwx(rx}%Z}!O$:f!O!Q%Z!Q![$:f![!^%Z!^!_*g!_!c%Z!c!}$:f!}#O%Z#O#P&c#P#R%Z#R#S$:f#S#T%Z#T#o$:f#o#p*g#p$g%Z$g;'S$:f;'S;=`$<f<%lO$:f+d$<iP;=`<%l$:f07[$<oP;=`<%l$8b#Jf$<{X!_#Hb(Vp(Y!bOY*gZr*grs'}sw*gwx)rx#O*g#P;'S*g;'S;=`+Z<%lO*g,#x$=sa(x+JY$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_!`Ka!`#O%Z#O#P&c#P#o%Z#o#p*g#p#q$+e#q;'S%Z;'S;=`+a<%lO%Z)>v$?V_!^(CdvBr$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z?O$@a_!q7`$i&j(Vp(Y!bOY%ZYZ&cZr%Zrs&}sw%Zwx(rx!^%Z!^!_*g!_#O%Z#O#P&c#P#o%Z#o#p*g#p;'S%Z;'S;=`+a<%lO%Z07[$Aq|$i&j(Vp(Y!b'{0/l$]#t(S,2j(d$I[OX%ZXY+gYZ&cZ[+g[p%Zpq+gqr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$f%Z$f$g+g$g#BYEr#BY#BZ$A`#BZ$ISEr$IS$I_$A`$I_$JTEr$JT$JU$A`$JU$KVEr$KV$KW$A`$KW&FUEr&FU&FV$A`&FV;'SEr;'S;=`I|<%l?HTEr?HT?HU$A`?HUOEr07[$D|k$i&j(Vp(Y!b'|0/l$]#t(S,2j(d$I[OY%ZYZ&cZr%Zrs&}st%ZtuEruw%Zwx(rx}%Z}!OGv!O!Q%Z!Q![Er![!^%Z!^!_*g!_!c%Z!c!}Er!}#O%Z#O#P&c#P#R%Z#R#SEr#S#T%Z#T#oEr#o#p*g#p$g%Z$g;'SEr;'S;=`I|<%lOEr",tokenizers:[eR,eq,eM,eY,2,3,4,5,6,7,8,9,10,11,12,13,14,eA,new B("$S~RRtu[#O#Pg#S#T#|~_P#o#pb~gOx~~jVO#i!P#i#j!U#j#l!P#l#m!q#m;'S!P;'S;=`#v<%lO!P~!UO!U~~!XS!Q![!e!c!i!e#T#Z!e#o#p#Z~!hR!Q![!q!c!i!q#T#Z!q~!tR!Q![!}!c!i!}#T#Z!}~#QR!Q![!P!c!i!P#T#Z!P~#^R!Q![#g!c!i#g#T#Z#g~#jS!Q![#g!c!i#g#T#Z#g#q#r!P~#yP;=`<%l!P~$RO(b~~",141,339),new B("j~RQYZXz{^~^O(P~~aP!P!Qd~iO(Q~~",25,322)],topRules:{Script:[0,7],SingleExpression:[1,275],SingleClassItem:[2,276]},dialects:{jsx:0,ts:15098},dynamicPrecedences:{80:1,82:1,94:1,169:1,199:1},specialized:[{term:326,get:e=>eI[e]||-1},{term:342,get:e=>eE[e]||-1},{term:95,get:e=>ez[e]||-1}],tokenPrec:15124}),eL=0;class eW{constructor(e,t){this.from=e,this.to=t}}class eG{constructor(e={}){this.id=eL++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=eD.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}eG.closedBy=new eG({deserialize:e=>e.split(" ")}),eG.openedBy=new eG({deserialize:e=>e.split(" ")}),eG.group=new eG({deserialize:e=>e.split(" ")}),eG.isolate=new eG({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw RangeError("Invalid value for isolate: "+e);return e||"auto"}}),eG.contextHash=new eG({perNode:!0}),eG.lookAhead=new eG({perNode:!0}),eG.mounted=new eG({perNode:!0});class eB{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[eG.mounted.id]}}let eU=Object.create(null);class eD{constructor(e,t,i,r=0){this.name=e,this.props=t,this.id=i,this.flags=r}static define(e){let t=e.props&&e.props.length?Object.create(null):eU,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),r=new eD(e.name||"",t,e.id,i);if(e.props){for(let i of e.props)if(Array.isArray(i)||(i=i(r)),i){if(i[0].perNode)throw RangeError("Can't store a per-node prop on a node type");t[i[0].id]=i[1]}}return r}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(eG.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let r of i.split(" "))t[r]=e[i];return e=>{for(let i=e.prop(eG.group),r=-1;r<(i?i.length:0);r++){let n=t[r<0?e.name:i[r]];if(n)return n}}}}eD.none=new eD("",Object.create(null),0,8);class eJ{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let r=null;for(let t of e){let e=t(i);e&&(r||(r=Object.assign({},i.props)),r[e[0].id]=e[1])}t.push(r?new eD(i.name,r,i.id,i.flags):i)}return new eJ(t)}}let eF=new WeakMap,eK=new WeakMap;(C=q||(q={}))[C.ExcludeBuffers=1]="ExcludeBuffers",C[C.IncludeAnonymous=2]="IncludeAnonymous",C[C.IgnoreMounts=4]="IgnoreMounts",C[C.IgnoreOverlays=8]="IgnoreOverlays";class eH{constructor(e,t,i,r,n){if(this.type=e,this.children=t,this.positions=i,this.length=r,this.props=null,n&&n.length)for(let[e,t]of(this.props=Object.create(null),n))this.props["number"==typeof e?e:e.id]=t}toString(){let e=eB.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let i=e.toString();i&&(t&&(t+=","),t+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new ti(this.topNode,e)}cursorAt(e,t=0,i=0){let r=eF.get(this)||this.topNode,n=new ti(r);return n.moveTo(e,t),eF.set(this,n._tree),n}get topNode(){return new e3(this,0,0,null)}resolve(e,t=0){let i=e5(eF.get(this)||this.topNode,e,t,!1);return eF.set(this,i),i}resolveInner(e,t=0){let i=e5(eK.get(this)||this.topNode,e,t,!0);return eK.set(this,i),i}resolveStack(e,t=0){return function(e,t,i){let r=e.resolveInner(t,i),n=null;for(let e=r instanceof e3?r:r.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(n||(n=[r])).push(s.resolve(t,i)),e=s}else{let s=eB.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new e3(s.tree,s.overlay[0].from+e.from,-1,e);(n||(n=[r])).push(e5(o,t,i,!1))}}return n?te(n):r}(this,e,t)}iterate(e){let{enter:t,leave:i,from:r=0,to:n=this.length}=e,s=e.mode||0,o=(s&q.IncludeAnonymous)>0;for(let e=this.cursor(s|q.IncludeAnonymous);;){let s=!1;if(e.from<=n&&e.to>=r&&(!o&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&i&&(o||!e.type.isAnonymous)&&i(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:to(eD.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new eH(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new eH(eD.none,e,t,i)))}static build(e){return function(e){var t;let{buffer:i,nodeSet:r,maxBufferLength:n=1024,reused:s=[],minRepeatType:o=r.types.length}=e,a=Array.isArray(i)?new e0(i,i.length):i,l=r.types,h=0,c=0;function f(e,t,i,n,s,o,a,l,h){let c=[],f=[];for(;e.length>n;)c.push(e.pop()),f.push(t.pop()+i-s);e.push(u(r.types[a],c,f,o-s,l-o,h)),t.push(s-i)}function u(e,t,i,r,n,s,o){if(s){let e=[eG.contextHash,s];o=o?[e].concat(o):[e]}if(n>25){let e=[eG.lookAhead,n];o=o?[e].concat(o):[e]}return new eH(e,t,i,r,o)}let p=[],O=[];for(;a.pos>0;)!function e(t,i,p,O,d,g){let{id:m,start:y,end:x,size:k}=a,Q=c,b=h;for(;k<0;){if(a.next(),-1==k){let e=s[m];p.push(e),O.push(y-t);return}if(-3==k){h=m;return}if(-4==k){c=m;return}throw RangeError(`Unrecognized record size: ${k}`)}let v=l[m],S,w,$=y-t;if(x-y<=n&&(w=function(e,t){let i=a.fork(),r=0,s=0,l=0,h=i.end-n,c={size:0,start:0,skip:0};e:for(let n=i.pos-e;i.pos>n;){let e=i.size;if(i.id==t&&e>=0){c.size=r,c.start=s,c.skip=l,l+=4,r+=4,i.next();continue}let a=i.pos-e;if(e<0||a<n||i.start<h)break;let f=i.id>=o?4:0,u=i.start;for(i.next();i.pos>a;){if(i.size<0){if(-3==i.size)f+=4;else break e}else i.id>=o&&(f+=4);i.next()}s=u,r+=e,l+=f}return(t<0||r==e)&&(c.size=r,c.start=s,c.skip=l),c.size>4?c:void 0}(a.pos-i,d))){let e=new Uint16Array(w.size-w.skip),i=a.pos-w.size,n=e.length;for(;a.pos>i;)n=function e(t,i,r){let{id:n,start:s,end:l,size:f}=a;if(a.next(),f>=0&&n<o){let o=r;if(f>4){let n=a.pos-(f-4);for(;a.pos>n;)r=e(t,i,r)}i[--r]=o,i[--r]=l-t,i[--r]=s-t,i[--r]=n}else -3==f?h=n:-4==f&&(c=n);return r}(w.start,e,n);S=new e1(e,x-w.start,r),$=w.start-t}else{let t=a.pos-k;a.next();let i=[],s=[],l=m>=o?m:-1,h=0,c=x;for(;a.pos>t;)l>=0&&a.id==l&&a.size>=0?(a.end<=c-n&&(f(i,s,y,h,a.end,c,l,Q,b),h=i.length,c=a.end),a.next()):g>2500?function(e,t,i,s){let o=[],l=0,h=-1;for(;a.pos>t;){let{id:e,start:t,end:i,size:r}=a;if(r>4)a.next();else if(h>-1&&t<h)break;else h<0&&(h=i-n),o.push(e,t,i),l++,a.next()}if(l){let t=new Uint16Array(4*l),n=o[o.length-2];for(let e=o.length-3,i=0;e>=0;e-=3)t[i++]=o[e],t[i++]=o[e+1]-n,t[i++]=o[e+2]-n,t[i++]=i;i.push(new e1(t,o[2]-n,r)),s.push(n-e)}}(y,t,i,s):e(y,t,i,s,l,g+1);if(l>=0&&h>0&&h<i.length&&f(i,s,y,h,y,c,l,Q,b),i.reverse(),s.reverse(),l>-1&&h>0){let e=function(e,t){return(i,r,n)=>{let s=0,o=i.length-1,a,l;if(o>=0&&(a=i[o])instanceof eH){if(!o&&a.type==e&&a.length==n)return a;(l=a.prop(eG.lookAhead))&&(s=r[o]+a.length+l)}return u(e,i,r,n,s,t)}}(v,b);S=to(v,i,s,0,i.length,0,x-y,e,e)}else S=u(v,i,s,x-y,Q-x,b)}p.push(S),O.push($)}(e.start||0,e.bufferStart||0,p,O,-1,0);let d=null!==(t=e.length)&&void 0!==t?t:p.length?O[0]+p[0].length:0;return new eH(l[e.topID],p.reverse(),O.reverse(),d)}(e)}}eH.empty=new eH(eD.none,[],[],0);class e0{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new e0(this.buffer,this.index)}}class e1{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return eD.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],r=this.set.types[t],n=r.name;if(/\W/.test(n)&&!r.isError&&(n=JSON.stringify(n)),i==(e+=4))return n;let s=[];for(;e<i;)s.push(this.childString(e)),e=this.buffer[e+3];return n+"("+s.join(",")+")"}findChild(e,t,i,r,n){let{buffer:s}=this,o=-1;for(let a=e;a!=t&&(!e2(n,r,s[a+1],s[a+2])||(o=a,!(i>0)));a=s[a+3]);return o}slice(e,t,i){let r=this.buffer,n=new Uint16Array(t-e),s=0;for(let o=e,a=0;o<t;){n[a++]=r[o++],n[a++]=r[o++]-i;let t=n[a++]=r[o++]-i;n[a++]=r[o++]-e,s=Math.max(s,t)}return new e1(n,s,this.set)}}function e2(e,t,i,r){switch(e){case -2:return i<t;case -1:return r>=t&&i<t;case 0:return i<t&&r>t;case 1:return i<=t&&r>t;case 2:return r>t;case 4:return!0}}function e5(e,t,i,r){for(var n;e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t);){let t=!r&&e instanceof e3&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=r?0:q.IgnoreOverlays;if(r)for(let r=e,o=r.parent;o;o=(r=o).parent)r instanceof e3&&r.index<0&&(null===(n=o.enter(t,i,s))||void 0===n?void 0:n.from)!=r.from&&(e=o);for(;;){let r=e.enter(t,i,s);if(!r)return e;e=r}}class e4{cursor(e=0){return new ti(this,e)}getChild(e,t=null,i=null){let r=e7(this,e,t,i);return r.length?r[0]:null}getChildren(e,t=null,i=null){return e7(this,e,t,i)}resolve(e,t=0){return e5(this,e,t,!1)}resolveInner(e,t=0){return e5(this,e,t,!0)}matchContext(e){return e9(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(i=t,t=e.prevSibling):t=e}return i}get node(){return this}get next(){return this.parent}}class e3 extends e4{constructor(e,t,i,r){super(),this._tree=e,this.from=t,this.index=i,this._parent=r}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,r,n=0){for(let s=this;;){for(let{children:o,positions:a}=s._tree,l=t>0?o.length:-1;e!=l;e+=t){let l=o[e],h=a[e]+s.from;if(e2(r,i,h,h+l.length)){if(l instanceof e1){if(n&q.ExcludeBuffers)continue;let o=l.findChild(0,l.buffer.length,t,i-h,r);if(o>-1)return new e8(new e6(s,l,e,h),null,o)}else if(n&q.IncludeAnonymous||!l.type.isAnonymous||tr(l)){let o;if(!(n&q.IgnoreMounts)&&(o=eB.get(l))&&!o.overlay)return new e3(o.tree,h,e,s);let a=new e3(l,h,e,s);return n&q.IncludeAnonymous||!a.type.isAnonymous?a:a.nextChild(t<0?l.children.length-1:0,t,i,r)}}}if(n&q.IncludeAnonymous||!s.type.isAnonymous||(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,!(s=s._parent)))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let r;if(!(i&q.IgnoreOverlays)&&(r=eB.get(this._tree))&&r.overlay){let i=e-this.from;for(let{from:e,to:n}of r.overlay)if((t>0?e<=i:e<i)&&(t<0?n>=i:n>i))return new e3(r.tree,r.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function e7(e,t,i,r){let n=e.cursor(),s=[];if(!n.firstChild())return s;if(null!=i){for(let e=!1;!e;)if(e=n.type.is(i),!n.nextSibling())return s}for(;;){if(null!=r&&n.type.is(r))return s;if(n.type.is(t)&&s.push(n.node),!n.nextSibling())return null==r?s:[]}}function e9(e,t,i=t.length-1){for(let r=e;i>=0;r=r.parent){if(!r)return!1;if(!r.type.isAnonymous){if(t[i]&&t[i]!=r.name)return!1;i--}}return!0}class e6{constructor(e,t,i,r){this.parent=e,this.buffer=t,this.index=i,this.start=r}}class e8 extends e4{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.context.start,i);return n<0?null:new e8(this.context,this,n)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&q.ExcludeBuffers)return null;let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return n<0?null:new e8(this.context,this,n)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new e8(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new e8(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,r=this.index+4,n=i.buffer[this.index+3];if(n>r){let s=i.buffer[this.index+1];e.push(i.slice(r,n,s)),t.push(0)}return new eH(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function te(e){if(!e.length)return null;let t=0,i=e[0];for(let r=1;r<e.length;r++){let n=e[r];(n.from>i.from||n.to<i.to)&&(i=n,t=r)}let r=i instanceof e3&&i.index<0?null:i.parent,n=e.slice();return r?n[t]=r:n.splice(t,1),new tt(n,i)}class tt{constructor(e,t){this.heads=e,this.node=t}get next(){return te(this.heads)}}class ti{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof e3)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:r}=this.buffer;return this.type=t||r.set.types[r.buffer[e]],this.from=i+r.buffer[e+1],this.to=i+r.buffer[e+2],!0}yield(e){return!!e&&(e instanceof e3?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:r}=this.buffer,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.buffer.start,i);return!(n<0)&&(this.stack.push(this.index),this.yieldBuf(n))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&q.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&q.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&q.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:r}=this;if(r){if(e>0){if(this.index<r.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(r.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=r)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,n=e<0?-1:i._tree.children.length;r!=n;r+=e){let e=i._tree.children[r];if(this.mode&q.IncludeAnonymous||e instanceof e1||!e.type.isAnonymous||tr(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let r=this.index,n=this.stack.length;n>=0;){for(let s=e;s;s=s._parent)if(s.index==r){if(r==this.index)return s;t=s,i=n+1;break e}r=this.stack[--n]}for(let e=i;e<this.stack.length;e++)t=new e8(this.buffer,t,this.stack[e]);return this.bufferNode=new e8(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let r=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(r=!0)}for(;;){if(r&&t&&t(this),r=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,r=!0}}}matchContext(e){if(!this.buffer)return e9(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let r=e.length-1,n=this.stack.length-1;r>=0;n--){if(n<0)return e9(this._tree,e,r);let s=i[t.buffer[this.stack[n]]];if(!s.isAnonymous){if(e[r]&&e[r]!=s.name)return!1;r--}}return!0}}function tr(e){return e.children.some(e=>e instanceof e1||!e.type.isAnonymous||tr(e))}let tn=new WeakMap;function ts(e,t){if(!e.isAnonymous||t instanceof e1||t.type!=e)return 1;let i=tn.get(t);if(null==i){for(let r of(i=1,t.children)){if(r.type!=e||!(r instanceof eH)){i=1;break}i+=ts(e,r)}tn.set(t,i)}return i}function to(e,t,i,r,n,s,o,a,l){let h=0;for(let i=r;i<n;i++)h+=ts(e,t[i]);let c=Math.ceil(1.5*h/8),f=[],u=[];return!function t(i,r,n,o,a){for(let h=n;h<o;){let n=h,p=r[h],O=ts(e,i[h]);for(h++;h<o;h++){let t=ts(e,i[h]);if(O+t>=c)break;O+=t}if(h==n+1){if(O>c){let e=i[n];t(e.children,e.positions,0,e.children.length,r[n]+a);continue}f.push(i[n])}else{let t=r[h-1]+i[h-1].length-p;f.push(to(e,i,r,n,h,p,t,null,l))}u.push(p+a-s)}}(t,i,r,n,0),(a||l)(f,u,o)}class ta{constructor(e,t,i,r,n=!1,s=!1){this.from=e,this.to=t,this.tree=i,this.offset=r,this.open=(n?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],i=!1){let r=[new ta(0,e.length,e,0,!1,i)];for(let i of t)i.to>e.length&&r.push(i);return r}static applyChanges(e,t,i=128){if(!t.length)return e;let r=[],n=1,s=e.length?e[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<t.length?t[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;s&&s.from<c;){let t=s;if(a>=t.from||c<=t.to||l){let e=Math.max(t.from,a)-l,i=Math.min(t.to,c)-l;t=e>=i?null:new ta(e,i,t.tree,t.offset+l,o>0,!!h)}if(t&&r.push(t),s.to>c)break;s=n<e.length?e[n++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return r}}class tl{startParse(e,t,i){return"string"==typeof e&&(e=new th(e)),i=i?i.length?i.map(e=>new eW(e.from,e.to)):[new eW(0,0)]:[new eW(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let r=this.startParse(e,t,i);for(;;){let e=r.advance();if(e)return e}}}class th{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new eG({perNode:!0});var tc=i(7474),tf=i(188);let tu=0;class tp{constructor(e,t,i,r){this.name=e,this.set=t,this.base=i,this.modified=r,this.id=tu++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i="string"==typeof e?e:"?";if(e instanceof tp&&(t=e),null==t?void 0:t.base)throw Error("Can not derive from a modified tag");let r=new tp(i,[],null,[]);if(r.set.push(r),t)for(let e of t.set)r.set.push(e);return r}static defineModifier(e){let t=new td(e);return e=>e.modified.indexOf(t)>-1?e:td.get(e.base||e,e.modified.concat(t).sort((e,t)=>e.id-t.id))}}let tO=0;class td{constructor(e){this.name=e,this.instances=[],this.id=tO++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>{var r;return i.base==e&&(r=i.modified,t.length==r.length&&t.every((e,t)=>e==r[t]))});if(i)return i;let r=[],n=new tp(e.name,r,e,t);for(let e of t)e.instances.push(n);let s=function(e){let t=[[]];for(let i=0;i<e.length;i++)for(let r=0,n=t.length;r<n;r++)t.push(t[r].concat(e[i]));return t.sort((e,t)=>t.length-e.length)}(t);for(let t of e.set)if(!t.modified.length)for(let e of s)r.push(td.get(t,e));return n}}let tg=new eG;class tm{constructor(e,t,i,r){this.tags=e,this.mode=t,this.context=i,this.next=r}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}function ty(e,t){let i=Object.create(null);for(let t of e)if(Array.isArray(t.tag))for(let e of t.tag)i[e.id]=t.class;else i[t.tag.id]=t.class;let{scope:r,all:n=null}=t||{};return{style:e=>{let t=n;for(let r of e)for(let e of r.set){let r=i[e.id];if(r){t=t?t+" "+r:r;break}}return t},scope:r}}tm.empty=new tm([],2,null);let tx=tp.define,tk=tx(),tQ=tx(),tb=tx(tQ),tv=tx(tQ),tS=tx(),tw=tx(tS),t$=tx(tS),tP=tx(),tZ=tx(tP),tT=tx(),t_=tx(),tX=tx(),tC=tx(tX),tA=tx(),tR={comment:tk,lineComment:tx(tk),blockComment:tx(tk),docComment:tx(tk),name:tQ,variableName:tx(tQ),typeName:tb,tagName:tx(tb),propertyName:tv,attributeName:tx(tv),className:tx(tQ),labelName:tx(tQ),namespace:tx(tQ),macroName:tx(tQ),literal:tS,string:tw,docString:tx(tw),character:tx(tw),attributeValue:tx(tw),number:t$,integer:tx(t$),float:tx(t$),bool:tx(tS),regexp:tx(tS),escape:tx(tS),color:tx(tS),url:tx(tS),keyword:tT,self:tx(tT),null:tx(tT),atom:tx(tT),unit:tx(tT),modifier:tx(tT),operatorKeyword:tx(tT),controlKeyword:tx(tT),definitionKeyword:tx(tT),moduleKeyword:tx(tT),operator:t_,derefOperator:tx(t_),arithmeticOperator:tx(t_),logicOperator:tx(t_),bitwiseOperator:tx(t_),compareOperator:tx(t_),updateOperator:tx(t_),definitionOperator:tx(t_),typeOperator:tx(t_),controlOperator:tx(t_),punctuation:tX,separator:tx(tX),bracket:tC,angleBracket:tx(tC),squareBracket:tx(tC),paren:tx(tC),brace:tx(tC),content:tP,heading:tZ,heading1:tx(tZ),heading2:tx(tZ),heading3:tx(tZ),heading4:tx(tZ),heading5:tx(tZ),heading6:tx(tZ),contentSeparator:tx(tP),list:tx(tP),quote:tx(tP),emphasis:tx(tP),strong:tx(tP),link:tx(tP),monospace:tx(tP),strikethrough:tx(tP),inserted:tx(),deleted:tx(),changed:tx(),invalid:tx(),meta:tA,documentMeta:tx(tA),annotation:tx(tA),processingInstruction:tx(tA),definition:tp.defineModifier("definition"),constant:tp.defineModifier("constant"),function:tp.defineModifier("function"),standard:tp.defineModifier("standard"),local:tp.defineModifier("local"),special:tp.defineModifier("special")};for(let e in tR){let t=tR[e];t instanceof tp&&(t.name=e)}ty([{tag:tR.link,class:"tok-link"},{tag:tR.heading,class:"tok-heading"},{tag:tR.emphasis,class:"tok-emphasis"},{tag:tR.strong,class:"tok-strong"},{tag:tR.keyword,class:"tok-keyword"},{tag:tR.atom,class:"tok-atom"},{tag:tR.bool,class:"tok-bool"},{tag:tR.url,class:"tok-url"},{tag:tR.labelName,class:"tok-labelName"},{tag:tR.inserted,class:"tok-inserted"},{tag:tR.deleted,class:"tok-deleted"},{tag:tR.literal,class:"tok-literal"},{tag:tR.string,class:"tok-string"},{tag:tR.number,class:"tok-number"},{tag:[tR.regexp,tR.escape,tR.special(tR.string)],class:"tok-string2"},{tag:tR.variableName,class:"tok-variableName"},{tag:tR.local(tR.variableName),class:"tok-variableName tok-local"},{tag:tR.definition(tR.variableName),class:"tok-variableName tok-definition"},{tag:tR.special(tR.variableName),class:"tok-variableName2"},{tag:tR.definition(tR.propertyName),class:"tok-propertyName tok-definition"},{tag:tR.typeName,class:"tok-typeName"},{tag:tR.namespace,class:"tok-namespace"},{tag:tR.className,class:"tok-className"},{tag:tR.macroName,class:"tok-macroName"},{tag:tR.propertyName,class:"tok-propertyName"},{tag:tR.operator,class:"tok-operator"},{tag:tR.comment,class:"tok-comment"},{tag:tR.meta,class:"tok-meta"},{tag:tR.invalid,class:"tok-invalid"},{tag:tR.punctuation,class:"tok-punctuation"}]);var tq=i(3237);let tM=new eG;function tj(e){return tc.r$.define({combine:e?t=>t.concat(e):void 0})}let tY=new eG;class tN{constructor(e,t,i=[],r=""){this.data=e,this.name=r,tc.yy.prototype.hasOwnProperty("tree")||Object.defineProperty(tc.yy.prototype,"tree",{get(){return tz(this)}}),this.parser=t,this.extension=[tF.of(this),tc.yy.languageData.of((e,t,i)=>{let r=tI(e,t,i),n=r.type.prop(tM);if(!n)return[];let s=e.facet(n),o=r.type.prop(tY);if(o){let n=r.resolve(t-r.from,i);for(let t of o)if(t.test(n,e)){let i=e.facet(t.facet);return"replace"==t.type?i:i.concat(s)}}return s})].concat(i)}isActiveAt(e,t,i=-1){return tI(e,t,i).type.prop(tM)==this.data}findRegions(e){let t=e.facet(tF);if((null==t?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],r=(e,t)=>{if(e.prop(tM)==this.data){i.push({from:t,to:t+e.length});return}let n=e.prop(eG.mounted);if(n){if(n.tree.prop(tM)==this.data){if(n.overlay)for(let e of n.overlay)i.push({from:e.from+t,to:e.to+t});else i.push({from:t,to:t+e.length});return}if(n.overlay){let e=i.length;if(r(n.tree,n.overlay[0].from+t),i.length>e)return}}for(let i=0;i<e.children.length;i++){let n=e.children[i];n instanceof eH&&r(n,e.positions[i]+t)}};return r(tz(e),0),i}get allowsNesting(){return!0}}function tI(e,t,i){let r=e.facet(tF),n=tz(e).topNode;if(!r||r.allowsNesting)for(let e=n;e;e=e.enter(t,i,q.ExcludeBuffers))e.type.isTop&&(n=e);return n}tN.setState=tc.Py.define();class tE extends tN{constructor(e,t,i){super(e,t,[],i),this.parser=t}static define(e){let t=tj(e.languageData);return new tE(t,e.parser.configure({props:[tM.add(e=>e.isTop?t:void 0)]}),e.name)}configure(e,t){return new tE(this.data,this.parser.configure(e),t||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function tz(e){let t=e.field(tN.state,!1);return t?t.tree:eH.empty}class tV{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let tL=null;class tW{constructor(e,t,i=[],r,n,s,o,a){this.parser=e,this.state=t,this.fragments=i,this.tree=r,this.treeLen=n,this.viewport=s,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new tW(e,t,[],eH.empty,0,i,[],null)}startParse(){return this.parser.startParse(new tV(this.state.doc),this.fragments)}work(e,t){return(null!=t&&t>=this.state.doc.length&&(t=void 0),this.tree!=eH.empty&&this.isDone(null!=t?t:this.state.doc.length))?(this.takeTree(),!0):this.withContext(()=>{var i;if("number"==typeof e){let t=Date.now()+e;e=()=>Date.now()>t}for(this.parse||(this.parse=this.startParse()),null!=t&&(null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let r=this.parse.advance();if(r){if(this.fragments=this.withoutTempSkipped(ta.addTree(r,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=r,this.parse=null,!(this.treeLen<(null!=t?t:this.state.doc.length)))return!0;this.parse=this.startParse()}if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(ta.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=tL;tL=this;try{return e()}finally{tL=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=tG(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:r,treeLen:n,viewport:s,skipped:o}=this;if(this.takeTree(),!e.empty){let t=[];if(e.iterChangedRanges((e,i,r,n)=>t.push({fromA:e,toA:i,fromB:r,toB:n})),i=ta.applyChanges(i,t),r=eH.empty,n=0,s={from:e.mapPos(s.from,-1),to:e.mapPos(s.to,1)},this.skipped.length)for(let t of(o=[],this.skipped)){let i=e.mapPos(t.from,1),r=e.mapPos(t.to,-1);i<r&&o.push({from:i,to:r})}}return new tW(this.parser,t,i,r,n,s,o,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let t=0;t<this.skipped.length;t++){let{from:i,to:r}=this.skipped[t];i<e.to&&r>e.from&&(this.fragments=tG(this.fragments,i,r),this.skipped.splice(t--,1))}return!(this.skipped.length>=t)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends tl{createParse(t,i,r){let n=r[0].from,s=r[r.length-1].to;return{parsedPos:n,advance(){let t=tL;if(t){for(let e of r)t.tempSkipped.push(e);e&&(t.scheduleOn=t.scheduleOn?Promise.all([t.scheduleOn,e]):e)}return this.parsedPos=s,new eH(eD.none,[],[],s-n)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&0==t[0].from&&t[0].to>=e}static get(){return tL}}function tG(e,t,i){return ta.applyChanges(e,[{fromA:t,toA:i,fromB:t,toB:i}])}class tB{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new tB(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=tW.create(e.facet(tF).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new tB(i)}}tN.state=tc.QQ.define({create:tB.init,update(e,t){for(let e of t.effects)if(e.is(tN.setState))return e.value;return t.startState.facet(tF)!=t.state.facet(tF)?tB.init(t.state):e.apply(t)}});let tU=e=>{let t=setTimeout(()=>e(),500);return()=>clearTimeout(t)};"undefined"!=typeof requestIdleCallback&&(tU=e=>{let t=-1,i=setTimeout(()=>{t=requestIdleCallback(e,{timeout:400})},100);return()=>t<0?clearTimeout(i):cancelIdleCallback(t)});let tD="undefined"!=typeof navigator&&(null===(M=navigator.scheduling)||void 0===M?void 0:M.isInputPending)?()=>navigator.scheduling.isInputPending():null,tJ=tf.lg.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(tN.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(tN.state);t.tree==t.context.tree&&t.context.isDone(e.doc.length)||(this.working=tU(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:r}}=this.view,n=i.field(tN.state);if(n.tree==n.context.tree&&n.context.isDone(r+1e5))return;let s=Date.now()+Math.min(this.chunkBudget,100,e&&!tD?Math.max(25,e.timeRemaining()-5):1e9),o=n.context.treeLen<r&&i.doc.length>r+1e3,a=n.context.work(()=>tD&&tD()||Date.now()>s,r+(o?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(n.context.takeTree(),this.view.dispatch({effects:tN.setState.of(new tB(n.context))})),this.chunkBudget>0&&!(a&&!o)&&this.scheduleWork(),this.checkAsyncSchedule(n.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(e=>(0,tf.OO)(this.view.state,e)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),tF=tc.r$.define({combine:e=>e.length?e[0]:null,enables:e=>[tN.state,tJ,tf.tk.contentAttributes.compute([e],t=>{let i=t.facet(e);return i&&i.name?{"data-language":i.name}:{}})]});class tK{constructor(e,t=[]){this.language=e,this.support=t,this.extension=[e,t]}}let tH=tc.r$.define({combine:e=>{if(!e.length)return"  ";let t=e[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw Error("Invalid indent unit: "+JSON.stringify(e[0]));return t}}),t0=new eG;function t1({except:e,units:t=1}={}){return i=>{let r=e&&e.test(i.textAfter);return i.baseIndent+(r?0:t*i.unit)}}let t2=new eG;function t5(e,t){let i=t.mapPos(e.from,1),r=t.mapPos(e.to,-1);return i>=r?void 0:{from:i,to:r}}let t4=tc.Py.define({map:t5}),t3=tc.Py.define({map:t5}),t7=tc.QQ.define({create:()=>tf.p.none,update(e,t){for(let i of(t.isUserEvent("delete")&&t.changes.iterChangedRanges((t,i)=>e=t9(e,t,i)),e=e.map(t.changes),t.effects))if(i.is(t4)&&!function(e,t,i){let r=!1;return e.between(t,t,(e,n)=>{e==t&&n==i&&(r=!0)}),r}(e,i.value.from,i.value.to)){let{preparePlaceholder:r}=t.state.facet(t8),n=r?tf.p.replace({widget:new ii(r(t.state,i.value))}):it;e=e.update({add:[n.range(i.value.from,i.value.to)]})}else i.is(t3)&&(e=e.update({filter:(e,t)=>i.value.from!=e||i.value.to!=t,filterFrom:i.value.from,filterTo:i.value.to}));return t.selection&&(e=t9(e,t.selection.main.head)),e},provide:e=>tf.tk.decorations.from(e),toJSON(e,t){let i=[];return e.between(0,t.doc.length,(e,t)=>{i.push(e,t)}),i},fromJSON(e){if(!Array.isArray(e)||e.length%2)throw RangeError("Invalid JSON for fold state");let t=[];for(let i=0;i<e.length;){let r=e[i++],n=e[i++];if("number"!=typeof r||"number"!=typeof n)throw RangeError("Invalid JSON for fold state");t.push(it.range(r,n))}return tf.p.set(t,!0)}});function t9(e,t,i=t){let r=!1;return e.between(t,i,(e,n)=>{e<i&&n>t&&(r=!0)}),r?e.update({filterFrom:t,filterTo:i,filter:(e,r)=>e>=i||r<=t}):e}let t6={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},t8=tc.r$.define({combine:e=>(0,tc.BO)(e,t6)});function ie(e,t){let{state:i}=e,r=i.facet(t8),n=t=>{var i,r,n,s;let o,a=e.lineBlockAt(e.posAtDOM(t.target)),l=(i=e.state,r=a.from,n=a.to,o=null,null===(s=i.field(t7,!1))||void 0===s||s.between(r,n,(e,t)=>{(!o||o.from>e)&&(o={from:e,to:t})}),o);l&&e.dispatch({effects:t3.of(l)}),t.preventDefault()};if(r.placeholderDOM)return r.placeholderDOM(e,n,t);let s=document.createElement("span");return s.textContent=r.placeholderText,s.setAttribute("aria-label",i.phrase("folded code")),s.title=i.phrase("unfold"),s.className="cm-foldPlaceholder",s.onclick=n,s}let it=tf.p.replace({widget:new class extends tf.l9{toDOM(e){return ie(e,null)}}});class ii extends tf.l9{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return ie(e,this.value)}}class ir{constructor(e,t){let i;function r(e){let t=tq.V.newName();return(i||(i=Object.create(null)))["."+t]=e,t}this.specs=e;let n="string"==typeof t.all?t.all:t.all?r(t.all):void 0,s=t.scope;this.scope=s instanceof tN?e=>e.prop(tM)==s.data:s?e=>e==s:void 0,this.style=ty(e.map(e=>({tag:e.tag,class:e.class||r(Object.assign({},e,{tag:null}))})),{all:n}).style,this.module=i?new tq.V(i):null,this.themeType=t.themeType}static define(e,t){return new ir(e,t||{})}}tR.meta,tR.link,tR.heading,tR.emphasis,tR.strong,tR.strikethrough,tR.keyword,tR.atom,tR.bool,tR.url,tR.contentSeparator,tR.labelName,tR.literal,tR.inserted,tR.string,tR.deleted,tR.regexp,tR.escape,tR.string,tR.variableName,tR.variableName,tR.typeName,tR.namespace,tR.className,tR.variableName,tR.macroName,tR.propertyName,tR.comment,tR.invalid;let is=Object.create(null),io=[eD.none],ia=[],il=Object.create(null),ih=Object.create(null);for(let[e,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])ih[e]=function(e,t){let i=[];for(let r of t.split(" ")){let t=[];for(let i of r.split(".")){let r=e[i]||tR[i];r?"function"==typeof r?t.length?t=t.map(r):ic(i,`Modifier ${i} used at start of tag`):t.length?ic(i,`Tag ${i} used as modifier`):t=Array.isArray(r)?r:[r]:ic(i,`Unknown highlighting tag ${i}`)}for(let e of t)i.push(e)}if(!i.length)return 0;let r=t.replace(/ /g,"_"),n=r+" "+i.map(e=>e.id),s=il[n];if(s)return s.id;let o=il[n]=eD.define({id:io.length,name:r,props:[function(e){let t=Object.create(null);for(let i in e){let r=e[i];for(let e of(Array.isArray(r)||(r=[r]),i.split(" ")))if(e){let i=[],n=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){n=1;break}let r=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!r)throw RangeError("Invalid path: "+e);if(i.push("*"==r[0]?"":'"'==r[0][0]?JSON.parse(r[0]):r[0]),(t+=r[0].length)==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){n=0;break}if("/"!=o)throw RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,a=i[o];if(!a)throw RangeError("Invalid path: "+e);let l=new tm(r,n,o>0?i.slice(0,o):null);t[a]=l.sort(t[a])}}return tg.add(t)}({[r]:i})]});return io.push(o),o.id}(is,t);function ic(e,t){ia.indexOf(e)>-1||(ia.push(e),console.warn(t))}tf.Nm.RTL,tf.Nm.LTR;class iu{constructor(e,t,i,r){this.state=e,this.pos=t,this.explicit=i,this.view=r,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(e){let t=tz(this.state).resolveInner(this.pos,-1);for(;t&&0>e.indexOf(t.name);)t=t.parent;return t?{from:t.from,to:this.pos,text:this.state.sliceDoc(t.from,this.pos),type:t.type}:null}matchBefore(e){let t=this.state.doc.lineAt(this.pos),i=Math.max(t.from,this.pos-250),r=t.text.slice(i-t.from,this.pos-t.from),n=r.search(im(e,!1));return n<0?null:{from:i+n,to:this.pos,text:r.slice(n)}}get aborted(){return null==this.abortListeners}addEventListener(e,t,i){"abort"==e&&this.abortListeners&&(this.abortListeners.push(t),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function ip(e){let t=Object.keys(e).join(""),i=/\w/.test(t);return i&&(t=t.replace(/\w/g,"")),`[${i?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function iO(e){let t=e.map(e=>"string"==typeof e?{label:e}:e),[i,r]=t.every(e=>/^\w+$/.test(e.label))?[/\w*$/,/\w+$/]:function(e){let t=Object.create(null),i=Object.create(null);for(let{label:r}of e){t[r[0]]=!0;for(let e=1;e<r.length;e++)i[r[e]]=!0}let r=ip(t)+ip(i)+"*$";return[RegExp("^"+r),new RegExp(r)]}(t);return e=>{let n=e.matchBefore(r);return n||e.explicit?{from:n?n.from:e.pos,options:t,validFor:i}:null}}class id{constructor(e,t,i,r){this.completion=e,this.source=t,this.match=i,this.score=r}}function ig(e){return e.selection.main.from}function im(e,t){var i;let{source:r}=e,n=t&&"^"!=r[0],s="$"!=r[r.length-1];return n||s?RegExp(`${n?"^":""}(?:${r})${s?"$":""}`,null!==(i=e.flags)&&void 0!==i?i:e.ignoreCase?"i":""):e}let iy=tc.q6.define(),ix=new WeakMap;function ik(e){if(!Array.isArray(e))return e;let t=ix.get(e);return t||ix.set(e,t=iO(e)),t}let iQ=tc.Py.define(),ib=tc.Py.define();class iv{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let t=0;t<e.length;){let i=(0,tc.gm)(e,t),r=(0,tc.nZ)(i);this.chars.push(i);let n=e.slice(t,t+r),s=n.toUpperCase();this.folded.push((0,tc.gm)(s==n?n.toLowerCase():s,0)),t+=r}this.astral=e.length!=this.chars.length}ret(e,t){return this.score=e,this.matched=t,this}match(e){if(0==this.pattern.length)return this.ret(-100,[]);if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:r,precise:n,byWord:s}=this;if(1==t.length){let r=(0,tc.gm)(e,0),n=(0,tc.nZ)(r),s=n==e.length?0:-100;if(r==t[0]);else{if(r!=i[0])return null;s+=-200}return this.ret(s,[0,n])}let o=e.indexOf(this.pattern);if(0==o)return this.ret(e.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=t.length,l=0;if(o<0){for(let n=0,s=Math.min(e.length,200);n<s&&l<a;){let s=(0,tc.gm)(e,n);(s==t[l]||s==i[l])&&(r[l++]=n),n+=(0,tc.nZ)(s)}if(l<a)return null}let h=0,c=0,f=!1,u=0,p=-1,O=-1,d=/[a-z]/.test(e),g=!0;for(let r=0,l=Math.min(e.length,200),m=0;r<l&&c<a;){let l=(0,tc.gm)(e,r);o<0&&(h<a&&l==t[h]&&(n[h++]=r),u<a&&(l==t[u]||l==i[u]?(0==u&&(p=r),O=r+1,u++):u=0));let y,x=l<255?l>=48&&l<=57||l>=97&&l<=122?2:l>=65&&l<=90?1:0:(y=(0,tc.bg)(l))!=y.toLowerCase()?1:y!=y.toUpperCase()?2:0;(!r||1==x&&d||0==m&&0!=x)&&(t[c]==l||i[c]==l&&(f=!0)?s[c++]=r:s.length&&(g=!1)),m=x,r+=(0,tc.nZ)(l)}return c==a&&0==s[0]&&g?this.result(-100+(f?-200:0),s,e):u==a&&0==p?this.ret(-200-e.length+(O==e.length?0:-100),[0,O]):o>-1?this.ret(-700-e.length,[o,o+this.pattern.length]):u==a?this.ret(-900-e.length,[p,O]):c==a?this.result(-100+(f?-200:0)+-700+(g?0:-1100),s,e):2==t.length?null:this.result((r[0]?-700:0)+-200+-1100,r,e)}result(e,t,i){let r=[],n=0;for(let e of t){let t=e+(this.astral?(0,tc.nZ)((0,tc.gm)(i,e)):1);n&&r[n-1]==e?r[n-1]=t:(r[n++]=e,r[n++]=t)}return this.ret(e-i.length,r)}}class iS{constructor(e){this.pattern=e,this.matched=[],this.score=0,this.folded=e.toLowerCase()}match(e){if(e.length<this.pattern.length)return null;let t=e.slice(0,this.pattern.length),i=t==this.pattern?0:t.toLowerCase()==this.folded?-200:null;return null==i?null:(this.matched=[0,t.length],this.score=i+(e.length==this.pattern.length?0:-100),this)}}let iw=tc.r$.define({combine:e=>(0,tc.BO)(e,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:iP,filterStrict:!1,compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,tooltipClass:(e,t)=>i=>i$(e(i),t(i)),optionClass:(e,t)=>i=>i$(e(i),t(i)),addToOptions:(e,t)=>e.concat(t),filterStrict:(e,t)=>e||t})});function i$(e,t){return e?t?e+" "+t:e:t}function iP(e,t,i,r,n,s){let o=e.textDirection==tf.Nm.RTL,a=o,l=!1,h="top",c,f,u=t.left-n.left,p=n.right-t.right,O=r.right-r.left,d=r.bottom-r.top;if(a&&u<Math.min(O,p)?a=!1:!a&&p<Math.min(O,u)&&(a=!0),O<=(a?u:p))c=Math.max(n.top,Math.min(i.top,n.bottom-d))-t.top,f=Math.min(400,a?u:p);else{l=!0,f=Math.min(400,(o?t.right:n.right-t.left)-30);let e=n.bottom-t.bottom;e>=d||e>t.top?c=i.bottom-t.top:(h="bottom",c=t.bottom-i.top)}return{style:`${h}: ${c/((t.bottom-t.top)/s.offsetHeight)}px; max-width: ${f/((t.right-t.left)/s.offsetWidth)}px`,class:"cm-completionInfo-"+(l?o?"left-narrow":"right-narrow":a?"left":"right")}}function iZ(e,t,i){if(e<=i)return{from:0,to:e};if(t<0&&(t=0),t<=e>>1){let e=Math.floor(t/i);return{from:e*i,to:(e+1)*i}}let r=Math.floor((e-t)/i);return{from:e-(r+1)*i,to:e-r*i}}class iT{constructor(e,t,i){let r;this.view=e,this.stateField=t,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:e=>this.placeInfo(e),key:this},this.space=null,this.currentClass="";let n=e.state.field(t),{options:s,selected:o}=n.open,a=e.state.facet(iw);this.optionContent=(r=a.addToOptions.slice(),a.icons&&r.push({render(e){let t=document.createElement("div");return t.classList.add("cm-completionIcon"),e.type&&t.classList.add(...e.type.split(/\s+/g).map(e=>"cm-completionIcon-"+e)),t.setAttribute("aria-hidden","true"),t},position:20}),r.push({render(e,t,i,r){let n=document.createElement("span");n.className="cm-completionLabel";let s=e.displayLabel||e.label,o=0;for(let e=0;e<r.length;){let t=r[e++],i=r[e++];t>o&&n.appendChild(document.createTextNode(s.slice(o,t)));let a=n.appendChild(document.createElement("span"));a.appendChild(document.createTextNode(s.slice(t,i))),a.className="cm-completionMatchedText",o=i}return o<s.length&&n.appendChild(document.createTextNode(s.slice(o))),n},position:50},{render(e){if(!e.detail)return null;let t=document.createElement("span");return t.className="cm-completionDetail",t.textContent=e.detail,t},position:80}),r.sort((e,t)=>e.position-t.position).map(e=>e.render)),this.optionClass=a.optionClass,this.tooltipClass=a.tooltipClass,this.range=iZ(s.length,o,a.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(e.state),this.dom.addEventListener("mousedown",i=>{let{options:r}=e.state.field(t).open;for(let t=i.target,n;t&&t!=this.dom;t=t.parentNode)if("LI"==t.nodeName&&(n=/-(\d+)$/.exec(t.id))&&+n[1]<r.length){this.applyCompletion(e,r[+n[1]]),i.preventDefault();return}}),this.dom.addEventListener("focusout",t=>{let i=e.state.field(this.stateField,!1);i&&i.tooltip&&e.state.facet(iw).closeOnBlur&&t.relatedTarget!=e.contentDOM&&e.dispatch({effects:ib.of(null)})}),this.showOptions(s,n.id)}mount(){this.updateSel()}showOptions(e,t){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(e,t,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(e){var t;let i=e.state.field(this.stateField),r=e.startState.field(this.stateField);if(this.updateTooltipClass(e.state),i!=r){let{options:n,selected:s,disabled:o}=i.open;r.open&&r.open.options==n||(this.range=iZ(n.length,s,e.state.facet(iw).maxRenderedOptions),this.showOptions(n,i.id)),this.updateSel(),o!=(null===(t=r.open)||void 0===t?void 0:t.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!o)}}updateTooltipClass(e){let t=this.tooltipClass(e);if(t!=this.currentClass){for(let e of this.currentClass.split(" "))e&&this.dom.classList.remove(e);for(let e of t.split(" "))e&&this.dom.classList.add(e);this.currentClass=t}}positioned(e){this.space=e,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=iZ(t.options.length,t.selected,this.view.state.facet(iw).maxRenderedOptions),this.showOptions(t.options,e.id)),this.updateSelectedOption(t.selected)){this.destroyInfo();let{completion:i}=t.options[t.selected],{info:r}=i;if(!r)return;let n="string"==typeof r?document.createTextNode(r):r(i);if(!n)return;"then"in n?n.then(t=>{t&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(t,i)}).catch(e=>(0,tf.OO)(this.view.state,e,"completion info")):this.addInfoPane(n,i)}}addInfoPane(e,t){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",null!=e.nodeType)i.appendChild(e),this.infoDestroy=null;else{let{dom:t,destroy:r}=e;i.appendChild(t),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(e){var t,i;let r,n,s,o=null;for(let t=this.list.firstChild,i=this.range.from;t;t=t.nextSibling,i++)"LI"==t.nodeName&&t.id?i==e?t.hasAttribute("aria-selected")||(t.setAttribute("aria-selected","true"),o=t):t.hasAttribute("aria-selected")&&t.removeAttribute("aria-selected"):i--;return o&&(t=this.list,i=o,r=t.getBoundingClientRect(),n=i.getBoundingClientRect(),s=r.height/t.offsetHeight,n.top<r.top?t.scrollTop-=(r.top-n.top)/s:n.bottom>r.bottom&&(t.scrollTop+=(n.bottom-r.bottom)/s)),o}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),r=e.getBoundingClientRect(),n=this.space;if(!n){let e=this.dom.ownerDocument.documentElement;n={left:0,top:0,right:e.clientWidth,bottom:e.clientHeight}}return r.top>Math.min(n.bottom,t.bottom)-10||r.bottom<Math.max(n.top,t.top)+10?null:this.view.state.facet(iw).positionInfo(this.view,t,r,i,n,this.dom)}placeInfo(e){this.info&&(e?(e.style&&(this.info.style.cssText=e.style),this.info.className="cm-tooltip cm-completionInfo "+(e.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(e,t,i){let r=document.createElement("ul");r.id=t,r.setAttribute("role","listbox"),r.setAttribute("aria-expanded","true"),r.setAttribute("aria-label",this.view.state.phrase("Completions")),r.addEventListener("mousedown",e=>{e.target==r&&e.preventDefault()});let n=null;for(let s=i.from;s<i.to;s++){let{completion:o,match:a}=e[s],{section:l}=o;if(l){let e="string"==typeof l?l:l.name;e!=n&&(s>i.from||0==i.from)&&(n=e,"string"!=typeof l&&l.header?r.appendChild(l.header(l)):r.appendChild(document.createElement("completion-section")).textContent=e)}let h=r.appendChild(document.createElement("li"));h.id=t+"-"+s,h.setAttribute("role","option");let c=this.optionClass(o);for(let e of(c&&(h.className=c),this.optionContent)){let t=e(o,this.view.state,this.view,a);t&&h.appendChild(t)}}return i.from&&r.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&r.classList.add("cm-completionListIncompleteBottom"),r}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function i_(e){return 100*(e.boost||0)+(e.apply?10:0)+(e.info?5:0)+(e.type?1:0)}class iX{constructor(e,t,i,r,n,s){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=r,this.selected=n,this.disabled=s}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new iX(this.options,iq(t,e),this.tooltip,this.timestamp,e,this.disabled)}static build(e,t,i,r,n,s){if(r&&!s&&e.some(e=>e.isPending))return r.setDisabled();let o=function(e,t){let i=[],r=null,n=e=>{i.push(e);let{section:t}=e.completion;if(t){r||(r=[]);let e="string"==typeof t?t:t.name;r.some(t=>t.name==e)||r.push("string"==typeof t?{name:e}:t)}},s=t.facet(iw);for(let r of e)if(r.hasResult()){let e=r.result.getMatch;if(!1===r.result.filter)for(let t of r.result.options)n(new id(t,r.source,e?e(t):[],1e9-i.length));else{let i=t.sliceDoc(r.from,r.to),o,a=s.filterStrict?new iS(i):new iv(i);for(let t of r.result.options)if(o=a.match(t.label)){let i=t.displayLabel?e?e(t,o.matched):[]:o.matched;n(new id(t,r.source,i,o.score+(t.boost||0)))}}}if(r){let e=Object.create(null),t=0;for(let i of r.sort((e,t)=>{var i,r;return(null!==(i=e.rank)&&void 0!==i?i:1e9)-(null!==(r=t.rank)&&void 0!==r?r:1e9)||(e.name<t.name?-1:1)}))t-=1e5,e[i.name]=t;for(let t of i){let{section:i}=t.completion;i&&(t.score+=e["string"==typeof i?i:i.name])}}let o=[],a=null,l=s.compareCompletions;for(let e of i.sort((e,t)=>t.score-e.score||l(e.completion,t.completion))){let t=e.completion;a&&a.label==t.label&&a.detail==t.detail&&(null==a.type||null==t.type||a.type==t.type)&&a.apply==t.apply&&a.boost==t.boost?i_(e.completion)>i_(a)&&(o[o.length-1]=e):o.push(e),a=e.completion}return o}(e,t);if(!o.length)return r&&e.some(e=>e.isPending)?r.setDisabled():null;let a=t.facet(iw).selectOnOpen?0:-1;if(r&&r.selected!=a&&-1!=r.selected){let e=r.options[r.selected].completion;for(let t=0;t<o.length;t++)if(o[t].completion==e){a=t;break}}return new iX(o,iq(i,a),{pos:e.reduce((e,t)=>t.hasResult()?Math.min(e,t.from):e,1e8),create:iV,above:n.aboveCursor},r?r.timestamp:Date.now(),a,!1)}map(e){return new iX(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new iX(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class iC{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new iC(iM,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(e){let{state:t}=e,i=t.facet(iw),r=(i.override||t.languageDataAt("autocomplete",ig(t)).map(ik)).map(t=>(this.active.find(e=>e.source==t)||new ij(t,this.active.some(e=>0!=e.state)?1:0)).update(e,i));r.length==this.active.length&&r.every((e,t)=>e==this.active[t])&&(r=this.active);let n=this.open,s=e.effects.some(e=>e.is(iN));for(let o of(n&&e.docChanged&&(n=n.map(e.changes)),e.selection||r.some(t=>t.hasResult()&&e.changes.touchesRange(t.from,t.to))||!function(e,t){if(e==t)return!0;for(let i=0,r=0;;){for(;i<e.length&&!e[i].hasResult();)i++;for(;r<t.length&&!t[r].hasResult();)r++;let n=i==e.length,s=r==t.length;if(n||s)return n==s;if(e[i++].result!=t[r++].result)return!1}}(r,this.active)||s?n=iX.build(r,t,this.id,n,i,s):n&&n.disabled&&!r.some(e=>e.isPending)&&(n=null),!n&&r.every(e=>!e.isPending)&&r.some(e=>e.hasResult())&&(r=r.map(e=>e.hasResult()?new ij(e.source,0):e)),e.effects))o.is(iI)&&(n=n&&n.setSelected(o.value,this.id));return r==this.active&&n==this.open?this:new iC(r,this.id,n)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?iA:iR}}let iA={"aria-autocomplete":"list"},iR={};function iq(e,t){let i={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":e};return t>-1&&(i["aria-activedescendant"]=e+"-"+t),i}let iM=[];class ij{constructor(e,t,i=!1){this.source=e,this.state=t,this.explicit=i}hasResult(){return!1}get isPending(){return 1==this.state}update(e,t){let i=function(e,t){if(e.isUserEvent("input.complete")){let i=e.annotation(iy);if(i&&t.activateOnCompletion(i))return 12}let i=e.isUserEvent("input.type");return i&&t.activateOnTyping?5:i?1:e.isUserEvent("delete.backward")?2:e.selection?8:e.docChanged?16:0}(e,t),r=this;for(let t of((8&i||16&i&&this.touches(e))&&(r=new ij(r.source,0)),4&i&&0==r.state&&(r=new ij(this.source,1)),r=r.updateFor(e,i),e.effects))if(t.is(iQ))r=new ij(r.source,1,t.value);else if(t.is(ib))r=new ij(r.source,0);else if(t.is(iN))for(let e of t.value)e.source==r.source&&(r=e);return r}updateFor(e,t){return this.map(e.changes)}map(e){return this}touches(e){return e.changes.touchesRange(ig(e.state))}}class iY extends ij{constructor(e,t,i,r,n,s){super(e,3,t),this.limit=i,this.result=r,this.from=n,this.to=s}hasResult(){return!0}updateFor(e,t){var i;if(!(3&t))return this.map(e.changes);let r=this.result;r.map&&!e.changes.empty&&(r=r.map(r,e.changes));let n=e.changes.mapPos(this.from),s=e.changes.mapPos(this.to,1),o=ig(e.state);if(o>s||!r||2&t&&(ig(e.startState)==this.from||o<this.limit))return new ij(this.source,4&t?1:0);let a=e.changes.mapPos(this.limit);return!function(e,t,i,r){if(!e)return!1;let n=t.sliceDoc(i,r);return"function"==typeof e?e(n,i,r,t):im(e,!0).test(n)}(r.validFor,e.state,n,s)?r.update&&(r=r.update(r,n,s,new iu(e.state,o,!1)))?new iY(this.source,this.explicit,a,r,r.from,null!==(i=r.to)&&void 0!==i?i:ig(e.state)):new ij(this.source,1,this.explicit):new iY(this.source,this.explicit,a,r,n,s)}map(e){return e.empty?this:(this.result.map?this.result.map(this.result,e):this.result)?new iY(this.source,this.explicit,e.mapPos(this.limit),this.result,e.mapPos(this.from),e.mapPos(this.to,1)):new ij(this.source,0)}touches(e){return e.changes.touchesRange(this.from,this.to)}}let iN=tc.Py.define({map:(e,t)=>e.map(e=>e.map(t))}),iI=tc.Py.define(),iE=tc.QQ.define({create:()=>iC.start(),update:(e,t)=>e.update(t),provide:e=>[tf.hJ.from(e,e=>e.tooltip),tf.tk.contentAttributes.from(e,e=>e.attrs)]});function iz(e,t){let i=t.completion.apply||t.completion.label,r=e.state.field(iE).active.find(e=>e.source==t.source);return r instanceof iY&&("string"==typeof i?e.dispatch(Object.assign(Object.assign({},function(e,t,i,r){let{main:n}=e.selection,s=i-n.from,o=r-n.from;return Object.assign(Object.assign({},e.changeByRange(a=>{if(a!=n&&i!=r&&e.sliceDoc(a.from+s,a.from+o)!=e.sliceDoc(i,r))return{range:a};let l=e.toText(t);return{changes:{from:a.from+s,to:r==n.from?a.to:a.from+o,insert:l},range:tc.jT.cursor(a.from+s+l.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}(e.state,i,r.from,r.to)),{annotations:iy.of(t.completion)})):i(e,t.completion,r.from,r.to),!0)}let iV=e=>new iT(e,iE,iz);"object"==typeof navigator&&navigator.platform;let iL=tf.tk.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"\xb7\xb7\xb7"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'\uD835\uDC65'"}},".cm-completionIcon-constant":{"&:after":{content:"'\uD835\uDC36'"}},".cm-completionIcon-type":{"&:after":{content:"'\uD835\uDC61'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'\uD83D\uDD11︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class iW{constructor(e,t,i,r){this.field=e,this.line=t,this.from=i,this.to=r}}class iG{constructor(e,t,i){this.field=e,this.from=t,this.to=i}map(e){let t=e.mapPos(this.from,-1,tc.gc.TrackDel),i=e.mapPos(this.to,1,tc.gc.TrackDel);return null==t||null==i?null:new iG(this.field,t,i)}}class iB{constructor(e,t){this.lines=e,this.fieldPositions=t}instantiate(e,t){let i=[],r=[t],n=e.doc.lineAt(t),s=/^\s*/.exec(n.text)[0];for(let n of this.lines){if(i.length){let i=s,o=/^\t*/.exec(n)[0].length;for(let t=0;t<o;t++)i+=e.facet(tH);r.push(t+i.length-o),n=i+n.slice(o)}i.push(n),t+=n.length+1}return{text:i,ranges:this.fieldPositions.map(e=>new iG(e.field,r[e.line]+e.from,r[e.line]+e.to))}}static parse(e){let t=[],i=[],r=[],n;for(let s of e.split(/\r\n?|\n/)){for(;n=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(s);){let e=n[1]?+n[1]:null,o=n[2]||n[3]||"",a=-1,l=o.replace(/\\[{}]/g,e=>e[1]);for(let i=0;i<t.length;i++)(null!=e?t[i].seq==e:l&&t[i].name==l)&&(a=i);if(a<0){let i=0;for(;i<t.length&&(null==e||null!=t[i].seq&&t[i].seq<e);)i++;for(let n of(t.splice(i,0,{seq:e,name:l}),a=i,r))n.field>=a&&n.field++}r.push(new iW(a,i.length,n.index,n.index+l.length)),s=s.slice(0,n.index)+o+s.slice(n.index+n[0].length)}s=s.replace(/\\([{}])/g,(e,t,n)=>{for(let e of r)e.line==i.length&&e.from>n&&(e.from--,e.to--);return t}),i.push(s)}return new iB(i,r)}}let iU=tf.p.widget({widget:new class extends tf.l9{toDOM(){let e=document.createElement("span");return e.className="cm-snippetFieldPosition",e}ignoreEvent(){return!1}}}),iD=tf.p.mark({class:"cm-snippetField"});class iJ{constructor(e,t){this.ranges=e,this.active=t,this.deco=tf.p.set(e.map(e=>(e.from==e.to?iU:iD).range(e.from,e.to)))}map(e){let t=[];for(let i of this.ranges){let r=i.map(e);if(!r)return null;t.push(r)}return new iJ(t,this.active)}selectionInsideField(e){return e.ranges.every(e=>this.ranges.some(t=>t.field==this.active&&t.from<=e.from&&t.to>=e.to))}}let iF=tc.Py.define({map:(e,t)=>e&&e.map(t)}),iK=tc.Py.define(),iH=tc.QQ.define({create:()=>null,update(e,t){for(let i of t.effects){if(i.is(iF))return i.value;if(i.is(iK)&&e)return new iJ(e.ranges,i.value)}return e&&t.docChanged&&(e=e.map(t.changes)),e&&t.selection&&!e.selectionInsideField(t.selection)&&(e=null),e},provide:e=>tf.tk.decorations.from(e,e=>e?e.deco:tf.p.none)});function i0(e,t){return tc.jT.create(e.filter(e=>e.field==t).map(e=>tc.jT.range(e.from,e.to)))}function i1(e){return({state:t,dispatch:i})=>{let r=t.field(iH,!1);if(!r||e<0&&0==r.active)return!1;let n=r.active+e,s=e>0&&!r.ranges.some(t=>t.field==n+e);return i(t.update({selection:i0(r.ranges,n),effects:iF.of(s?null:new iJ(r.ranges,n)),scrollIntoView:!0})),!0}}let i2=i1(1),i5=i1(-1),i4=[{key:"Tab",run:i2,shift:i5},{key:"Escape",run:({state:e,dispatch:t})=>!!e.field(iH,!1)&&(t(e.update({effects:iF.of(null)})),!0)}],i3=tc.r$.define({combine:e=>e.length?e[0]:i4}),i7=tc.Wl.highest(tf.$f.compute([i3],e=>e.facet(i3)));function i9(e,t){let i;return Object.assign(Object.assign({},t),{apply:(i=iB.parse(e),(e,t,r,n)=>{let{text:s,ranges:o}=i.instantiate(e.state,r),{main:a}=e.state.selection,l={changes:{from:r,to:n==a.from?a.to:n,insert:tc.xv.of(s)},scrollIntoView:!0,annotations:t?[iy.of(t),tc.YW.userEvent.of("input.complete")]:void 0};if(o.length&&(l.selection=i0(o,0)),o.some(e=>e.field>0)){let t=new iJ(o,0),i=l.effects=[iF.of(t)];void 0===e.state.field(iH,!1)&&i.push(tc.Py.appendConfig.of([iH,i7,i6,iL]))}e.dispatch(e.state.update(l))})})}let i6=tf.tk.domEventHandlers({mousedown(e,t){let i=t.state.field(iH,!1),r;if(!i||null==(r=t.posAtCoords({x:e.clientX,y:e.clientY})))return!1;let n=i.ranges.find(e=>e.from<=r&&e.to>=r);return!!n&&n.field!=i.active&&(t.dispatch({selection:i0(i.ranges,n.field),effects:iF.of(i.ranges.some(e=>e.field>n.field)?new iJ(i.ranges,n.field):null),scrollIntoView:!0}),!0)}}),i8=tc.Py.define({map(e,t){let i=t.mapPos(e,-1,tc.gc.TrackAfter);return null==i?void 0:i}}),re=new class extends tc.uU{};re.startSide=1,re.endSide=-1,"object"==typeof navigator&&navigator.userAgent;let rt=[i9("function ${name}(${params}) {\n	${}\n}",{label:"function",detail:"definition",type:"keyword"}),i9("for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\n	${}\n}",{label:"for",detail:"loop",type:"keyword"}),i9("for (let ${name} of ${collection}) {\n	${}\n}",{label:"for",detail:"of loop",type:"keyword"}),i9("do {\n	${}\n} while (${})",{label:"do",detail:"loop",type:"keyword"}),i9("while (${}) {\n	${}\n}",{label:"while",detail:"loop",type:"keyword"}),i9("try {\n	${}\n} catch (${error}) {\n	${}\n}",{label:"try",detail:"/ catch block",type:"keyword"}),i9("if (${}) {\n	${}\n}",{label:"if",detail:"block",type:"keyword"}),i9("if (${}) {\n	${}\n} else {\n	${}\n}",{label:"if",detail:"/ else block",type:"keyword"}),i9("class ${name} {\n	constructor(${params}) {\n		${}\n	}\n}",{label:"class",detail:"definition",type:"keyword"}),i9('import {${names}} from "${module}"\n${}',{label:"import",detail:"named",type:"keyword"}),i9('import ${name} from "${module}"\n${}',{label:"import",detail:"default",type:"keyword"})],ri=rt.concat([i9("interface ${name} {\n	${}\n}",{label:"interface",detail:"definition",type:"keyword"}),i9("type ${name} = ${type}",{label:"type",detail:"definition",type:"keyword"}),i9("enum ${name} {\n	${}\n}",{label:"enum",detail:"definition",type:"keyword"})]),rr=new class{constructor(){this.map=new WeakMap}setBuffer(e,t,i){let r=this.map.get(e);r||this.map.set(e,r=new Map),r.set(t,i)}getBuffer(e,t){let i=this.map.get(e);return i&&i.get(t)}set(e,t){e instanceof e8?this.setBuffer(e.context.buffer,e.index,t):e instanceof e3&&this.map.set(e.tree,t)}get(e){return e instanceof e8?this.getBuffer(e.context.buffer,e.index):e instanceof e3?this.map.get(e.tree):void 0}cursorSet(e,t){e.buffer?this.setBuffer(e.buffer.buffer,e.index,t):this.map.set(e.tree,t)}cursorGet(e){return e.buffer?this.getBuffer(e.buffer.buffer,e.index):this.map.get(e.tree)}},rn=new Set(["Script","Block","FunctionExpression","FunctionDeclaration","ArrowFunction","MethodDeclaration","ForStatement"]);function rs(e){return(t,i)=>{let r=t.node.getChild("VariableDefinition");return r&&i(r,e),!0}}let ro=["FunctionDeclaration"],ra={FunctionDeclaration:rs("function"),ClassDeclaration:rs("class"),ClassExpression:()=>!0,EnumDeclaration:rs("constant"),TypeAliasDeclaration:rs("type"),NamespaceDeclaration:rs("namespace"),VariableDefinition(e,t){e.matchContext(ro)||t(e,"variable")},TypeDefinition(e,t){t(e,"type")},__proto__:null},rl=/^[\w$\xa1-\uffff][\w$\d\xa1-\uffff]*$/,rh=["TemplateString","String","RegExp","LineComment","BlockComment","VariableDefinition","TypeDefinition","Label","PropertyDefinition","PropertyName","PrivatePropertyDefinition","PrivatePropertyName","JSXText","JSXAttributeValue","JSXOpenTag","JSXCloseTag","JSXSelfClosingTag",".","?."];function rc(e){let t=tz(e.state).resolveInner(e.pos,-1);if(rh.indexOf(t.name)>-1)return null;let i="VariableName"==t.name||t.to-t.from<20&&rl.test(e.state.sliceDoc(t.from,t.to));if(!i&&!e.explicit)return null;let r=[];for(let i=t;i;i=i.parent)rn.has(i.name)&&(r=r.concat(function e(t,i){let r=rr.get(i);if(r)return r;let n=[],s=!0;function o(e,i){let r=t.sliceString(e.from,e.to);n.push({label:r,type:i})}return i.cursor(q.IncludeAnonymous).iterate(i=>{if(s)s=!1;else if(i.name){let e=ra[i.name];if(e&&e(i,o)||rn.has(i.name))return!1}else if(i.to-i.from>8192){for(let r of e(t,i.node))n.push(r);return!1}}),rr.set(i,n),n}(e.state.doc,i)));return{options:r,from:i?t.from:e.pos,validFor:rl}}let rf=tE.define({name:"javascript",parser:eV.configure({props:[t0.add({IfStatement:t1({except:/^\s*({|else\b)/}),TryStatement:t1({except:/^\s*({|catch\b|finally\b)/}),LabeledStatement:e=>e.baseIndent,SwitchBody:e=>{let t=e.textAfter,i=/^\s*\}/.test(t),r=/^\s*(case|default)\b/.test(t);return e.baseIndent+(i?0:r?1:2)*e.unit},Block:function({closing:e,align:t=!0,units:i=1}){return r=>{let n,s,o,a;return s=(n=r.textAfter).match(/^\s*/)[0].length,o=e&&n.slice(s,s+e.length)==e||void 0==r.pos+s,(a=t?function(e){let t=e.node,i=t.childAfter(t.from),r=t.lastChild;if(!i)return null;let n=e.options.simulateBreak,s=e.state.doc.lineAt(i.from),o=null==n||n<=s.from?s.to:Math.min(s.to,n);for(let e=i.to;;){let n=t.childAfter(e);if(!n||n==r)return null;if(!n.type.isSkipped){if(n.from>=o)return null;let e=/^ */.exec(s.text.slice(i.to-s.from))[0].length;return{from:i.from,to:i.to+e}}e=n.to}}(r):null)?o?r.column(a.from):r.column(a.to):r.baseIndent+(o?0:r.unit*i)}}({closing:"}"}),ArrowFunction:e=>e.baseIndent+e.unit,"TemplateString BlockComment":()=>null,"Statement Property":t1({except:/^\s*{/}),JSXElement(e){let t=/^\s*<\//.test(e.textAfter);return e.lineIndent(e.node.from)+(t?0:e.unit)},JSXEscape(e){let t=/\s*\}/.test(e.textAfter);return e.lineIndent(e.node.from)+(t?0:e.unit)},"JSXOpenTag JSXSelfClosingTag":e=>e.column(e.node.from)+e.unit}),t2.add({"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType":function(e){let t=e.firstChild,i=e.lastChild;return t&&t.to<i.from?{from:t.to,to:i.type.isError?e.to:i.from}:null},BlockComment:e=>({from:e.from+2,to:e.to-2})})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"`"]},commentTokens:{line:"//",block:{open:"/*",close:"*/"}},indentOnInput:/^\s*(?:case |default:|\{|\}|<\/)$/,wordChars:"$"}}),ru={test:e=>/^JSX/.test(e.name),facet:tj({commentTokens:{block:{open:"{/*",close:"*/}"}}})},rp=rf.configure({dialect:"ts"},"typescript"),rO=rf.configure({dialect:"jsx",props:[tY.add(e=>e.isTop?[ru]:void 0)]}),rd=rf.configure({dialect:"jsx ts",props:[tY.add(e=>e.isTop?[ru]:void 0)]},"typescript"),rg=e=>({label:e,type:"keyword"}),rm="break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield".split(" ").map(rg),ry=rm.concat(["declare","implements","private","protected","public"].map(rg));function rx(e={}){var t;let i=e.jsx?e.typescript?rd:rO:e.typescript?rp:rf,r=e.typescript?ri.concat(ry):rt.concat(rm);return new tK(i,[rf.data.of({autocomplete:(t=iO(r),e=>{for(let t=tz(e.state).resolveInner(e.pos,-1);t;t=t.parent){if(rh.indexOf(t.name)>-1)return null;if(t.type.isTop)break}return t(e)})}),rf.data.of({autocomplete:rc}),e.jsx?rb:[]])}function rk(e,t,i=e.length){for(let r=null==t?void 0:t.firstChild;r;r=r.nextSibling)if("JSXIdentifier"==r.name||"JSXBuiltin"==r.name||"JSXNamespacedName"==r.name||"JSXMemberExpression"==r.name)return e.sliceString(r.from,Math.min(r.to,i));return""}let rQ="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),rb=tf.tk.inputHandler.of((e,t,i,r,n)=>{if((rQ?e.composing:e.compositionStarted)||e.state.readOnly||t!=i||">"!=r&&"/"!=r||!rf.isActiveAt(e.state,t,-1))return!1;let s=n(),{state:o}=s,a=o.changeByRange(e=>{var t;let{head:i}=e,n=tz(o).resolveInner(i-1,-1),s;if("JSXStartTag"==n.name&&(n=n.parent),o.doc.sliceString(i-1,i)!=r||"JSXAttributeValue"==n.name&&n.to>i);else if(">"==r&&"JSXFragmentTag"==n.name)return{range:e,changes:{from:i,insert:"</>"}};else if("/"==r&&"JSXStartCloseTag"==n.name){let e=n.parent,r=e.parent;if(r&&e.from==i-2&&((s=rk(o.doc,r.firstChild,i))||(null===(t=r.firstChild)||void 0===t?void 0:t.name)=="JSXFragmentTag")){let e=`${s}>`;return{range:tc.jT.cursor(i+e.length,-1),changes:{from:i,insert:e}}}}else if(">"==r){let t=function(e){for(;;){if("JSXOpenTag"==e.name||"JSXSelfClosingTag"==e.name||"JSXFragmentTag"==e.name)return e;if("JSXEscape"==e.name||!e.parent)return null;e=e.parent}}(n);if(t&&"JSXOpenTag"==t.name&&!/^\/?>|^<\//.test(o.doc.sliceString(i,i+2))&&(s=rk(o.doc,t,i)))return{range:e,changes:{from:i,insert:`</${s}>`}}}return{range:e}});return!a.changes.empty&&(e.dispatch([s,o.update(a,{userEvent:"input.complete",scrollIntoView:!0})]),!0)})},7474:function(e,t,i){let r;i.d(t,{q6:function(){return ec},n0:function(){return T},as:function(){return _},D0:function(){return ex},jT:function(){return Y},yy:function(){return eQ},r$:function(){return E},gc:function(){return Z},Wl:function(){return J},Xs:function(){return eP},f_:function(){return eZ},uU:function(){return ev},Py:function(){return ep},QQ:function(){return B},xv:function(){return u},YW:function(){return eO},gm:function(){return b},nZ:function(){return S},BO:function(){return eb},IS:function(){return eN},cp:function(){return Q},Gz:function(){return eI},bg:function(){return v}});let n=[],s=[];function o(e){return e>=127462&&e<=127487}function a(e,t,i){if(t==e.length)return t;t&&h(e.charCodeAt(t))&&c(e.charCodeAt(t-1))&&t--;let r=l(e,t);for(t+=f(r);t<e.length;){let a=l(e,t);if(8205==r||8205==a||i&&function(e){if(e<768)return!1;for(let t=0,i=n.length;;){let r=t+i>>1;if(e<n[r])i=r;else{if(!(e>=s[r]))return!0;t=r+1}if(t==i)return!1}}(a))t+=f(a),r=a;else if(o(a)){let i=0,r=t-2;for(;r>=0&&o(l(e,r));)i++,r-=2;if(i%2==0)break;t+=2}else break}return t}function l(e,t){let i=e.charCodeAt(t);if(!c(i)||t+1==e.length)return i;let r=e.charCodeAt(t+1);return h(r)?(i-55296<<10)+(r-56320)+65536:i}function h(e){return e>=56320&&e<57344}function c(e){return e>=55296&&e<56320}function f(e){return e<65536?1:2}(()=>{let e="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(e=>e?parseInt(e,36):1);for(let t=0,i=0;t<e.length;t++)(t%2?s:n).push(i+=e[t])})();class u{lineAt(e){if(e<0||e>this.length)throw RangeError(`Invalid position ${e} in document of length ${this.length}`);return this.lineInner(e,!1,1,0)}line(e){if(e<1||e>this.lines)throw RangeError(`Invalid line number ${e} in ${this.lines}-line document`);return this.lineInner(e,!0,1,0)}replace(e,t,i){[e,t]=k(this,e,t);let r=[];return this.decompose(0,e,r,2),i.length&&i.decompose(0,i.length,r,3),this.decompose(t,this.length,r,1),O.from(r,this.length-(t-e)+i.length)}append(e){return this.replace(this.length,this.length,e)}slice(e,t=this.length){[e,t]=k(this,e,t);let i=[];return this.decompose(e,t,i,0),O.from(i,t-e)}eq(e){if(e==this)return!0;if(e.length!=this.length||e.lines!=this.lines)return!1;let t=this.scanIdentical(e,1),i=this.length-this.scanIdentical(e,-1),r=new g(this),n=new g(e);for(let e=t,s=t;;){if(r.next(e),n.next(e),e=0,r.lineBreak!=n.lineBreak||r.done!=n.done||r.value!=n.value)return!1;if(s+=r.value.length,r.done||s>=i)return!0}}iter(e=1){return new g(this,e)}iterRange(e,t=this.length){return new m(this,e,t)}iterLines(e,t){let i;if(null==e)i=this.iter();else{null==t&&(t=this.lines+1);let r=this.line(e).from;i=this.iterRange(r,Math.max(r,t==this.lines+1?this.length:t<=1?0:this.line(t-1).to))}return new y(i)}toString(){return this.sliceString(0)}toJSON(){let e=[];return this.flatten(e),e}constructor(){}static of(e){if(0==e.length)throw RangeError("A document must have at least one line");return 1!=e.length||e[0]?e.length<=32?new p(e):O.from(p.split(e,[])):u.empty}}class p extends u{constructor(e,t=function(e){let t=-1;for(let i of e)t+=i.length+1;return t}(e)){super(),this.text=e,this.length=t}get lines(){return this.text.length}get children(){return null}lineInner(e,t,i,r){for(let n=0;;n++){let s=this.text[n],o=r+s.length;if((t?i:o)>=e)return new x(r,o,i,s);r=o+1,i++}}decompose(e,t,i,r){let n=e<=0&&t>=this.length?this:new p(d(this.text,[""],e,t),Math.min(t,this.length)-Math.max(0,e));if(1&r){let e=i.pop(),t=d(n.text,e.text.slice(),0,n.length);if(t.length<=32)i.push(new p(t,e.length+n.length));else{let e=t.length>>1;i.push(new p(t.slice(0,e)),new p(t.slice(e)))}}else i.push(n)}replace(e,t,i){var r;if(!(i instanceof p))return super.replace(e,t,i);[e,t]=k(this,e,t);let n=d(this.text,d(i.text,d(this.text,[""],0,e)),t),s=this.length+i.length-(t-e);return n.length<=32?new p(n,s):O.from(p.split(n,[]),s)}sliceString(e,t=this.length,i="\n"){[e,t]=k(this,e,t);let r="";for(let n=0,s=0;n<=t&&s<this.text.length;s++){let o=this.text[s],a=n+o.length;n>e&&s&&(r+=i),e<a&&t>n&&(r+=o.slice(Math.max(0,e-n),t-n)),n=a+1}return r}flatten(e){for(let t of this.text)e.push(t)}scanIdentical(){return 0}static split(e,t){let i=[],r=-1;for(let n of e)i.push(n),r+=n.length+1,32==i.length&&(t.push(new p(i,r)),i=[],r=-1);return r>-1&&t.push(new p(i,r)),t}}class O extends u{constructor(e,t){for(let i of(super(),this.children=e,this.length=t,this.lines=0,e))this.lines+=i.lines}lineInner(e,t,i,r){for(let n=0;;n++){let s=this.children[n],o=r+s.length,a=i+s.lines-1;if((t?a:o)>=e)return s.lineInner(e,t,i,r);r=o+1,i=a+1}}decompose(e,t,i,r){for(let n=0,s=0;s<=t&&n<this.children.length;n++){let o=this.children[n],a=s+o.length;if(e<=a&&t>=s){let n=r&((s<=e?1:0)|(a>=t?2:0));s>=e&&a<=t&&!n?i.push(o):o.decompose(e-s,t-s,i,n)}s=a+1}}replace(e,t,i){if([e,t]=k(this,e,t),i.lines<this.lines)for(let r=0,n=0;r<this.children.length;r++){let s=this.children[r],o=n+s.length;if(e>=n&&t<=o){let a=s.replace(e-n,t-n,i),l=this.lines-s.lines+a.lines;if(a.lines<l>>4&&a.lines>l>>6){let n=this.children.slice();return n[r]=a,new O(n,this.length-(t-e)+i.length)}return super.replace(n,o,a)}n=o+1}return super.replace(e,t,i)}sliceString(e,t=this.length,i="\n"){[e,t]=k(this,e,t);let r="";for(let n=0,s=0;n<this.children.length&&s<=t;n++){let o=this.children[n],a=s+o.length;s>e&&n&&(r+=i),e<a&&t>s&&(r+=o.sliceString(e-s,t-s,i)),s=a+1}return r}flatten(e){for(let t of this.children)t.flatten(e)}scanIdentical(e,t){if(!(e instanceof O))return 0;let i=0,[r,n,s,o]=t>0?[0,0,this.children.length,e.children.length]:[this.children.length-1,e.children.length-1,-1,-1];for(;;r+=t,n+=t){if(r==s||n==o)return i;let a=this.children[r],l=e.children[n];if(a!=l)return i+a.scanIdentical(l,t);i+=a.length+1}}static from(e,t=e.reduce((e,t)=>e+t.length+1,-1)){let i=0;for(let t of e)i+=t.lines;if(i<32){let i=[];for(let t of e)t.flatten(i);return new p(i,t)}let r=Math.max(32,i>>5),n=r<<1,s=r>>1,o=[],a=0,l=-1,h=[];function c(){0!=a&&(o.push(1==h.length?h[0]:O.from(h,l)),l=-1,a=h.length=0)}for(let t of e)!function e(t){let i;if(t.lines>n&&t instanceof O)for(let i of t.children)e(i);else t.lines>s&&(a>s||!a)?(c(),o.push(t)):t instanceof p&&a&&(i=h[h.length-1])instanceof p&&t.lines+i.lines<=32?(a+=t.lines,l+=t.length+1,h[h.length-1]=new p(i.text.concat(t.text),i.length+1+t.length)):(a+t.lines>r&&c(),a+=t.lines,l+=t.length+1,h.push(t))}(t);return c(),1==o.length?o[0]:new O(o,t)}}function d(e,t,i=0,r=1e9){for(let n=0,s=0,o=!0;s<e.length&&n<=r;s++){let a=e[s],l=n+a.length;l>=i&&(l>r&&(a=a.slice(0,r-n)),n<i&&(a=a.slice(i-n)),o?(t[t.length-1]+=a,o=!1):t.push(a)),n=l+1}return t}u.empty=new p([""],0);class g{constructor(e,t=1){this.dir=t,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[e],this.offsets=[t>0?1:(e instanceof p?e.text.length:e.children.length)<<1]}nextInner(e,t){for(this.done=this.lineBreak=!1;;){let i=this.nodes.length-1,r=this.nodes[i],n=this.offsets[i],s=n>>1,o=r instanceof p?r.text.length:r.children.length;if(s==(t>0?o:0)){if(0==i)return this.done=!0,this.value="",this;t>0&&this.offsets[i-1]++,this.nodes.pop(),this.offsets.pop()}else if((1&n)==(t>0?0:1)){if(this.offsets[i]+=t,0==e)return this.lineBreak=!0,this.value="\n",this;e--}else if(r instanceof p){let n=r.text[s+(t<0?-1:0)];if(this.offsets[i]+=t,n.length>Math.max(0,e))return this.value=0==e?n:t>0?n.slice(e):n.slice(0,n.length-e),this;e-=n.length}else{let n=r.children[s+(t<0?-1:0)];e>n.length?(e-=n.length,this.offsets[i]+=t):(t<0&&this.offsets[i]--,this.nodes.push(n),this.offsets.push(t>0?1:(n instanceof p?n.text.length:n.children.length)<<1))}}}next(e=0){return e<0&&(this.nextInner(-e,-this.dir),e=this.value.length),this.nextInner(e,this.dir)}}class m{constructor(e,t,i){this.value="",this.done=!1,this.cursor=new g(e,t>i?-1:1),this.pos=t>i?e.length:0,this.from=Math.min(t,i),this.to=Math.max(t,i)}nextInner(e,t){if(t<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;e+=Math.max(0,t<0?this.pos-this.to:this.from-this.pos);let i=t<0?this.pos-this.from:this.to-this.pos;e>i&&(e=i),i-=e;let{value:r}=this.cursor.next(e);return this.pos+=(r.length+e)*t,this.value=r.length<=i?r:t<0?r.slice(r.length-i):r.slice(0,i),this.done=!this.value,this}next(e=0){return e<0?e=Math.max(e,this.from-this.pos):e>0&&(e=Math.min(e,this.to-this.pos)),this.nextInner(e,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&""!=this.value}}class y{constructor(e){this.inner=e,this.afterBreak=!0,this.value="",this.done=!1}next(e=0){let{done:t,lineBreak:i,value:r}=this.inner.next(e);return t&&this.afterBreak?(this.value="",this.afterBreak=!1):t?(this.done=!0,this.value=""):i?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=r,this.afterBreak=!1),this}get lineBreak(){return!1}}"undefined"!=typeof Symbol&&(u.prototype[Symbol.iterator]=function(){return this.iter()},g.prototype[Symbol.iterator]=m.prototype[Symbol.iterator]=y.prototype[Symbol.iterator]=function(){return this});class x{constructor(e,t,i,r){this.from=e,this.to=t,this.number=i,this.text=r}get length(){return this.to-this.from}}function k(e,t,i){return[t=Math.max(0,Math.min(e.length,t)),Math.max(t,Math.min(e.length,i))]}function Q(e,t,i=!0,r=!0){return function(e,t,i=!0,r=!0){return(i?a:function(e,t,i){for(;t>0;){let r=a(e,t-2,i);if(r<t)return r;t--}return 0})(e,t,r)}(e,t,i,r)}function b(e,t){let i=e.charCodeAt(t);if(!(i>=55296&&i<56320)||t+1==e.length)return i;let r=e.charCodeAt(t+1);return r>=56320&&r<57344?(i-55296<<10)+(r-56320)+65536:i}function v(e){return e<=65535?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,(1023&e)+56320)}function S(e){return e<65536?1:2}let w=/\r\n?|\n/;var $,P,Z=(($=Z||(Z={}))[$.Simple=0]="Simple",$[$.TrackDel=1]="TrackDel",$[$.TrackBefore=2]="TrackBefore",$[$.TrackAfter=3]="TrackAfter",$);class T{constructor(e){this.sections=e}get length(){let e=0;for(let t=0;t<this.sections.length;t+=2)e+=this.sections[t];return e}get newLength(){let e=0;for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t+1];e+=i<0?this.sections[t]:i}return e}get empty(){return 0==this.sections.length||2==this.sections.length&&this.sections[1]<0}iterGaps(e){for(let t=0,i=0,r=0;t<this.sections.length;){let n=this.sections[t++],s=this.sections[t++];s<0?(e(i,r,n),r+=n):r+=s,i+=n}}iterChangedRanges(e,t=!1){A(this,e,t)}get invertedDesc(){let e=[];for(let t=0;t<this.sections.length;){let i=this.sections[t++],r=this.sections[t++];r<0?e.push(i,r):e.push(r,i)}return new T(e)}composeDesc(e){return this.empty?e:e.empty?this:q(this,e)}mapDesc(e,t=!1){return e.empty?this:R(this,e,t)}mapPos(e,t=-1,i=Z.Simple){let r=0,n=0;for(let s=0;s<this.sections.length;){let o=this.sections[s++],a=this.sections[s++],l=r+o;if(a<0){if(l>e)return n+(e-r);n+=o}else{if(i!=Z.Simple&&l>=e&&(i==Z.TrackDel&&r<e&&l>e||i==Z.TrackBefore&&r<e||i==Z.TrackAfter&&l>e))return null;if(l>e||l==e&&t<0&&!o)return e==r||t<0?n:n+a;n+=a}r=l}if(e>r)throw RangeError(`Position ${e} is out of range for changeset of length ${r}`);return n}touchesRange(e,t=e){for(let i=0,r=0;i<this.sections.length&&r<=t;){let n=this.sections[i++],s=this.sections[i++],o=r+n;if(s>=0&&r<=t&&o>=e)return!(r<e)||!(o>t)||"cover";r=o}return!1}toString(){let e="";for(let t=0;t<this.sections.length;){let i=this.sections[t++],r=this.sections[t++];e+=(e?" ":"")+i+(r>=0?":"+r:"")}return e}toJSON(){return this.sections}static fromJSON(e){if(!Array.isArray(e)||e.length%2||e.some(e=>"number"!=typeof e))throw RangeError("Invalid JSON representation of ChangeDesc");return new T(e)}static create(e){return new T(e)}}class _ extends T{constructor(e,t){super(e),this.inserted=t}apply(e){if(this.length!=e.length)throw RangeError("Applying change set to a document with the wrong length");return A(this,(t,i,r,n,s)=>e=e.replace(r,r+(i-t),s),!1),e}mapDesc(e,t=!1){return R(this,e,t,!0)}invert(e){let t=this.sections.slice(),i=[];for(let r=0,n=0;r<t.length;r+=2){let s=t[r],o=t[r+1];if(o>=0){t[r]=o,t[r+1]=s;let a=r>>1;for(;i.length<a;)i.push(u.empty);i.push(s?e.slice(n,n+s):u.empty)}n+=s}return new _(t,i)}compose(e){return this.empty?e:e.empty?this:q(this,e,!0)}map(e,t=!1){return e.empty?this:R(this,e,t,!0)}iterChanges(e,t=!1){A(this,e,t)}get desc(){return T.create(this.sections)}filter(e){let t=[],i=[],r=[],n=new M(this);i:for(let s=0,o=0;;){let a=s==e.length?1e9:e[s++];for(;o<a||o==a&&0==n.len;){if(n.done)break i;let e=Math.min(n.len,a-o);X(r,e,-1);let s=-1==n.ins?-1:0==n.off?n.ins:0;X(t,e,s),s>0&&C(i,t,n.text),n.forward(e),o+=e}let l=e[s++];for(;o<l;){if(n.done)break i;let e=Math.min(n.len,l-o);X(t,e,-1),X(r,e,-1==n.ins?-1:0==n.off?n.ins:0),n.forward(e),o+=e}}return{changes:new _(t,i),filtered:T.create(r)}}toJSON(){let e=[];for(let t=0;t<this.sections.length;t+=2){let i=this.sections[t],r=this.sections[t+1];r<0?e.push(i):0==r?e.push([i]):e.push([i].concat(this.inserted[t>>1].toJSON()))}return e}static of(e,t,i){let r=[],n=[],s=0,o=null;function a(e=!1){if(!e&&!r.length)return;s<t&&X(r,t-s,-1);let i=new _(r,n);o=o?o.compose(i.map(o)):i,r=[],n=[],s=0}return!function e(l){if(Array.isArray(l))for(let t of l)e(t);else if(l instanceof _){if(l.length!=t)throw RangeError(`Mismatched change set length (got ${l.length}, expected ${t})`);a(),o=o?o.compose(l.map(o)):l}else{let{from:e,to:o=e,insert:h}=l;if(e>o||e<0||o>t)throw RangeError(`Invalid change range ${e} to ${o} (in doc of length ${t})`);let c=h?"string"==typeof h?u.of(h.split(i||w)):h:u.empty,f=c.length;if(e==o&&0==f)return;e<s&&a(),e>s&&X(r,e-s,-1),X(r,o-e,f),C(n,r,c),s=o}}(e),a(!o),o}static empty(e){return new _(e?[e,-1]:[],[])}static fromJSON(e){if(!Array.isArray(e))throw RangeError("Invalid JSON representation of ChangeSet");let t=[],i=[];for(let r=0;r<e.length;r++){let n=e[r];if("number"==typeof n)t.push(n,-1);else if(!Array.isArray(n)||"number"!=typeof n[0]||n.some((e,t)=>t&&"string"!=typeof e))throw RangeError("Invalid JSON representation of ChangeSet");else if(1==n.length)t.push(n[0],0);else{for(;i.length<r;)i.push(u.empty);i[r]=u.of(n.slice(1)),t.push(n[0],i[r].length)}}return new _(t,i)}static createSet(e,t){return new _(e,t)}}function X(e,t,i,r=!1){if(0==t&&i<=0)return;let n=e.length-2;n>=0&&i<=0&&i==e[n+1]?e[n]+=t:n>=0&&0==t&&0==e[n]?e[n+1]+=i:r?(e[n]+=t,e[n+1]+=i):e.push(t,i)}function C(e,t,i){if(0==i.length)return;let r=t.length-2>>1;if(r<e.length)e[e.length-1]=e[e.length-1].append(i);else{for(;e.length<r;)e.push(u.empty);e.push(i)}}function A(e,t,i){let r=e.inserted;for(let n=0,s=0,o=0;o<e.sections.length;){let a=e.sections[o++],l=e.sections[o++];if(l<0)n+=a,s+=a;else{let h=n,c=s,f=u.empty;for(;h+=a,c+=l,l&&r&&(f=f.append(r[o-2>>1])),!i&&o!=e.sections.length&&!(e.sections[o+1]<0);)a=e.sections[o++],l=e.sections[o++];t(n,h,s,c,f),n=h,s=c}}}function R(e,t,i,r=!1){let n=[],s=r?[]:null,o=new M(e),a=new M(t);for(let e=-1;;){if(o.done&&a.len||a.done&&o.len)throw Error("Mismatched change set lengths");if(-1==o.ins&&-1==a.ins){let e=Math.min(o.len,a.len);X(n,e,-1),o.forward(e),a.forward(e)}else if(a.ins>=0&&(o.ins<0||e==o.i||0==o.off&&(a.len<o.len||a.len==o.len&&!i))){let t=a.len;for(X(n,a.ins,-1);t;){let i=Math.min(o.len,t);o.ins>=0&&e<o.i&&o.len<=i&&(X(n,0,o.ins),s&&C(s,n,o.text),e=o.i),o.forward(i),t-=i}a.next()}else if(o.ins>=0){let t=0,i=o.len;for(;i;)if(-1==a.ins){let e=Math.min(i,a.len);t+=e,i-=e,a.forward(e)}else if(0==a.ins&&a.len<i)i-=a.len,a.next();else break;X(n,t,e<o.i?o.ins:0),s&&e<o.i&&C(s,n,o.text),e=o.i,o.forward(o.len-i)}else if(o.done&&a.done)return s?_.createSet(n,s):T.create(n);else throw Error("Mismatched change set lengths")}}function q(e,t,i=!1){let r=[],n=i?[]:null,s=new M(e),o=new M(t);for(let e=!1;;){if(s.done&&o.done)return n?_.createSet(r,n):T.create(r);if(0==s.ins)X(r,s.len,0,e),s.next();else if(0!=o.len||o.done){if(s.done||o.done)throw Error("Mismatched change set lengths");{let t=Math.min(s.len2,o.len),i=r.length;if(-1==s.ins){let i=-1==o.ins?-1:o.off?0:o.ins;X(r,t,i,e),n&&i&&C(n,r,o.text)}else -1==o.ins?(X(r,s.off?0:s.len,t,e),n&&C(n,r,s.textBit(t))):(X(r,s.off?0:s.len,o.off?0:o.ins,e),n&&!o.off&&C(n,r,o.text));e=(s.ins>t||o.ins>=0&&o.len>t)&&(e||r.length>i),s.forward2(t),o.forward(t)}}else X(r,0,o.ins,e),n&&C(n,r,o.text),o.next()}}class M{constructor(e){this.set=e,this.i=0,this.next()}next(){let{sections:e}=this.set;this.i<e.length?(this.len=e[this.i++],this.ins=e[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return -2==this.ins}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:e}=this.set,t=this.i-2>>1;return t>=e.length?u.empty:e[t]}textBit(e){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length&&!e?u.empty:t[i].slice(this.off,null==e?void 0:this.off+e)}forward(e){e==this.len?this.next():(this.len-=e,this.off+=e)}forward2(e){-1==this.ins?this.forward(e):e==this.ins?this.next():(this.ins-=e,this.off+=e)}}class j{constructor(e,t,i){this.from=e,this.to=t,this.flags=i}get anchor(){return 32&this.flags?this.to:this.from}get head(){return 32&this.flags?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return 8&this.flags?-1:16&this.flags?1:0}get bidiLevel(){let e=7&this.flags;return 7==e?null:e}get goalColumn(){let e=this.flags>>6;return 16777215==e?void 0:e}map(e,t=-1){let i,r;return this.empty?i=r=e.mapPos(this.from,t):(i=e.mapPos(this.from,1),r=e.mapPos(this.to,-1)),i==this.from&&r==this.to?this:new j(i,r,this.flags)}extend(e,t=e){if(e<=this.anchor&&t>=this.anchor)return Y.range(e,t);let i=Math.abs(e-this.anchor)>Math.abs(t-this.anchor)?e:t;return Y.range(this.anchor,i)}eq(e,t=!1){return this.anchor==e.anchor&&this.head==e.head&&(!t||!this.empty||this.assoc==e.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(e){if(!e||"number"!=typeof e.anchor||"number"!=typeof e.head)throw RangeError("Invalid JSON representation for SelectionRange");return Y.range(e.anchor,e.head)}static create(e,t,i){return new j(e,t,i)}}class Y{constructor(e,t){this.ranges=e,this.mainIndex=t}map(e,t=-1){return e.empty?this:Y.create(this.ranges.map(i=>i.map(e,t)),this.mainIndex)}eq(e,t=!1){if(this.ranges.length!=e.ranges.length||this.mainIndex!=e.mainIndex)return!1;for(let i=0;i<this.ranges.length;i++)if(!this.ranges[i].eq(e.ranges[i],t))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return 1==this.ranges.length?this:new Y([this.main],0)}addRange(e,t=!0){return Y.create([e].concat(this.ranges),t?0:this.mainIndex+1)}replaceRange(e,t=this.mainIndex){let i=this.ranges.slice();return i[t]=e,Y.create(i,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(e=>e.toJSON()),main:this.mainIndex}}static fromJSON(e){if(!e||!Array.isArray(e.ranges)||"number"!=typeof e.main||e.main>=e.ranges.length)throw RangeError("Invalid JSON representation for EditorSelection");return new Y(e.ranges.map(e=>j.fromJSON(e)),e.main)}static single(e,t=e){return new Y([Y.range(e,t)],0)}static create(e,t=0){if(0==e.length)throw RangeError("A selection needs at least one range");for(let i=0,r=0;r<e.length;r++){let n=e[r];if(n.empty?n.from<=i:n.from<i)return Y.normalized(e.slice(),t);i=n.to}return new Y(e,t)}static cursor(e,t=0,i,r){return j.create(e,e,(0==t?0:t<0?8:16)|(null==i?7:Math.min(6,i))|(null!=r?r:16777215)<<6)}static range(e,t,i,r){let n=(null!=i?i:16777215)<<6|(null==r?7:Math.min(6,r));return t<e?j.create(t,e,48|n):j.create(e,t,(t>e?8:0)|n)}static normalized(e,t=0){let i=e[t];e.sort((e,t)=>e.from-t.from),t=e.indexOf(i);for(let i=1;i<e.length;i++){let r=e[i],n=e[i-1];if(r.empty?r.from<=n.to:r.from<n.to){let s=n.from,o=Math.max(r.to,n.to);i<=t&&t--,e.splice(--i,2,r.anchor>r.head?Y.range(o,s):Y.range(s,o))}}return new Y(e,t)}}function N(e,t){for(let i of e.ranges)if(i.to>t)throw RangeError("Selection points outside of document")}let I=0;class E{constructor(e,t,i,r,n){this.combine=e,this.compareInput=t,this.compare=i,this.isStatic=r,this.id=I++,this.default=e([]),this.extensions="function"==typeof n?n(this):n}get reader(){return this}static define(e={}){return new E(e.combine||(e=>e),e.compareInput||((e,t)=>e===t),e.compare||(e.combine?(e,t)=>e===t:z),!!e.static,e.enables)}of(e){return new V([],this,0,e)}compute(e,t){if(this.isStatic)throw Error("Can't compute a static facet");return new V(e,this,1,t)}computeN(e,t){if(this.isStatic)throw Error("Can't compute a static facet");return new V(e,this,2,t)}from(e,t){return t||(t=e=>e),this.compute([e],i=>t(i.field(e)))}}function z(e,t){return e==t||e.length==t.length&&e.every((e,i)=>e===t[i])}class V{constructor(e,t,i,r){this.dependencies=e,this.facet=t,this.type=i,this.value=r,this.id=I++}dynamicSlot(e){var t;let i=this.value,r=this.facet.compareInput,n=this.id,s=e[n]>>1,o=2==this.type,a=!1,l=!1,h=[];for(let i of this.dependencies)"doc"==i?a=!0:"selection"==i?l=!0:((null!==(t=e[i.id])&&void 0!==t?t:1)&1)==0&&h.push(e[i.id]);return{create:e=>(e.values[s]=i(e),1),update(e,t){if(a&&t.docChanged||l&&(t.docChanged||t.selection)||W(e,h)){let t=i(e);if(o?!L(t,e.values[s],r):!r(t,e.values[s]))return e.values[s]=t,1}return 0},reconfigure:(e,t)=>{let a,l=t.config.address[n];if(null!=l){let n=ei(t,l);if(this.dependencies.every(i=>i instanceof E?t.facet(i)===e.facet(i):!(i instanceof B)||t.field(i,!1)==e.field(i,!1))||(o?L(a=i(e),n,r):r(a=i(e),n)))return e.values[s]=n,0}else a=i(e);return e.values[s]=a,1}}}}function L(e,t,i){if(e.length!=t.length)return!1;for(let r=0;r<e.length;r++)if(!i(e[r],t[r]))return!1;return!0}function W(e,t){let i=!1;for(let r of t)1&et(e,r)&&(i=!0);return i}let G=E.define({static:!0});class B{constructor(e,t,i,r,n){this.id=e,this.createF=t,this.updateF=i,this.compareF=r,this.spec=n,this.provides=void 0}static define(e){let t=new B(I++,e.create,e.update,e.compare||((e,t)=>e===t),e);return e.provide&&(t.provides=e.provide(t)),t}create(e){let t=e.facet(G).find(e=>e.field==this);return((null==t?void 0:t.create)||this.createF)(e)}slot(e){let t=e[this.id]>>1;return{create:e=>(e.values[t]=this.create(e),1),update:(e,i)=>{let r=e.values[t],n=this.updateF(r,i);return this.compareF(r,n)?0:(e.values[t]=n,1)},reconfigure:(e,i)=>{let r=e.facet(G),n=i.facet(G),s;return(s=r.find(e=>e.field==this))&&s!=n.find(e=>e.field==this)?(e.values[t]=s.create(e),1):null!=i.config.address[this.id]?(e.values[t]=i.field(this),0):(e.values[t]=this.create(e),1)}}}init(e){return[this,G.of({field:this,create:e})]}get extension(){return this}}let U={lowest:4,low:3,default:2,high:1,highest:0};function D(e){return t=>new F(t,e)}let J={highest:D(U.highest),high:D(U.high),default:D(U.default),low:D(U.low),lowest:D(U.lowest)};class F{constructor(e,t){this.inner=e,this.prec=t}}class K{of(e){return new H(this,e)}reconfigure(e){return K.reconfigure.of({compartment:this,extension:e})}get(e){return e.config.compartments.get(this)}}class H{constructor(e,t){this.compartment=e,this.inner=t}}class ee{constructor(e,t,i,r,n,s){for(this.base=e,this.compartments=t,this.dynamicSlots=i,this.address=r,this.staticValues=n,this.facets=s,this.statusTemplate=[];this.statusTemplate.length<i.length;)this.statusTemplate.push(0)}staticFacet(e){let t=this.address[e.id];return null==t?e.default:this.staticValues[t>>1]}static resolve(e,t,i){let r,n,s=[],o=Object.create(null),a=new Map;for(let i of(r=[[],[],[],[],[]],n=new Map,!function e(i,s){let o=n.get(i);if(null!=o){if(o<=s)return;let e=r[o].indexOf(i);e>-1&&r[o].splice(e,1),i instanceof H&&a.delete(i.compartment)}if(n.set(i,s),Array.isArray(i))for(let t of i)e(t,s);else if(i instanceof H){if(a.has(i.compartment))throw RangeError("Duplicate use of compartment in extensions");let r=t.get(i.compartment)||i.inner;a.set(i.compartment,r),e(r,s)}else if(i instanceof F)e(i.inner,i.prec);else if(i instanceof B)r[s].push(i),i.provides&&e(i.provides,s);else if(i instanceof V)r[s].push(i),i.facet.extensions&&e(i.facet.extensions,U.default);else{let t=i.extension;if(!t)throw Error(`Unrecognized extension value in extension set (${i}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);e(t,s)}}(e,U.default),r.reduce((e,t)=>e.concat(t))))i instanceof B?s.push(i):(o[i.facet.id]||(o[i.facet.id]=[])).push(i);let l=Object.create(null),h=[],c=[];for(let e of s)l[e.id]=c.length<<1,c.push(t=>e.slot(t));let f=null==i?void 0:i.config.facets;for(let e in o){let t=o[e],r=t[0].facet,n=f&&f[e]||[];if(t.every(e=>0==e.type)){if(l[r.id]=h.length<<1|1,z(n,t))h.push(i.facet(r));else{let e=r.combine(t.map(e=>e.value));h.push(i&&r.compare(e,i.facet(r))?i.facet(r):e)}}else{for(let e of t)0==e.type?(l[e.id]=h.length<<1|1,h.push(e.value)):(l[e.id]=c.length<<1,c.push(t=>e.dynamicSlot(t)));l[r.id]=c.length<<1,c.push(e=>(function(e,t,i){let r=i.map(t=>e[t.id]),n=i.map(e=>e.type),s=r.filter(e=>!(1&e)),o=e[t.id]>>1;function a(e){let i=[];for(let t=0;t<r.length;t++){let s=ei(e,r[t]);if(2==n[t])for(let e of s)i.push(e);else i.push(s)}return t.combine(i)}return{create(e){for(let t of r)et(e,t);return e.values[o]=a(e),1},update(e,i){if(!W(e,s))return 0;let r=a(e);return t.compare(r,e.values[o])?0:(e.values[o]=r,1)},reconfigure(e,n){let s=W(e,r),l=n.config.facets[t.id],h=n.facet(t);if(l&&!s&&z(i,l))return e.values[o]=h,0;let c=a(e);return t.compare(c,h)?(e.values[o]=h,0):(e.values[o]=c,1)}}})(e,r,t))}}let u=c.map(e=>e(l));return new ee(e,a,u,l,h,o)}}function et(e,t){if(1&t)return 2;let i=t>>1,r=e.status[i];if(4==r)throw Error("Cyclic dependency between fields and/or facets");if(2&r)return r;e.status[i]=4;let n=e.computeSlot(e,e.config.dynamicSlots[i]);return e.status[i]=2|n}function ei(e,t){return 1&t?e.config.staticValues[t>>1]:e.values[t>>1]}let er=E.define(),en=E.define({combine:e=>e.some(e=>e),static:!0}),es=E.define({combine:e=>e.length?e[0]:void 0,static:!0}),eo=E.define(),ea=E.define(),el=E.define(),eh=E.define({combine:e=>!!e.length&&e[0]});class ec{constructor(e,t){this.type=e,this.value=t}static define(){return new ef}}class ef{of(e){return new ec(this,e)}}class eu{constructor(e){this.map=e}of(e){return new ep(this,e)}}class ep{constructor(e,t){this.type=e,this.value=t}map(e){let t=this.type.map(this.value,e);return void 0===t?void 0:t==this.value?this:new ep(this.type,t)}is(e){return this.type==e}static define(e={}){return new eu(e.map||(e=>e))}static mapEffects(e,t){if(!e.length)return e;let i=[];for(let r of e){let e=r.map(t);e&&i.push(e)}return i}}ep.reconfigure=ep.define(),ep.appendConfig=ep.define();class eO{constructor(e,t,i,r,n,s){this.startState=e,this.changes=t,this.selection=i,this.effects=r,this.annotations=n,this.scrollIntoView=s,this._doc=null,this._state=null,i&&N(i,t.newLength),n.some(e=>e.type==eO.time)||(this.annotations=n.concat(eO.time.of(Date.now())))}static create(e,t,i,r,n,s){return new eO(e,t,i,r,n,s)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(e){for(let t of this.annotations)if(t.type==e)return t.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(e){let t=this.annotation(eO.userEvent);return!!(t&&(t==e||t.length>e.length&&t.slice(0,e.length)==e&&"."==t[e.length]))}}function ed(e,t,i){var r;let n,s,o;return i?(n=t.changes,s=_.empty(t.changes.length),o=e.changes.compose(t.changes)):(n=t.changes.map(e.changes),s=e.changes.mapDesc(t.changes,!0),o=e.changes.compose(n)),{changes:o,selection:t.selection?t.selection.map(s):null===(r=e.selection)||void 0===r?void 0:r.map(n),effects:ep.mapEffects(e.effects,n).concat(ep.mapEffects(t.effects,s)),annotations:e.annotations.length?e.annotations.concat(t.annotations):t.annotations,scrollIntoView:e.scrollIntoView||t.scrollIntoView}}function eg(e,t,i){let r=t.selection,n=ey(t.annotations);return t.userEvent&&(n=n.concat(eO.userEvent.of(t.userEvent))),{changes:t.changes instanceof _?t.changes:_.of(t.changes||[],i,e.facet(es)),selection:r&&(r instanceof Y?r:Y.single(r.anchor,r.head)),effects:ey(t.effects),annotations:n,scrollIntoView:!!t.scrollIntoView}}eO.time=ec.define(),eO.userEvent=ec.define(),eO.addToHistory=ec.define(),eO.remote=ec.define();let em=[];function ey(e){return null==e?em:Array.isArray(e)?e:[e]}var ex=((P=ex||(ex={}))[P.Word=0]="Word",P[P.Space=1]="Space",P[P.Other=2]="Other",P);let ek=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;try{r=RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch(e){}class eQ{constructor(e,t,i,r,n,s){this.config=e,this.doc=t,this.selection=i,this.values=r,this.status=e.statusTemplate.slice(),this.computeSlot=n,s&&(s._state=this);for(let e=0;e<this.config.dynamicSlots.length;e++)et(this,e<<1);this.computeSlot=null}field(e,t=!0){let i=this.config.address[e.id];if(null==i){if(t)throw RangeError("Field is not present in this state");return}return et(this,i),ei(this,i)}update(...e){return function e(t,i,r){let n=eg(t,i.length?i[0]:{},t.doc.length);i.length&&!1===i[0].filter&&(r=!1);for(let e=1;e<i.length;e++){!1===i[e].filter&&(r=!1);let s=!!i[e].sequential;n=ed(n,eg(t,i[e],s?n.changes.newLength:t.doc.length),s)}let s=eO.create(t,n.changes,n.selection,n.effects,n.annotations,n.scrollIntoView);return function(e){let t=e.startState,i=t.facet(el),r=e;for(let n=i.length-1;n>=0;n--){let s=i[n](e);s&&Object.keys(s).length&&(r=ed(r,eg(t,s,e.changes.newLength),!0))}return r==e?e:eO.create(t,e.changes,e.selection,r.effects,r.annotations,r.scrollIntoView)}(r?function(t){let i=t.startState,r=!0;for(let e of i.facet(eo)){let i=e(t);if(!1===i){r=!1;break}Array.isArray(i)&&(r=!0===r?i:function(e,t){let i=[];for(let r=0,n=0;;){let s,o;if(r<e.length&&(n==t.length||t[n]>=e[r]))s=e[r++],o=e[r++];else{if(!(n<t.length))return i;s=t[n++],o=t[n++]}!i.length||i[i.length-1]<s?i.push(s,o):i[i.length-1]<o&&(i[i.length-1]=o)}}(r,i))}if(!0!==r){let e,n;if(!1===r)n=t.changes.invertedDesc,e=_.empty(i.doc.length);else{let i=t.changes.filter(r);e=i.changes,n=i.filtered.mapDesc(i.changes).invertedDesc}t=eO.create(i,e,t.selection&&t.selection.map(n),ep.mapEffects(t.effects,n),t.annotations,t.scrollIntoView)}let n=i.facet(ea);for(let r=n.length-1;r>=0;r--){let s=n[r](t);t=s instanceof eO?s:Array.isArray(s)&&1==s.length&&s[0]instanceof eO?s[0]:e(i,ey(s),!1)}return t}(s):s)}(this,e,!0)}applyTransaction(e){let t,i=this.config,{base:r,compartments:n}=i;for(let t of e.effects)t.is(K.reconfigure)?(i&&(n=new Map,i.compartments.forEach((e,t)=>n.set(t,e)),i=null),n.set(t.value.compartment,t.value.extension)):t.is(ep.reconfigure)?(i=null,r=t.value):t.is(ep.appendConfig)&&(i=null,r=ey(r).concat(t.value));i?t=e.startState.values.slice():(i=ee.resolve(r,n,this),t=new eQ(i,this.doc,this.selection,i.dynamicSlots.map(()=>null),(e,t)=>t.reconfigure(e,this),null).values);let s=e.startState.facet(en)?e.newSelection:e.newSelection.asSingle();new eQ(i,e.newDoc,s,t,(t,i)=>i.update(t,e),e)}replaceSelection(e){return"string"==typeof e&&(e=this.toText(e)),this.changeByRange(t=>({changes:{from:t.from,to:t.to,insert:e},range:Y.cursor(t.from+e.length)}))}changeByRange(e){let t=this.selection,i=e(t.ranges[0]),r=this.changes(i.changes),n=[i.range],s=ey(i.effects);for(let i=1;i<t.ranges.length;i++){let o=e(t.ranges[i]),a=this.changes(o.changes),l=a.map(r);for(let e=0;e<i;e++)n[e]=n[e].map(l);let h=r.mapDesc(a,!0);n.push(o.range.map(h)),r=r.compose(l),s=ep.mapEffects(s,l).concat(ep.mapEffects(ey(o.effects),h))}return{changes:r,selection:Y.create(n,t.mainIndex),effects:s}}changes(e=[]){return e instanceof _?e:_.of(e,this.doc.length,this.facet(eQ.lineSeparator))}toText(e){return u.of(e.split(this.facet(eQ.lineSeparator)||w))}sliceDoc(e=0,t=this.doc.length){return this.doc.sliceString(e,t,this.lineBreak)}facet(e){let t=this.config.address[e.id];return null==t?e.default:(et(this,t),ei(this,t))}toJSON(e){let t={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(e)for(let i in e){let r=e[i];r instanceof B&&null!=this.config.address[r.id]&&(t[i]=r.spec.toJSON(this.field(e[i]),this))}return t}static fromJSON(e,t={},i){if(!e||"string"!=typeof e.doc)throw RangeError("Invalid JSON representation for EditorState");let r=[];if(i){for(let t in i)if(Object.prototype.hasOwnProperty.call(e,t)){let n=i[t],s=e[t];r.push(n.init(e=>n.spec.fromJSON(s,e)))}}return eQ.create({doc:e.doc,selection:Y.fromJSON(e.selection),extensions:t.extensions?r.concat([t.extensions]):r})}static create(e={}){let t=ee.resolve(e.extensions||[],new Map),i=e.doc instanceof u?e.doc:u.of((e.doc||"").split(t.staticFacet(eQ.lineSeparator)||w)),r=e.selection?e.selection instanceof Y?e.selection:Y.single(e.selection.anchor,e.selection.head):Y.single(0);return N(r,i.length),t.staticFacet(en)||(r=r.asSingle()),new eQ(t,i,r,t.dynamicSlots.map(()=>null),(e,t)=>t.create(e),null)}get tabSize(){return this.facet(eQ.tabSize)}get lineBreak(){return this.facet(eQ.lineSeparator)||"\n"}get readOnly(){return this.facet(eh)}phrase(e,...t){for(let t of this.facet(eQ.phrases))if(Object.prototype.hasOwnProperty.call(t,e)){e=t[e];break}return t.length&&(e=e.replace(/\$(\$|\d*)/g,(e,i)=>{if("$"==i)return"$";let r=+(i||1);return!r||r>t.length?e:t[r-1]})),e}languageDataAt(e,t,i=-1){let r=[];for(let n of this.facet(er))for(let s of n(this,t,i))Object.prototype.hasOwnProperty.call(s,e)&&r.push(s[e]);return r}charCategorizer(e){var t;return t=this.languageDataAt("wordChars",e).join(""),e=>{if(!/\S/.test(e))return ex.Space;if(function(e){if(r)return r.test(e);for(let t=0;t<e.length;t++){let i=e[t];if(/\w/.test(i)||i>"\x80"&&(i.toUpperCase()!=i.toLowerCase()||ek.test(i)))return!0}return!1}(e))return ex.Word;for(let i=0;i<t.length;i++)if(e.indexOf(t[i])>-1)return ex.Word;return ex.Other}}wordAt(e){let{text:t,from:i,length:r}=this.doc.lineAt(e),n=this.charCategorizer(e),s=e-i,o=e-i;for(;s>0;){let e=Q(t,s,!1);if(n(t.slice(e,s))!=ex.Word)break;s=e}for(;o<r;){let e=Q(t,o);if(n(t.slice(o,e))!=ex.Word)break;o=e}return s==o?null:Y.range(s+i,o+i)}}function eb(e,t,i={}){let r={};for(let t of e)for(let e of Object.keys(t)){let n=t[e],s=r[e];if(void 0===s)r[e]=n;else if(s===n||void 0===n);else if(Object.hasOwnProperty.call(i,e))r[e]=i[e](s,n);else throw Error("Config merge conflict for field "+e)}for(let e in t)void 0===r[e]&&(r[e]=t[e]);return r}eQ.allowMultipleSelections=en,eQ.tabSize=E.define({combine:e=>e.length?e[0]:4}),eQ.lineSeparator=es,eQ.readOnly=eh,eQ.phrases=E.define({compare(e,t){let i=Object.keys(e),r=Object.keys(t);return i.length==r.length&&i.every(i=>e[i]==t[i])}}),eQ.languageData=er,eQ.changeFilter=eo,eQ.transactionFilter=ea,eQ.transactionExtender=el,K.reconfigure=ep.define();class ev{eq(e){return this==e}range(e,t=e){return eS.create(e,t,this)}}ev.prototype.startSide=ev.prototype.endSide=0,ev.prototype.point=!1,ev.prototype.mapMode=Z.TrackDel;class eS{constructor(e,t,i){this.from=e,this.to=t,this.value=i}static create(e,t,i){return new eS(e,t,i)}}function ew(e,t){return e.from-t.from||e.value.startSide-t.value.startSide}class e${constructor(e,t,i,r){this.from=e,this.to=t,this.value=i,this.maxPoint=r}get length(){return this.to[this.to.length-1]}findIndex(e,t,i,r=0){let n=i?this.to:this.from;for(let s=r,o=n.length;;){if(s==o)return s;let r=s+o>>1,a=n[r]-e||(i?this.value[r].endSide:this.value[r].startSide)-t;if(r==s)return a>=0?s:o;a>=0?o=r:s=r+1}}between(e,t,i,r){for(let n=this.findIndex(t,-1e9,!0),s=this.findIndex(i,1e9,!1,n);n<s;n++)if(!1===r(this.from[n]+e,this.to[n]+e,this.value[n]))return!1}map(e,t){let i=[],r=[],n=[],s=-1,o=-1;for(let a=0;a<this.value.length;a++){let l=this.value[a],h=this.from[a]+e,c=this.to[a]+e,f,u;if(h==c){let e=t.mapPos(h,l.startSide,l.mapMode);if(null==e||(f=u=e,l.startSide!=l.endSide&&(u=t.mapPos(h,l.endSide))<f))continue}else if((f=t.mapPos(h,l.startSide))>(u=t.mapPos(c,l.endSide))||f==u&&l.startSide>0&&l.endSide<=0)continue;0>(u-f||l.endSide-l.startSide)||(s<0&&(s=f),l.point&&(o=Math.max(o,u-f)),i.push(l),r.push(f-s),n.push(u-s))}return{mapped:i.length?new e$(r,n,i,o):null,pos:s}}}class eP{constructor(e,t,i,r){this.chunkPos=e,this.chunk=t,this.nextLayer=i,this.maxPoint=r}static create(e,t,i,r){return new eP(e,t,i,r)}get length(){let e=this.chunk.length-1;return e<0?0:Math.max(this.chunkEnd(e),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let e=this.nextLayer.size;for(let t of this.chunk)e+=t.value.length;return e}chunkEnd(e){return this.chunkPos[e]+this.chunk[e].length}update(e){let{add:t=[],sort:i=!1,filterFrom:r=0,filterTo:n=this.length}=e,s=e.filter;if(0==t.length&&!s)return this;if(i&&(t=t.slice().sort(ew)),this.isEmpty)return t.length?eP.of(t):this;let o=new e_(this,null,-1).goto(0),a=0,l=[],h=new eZ;for(;o.value||a<t.length;)if(a<t.length&&(o.from-t[a].from||o.startSide-t[a].value.startSide)>=0){let e=t[a++];h.addInner(e.from,e.to,e.value)||l.push(e)}else 1==o.rangeIndex&&o.chunkIndex<this.chunk.length&&(a==t.length||this.chunkEnd(o.chunkIndex)<t[a].from)&&(!s||r>this.chunkEnd(o.chunkIndex)||n<this.chunkPos[o.chunkIndex])&&h.addChunk(this.chunkPos[o.chunkIndex],this.chunk[o.chunkIndex])?o.nextChunk():((!s||r>o.to||n<o.from||s(o.from,o.to,o.value))&&!h.addInner(o.from,o.to,o.value)&&l.push(eS.create(o.from,o.to,o.value)),o.next());return h.finishInner(this.nextLayer.isEmpty&&!l.length?eP.empty:this.nextLayer.update({add:l,filter:s,filterFrom:r,filterTo:n}))}map(e){if(e.empty||this.isEmpty)return this;let t=[],i=[],r=-1;for(let n=0;n<this.chunk.length;n++){let s=this.chunkPos[n],o=this.chunk[n],a=e.touchesRange(s,s+o.length);if(!1===a)r=Math.max(r,o.maxPoint),t.push(o),i.push(e.mapPos(s));else if(!0===a){let{mapped:n,pos:a}=o.map(s,e);n&&(r=Math.max(r,n.maxPoint),t.push(n),i.push(a))}}let n=this.nextLayer.map(e);return 0==t.length?n:new eP(i,t,n||eP.empty,r)}between(e,t,i){if(!this.isEmpty){for(let r=0;r<this.chunk.length;r++){let n=this.chunkPos[r],s=this.chunk[r];if(t>=n&&e<=n+s.length&&!1===s.between(n,e-n,t-n,i))return}this.nextLayer.between(e,t,i)}}iter(e=0){return eX.from([this]).goto(e)}get isEmpty(){return this.nextLayer==this}static iter(e,t=0){return eX.from(e).goto(t)}static compare(e,t,i,r,n=-1){let s=e.filter(e=>e.maxPoint>0||!e.isEmpty&&e.maxPoint>=n),o=t.filter(e=>e.maxPoint>0||!e.isEmpty&&e.maxPoint>=n),a=eT(s,o,i),l=new eA(s,a,n),h=new eA(o,a,n);i.iterGaps((e,t,i)=>eR(l,e,h,t,i,r)),i.empty&&0==i.length&&eR(l,0,h,0,0,r)}static eq(e,t,i=0,r){null==r&&(r=999999999);let n=e.filter(e=>!e.isEmpty&&0>t.indexOf(e)),s=t.filter(t=>!t.isEmpty&&0>e.indexOf(t));if(n.length!=s.length)return!1;if(!n.length)return!0;let o=eT(n,s),a=new eA(n,o,0).goto(i),l=new eA(s,o,0).goto(i);for(;;){if(a.to!=l.to||!eq(a.active,l.active)||a.point&&(!l.point||!a.point.eq(l.point)))return!1;if(a.to>r)return!0;a.next(),l.next()}}static spans(e,t,i,r,n=-1){let s=new eA(e,null,n).goto(t),o=t,a=s.openStart;for(;;){let e=Math.min(s.to,i);if(s.point){let i=s.activeForPoint(s.to),n=s.pointFrom<t?i.length+1:s.point.startSide<0?i.length:Math.min(i.length,a);r.point(o,e,s.point,i,n,s.pointRank),a=Math.min(s.openEnd(e),i.length)}else e>o&&(r.span(o,e,s.active,a),a=s.openEnd(e));if(s.to>i)return a+(s.point&&s.to>i?1:0);o=s.to,s.next()}}static of(e,t=!1){let i=new eZ;for(let r of e instanceof eS?[e]:t?function(e){if(e.length>1)for(let t=e[0],i=1;i<e.length;i++){let r=e[i];if(ew(t,r)>0)return e.slice().sort(ew);t=r}return e}(e):e)i.add(r.from,r.to,r.value);return i.finish()}static join(e){if(!e.length)return eP.empty;let t=e[e.length-1];for(let i=e.length-2;i>=0;i--)for(let r=e[i];r!=eP.empty;r=r.nextLayer)t=new eP(r.chunkPos,r.chunk,t,Math.max(r.maxPoint,t.maxPoint));return t}}eP.empty=new eP([],[],null,-1),eP.empty.nextLayer=eP.empty;class eZ{finishChunk(e){this.chunks.push(new e$(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,e&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(e,t,i){this.addInner(e,t,i)||(this.nextLayer||(this.nextLayer=new eZ)).add(e,t,i)}addInner(e,t,i){let r=e-this.lastTo||i.startSide-this.last.endSide;if(r<=0&&0>(e-this.lastFrom||i.startSide-this.last.startSide))throw Error("Ranges must be added sorted by `from` position and `startSide`");return!(r<0)&&(250==this.from.length&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=e),this.from.push(e-this.chunkStart),this.to.push(t-this.chunkStart),this.last=i,this.lastFrom=e,this.lastTo=t,this.value.push(i),i.point&&(this.maxPoint=Math.max(this.maxPoint,t-e)),!0)}addChunk(e,t){if(0>(e-this.lastTo||t.value[0].startSide-this.last.endSide))return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,t.maxPoint),this.chunks.push(t),this.chunkPos.push(e);let i=t.value.length-1;return this.last=t.value[i],this.lastFrom=t.from[i]+e,this.lastTo=t.to[i]+e,!0}finish(){return this.finishInner(eP.empty)}finishInner(e){if(this.from.length&&this.finishChunk(!1),0==this.chunks.length)return e;let t=eP.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(e):e,this.setMaxPoint);return this.from=null,t}}function eT(e,t,i){let r=new Map;for(let t of e)for(let e=0;e<t.chunk.length;e++)t.chunk[e].maxPoint<=0&&r.set(t.chunk[e],t.chunkPos[e]);let n=new Set;for(let e of t)for(let t=0;t<e.chunk.length;t++){let s=r.get(e.chunk[t]);null==s||(i?i.mapPos(s):s)!=e.chunkPos[t]||(null==i?void 0:i.touchesRange(s,s+e.chunk[t].length))||n.add(e.chunk[t])}return n}class e_{constructor(e,t,i,r=0){this.layer=e,this.skip=t,this.minPoint=i,this.rank=r}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(e,t=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(e,t,!1),this}gotoInner(e,t,i){for(;this.chunkIndex<this.layer.chunk.length;){let t=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(t)||this.layer.chunkEnd(this.chunkIndex)<e||t.maxPoint<this.minPoint))break;this.chunkIndex++,i=!1}if(this.chunkIndex<this.layer.chunk.length){let r=this.layer.chunk[this.chunkIndex].findIndex(e-this.layer.chunkPos[this.chunkIndex],t,!0);(!i||this.rangeIndex<r)&&this.setRangeIndex(r)}this.next()}forward(e,t){0>(this.to-e||this.endSide-t)&&this.gotoInner(e,t,!0)}next(){for(;;){if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}{let e=this.layer.chunkPos[this.chunkIndex],t=this.layer.chunk[this.chunkIndex],i=e+t.from[this.rangeIndex];if(this.from=i,this.to=e+t.to[this.rangeIndex],this.value=t.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}}setRangeIndex(e){if(e==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=e}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(e){return this.from-e.from||this.startSide-e.startSide||this.rank-e.rank||this.to-e.to||this.endSide-e.endSide}}class eX{constructor(e){this.heap=e}static from(e,t=null,i=-1){let r=[];for(let n=0;n<e.length;n++)for(let s=e[n];!s.isEmpty;s=s.nextLayer)s.maxPoint>=i&&r.push(new e_(s,t,i,n));return 1==r.length?r[0]:new eX(r)}get startSide(){return this.value?this.value.startSide:0}goto(e,t=-1e9){for(let i of this.heap)i.goto(e,t);for(let e=this.heap.length>>1;e>=0;e--)eC(this.heap,e);return this.next(),this}forward(e,t){for(let i of this.heap)i.forward(e,t);for(let e=this.heap.length>>1;e>=0;e--)eC(this.heap,e);0>(this.to-e||this.value.endSide-t)&&this.next()}next(){if(0==this.heap.length)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let e=this.heap[0];this.from=e.from,this.to=e.to,this.value=e.value,this.rank=e.rank,e.value&&e.next(),eC(this.heap,0)}}}function eC(e,t){for(let i=e[t];;){let r=(t<<1)+1;if(r>=e.length)break;let n=e[r];if(r+1<e.length&&n.compare(e[r+1])>=0&&(n=e[r+1],r++),0>i.compare(n))break;e[r]=i,e[t]=n,t=r}}class eA{constructor(e,t,i){this.minPoint=i,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=eX.from(e,t,i)}goto(e,t=-1e9){return this.cursor.goto(e,t),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=e,this.endSide=t,this.openStart=-1,this.next(),this}forward(e,t){for(;this.minActive>-1&&0>(this.activeTo[this.minActive]-e||this.active[this.minActive].endSide-t);)this.removeActive(this.minActive);this.cursor.forward(e,t)}removeActive(e){eM(this.active,e),eM(this.activeTo,e),eM(this.activeRank,e),this.minActive=eY(this.active,this.activeTo)}addActive(e){let t=0,{value:i,to:r,rank:n}=this.cursor;for(;t<this.activeRank.length&&(n-this.activeRank[t]||r-this.activeTo[t])>0;)t++;ej(this.active,t,i),ej(this.activeTo,t,r),ej(this.activeRank,t,n),e&&ej(e,t,this.cursor.from),this.minActive=eY(this.active,this.activeTo)}next(){let e=this.to,t=this.point;this.point=null;let i=this.openStart<0?[]:null;for(;;){let r=this.minActive;if(r>-1&&0>(this.activeTo[r]-this.cursor.from||this.active[r].endSide-this.cursor.startSide)){if(this.activeTo[r]>e){this.to=this.activeTo[r],this.endSide=this.active[r].endSide;break}this.removeActive(r),i&&eM(i,r)}else if(this.cursor.value){if(this.cursor.from>e){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}{let e=this.cursor.value;if(e.point){if(t&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=e,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=e.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else this.addActive(i),this.cursor.next()}}else{this.to=this.endSide=1e9;break}}if(i){this.openStart=0;for(let t=i.length-1;t>=0&&i[t]<e;t--)this.openStart++}}activeForPoint(e){if(!this.active.length)return this.active;let t=[];for(let i=this.active.length-1;i>=0&&!(this.activeRank[i]<this.pointRank);i--)(this.activeTo[i]>e||this.activeTo[i]==e&&this.active[i].endSide>=this.point.endSide)&&t.push(this.active[i]);return t.reverse()}openEnd(e){let t=0;for(let i=this.activeTo.length-1;i>=0&&this.activeTo[i]>e;i--)t++;return t}}function eR(e,t,i,r,n,s){e.goto(t),i.goto(r);let o=r+n,a=r,l=r-t;for(;;){let t=e.to+l-i.to,r=t||e.endSide-i.endSide,n=r<0?e.to+l:i.to,h=Math.min(n,o);if(e.point||i.point?e.point&&i.point&&(e.point==i.point||e.point.eq(i.point))&&eq(e.activeForPoint(e.to),i.activeForPoint(i.to))||s.comparePoint(a,h,e.point,i.point):h>a&&!eq(e.active,i.active)&&s.compareRange(a,h,e.active,i.active),n>o)break;(t||e.openEnd!=i.openEnd)&&s.boundChange&&s.boundChange(n),a=n,r<=0&&e.next(),r>=0&&i.next()}}function eq(e,t){if(e.length!=t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!=t[i]&&!e[i].eq(t[i]))return!1;return!0}function eM(e,t){for(let i=t,r=e.length-1;i<r;i++)e[i]=e[i+1];e.pop()}function ej(e,t,i){for(let i=e.length-1;i>=t;i--)e[i+1]=e[i];e[t]=i}function eY(e,t){let i=-1,r=1e9;for(let n=0;n<t.length;n++)0>(t[n]-r||e[n].endSide-e[i].endSide)&&(i=n,r=t[n]);return i}function eN(e,t,i=e.length){let r=0;for(let n=0;n<i&&n<e.length;)9==e.charCodeAt(n)?(r+=t-r%t,n++):(r++,n=Q(e,n));return r}function eI(e,t,i,r){for(let r=0,n=0;;){if(n>=t)return r;if(r==e.length)break;n+=9==e.charCodeAt(r)?i-n%i:1,r=Q(e,r)}return!0===r?-1:e.length}},8996:function(e,t,i){let r,n;i.d(t,{vk:function(){return e5}});var s,o,a,l,h=i(188);let c=0;class f{constructor(e,t){this.from=e,this.to=t}}class u{constructor(e={}){this.id=c++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=d.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}u.closedBy=new u({deserialize:e=>e.split(" ")}),u.openedBy=new u({deserialize:e=>e.split(" ")}),u.group=new u({deserialize:e=>e.split(" ")}),u.isolate=new u({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw RangeError("Invalid value for isolate: "+e);return e||"auto"}}),u.contextHash=new u({perNode:!0}),u.lookAhead=new u({perNode:!0}),u.mounted=new u({perNode:!0});class p{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[u.mounted.id]}}let O=Object.create(null);class d{constructor(e,t,i,r=0){this.name=e,this.props=t,this.id=i,this.flags=r}static define(e){let t=e.props&&e.props.length?Object.create(null):O,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),r=new d(e.name||"",t,e.id,i);if(e.props){for(let i of e.props)if(Array.isArray(i)||(i=i(r)),i){if(i[0].perNode)throw RangeError("Can't store a per-node prop on a node type");t[i[0].id]=i[1]}}return r}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(u.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let r of i.split(" "))t[r]=e[i];return e=>{for(let i=e.prop(u.group),r=-1;r<(i?i.length:0);r++){let n=t[r<0?e.name:i[r]];if(n)return n}}}}d.none=new d("",Object.create(null),0,8);class g{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let r=null;for(let t of e){let e=t(i);e&&(r||(r=Object.assign({},i.props)),r[e[0].id]=e[1])}t.push(r?new d(i.name,r,i.id,i.flags):i)}return new g(t)}}let m=new WeakMap,y=new WeakMap;(s=a||(a={}))[s.ExcludeBuffers=1]="ExcludeBuffers",s[s.IncludeAnonymous=2]="IncludeAnonymous",s[s.IgnoreMounts=4]="IgnoreMounts",s[s.IgnoreOverlays=8]="IgnoreOverlays";class x{constructor(e,t,i,r,n){if(this.type=e,this.children=t,this.positions=i,this.length=r,this.props=null,n&&n.length)for(let[e,t]of(this.props=Object.create(null),n))this.props["number"==typeof e?e:e.id]=t}toString(){let e=p.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let i=e.toString();i&&(t&&(t+=","),t+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new C(this.topNode,e)}cursorAt(e,t=0,i=0){let r=m.get(this)||this.topNode,n=new C(r);return n.moveTo(e,t),m.set(this,n._tree),n}get topNode(){return new w(this,0,0,null)}resolve(e,t=0){let i=v(m.get(this)||this.topNode,e,t,!1);return m.set(this,i),i}resolveInner(e,t=0){let i=v(y.get(this)||this.topNode,e,t,!0);return y.set(this,i),i}resolveStack(e,t=0){return function(e,t,i){let r=e.resolveInner(t,i),n=null;for(let e=r instanceof w?r:r.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(n||(n=[r])).push(s.resolve(t,i)),e=s}else{let s=p.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new w(s.tree,s.overlay[0].from+e.from,-1,e);(n||(n=[r])).push(v(o,t,i,!1))}}return n?_(n):r}(this,e,t)}iterate(e){let{enter:t,leave:i,from:r=0,to:n=this.length}=e,s=e.mode||0,o=(s&a.IncludeAnonymous)>0;for(let e=this.cursor(s|a.IncludeAnonymous);;){let s=!1;if(e.from<=n&&e.to>=r&&(!o&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&i&&(o||!e.type.isAnonymous)&&i(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:M(d.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new x(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new x(d.none,e,t,i)))}static build(e){return function(e){var t;let{buffer:i,nodeSet:r,maxBufferLength:n=1024,reused:s=[],minRepeatType:o=r.types.length}=e,a=Array.isArray(i)?new k(i,i.length):i,l=r.types,h=0,c=0;function f(e,t,i,n,s,o,a,l,h){let c=[],f=[];for(;e.length>n;)c.push(e.pop()),f.push(t.pop()+i-s);e.push(p(r.types[a],c,f,o-s,l-o,h)),t.push(s-i)}function p(e,t,i,r,n,s,o){if(s){let e=[u.contextHash,s];o=o?[e].concat(o):[e]}if(n>25){let e=[u.lookAhead,n];o=o?[e].concat(o):[e]}return new x(e,t,i,r,o)}let O=[],d=[];for(;a.pos>0;)!function e(t,i,O,d,g,m){let{id:y,start:k,end:b,size:v}=a,S=c,w=h;for(;v<0;){if(a.next(),-1==v){let e=s[y];O.push(e),d.push(k-t);return}if(-3==v){h=y;return}if(-4==v){c=y;return}throw RangeError(`Unrecognized record size: ${v}`)}let $=l[y],P,Z,T=k-t;if(b-k<=n&&(Z=function(e,t){let i=a.fork(),r=0,s=0,l=0,h=i.end-n,c={size:0,start:0,skip:0};e:for(let n=i.pos-e;i.pos>n;){let e=i.size;if(i.id==t&&e>=0){c.size=r,c.start=s,c.skip=l,l+=4,r+=4,i.next();continue}let a=i.pos-e;if(e<0||a<n||i.start<h)break;let f=i.id>=o?4:0,u=i.start;for(i.next();i.pos>a;){if(i.size<0){if(-3==i.size)f+=4;else break e}else i.id>=o&&(f+=4);i.next()}s=u,r+=e,l+=f}return(t<0||r==e)&&(c.size=r,c.start=s,c.skip=l),c.size>4?c:void 0}(a.pos-i,g))){let e=new Uint16Array(Z.size-Z.skip),i=a.pos-Z.size,n=e.length;for(;a.pos>i;)n=function e(t,i,r){let{id:n,start:s,end:l,size:f}=a;if(a.next(),f>=0&&n<o){let o=r;if(f>4){let n=a.pos-(f-4);for(;a.pos>n;)r=e(t,i,r)}i[--r]=o,i[--r]=l-t,i[--r]=s-t,i[--r]=n}else -3==f?h=n:-4==f&&(c=n);return r}(Z.start,e,n);P=new Q(e,b-Z.start,r),T=Z.start-t}else{let t=a.pos-v;a.next();let i=[],s=[],l=y>=o?y:-1,h=0,c=b;for(;a.pos>t;)l>=0&&a.id==l&&a.size>=0?(a.end<=c-n&&(f(i,s,k,h,a.end,c,l,S,w),h=i.length,c=a.end),a.next()):m>2500?function(e,t,i,s){let o=[],l=0,h=-1;for(;a.pos>t;){let{id:e,start:t,end:i,size:r}=a;if(r>4)a.next();else if(h>-1&&t<h)break;else h<0&&(h=i-n),o.push(e,t,i),l++,a.next()}if(l){let t=new Uint16Array(4*l),n=o[o.length-2];for(let e=o.length-3,i=0;e>=0;e-=3)t[i++]=o[e],t[i++]=o[e+1]-n,t[i++]=o[e+2]-n,t[i++]=i;i.push(new Q(t,o[2]-n,r)),s.push(n-e)}}(k,t,i,s):e(k,t,i,s,l,m+1);if(l>=0&&h>0&&h<i.length&&f(i,s,k,h,k,c,l,S,w),i.reverse(),s.reverse(),l>-1&&h>0){let e=function(e,t){return(i,r,n)=>{let s=0,o=i.length-1,a,l;if(o>=0&&(a=i[o])instanceof x){if(!o&&a.type==e&&a.length==n)return a;(l=a.prop(u.lookAhead))&&(s=r[o]+a.length+l)}return p(e,i,r,n,s,t)}}($,w);P=M($,i,s,0,i.length,0,b-k,e,e)}else P=p($,i,s,b-k,S-b,w)}O.push(P),d.push(T)}(e.start||0,e.bufferStart||0,O,d,-1,0);let g=null!==(t=e.length)&&void 0!==t?t:O.length?d[0]+O[0].length:0;return new x(l[e.topID],O.reverse(),d.reverse(),g)}(e)}}x.empty=new x(d.none,[],[],0);class k{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new k(this.buffer,this.index)}}class Q{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return d.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],r=this.set.types[t],n=r.name;if(/\W/.test(n)&&!r.isError&&(n=JSON.stringify(n)),i==(e+=4))return n;let s=[];for(;e<i;)s.push(this.childString(e)),e=this.buffer[e+3];return n+"("+s.join(",")+")"}findChild(e,t,i,r,n){let{buffer:s}=this,o=-1;for(let a=e;a!=t&&(!b(n,r,s[a+1],s[a+2])||(o=a,!(i>0)));a=s[a+3]);return o}slice(e,t,i){let r=this.buffer,n=new Uint16Array(t-e),s=0;for(let o=e,a=0;o<t;){n[a++]=r[o++],n[a++]=r[o++]-i;let t=n[a++]=r[o++]-i;n[a++]=r[o++]-e,s=Math.max(s,t)}return new Q(n,s,this.set)}}function b(e,t,i,r){switch(e){case -2:return i<t;case -1:return r>=t&&i<t;case 0:return i<t&&r>t;case 1:return i<=t&&r>t;case 2:return r>t;case 4:return!0}}function v(e,t,i,r){for(var n;e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t);){let t=!r&&e instanceof w&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=r?0:a.IgnoreOverlays;if(r)for(let r=e,o=r.parent;o;o=(r=o).parent)r instanceof w&&r.index<0&&(null===(n=o.enter(t,i,s))||void 0===n?void 0:n.from)!=r.from&&(e=o);for(;;){let r=e.enter(t,i,s);if(!r)return e;e=r}}class S{cursor(e=0){return new C(this,e)}getChild(e,t=null,i=null){let r=$(this,e,t,i);return r.length?r[0]:null}getChildren(e,t=null,i=null){return $(this,e,t,i)}resolve(e,t=0){return v(this,e,t,!1)}resolveInner(e,t=0){return v(this,e,t,!0)}matchContext(e){return P(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(i=t,t=e.prevSibling):t=e}return i}get node(){return this}get next(){return this.parent}}class w extends S{constructor(e,t,i,r){super(),this._tree=e,this.from=t,this.index=i,this._parent=r}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,r,n=0){for(let s=this;;){for(let{children:o,positions:l}=s._tree,h=t>0?o.length:-1;e!=h;e+=t){let h=o[e],c=l[e]+s.from;if(b(r,i,c,c+h.length)){if(h instanceof Q){if(n&a.ExcludeBuffers)continue;let o=h.findChild(0,h.buffer.length,t,i-c,r);if(o>-1)return new T(new Z(s,h,e,c),null,o)}else if(n&a.IncludeAnonymous||!h.type.isAnonymous||A(h)){let o;if(!(n&a.IgnoreMounts)&&(o=p.get(h))&&!o.overlay)return new w(o.tree,c,e,s);let l=new w(h,c,e,s);return n&a.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(t<0?h.children.length-1:0,t,i,r)}}}if(n&a.IncludeAnonymous||!s.type.isAnonymous||(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,!(s=s._parent)))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let r;if(!(i&a.IgnoreOverlays)&&(r=p.get(this._tree))&&r.overlay){let i=e-this.from;for(let{from:e,to:n}of r.overlay)if((t>0?e<=i:e<i)&&(t<0?n>=i:n>i))return new w(r.tree,r.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function $(e,t,i,r){let n=e.cursor(),s=[];if(!n.firstChild())return s;if(null!=i){for(let e=!1;!e;)if(e=n.type.is(i),!n.nextSibling())return s}for(;;){if(null!=r&&n.type.is(r))return s;if(n.type.is(t)&&s.push(n.node),!n.nextSibling())return null==r?s:[]}}function P(e,t,i=t.length-1){for(let r=e;i>=0;r=r.parent){if(!r)return!1;if(!r.type.isAnonymous){if(t[i]&&t[i]!=r.name)return!1;i--}}return!0}class Z{constructor(e,t,i,r){this.parent=e,this.buffer=t,this.index=i,this.start=r}}class T extends S{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.context.start,i);return n<0?null:new T(this.context,this,n)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&a.ExcludeBuffers)return null;let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return n<0?null:new T(this.context,this,n)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new T(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new T(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,r=this.index+4,n=i.buffer[this.index+3];if(n>r){let s=i.buffer[this.index+1];e.push(i.slice(r,n,s)),t.push(0)}return new x(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function _(e){if(!e.length)return null;let t=0,i=e[0];for(let r=1;r<e.length;r++){let n=e[r];(n.from>i.from||n.to<i.to)&&(i=n,t=r)}let r=i instanceof w&&i.index<0?null:i.parent,n=e.slice();return r?n[t]=r:n.splice(t,1),new X(n,i)}class X{constructor(e,t){this.heads=e,this.node=t}get next(){return _(this.heads)}}class C{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof w)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:r}=this.buffer;return this.type=t||r.set.types[r.buffer[e]],this.from=i+r.buffer[e+1],this.to=i+r.buffer[e+2],!0}yield(e){return!!e&&(e instanceof w?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:r}=this.buffer,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.buffer.start,i);return!(n<0)&&(this.stack.push(this.index),this.yieldBuf(n))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&a.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&a.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&a.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:r}=this;if(r){if(e>0){if(this.index<r.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(r.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=r)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,n=e<0?-1:i._tree.children.length;r!=n;r+=e){let e=i._tree.children[r];if(this.mode&a.IncludeAnonymous||e instanceof Q||!e.type.isAnonymous||A(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let r=this.index,n=this.stack.length;n>=0;){for(let s=e;s;s=s._parent)if(s.index==r){if(r==this.index)return s;t=s,i=n+1;break e}r=this.stack[--n]}for(let e=i;e<this.stack.length;e++)t=new T(this.buffer,t,this.stack[e]);return this.bufferNode=new T(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let r=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(r=!0)}for(;;){if(r&&t&&t(this),r=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,r=!0}}}matchContext(e){if(!this.buffer)return P(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let r=e.length-1,n=this.stack.length-1;r>=0;n--){if(n<0)return P(this._tree,e,r);let s=i[t.buffer[this.stack[n]]];if(!s.isAnonymous){if(e[r]&&e[r]!=s.name)return!1;r--}}return!0}}function A(e){return e.children.some(e=>e instanceof Q||!e.type.isAnonymous||A(e))}let R=new WeakMap;function q(e,t){if(!e.isAnonymous||t instanceof Q||t.type!=e)return 1;let i=R.get(t);if(null==i){for(let r of(i=1,t.children)){if(r.type!=e||!(r instanceof x)){i=1;break}i+=q(e,r)}R.set(t,i)}return i}function M(e,t,i,r,n,s,o,a,l){let h=0;for(let i=r;i<n;i++)h+=q(e,t[i]);let c=Math.ceil(1.5*h/8),f=[],u=[];return!function t(i,r,n,o,a){for(let h=n;h<o;){let n=h,p=r[h],O=q(e,i[h]);for(h++;h<o;h++){let t=q(e,i[h]);if(O+t>=c)break;O+=t}if(h==n+1){if(O>c){let e=i[n];t(e.children,e.positions,0,e.children.length,r[n]+a);continue}f.push(i[n])}else{let t=r[h-1]+i[h-1].length-p;f.push(M(e,i,r,n,h,p,t,null,l))}u.push(p+a-s)}}(t,i,r,n,0),(a||l)(f,u,o)}class j{constructor(e,t,i,r,n=!1,s=!1){this.from=e,this.to=t,this.tree=i,this.offset=r,this.open=(n?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],i=!1){let r=[new j(0,e.length,e,0,!1,i)];for(let i of t)i.to>e.length&&r.push(i);return r}static applyChanges(e,t,i=128){if(!t.length)return e;let r=[],n=1,s=e.length?e[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<t.length?t[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;s&&s.from<c;){let t=s;if(a>=t.from||c<=t.to||l){let e=Math.max(t.from,a)-l,i=Math.min(t.to,c)-l;t=e>=i?null:new j(e,i,t.tree,t.offset+l,o>0,!!h)}if(t&&r.push(t),s.to>c)break;s=n<e.length?e[n++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return r}}class Y{startParse(e,t,i){return"string"==typeof e&&(e=new N(e)),i=i?i.length?i.map(e=>new f(e.from,e.to)):[new f(0,0)]:[new f(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let r=this.startParse(e,t,i);for(;;){let e=r.advance();if(e)return e}}}class N{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new u({perNode:!0});var I=i(7474);let E=0;class z{constructor(e,t,i,r){this.name=e,this.set=t,this.base=i,this.modified=r,this.id=E++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i="string"==typeof e?e:"?";if(e instanceof z&&(t=e),null==t?void 0:t.base)throw Error("Can not derive from a modified tag");let r=new z(i,[],null,[]);if(r.set.push(r),t)for(let e of t.set)r.set.push(e);return r}static defineModifier(e){let t=new L(e);return e=>e.modified.indexOf(t)>-1?e:L.get(e.base||e,e.modified.concat(t).sort((e,t)=>e.id-t.id))}}let V=0;class L{constructor(e){this.name=e,this.instances=[],this.id=V++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>{var r;return i.base==e&&(r=i.modified,t.length==r.length&&t.every((e,t)=>e==r[t]))});if(i)return i;let r=[],n=new z(e.name,r,e,t);for(let e of t)e.instances.push(n);let s=function(e){let t=[[]];for(let i=0;i<e.length;i++)for(let r=0,n=t.length;r<n;r++)t.push(t[r].concat(e[i]));return t.sort((e,t)=>t.length-e.length)}(t);for(let t of e.set)if(!t.modified.length)for(let e of s)r.push(L.get(t,e));return n}}let W=new u;class G{constructor(e,t,i,r){this.tags=e,this.mode=t,this.context=i,this.next=r}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}function B(e,t){let i=Object.create(null);for(let t of e)if(Array.isArray(t.tag))for(let e of t.tag)i[e.id]=t.class;else i[t.tag.id]=t.class;let{scope:r,all:n=null}=t||{};return{style:e=>{let t=n;for(let r of e)for(let e of r.set){let r=i[e.id];if(r){t=t?t+" "+r:r;break}}return t},scope:r}}G.empty=new G([],2,null);class U{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,r,n){let{type:s,from:o,to:a}=e;if(o>=i||a<=t)return;s.isTop&&(n=this.highlighters.filter(e=>!e.scope||e.scope(s)));let l=r,h=function(e){let t=e.type.prop(W);for(;t&&t.context&&!e.matchContext(t.context);)t=t.next;return t||null}(e)||G.empty,c=function(e,t){let i=null;for(let r of e){let e=r.style(t);e&&(i=i?i+" "+e:e)}return i}(n,h.tags);if(c&&(l&&(l+=" "),l+=c,1==h.mode&&(r+=(r?" ":"")+c)),this.startSpan(Math.max(t,o),l),h.opaque)return;let f=e.tree&&e.tree.prop(u.mounted);if(f&&f.overlay){let s=e.node.enter(f.overlay[0].from+o,1),h=this.highlighters.filter(e=>!e.scope||e.scope(f.tree.type)),c=e.firstChild();for(let u=0,p=o;;u++){let O=u<f.overlay.length?f.overlay[u]:null,d=O?O.from+o:a,g=Math.max(t,p),m=Math.min(i,d);if(g<m&&c)for(;e.from<m&&(this.highlightRange(e,g,m,r,n),this.startSpan(Math.min(m,e.to),l),!(e.to>=d)&&e.nextSibling()););if(!O||d>i)break;(p=O.to+o)>t&&(this.highlightRange(s.cursor(),Math.max(t,O.from+o),Math.min(i,p),"",h),this.startSpan(Math.min(i,p),l))}c&&e.parent()}else if(e.firstChild()){f&&(r="");do{if(e.to<=t)continue;if(e.from>=i)break;this.highlightRange(e,t,i,r,n),this.startSpan(Math.min(i,e.to),l)}while(e.nextSibling());e.parent()}}}let D=z.define,J=D(),F=D(),K=D(F),H=D(F),ee=D(),et=D(ee),ei=D(ee),er=D(),en=D(er),es=D(),eo=D(),ea=D(),el=D(ea),eh=D(),ec={comment:J,lineComment:D(J),blockComment:D(J),docComment:D(J),name:F,variableName:D(F),typeName:K,tagName:D(K),propertyName:H,attributeName:D(H),className:D(F),labelName:D(F),namespace:D(F),macroName:D(F),literal:ee,string:et,docString:D(et),character:D(et),attributeValue:D(et),number:ei,integer:D(ei),float:D(ei),bool:D(ee),regexp:D(ee),escape:D(ee),color:D(ee),url:D(ee),keyword:es,self:D(es),null:D(es),atom:D(es),unit:D(es),modifier:D(es),operatorKeyword:D(es),controlKeyword:D(es),definitionKeyword:D(es),moduleKeyword:D(es),operator:eo,derefOperator:D(eo),arithmeticOperator:D(eo),logicOperator:D(eo),bitwiseOperator:D(eo),compareOperator:D(eo),updateOperator:D(eo),definitionOperator:D(eo),typeOperator:D(eo),controlOperator:D(eo),punctuation:ea,separator:D(ea),bracket:el,angleBracket:D(el),squareBracket:D(el),paren:D(el),brace:D(el),content:er,heading:en,heading1:D(en),heading2:D(en),heading3:D(en),heading4:D(en),heading5:D(en),heading6:D(en),contentSeparator:D(er),list:D(er),quote:D(er),emphasis:D(er),strong:D(er),link:D(er),monospace:D(er),strikethrough:D(er),inserted:D(),deleted:D(),changed:D(),invalid:D(),meta:eh,documentMeta:D(eh),annotation:D(eh),processingInstruction:D(eh),definition:z.defineModifier("definition"),constant:z.defineModifier("constant"),function:z.defineModifier("function"),standard:z.defineModifier("standard"),local:z.defineModifier("local"),special:z.defineModifier("special")};for(let e in ec){let t=ec[e];t instanceof z&&(t.name=e)}B([{tag:ec.link,class:"tok-link"},{tag:ec.heading,class:"tok-heading"},{tag:ec.emphasis,class:"tok-emphasis"},{tag:ec.strong,class:"tok-strong"},{tag:ec.keyword,class:"tok-keyword"},{tag:ec.atom,class:"tok-atom"},{tag:ec.bool,class:"tok-bool"},{tag:ec.url,class:"tok-url"},{tag:ec.labelName,class:"tok-labelName"},{tag:ec.inserted,class:"tok-inserted"},{tag:ec.deleted,class:"tok-deleted"},{tag:ec.literal,class:"tok-literal"},{tag:ec.string,class:"tok-string"},{tag:ec.number,class:"tok-number"},{tag:[ec.regexp,ec.escape,ec.special(ec.string)],class:"tok-string2"},{tag:ec.variableName,class:"tok-variableName"},{tag:ec.local(ec.variableName),class:"tok-variableName tok-local"},{tag:ec.definition(ec.variableName),class:"tok-variableName tok-definition"},{tag:ec.special(ec.variableName),class:"tok-variableName2"},{tag:ec.definition(ec.propertyName),class:"tok-propertyName tok-definition"},{tag:ec.typeName,class:"tok-typeName"},{tag:ec.namespace,class:"tok-namespace"},{tag:ec.className,class:"tok-className"},{tag:ec.macroName,class:"tok-macroName"},{tag:ec.propertyName,class:"tok-propertyName"},{tag:ec.operator,class:"tok-operator"},{tag:ec.comment,class:"tok-comment"},{tag:ec.meta,class:"tok-meta"},{tag:ec.invalid,class:"tok-invalid"},{tag:ec.punctuation,class:"tok-punctuation"}]);var ef=i(3237);let eu=new u,ep=new u;class eO{constructor(e,t,i=[],r=""){this.data=e,this.name=r,I.yy.prototype.hasOwnProperty("tree")||Object.defineProperty(I.yy.prototype,"tree",{get(){return eg(this)}}),this.parser=t,this.extension=[ew.of(this),I.yy.languageData.of((e,t,i)=>{let r=ed(e,t,i),n=r.type.prop(eu);if(!n)return[];let s=e.facet(n),o=r.type.prop(ep);if(o){let n=r.resolve(t-r.from,i);for(let t of o)if(t.test(n,e)){let i=e.facet(t.facet);return"replace"==t.type?i:i.concat(s)}}return s})].concat(i)}isActiveAt(e,t,i=-1){return ed(e,t,i).type.prop(eu)==this.data}findRegions(e){let t=e.facet(ew);if((null==t?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],r=(e,t)=>{if(e.prop(eu)==this.data){i.push({from:t,to:t+e.length});return}let n=e.prop(u.mounted);if(n){if(n.tree.prop(eu)==this.data){if(n.overlay)for(let e of n.overlay)i.push({from:e.from+t,to:e.to+t});else i.push({from:t,to:t+e.length});return}if(n.overlay){let e=i.length;if(r(n.tree,n.overlay[0].from+t),i.length>e)return}}for(let i=0;i<e.children.length;i++){let n=e.children[i];n instanceof x&&r(n,e.positions[i]+t)}};return r(eg(e),0),i}get allowsNesting(){return!0}}function ed(e,t,i){let r=e.facet(ew),n=eg(e).topNode;if(!r||r.allowsNesting)for(let e=n;e;e=e.enter(t,i,a.ExcludeBuffers))e.type.isTop&&(n=e);return n}function eg(e){let t=e.field(eO.state,!1);return t?t.tree:x.empty}eO.setState=I.Py.define();class em{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let ey=null;class ex{constructor(e,t,i=[],r,n,s,o,a){this.parser=e,this.state=t,this.fragments=i,this.tree=r,this.treeLen=n,this.viewport=s,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new ex(e,t,[],x.empty,0,i,[],null)}startParse(){return this.parser.startParse(new em(this.state.doc),this.fragments)}work(e,t){return(null!=t&&t>=this.state.doc.length&&(t=void 0),this.tree!=x.empty&&this.isDone(null!=t?t:this.state.doc.length))?(this.takeTree(),!0):this.withContext(()=>{var i;if("number"==typeof e){let t=Date.now()+e;e=()=>Date.now()>t}for(this.parse||(this.parse=this.startParse()),null!=t&&(null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let r=this.parse.advance();if(r){if(this.fragments=this.withoutTempSkipped(j.addTree(r,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=r,this.parse=null,!(this.treeLen<(null!=t?t:this.state.doc.length)))return!0;this.parse=this.startParse()}if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(j.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=ey;ey=this;try{return e()}finally{ey=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=ek(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:r,treeLen:n,viewport:s,skipped:o}=this;if(this.takeTree(),!e.empty){let t=[];if(e.iterChangedRanges((e,i,r,n)=>t.push({fromA:e,toA:i,fromB:r,toB:n})),i=j.applyChanges(i,t),r=x.empty,n=0,s={from:e.mapPos(s.from,-1),to:e.mapPos(s.to,1)},this.skipped.length)for(let t of(o=[],this.skipped)){let i=e.mapPos(t.from,1),r=e.mapPos(t.to,-1);i<r&&o.push({from:i,to:r})}}return new ex(this.parser,t,i,r,n,s,o,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let t=0;t<this.skipped.length;t++){let{from:i,to:r}=this.skipped[t];i<e.to&&r>e.from&&(this.fragments=ek(this.fragments,i,r),this.skipped.splice(t--,1))}return!(this.skipped.length>=t)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends Y{createParse(t,i,r){let n=r[0].from,s=r[r.length-1].to;return{parsedPos:n,advance(){let t=ey;if(t){for(let e of r)t.tempSkipped.push(e);e&&(t.scheduleOn=t.scheduleOn?Promise.all([t.scheduleOn,e]):e)}return this.parsedPos=s,new x(d.none,[],[],s-n)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&0==t[0].from&&t[0].to>=e}static get(){return ey}}function ek(e,t,i){return j.applyChanges(e,[{fromA:t,toA:i,fromB:t,toB:i}])}class eQ{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new eQ(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=ex.create(e.facet(ew).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new eQ(i)}}eO.state=I.QQ.define({create:eQ.init,update(e,t){for(let e of t.effects)if(e.is(eO.setState))return e.value;return t.startState.facet(ew)!=t.state.facet(ew)?eQ.init(t.state):e.apply(t)}});let eb=e=>{let t=setTimeout(()=>e(),500);return()=>clearTimeout(t)};"undefined"!=typeof requestIdleCallback&&(eb=e=>{let t=-1,i=setTimeout(()=>{t=requestIdleCallback(e,{timeout:400})},100);return()=>t<0?clearTimeout(i):cancelIdleCallback(t)});let ev="undefined"!=typeof navigator&&(null===(l=navigator.scheduling)||void 0===l?void 0:l.isInputPending)?()=>navigator.scheduling.isInputPending():null,eS=h.lg.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(eO.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(eO.state);t.tree==t.context.tree&&t.context.isDone(e.doc.length)||(this.working=eb(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:r}}=this.view,n=i.field(eO.state);if(n.tree==n.context.tree&&n.context.isDone(r+1e5))return;let s=Date.now()+Math.min(this.chunkBudget,100,e&&!ev?Math.max(25,e.timeRemaining()-5):1e9),o=n.context.treeLen<r&&i.doc.length>r+1e3,a=n.context.work(()=>ev&&ev()||Date.now()>s,r+(o?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(n.context.takeTree(),this.view.dispatch({effects:eO.setState.of(new eQ(n.context))})),this.chunkBudget>0&&!(a&&!o)&&this.scheduleWork(),this.checkAsyncSchedule(n.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(e=>(0,h.OO)(this.view.state,e)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),ew=I.r$.define({combine:e=>e.length?e[0]:null,enables:e=>[eO.state,eS,h.tk.contentAttributes.compute([e],t=>{let i=t.facet(e);return i&&i.name?{"data-language":i.name}:{}})]});function e$(e,t){let i=t.mapPos(e.from,1),r=t.mapPos(e.to,-1);return i>=r?void 0:{from:i,to:r}}let eP=I.Py.define({map:e$}),eZ=I.Py.define({map:e$}),eT=I.QQ.define({create:()=>h.p.none,update(e,t){for(let i of(t.isUserEvent("delete")&&t.changes.iterChangedRanges((t,i)=>e=e_(e,t,i)),e=e.map(t.changes),t.effects))if(i.is(eP)&&!function(e,t,i){let r=!1;return e.between(t,t,(e,n)=>{e==t&&n==i&&(r=!0)}),r}(e,i.value.from,i.value.to)){let{preparePlaceholder:r}=t.state.facet(eC),n=r?h.p.replace({widget:new eq(r(t.state,i.value))}):eR;e=e.update({add:[n.range(i.value.from,i.value.to)]})}else i.is(eZ)&&(e=e.update({filter:(e,t)=>i.value.from!=e||i.value.to!=t,filterFrom:i.value.from,filterTo:i.value.to}));return t.selection&&(e=e_(e,t.selection.main.head)),e},provide:e=>h.tk.decorations.from(e),toJSON(e,t){let i=[];return e.between(0,t.doc.length,(e,t)=>{i.push(e,t)}),i},fromJSON(e){if(!Array.isArray(e)||e.length%2)throw RangeError("Invalid JSON for fold state");let t=[];for(let i=0;i<e.length;){let r=e[i++],n=e[i++];if("number"!=typeof r||"number"!=typeof n)throw RangeError("Invalid JSON for fold state");t.push(eR.range(r,n))}return h.p.set(t,!0)}});function e_(e,t,i=t){let r=!1;return e.between(t,i,(e,n)=>{e<i&&n>t&&(r=!0)}),r?e.update({filterFrom:t,filterTo:i,filter:(e,r)=>e>=i||r<=t}):e}let eX={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},eC=I.r$.define({combine:e=>(0,I.BO)(e,eX)});function eA(e,t){let{state:i}=e,r=i.facet(eC),n=t=>{var i,r,n,s;let o,a=e.lineBlockAt(e.posAtDOM(t.target)),l=(i=e.state,r=a.from,n=a.to,o=null,null===(s=i.field(eT,!1))||void 0===s||s.between(r,n,(e,t)=>{(!o||o.from>e)&&(o={from:e,to:t})}),o);l&&e.dispatch({effects:eZ.of(l)}),t.preventDefault()};if(r.placeholderDOM)return r.placeholderDOM(e,n,t);let s=document.createElement("span");return s.textContent=r.placeholderText,s.setAttribute("aria-label",i.phrase("folded code")),s.title=i.phrase("unfold"),s.className="cm-foldPlaceholder",s.onclick=n,s}let eR=h.p.replace({widget:new class extends h.l9{toDOM(e){return eA(e,null)}}});class eq extends h.l9{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return eA(e,this.value)}}class eM{constructor(e,t){let i;function r(e){let t=ef.V.newName();return(i||(i=Object.create(null)))["."+t]=e,t}this.specs=e;let n="string"==typeof t.all?t.all:t.all?r(t.all):void 0,s=t.scope;this.scope=s instanceof eO?e=>e.prop(eu)==s.data:s?e=>e==s:void 0,this.style=B(e.map(e=>({tag:e.tag,class:e.class||r(Object.assign({},e,{tag:null}))})),{all:n}).style,this.module=i?new ef.V(i):null,this.themeType=t.themeType}static define(e,t){return new eM(e,t||{})}}let ej=I.r$.define(),eY=I.r$.define({combine:e=>e.length?[e[0]]:null});function eN(e){let t=e.facet(ej);return t.length?t:e.facet(eY)}let eI=I.Wl.high(h.lg.fromClass(class{constructor(e){this.markCache=Object.create(null),this.tree=eg(e.state),this.decorations=this.buildDeco(e,eN(e.state)),this.decoratedTo=e.viewport.to}update(e){let t=eg(e.state),i=eN(e.state),r=i!=eN(e.startState),{viewport:n}=e.view,s=e.changes.mapPos(this.decoratedTo,1);t.length<n.to&&!r&&t.type==this.tree.type&&s>=n.to?(this.decorations=this.decorations.map(e.changes),this.decoratedTo=s):(t!=this.tree||e.viewportChanged||r)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i),this.decoratedTo=n.to)}buildDeco(e,t){if(!t||!this.tree.length)return h.p.none;let i=new I.f_;for(let{from:r,to:n}of e.visibleRanges)!function(e,t,i,r=0,n=e.length){let s=new U(r,Array.isArray(t)?t:[t],i);s.highlightRange(e.cursor(),r,n,"",s.highlighters),s.flush(n)}(this.tree,t,(e,t,r)=>{i.add(e,t,this.markCache[r]||(this.markCache[r]=h.p.mark({class:r})))},r,n);return i.finish()}},{decorations:e=>e.decorations}));ec.meta,ec.link,ec.heading,ec.emphasis,ec.strong,ec.strikethrough,ec.keyword,ec.atom,ec.bool,ec.url,ec.contentSeparator,ec.labelName,ec.literal,ec.inserted,ec.string,ec.deleted,ec.regexp,ec.escape,ec.string,ec.variableName,ec.variableName,ec.typeName,ec.namespace,ec.className,ec.variableName,ec.macroName,ec.propertyName,ec.comment,ec.invalid;let eE=Object.create(null),ez=[d.none],eV=[],eL=Object.create(null),eW=Object.create(null);for(let[e,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])eW[e]=function(e,t){let i=[];for(let r of t.split(" ")){let t=[];for(let i of r.split(".")){let r=e[i]||ec[i];r?"function"==typeof r?t.length?t=t.map(r):eG(i,`Modifier ${i} used at start of tag`):t.length?eG(i,`Tag ${i} used as modifier`):t=Array.isArray(r)?r:[r]:eG(i,`Unknown highlighting tag ${i}`)}for(let e of t)i.push(e)}if(!i.length)return 0;let r=t.replace(/ /g,"_"),n=r+" "+i.map(e=>e.id),s=eL[n];if(s)return s.id;let o=eL[n]=d.define({id:ez.length,name:r,props:[function(e){let t=Object.create(null);for(let i in e){let r=e[i];for(let e of(Array.isArray(r)||(r=[r]),i.split(" ")))if(e){let i=[],n=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){n=1;break}let r=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!r)throw RangeError("Invalid path: "+e);if(i.push("*"==r[0]?"":'"'==r[0][0]?JSON.parse(r[0]):r[0]),(t+=r[0].length)==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){n=0;break}if("/"!=o)throw RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,a=i[o];if(!a)throw RangeError("Invalid path: "+e);let l=new G(r,n,o>0?i.slice(0,o):null);t[a]=l.sort(t[a])}}return W.add(t)}({[r]:i})]});return ez.push(o),o.id}(eE,t);function eG(e,t){eV.indexOf(e)>-1||(eV.push(e),console.warn(t))}h.Nm.RTL,h.Nm.LTR;let eB="#e06c75",eU="#abb2bf",eD="#7d8799",eJ="#d19a66",eF="#2c313a",eK="#282c34",eH="#353a42",e0="#528bff",e1=h.tk.theme({"&":{color:eU,backgroundColor:eK},".cm-content":{caretColor:e0},".cm-cursor, .cm-dropCursor":{borderLeftColor:e0},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection":{backgroundColor:"#3E4451"},".cm-panels":{backgroundColor:"#21252b",color:eU},".cm-panels.cm-panels-top":{borderBottom:"2px solid black"},".cm-panels.cm-panels-bottom":{borderTop:"2px solid black"},".cm-searchMatch":{backgroundColor:"#72a1ff59",outline:"1px solid #457dff"},".cm-searchMatch.cm-searchMatch-selected":{backgroundColor:"#6199ff2f"},".cm-activeLine":{backgroundColor:"#6699ff0b"},".cm-selectionMatch":{backgroundColor:"#aafe661a"},"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bad0f847"},".cm-gutters":{backgroundColor:eK,color:eD,border:"none"},".cm-activeLineGutter":{backgroundColor:eF},".cm-foldPlaceholder":{backgroundColor:"transparent",border:"none",color:"#ddd"},".cm-tooltip":{border:"none",backgroundColor:eH},".cm-tooltip .cm-tooltip-arrow:before":{borderTopColor:"transparent",borderBottomColor:"transparent"},".cm-tooltip .cm-tooltip-arrow:after":{borderTopColor:eH,borderBottomColor:eH},".cm-tooltip-autocomplete":{"& > ul > li[aria-selected]":{backgroundColor:eF,color:eU}}},{dark:!0}),e2=eM.define([{tag:ec.keyword,color:"#c678dd"},{tag:[ec.name,ec.deleted,ec.character,ec.propertyName,ec.macroName],color:eB},{tag:[ec.function(ec.variableName),ec.labelName],color:"#61afef"},{tag:[ec.color,ec.constant(ec.name),ec.standard(ec.name)],color:eJ},{tag:[ec.definition(ec.name),ec.separator],color:eU},{tag:[ec.typeName,ec.className,ec.number,ec.changed,ec.annotation,ec.modifier,ec.self,ec.namespace],color:"#e5c07b"},{tag:[ec.operator,ec.operatorKeyword,ec.url,ec.escape,ec.regexp,ec.link,ec.special(ec.string)],color:"#56b6c2"},{tag:[ec.meta,ec.comment],color:eD},{tag:ec.strong,fontWeight:"bold"},{tag:ec.emphasis,fontStyle:"italic"},{tag:ec.strikethrough,textDecoration:"line-through"},{tag:ec.link,color:eD,textDecoration:"underline"},{tag:ec.heading,fontWeight:"bold",color:eB},{tag:[ec.atom,ec.bool,ec.special(ec.variableName)],color:eJ},{tag:[ec.processingInstruction,ec.string,ec.inserted],color:"#98c379"},{tag:ec.invalid,color:"#ffffff"}]),e5=[e1,(r=[eI],e2 instanceof eM&&(e2.module&&r.push(h.tk.styleModule.of(e2.module)),n=e2.themeType),(null==o?void 0:o.fallback)?r.push(eY.of(e2)):n?r.push(ej.computeN([h.tk.darkTheme],e=>e.facet(h.tk.darkTheme)==("dark"==n)?[e2]:[])):r.push(ej.of(e2)),r)]},5543:function(e,t,i){i.d(t,{Z:function(){return c}});var r=i(7653),n=i(8344),s=i(432),o=i(2741);let a={...o.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var l=i(5236),h=i(5159);let c=r.forwardRef((e,t)=>{let{asChild:i,className:o,...c}=(0,l.y)(e,a,h.E),f=i?s.fC:"div";return r.createElement(f,{ref:t,...c,className:n("rt-reset","rt-BaseCard","rt-Card",o)})});c.displayName="Card"},4480:function(e,t,i){i.d(t,{VY:function(){return j},aV:function(){return q},fC:function(){return R},xz:function(){return M}});var r=i(7653),n=i(8344),s=i(1082),o=i(4036),a=i(306),l=i(7575),h=i(8671),c=i(7205),f=i(7840),u=i(6303),p=i(7573),O="Tabs",[d,g]=(0,o.b)(O,[a.Pc]),m=(0,a.Pc)(),[y,x]=d(O),k=r.forwardRef((e,t)=>{let{__scopeTabs:i,value:r,onValueChange:n,defaultValue:s,orientation:o="horizontal",dir:a,activationMode:l="automatic",...d}=e,g=(0,c.gm)(a),[m,x]=(0,f.T)({prop:r,onChange:n,defaultProp:s??"",caller:O});return(0,p.jsx)(y,{scope:i,baseId:(0,u.M)(),value:m,onValueChange:x,orientation:o,dir:g,activationMode:l,children:(0,p.jsx)(h.WV.div,{dir:g,"data-orientation":o,...d,ref:t})})});k.displayName=O;var Q="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:i,loop:r=!0,...n}=e,s=x(Q,i),o=m(i);return(0,p.jsx)(a.fC,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:r,children:(0,p.jsx)(h.WV.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:t})})});b.displayName=Q;var v="TabsTrigger",S=r.forwardRef((e,t)=>{let{__scopeTabs:i,value:r,disabled:n=!1,...o}=e,l=x(v,i),c=m(i),f=P(l.baseId,r),u=Z(l.baseId,r),O=r===l.value;return(0,p.jsx)(a.ck,{asChild:!0,...c,focusable:!n,active:O,children:(0,p.jsx)(h.WV.button,{type:"button",role:"tab","aria-selected":O,"aria-controls":u,"data-state":O?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:f,...o,ref:t,onMouseDown:(0,s.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,s.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,s.M)(e.onFocus,()=>{let e="manual"!==l.activationMode;O||n||!e||l.onValueChange(r)})})})});S.displayName=v;var w="TabsContent",$=r.forwardRef((e,t)=>{let{__scopeTabs:i,value:n,forceMount:s,children:o,...a}=e,c=x(w,i),f=P(c.baseId,n),u=Z(c.baseId,n),O=n===c.value,d=r.useRef(O);return r.useEffect(()=>{let e=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(l.z,{present:s||O,children:({present:i})=>(0,p.jsx)(h.WV.div,{"data-state":O?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":f,hidden:!i,id:u,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:i&&o})})});function P(e,t){return`${e}-trigger-${t}`}function Z(e,t){return`${e}-content-${t}`}$.displayName=w;var T=i(605),_=i(3579);let X={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end"],responsive:!0},...T.EG,..._.K};var C=i(5236),A=i(5159);let R=r.forwardRef((e,t)=>{let{className:i,...s}=(0,C.y)(e,A.E);return r.createElement(k,{...s,ref:t,className:n("rt-TabsRoot",i)})});R.displayName="Tabs.Root";let q=r.forwardRef((e,t)=>{let{className:i,color:s,...o}=(0,C.y)(e,X,A.E);return r.createElement(b,{"data-accent-color":s,...o,asChild:!1,ref:t,className:n("rt-BaseTabList","rt-TabsList",i)})});q.displayName="Tabs.List";let M=r.forwardRef((e,t)=>{let{className:i,children:s,...o}=e;return r.createElement(S,{...o,asChild:!1,ref:t,className:n("rt-reset","rt-BaseTabListTrigger","rt-TabsTrigger",i)},r.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabsTriggerInner"},s),r.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden"},s))});M.displayName="Tabs.Trigger";let j=r.forwardRef((e,t)=>{let{className:i,...s}=(0,C.y)(e,A.E);return r.createElement($,{...s,ref:t,className:n("rt-TabsContent",i)})});j.displayName="Tabs.Content"},8473:function(e,t,i){i.d(t,{f:function(){return O}});var r=i(7653),n=i(8344),s=i(8556),o=i(605),a=i(6750),l=i(4494),h=i(837);let c={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft"],default:"surface"},...o.EG,...l.I},f={side:{type:"enum",values:["left","right"]},...o.EG,gap:h.l.gap,px:a.i.px,pl:a.i.pl,pr:a.i.pr};var u=i(5236),p=i(5159);let O=r.forwardRef((e,t)=>{let i=r.useRef(null),{children:o,className:a,color:l,radius:h,style:f,...O}=(0,u.y)(e,c,p.E);return r.createElement("div",{"data-accent-color":l,"data-radius":h,style:f,className:n("rt-TextFieldRoot",a),onPointerDown:e=>{let t=e.target;if(t.closest("input, button, a"))return;let r=i.current;if(!r)return;let n=t.closest(`
            .rt-TextFieldSlot[data-side='right'],
            .rt-TextFieldSlot:not([data-side='right']) ~ .rt-TextFieldSlot:not([data-side='left'])
          `)?r.value.length:0;requestAnimationFrame(()=>{try{r.setSelectionRange(n,n)}catch{}r.focus()})}},r.createElement("input",{spellCheck:"false",...O,ref:(0,s.F)(i,t),className:"rt-reset rt-TextFieldInput"}),o)});O.displayName="TextField.Root";let d=r.forwardRef((e,t)=>{let{className:i,color:s,side:o,...a}=(0,u.y)(e,f);return r.createElement("div",{"data-accent-color":s,"data-side":o,...a,ref:t,className:n("rt-TextFieldSlot",i)})});d.displayName="TextField.Slot"},2486:function(e,t,i){function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var r in i)({}).hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e}).apply(null,arguments)}i.d(t,{ZP:function(){return oC}});var n,s,o,a,l,h,c=i(7653),f=i(7474),u=i(188);let p=0;class O{constructor(e,t){this.from=e,this.to=t}}class d{constructor(e={}){this.id=p++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=y.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}d.closedBy=new d({deserialize:e=>e.split(" ")}),d.openedBy=new d({deserialize:e=>e.split(" ")}),d.group=new d({deserialize:e=>e.split(" ")}),d.isolate=new d({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw RangeError("Invalid value for isolate: "+e);return e||"auto"}}),d.contextHash=new d({perNode:!0}),d.lookAhead=new d({perNode:!0}),d.mounted=new d({perNode:!0});class g{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[d.mounted.id]}}let m=Object.create(null);class y{constructor(e,t,i,r=0){this.name=e,this.props=t,this.id=i,this.flags=r}static define(e){let t=e.props&&e.props.length?Object.create(null):m,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),r=new y(e.name||"",t,e.id,i);if(e.props){for(let i of e.props)if(Array.isArray(i)||(i=i(r)),i){if(i[0].perNode)throw RangeError("Can't store a per-node prop on a node type");t[i[0].id]=i[1]}}return r}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(d.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let r of i.split(" "))t[r]=e[i];return e=>{for(let i=e.prop(d.group),r=-1;r<(i?i.length:0);r++){let n=t[r<0?e.name:i[r]];if(n)return n}}}}y.none=new y("",Object.create(null),0,8);class x{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let r=null;for(let t of e){let e=t(i);e&&(r||(r=Object.assign({},i.props)),r[e[0].id]=e[1])}t.push(r?new y(i.name,r,i.id,i.flags):i)}return new x(t)}}let k=new WeakMap,Q=new WeakMap;(n=o||(o={}))[n.ExcludeBuffers=1]="ExcludeBuffers",n[n.IncludeAnonymous=2]="IncludeAnonymous",n[n.IgnoreMounts=4]="IgnoreMounts",n[n.IgnoreOverlays=8]="IgnoreOverlays";class b{constructor(e,t,i,r,n){if(this.type=e,this.children=t,this.positions=i,this.length=r,this.props=null,n&&n.length)for(let[e,t]of(this.props=Object.create(null),n))this.props["number"==typeof e?e:e.id]=t}toString(){let e=g.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let i=e.toString();i&&(t&&(t+=","),t+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new q(this.topNode,e)}cursorAt(e,t=0,i=0){let r=k.get(this)||this.topNode,n=new q(r);return n.moveTo(e,t),k.set(this,n._tree),n}get topNode(){return new Z(this,0,0,null)}resolve(e,t=0){let i=$(k.get(this)||this.topNode,e,t,!1);return k.set(this,i),i}resolveInner(e,t=0){let i=$(Q.get(this)||this.topNode,e,t,!0);return Q.set(this,i),i}resolveStack(e,t=0){return function(e,t,i){let r=e.resolveInner(t,i),n=null;for(let e=r instanceof Z?r:r.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(n||(n=[r])).push(s.resolve(t,i)),e=s}else{let s=g.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new Z(s.tree,s.overlay[0].from+e.from,-1,e);(n||(n=[r])).push($(o,t,i,!1))}}return n?A(n):r}(this,e,t)}iterate(e){let{enter:t,leave:i,from:r=0,to:n=this.length}=e,s=e.mode||0,a=(s&o.IncludeAnonymous)>0;for(let e=this.cursor(s|o.IncludeAnonymous);;){let s=!1;if(e.from<=n&&e.to>=r&&(!a&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&i&&(a||!e.type.isAnonymous)&&i(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:N(y.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new b(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new b(y.none,e,t,i)))}static build(e){return function(e){var t;let{buffer:i,nodeSet:r,maxBufferLength:n=1024,reused:s=[],minRepeatType:o=r.types.length}=e,a=Array.isArray(i)?new v(i,i.length):i,l=r.types,h=0,c=0;function f(e,t,i,n,s,o,a,l,h){let c=[],f=[];for(;e.length>n;)c.push(e.pop()),f.push(t.pop()+i-s);e.push(u(r.types[a],c,f,o-s,l-o,h)),t.push(s-i)}function u(e,t,i,r,n,s,o){if(s){let e=[d.contextHash,s];o=o?[e].concat(o):[e]}if(n>25){let e=[d.lookAhead,n];o=o?[e].concat(o):[e]}return new b(e,t,i,r,o)}let p=[],O=[];for(;a.pos>0;)!function e(t,i,p,O,g,m){let{id:y,start:x,end:k,size:Q}=a,v=c,w=h;for(;Q<0;){if(a.next(),-1==Q){let e=s[y];p.push(e),O.push(x-t);return}if(-3==Q){h=y;return}if(-4==Q){c=y;return}throw RangeError(`Unrecognized record size: ${Q}`)}let $=l[y],P,Z,T=x-t;if(k-x<=n&&(Z=function(e,t){let i=a.fork(),r=0,s=0,l=0,h=i.end-n,c={size:0,start:0,skip:0};e:for(let n=i.pos-e;i.pos>n;){let e=i.size;if(i.id==t&&e>=0){c.size=r,c.start=s,c.skip=l,l+=4,r+=4,i.next();continue}let a=i.pos-e;if(e<0||a<n||i.start<h)break;let f=i.id>=o?4:0,u=i.start;for(i.next();i.pos>a;){if(i.size<0){if(-3==i.size)f+=4;else break e}else i.id>=o&&(f+=4);i.next()}s=u,r+=e,l+=f}return(t<0||r==e)&&(c.size=r,c.start=s,c.skip=l),c.size>4?c:void 0}(a.pos-i,g))){let e=new Uint16Array(Z.size-Z.skip),i=a.pos-Z.size,n=e.length;for(;a.pos>i;)n=function e(t,i,r){let{id:n,start:s,end:l,size:f}=a;if(a.next(),f>=0&&n<o){let o=r;if(f>4){let n=a.pos-(f-4);for(;a.pos>n;)r=e(t,i,r)}i[--r]=o,i[--r]=l-t,i[--r]=s-t,i[--r]=n}else -3==f?h=n:-4==f&&(c=n);return r}(Z.start,e,n);P=new S(e,k-Z.start,r),T=Z.start-t}else{let t=a.pos-Q;a.next();let i=[],s=[],l=y>=o?y:-1,h=0,c=k;for(;a.pos>t;)l>=0&&a.id==l&&a.size>=0?(a.end<=c-n&&(f(i,s,x,h,a.end,c,l,v,w),h=i.length,c=a.end),a.next()):m>2500?function(e,t,i,s){let o=[],l=0,h=-1;for(;a.pos>t;){let{id:e,start:t,end:i,size:r}=a;if(r>4)a.next();else if(h>-1&&t<h)break;else h<0&&(h=i-n),o.push(e,t,i),l++,a.next()}if(l){let t=new Uint16Array(4*l),n=o[o.length-2];for(let e=o.length-3,i=0;e>=0;e-=3)t[i++]=o[e],t[i++]=o[e+1]-n,t[i++]=o[e+2]-n,t[i++]=i;i.push(new S(t,o[2]-n,r)),s.push(n-e)}}(x,t,i,s):e(x,t,i,s,l,m+1);if(l>=0&&h>0&&h<i.length&&f(i,s,x,h,x,c,l,v,w),i.reverse(),s.reverse(),l>-1&&h>0){let e=function(e,t){return(i,r,n)=>{let s=0,o=i.length-1,a,l;if(o>=0&&(a=i[o])instanceof b){if(!o&&a.type==e&&a.length==n)return a;(l=a.prop(d.lookAhead))&&(s=r[o]+a.length+l)}return u(e,i,r,n,s,t)}}($,w);P=N($,i,s,0,i.length,0,k-x,e,e)}else P=u($,i,s,k-x,v-k,w)}p.push(P),O.push(T)}(e.start||0,e.bufferStart||0,p,O,-1,0);let g=null!==(t=e.length)&&void 0!==t?t:p.length?O[0]+p[0].length:0;return new b(l[e.topID],p.reverse(),O.reverse(),g)}(e)}}b.empty=new b(y.none,[],[],0);class v{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new v(this.buffer,this.index)}}class S{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return y.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],r=this.set.types[t],n=r.name;if(/\W/.test(n)&&!r.isError&&(n=JSON.stringify(n)),i==(e+=4))return n;let s=[];for(;e<i;)s.push(this.childString(e)),e=this.buffer[e+3];return n+"("+s.join(",")+")"}findChild(e,t,i,r,n){let{buffer:s}=this,o=-1;for(let a=e;a!=t&&(!w(n,r,s[a+1],s[a+2])||(o=a,!(i>0)));a=s[a+3]);return o}slice(e,t,i){let r=this.buffer,n=new Uint16Array(t-e),s=0;for(let o=e,a=0;o<t;){n[a++]=r[o++],n[a++]=r[o++]-i;let t=n[a++]=r[o++]-i;n[a++]=r[o++]-e,s=Math.max(s,t)}return new S(n,s,this.set)}}function w(e,t,i,r){switch(e){case -2:return i<t;case -1:return r>=t&&i<t;case 0:return i<t&&r>t;case 1:return i<=t&&r>t;case 2:return r>t;case 4:return!0}}function $(e,t,i,r){for(var n;e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t);){let t=!r&&e instanceof Z&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=r?0:o.IgnoreOverlays;if(r)for(let r=e,o=r.parent;o;o=(r=o).parent)r instanceof Z&&r.index<0&&(null===(n=o.enter(t,i,s))||void 0===n?void 0:n.from)!=r.from&&(e=o);for(;;){let r=e.enter(t,i,s);if(!r)return e;e=r}}class P{cursor(e=0){return new q(this,e)}getChild(e,t=null,i=null){let r=T(this,e,t,i);return r.length?r[0]:null}getChildren(e,t=null,i=null){return T(this,e,t,i)}resolve(e,t=0){return $(this,e,t,!1)}resolveInner(e,t=0){return $(this,e,t,!0)}matchContext(e){return _(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(i=t,t=e.prevSibling):t=e}return i}get node(){return this}get next(){return this.parent}}class Z extends P{constructor(e,t,i,r){super(),this._tree=e,this.from=t,this.index=i,this._parent=r}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,r,n=0){for(let s=this;;){for(let{children:a,positions:l}=s._tree,h=t>0?a.length:-1;e!=h;e+=t){let h=a[e],c=l[e]+s.from;if(w(r,i,c,c+h.length)){if(h instanceof S){if(n&o.ExcludeBuffers)continue;let a=h.findChild(0,h.buffer.length,t,i-c,r);if(a>-1)return new C(new X(s,h,e,c),null,a)}else if(n&o.IncludeAnonymous||!h.type.isAnonymous||M(h)){let a;if(!(n&o.IgnoreMounts)&&(a=g.get(h))&&!a.overlay)return new Z(a.tree,c,e,s);let l=new Z(h,c,e,s);return n&o.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(t<0?h.children.length-1:0,t,i,r)}}}if(n&o.IncludeAnonymous||!s.type.isAnonymous||(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,!(s=s._parent)))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let r;if(!(i&o.IgnoreOverlays)&&(r=g.get(this._tree))&&r.overlay){let i=e-this.from;for(let{from:e,to:n}of r.overlay)if((t>0?e<=i:e<i)&&(t<0?n>=i:n>i))return new Z(r.tree,r.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function T(e,t,i,r){let n=e.cursor(),s=[];if(!n.firstChild())return s;if(null!=i){for(let e=!1;!e;)if(e=n.type.is(i),!n.nextSibling())return s}for(;;){if(null!=r&&n.type.is(r))return s;if(n.type.is(t)&&s.push(n.node),!n.nextSibling())return null==r?s:[]}}function _(e,t,i=t.length-1){for(let r=e;i>=0;r=r.parent){if(!r)return!1;if(!r.type.isAnonymous){if(t[i]&&t[i]!=r.name)return!1;i--}}return!0}class X{constructor(e,t,i,r){this.parent=e,this.buffer=t,this.index=i,this.start=r}}class C extends P{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.context.start,i);return n<0?null:new C(this.context,this,n)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&o.ExcludeBuffers)return null;let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return n<0?null:new C(this.context,this,n)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new C(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new C(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,r=this.index+4,n=i.buffer[this.index+3];if(n>r){let s=i.buffer[this.index+1];e.push(i.slice(r,n,s)),t.push(0)}return new b(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function A(e){if(!e.length)return null;let t=0,i=e[0];for(let r=1;r<e.length;r++){let n=e[r];(n.from>i.from||n.to<i.to)&&(i=n,t=r)}let r=i instanceof Z&&i.index<0?null:i.parent,n=e.slice();return r?n[t]=r:n.splice(t,1),new R(n,i)}class R{constructor(e,t){this.heads=e,this.node=t}get next(){return A(this.heads)}}class q{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof Z)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:r}=this.buffer;return this.type=t||r.set.types[r.buffer[e]],this.from=i+r.buffer[e+1],this.to=i+r.buffer[e+2],!0}yield(e){return!!e&&(e instanceof Z?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:r}=this.buffer,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.buffer.start,i);return!(n<0)&&(this.stack.push(this.index),this.yieldBuf(n))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&o.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&o.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&o.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:r}=this;if(r){if(e>0){if(this.index<r.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(r.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=r)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,n=e<0?-1:i._tree.children.length;r!=n;r+=e){let e=i._tree.children[r];if(this.mode&o.IncludeAnonymous||e instanceof S||!e.type.isAnonymous||M(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let r=this.index,n=this.stack.length;n>=0;){for(let s=e;s;s=s._parent)if(s.index==r){if(r==this.index)return s;t=s,i=n+1;break e}r=this.stack[--n]}for(let e=i;e<this.stack.length;e++)t=new C(this.buffer,t,this.stack[e]);return this.bufferNode=new C(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let r=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(r=!0)}for(;;){if(r&&t&&t(this),r=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,r=!0}}}matchContext(e){if(!this.buffer)return _(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let r=e.length-1,n=this.stack.length-1;r>=0;n--){if(n<0)return _(this._tree,e,r);let s=i[t.buffer[this.stack[n]]];if(!s.isAnonymous){if(e[r]&&e[r]!=s.name)return!1;r--}}return!0}}function M(e){return e.children.some(e=>e instanceof S||!e.type.isAnonymous||M(e))}let j=new WeakMap;function Y(e,t){if(!e.isAnonymous||t instanceof S||t.type!=e)return 1;let i=j.get(t);if(null==i){for(let r of(i=1,t.children)){if(r.type!=e||!(r instanceof b)){i=1;break}i+=Y(e,r)}j.set(t,i)}return i}function N(e,t,i,r,n,s,o,a,l){let h=0;for(let i=r;i<n;i++)h+=Y(e,t[i]);let c=Math.ceil(1.5*h/8),f=[],u=[];return!function t(i,r,n,o,a){for(let h=n;h<o;){let n=h,p=r[h],O=Y(e,i[h]);for(h++;h<o;h++){let t=Y(e,i[h]);if(O+t>=c)break;O+=t}if(h==n+1){if(O>c){let e=i[n];t(e.children,e.positions,0,e.children.length,r[n]+a);continue}f.push(i[n])}else{let t=r[h-1]+i[h-1].length-p;f.push(N(e,i,r,n,h,p,t,null,l))}u.push(p+a-s)}}(t,i,r,n,0),(a||l)(f,u,o)}class I{constructor(e,t,i,r,n=!1,s=!1){this.from=e,this.to=t,this.tree=i,this.offset=r,this.open=(n?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],i=!1){let r=[new I(0,e.length,e,0,!1,i)];for(let i of t)i.to>e.length&&r.push(i);return r}static applyChanges(e,t,i=128){if(!t.length)return e;let r=[],n=1,s=e.length?e[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<t.length?t[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;s&&s.from<c;){let t=s;if(a>=t.from||c<=t.to||l){let e=Math.max(t.from,a)-l,i=Math.min(t.to,c)-l;t=e>=i?null:new I(e,i,t.tree,t.offset+l,o>0,!!h)}if(t&&r.push(t),s.to>c)break;s=n<e.length?e[n++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return r}}class E{startParse(e,t,i){return"string"==typeof e&&(e=new z(e)),i=i?i.length?i.map(e=>new O(e.from,e.to)):[new O(0,0)]:[new O(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let r=this.startParse(e,t,i);for(;;){let e=r.advance();if(e)return e}}}class z{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new d({perNode:!0});let V=0;class L{constructor(e,t,i,r){this.name=e,this.set=t,this.base=i,this.modified=r,this.id=V++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i="string"==typeof e?e:"?";if(e instanceof L&&(t=e),null==t?void 0:t.base)throw Error("Can not derive from a modified tag");let r=new L(i,[],null,[]);if(r.set.push(r),t)for(let e of t.set)r.set.push(e);return r}static defineModifier(e){let t=new G(e);return e=>e.modified.indexOf(t)>-1?e:G.get(e.base||e,e.modified.concat(t).sort((e,t)=>e.id-t.id))}}let W=0;class G{constructor(e){this.name=e,this.instances=[],this.id=W++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>{var r;return i.base==e&&(r=i.modified,t.length==r.length&&t.every((e,t)=>e==r[t]))});if(i)return i;let r=[],n=new L(e.name,r,e,t);for(let e of t)e.instances.push(n);let s=function(e){let t=[[]];for(let i=0;i<e.length;i++)for(let r=0,n=t.length;r<n;r++)t.push(t[r].concat(e[i]));return t.sort((e,t)=>t.length-e.length)}(t);for(let t of e.set)if(!t.modified.length)for(let e of s)r.push(G.get(t,e));return n}}let B=new d;class U{constructor(e,t,i,r){this.tags=e,this.mode=t,this.context=i,this.next=r}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}function D(e,t){let i=Object.create(null);for(let t of e)if(Array.isArray(t.tag))for(let e of t.tag)i[e.id]=t.class;else i[t.tag.id]=t.class;let{scope:r,all:n=null}=t||{};return{style:e=>{let t=n;for(let r of e)for(let e of r.set){let r=i[e.id];if(r){t=t?t+" "+r:r;break}}return t},scope:r}}U.empty=new U([],2,null);let J=L.define,F=J(),K=J(),H=J(K),ee=J(K),et=J(),ei=J(et),er=J(et),en=J(),es=J(en),eo=J(),ea=J(),el=J(),eh=J(el),ec=J(),ef={comment:F,lineComment:J(F),blockComment:J(F),docComment:J(F),name:K,variableName:J(K),typeName:H,tagName:J(H),propertyName:ee,attributeName:J(ee),className:J(K),labelName:J(K),namespace:J(K),macroName:J(K),literal:et,string:ei,docString:J(ei),character:J(ei),attributeValue:J(ei),number:er,integer:J(er),float:J(er),bool:J(et),regexp:J(et),escape:J(et),color:J(et),url:J(et),keyword:eo,self:J(eo),null:J(eo),atom:J(eo),unit:J(eo),modifier:J(eo),operatorKeyword:J(eo),controlKeyword:J(eo),definitionKeyword:J(eo),moduleKeyword:J(eo),operator:ea,derefOperator:J(ea),arithmeticOperator:J(ea),logicOperator:J(ea),bitwiseOperator:J(ea),compareOperator:J(ea),updateOperator:J(ea),definitionOperator:J(ea),typeOperator:J(ea),controlOperator:J(ea),punctuation:el,separator:J(el),bracket:eh,angleBracket:J(eh),squareBracket:J(eh),paren:J(eh),brace:J(eh),content:en,heading:es,heading1:J(es),heading2:J(es),heading3:J(es),heading4:J(es),heading5:J(es),heading6:J(es),contentSeparator:J(en),list:J(en),quote:J(en),emphasis:J(en),strong:J(en),link:J(en),monospace:J(en),strikethrough:J(en),inserted:J(),deleted:J(),changed:J(),invalid:J(),meta:ec,documentMeta:J(ec),annotation:J(ec),processingInstruction:J(ec),definition:L.defineModifier("definition"),constant:L.defineModifier("constant"),function:L.defineModifier("function"),standard:L.defineModifier("standard"),local:L.defineModifier("local"),special:L.defineModifier("special")};for(let e in ef){let t=ef[e];t instanceof L&&(t.name=e)}D([{tag:ef.link,class:"tok-link"},{tag:ef.heading,class:"tok-heading"},{tag:ef.emphasis,class:"tok-emphasis"},{tag:ef.strong,class:"tok-strong"},{tag:ef.keyword,class:"tok-keyword"},{tag:ef.atom,class:"tok-atom"},{tag:ef.bool,class:"tok-bool"},{tag:ef.url,class:"tok-url"},{tag:ef.labelName,class:"tok-labelName"},{tag:ef.inserted,class:"tok-inserted"},{tag:ef.deleted,class:"tok-deleted"},{tag:ef.literal,class:"tok-literal"},{tag:ef.string,class:"tok-string"},{tag:ef.number,class:"tok-number"},{tag:[ef.regexp,ef.escape,ef.special(ef.string)],class:"tok-string2"},{tag:ef.variableName,class:"tok-variableName"},{tag:ef.local(ef.variableName),class:"tok-variableName tok-local"},{tag:ef.definition(ef.variableName),class:"tok-variableName tok-definition"},{tag:ef.special(ef.variableName),class:"tok-variableName2"},{tag:ef.definition(ef.propertyName),class:"tok-propertyName tok-definition"},{tag:ef.typeName,class:"tok-typeName"},{tag:ef.namespace,class:"tok-namespace"},{tag:ef.className,class:"tok-className"},{tag:ef.macroName,class:"tok-macroName"},{tag:ef.propertyName,class:"tok-propertyName"},{tag:ef.operator,class:"tok-operator"},{tag:ef.comment,class:"tok-comment"},{tag:ef.meta,class:"tok-meta"},{tag:ef.invalid,class:"tok-invalid"},{tag:ef.punctuation,class:"tok-punctuation"}]);var eu=i(3237);let ep=new d,eO=new d;class ed{constructor(e,t,i=[],r=""){this.data=e,this.name=r,f.yy.prototype.hasOwnProperty("tree")||Object.defineProperty(f.yy.prototype,"tree",{get(){return em(this)}}),this.parser=t,this.extension=[e$.of(this),f.yy.languageData.of((e,t,i)=>{let r=eg(e,t,i),n=r.type.prop(ep);if(!n)return[];let s=e.facet(n),o=r.type.prop(eO);if(o){let n=r.resolve(t-r.from,i);for(let t of o)if(t.test(n,e)){let i=e.facet(t.facet);return"replace"==t.type?i:i.concat(s)}}return s})].concat(i)}isActiveAt(e,t,i=-1){return eg(e,t,i).type.prop(ep)==this.data}findRegions(e){let t=e.facet(e$);if((null==t?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],r=(e,t)=>{if(e.prop(ep)==this.data){i.push({from:t,to:t+e.length});return}let n=e.prop(d.mounted);if(n){if(n.tree.prop(ep)==this.data){if(n.overlay)for(let e of n.overlay)i.push({from:e.from+t,to:e.to+t});else i.push({from:t,to:t+e.length});return}if(n.overlay){let e=i.length;if(r(n.tree,n.overlay[0].from+t),i.length>e)return}}for(let i=0;i<e.children.length;i++){let n=e.children[i];n instanceof b&&r(n,e.positions[i]+t)}};return r(em(e),0),i}get allowsNesting(){return!0}}function eg(e,t,i){let r=e.facet(e$),n=em(e).topNode;if(!r||r.allowsNesting)for(let e=n;e;e=e.enter(t,i,o.ExcludeBuffers))e.type.isTop&&(n=e);return n}function em(e){let t=e.field(ed.state,!1);return t?t.tree:b.empty}ed.setState=f.Py.define();class ey{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let ex=null;class ek{constructor(e,t,i=[],r,n,s,o,a){this.parser=e,this.state=t,this.fragments=i,this.tree=r,this.treeLen=n,this.viewport=s,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new ek(e,t,[],b.empty,0,i,[],null)}startParse(){return this.parser.startParse(new ey(this.state.doc),this.fragments)}work(e,t){return(null!=t&&t>=this.state.doc.length&&(t=void 0),this.tree!=b.empty&&this.isDone(null!=t?t:this.state.doc.length))?(this.takeTree(),!0):this.withContext(()=>{var i;if("number"==typeof e){let t=Date.now()+e;e=()=>Date.now()>t}for(this.parse||(this.parse=this.startParse()),null!=t&&(null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let r=this.parse.advance();if(r){if(this.fragments=this.withoutTempSkipped(I.addTree(r,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=r,this.parse=null,!(this.treeLen<(null!=t?t:this.state.doc.length)))return!0;this.parse=this.startParse()}if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(I.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=ex;ex=this;try{return e()}finally{ex=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=eQ(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:r,treeLen:n,viewport:s,skipped:o}=this;if(this.takeTree(),!e.empty){let t=[];if(e.iterChangedRanges((e,i,r,n)=>t.push({fromA:e,toA:i,fromB:r,toB:n})),i=I.applyChanges(i,t),r=b.empty,n=0,s={from:e.mapPos(s.from,-1),to:e.mapPos(s.to,1)},this.skipped.length)for(let t of(o=[],this.skipped)){let i=e.mapPos(t.from,1),r=e.mapPos(t.to,-1);i<r&&o.push({from:i,to:r})}}return new ek(this.parser,t,i,r,n,s,o,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let t=0;t<this.skipped.length;t++){let{from:i,to:r}=this.skipped[t];i<e.to&&r>e.from&&(this.fragments=eQ(this.fragments,i,r),this.skipped.splice(t--,1))}return!(this.skipped.length>=t)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends E{createParse(t,i,r){let n=r[0].from,s=r[r.length-1].to;return{parsedPos:n,advance(){let t=ex;if(t){for(let e of r)t.tempSkipped.push(e);e&&(t.scheduleOn=t.scheduleOn?Promise.all([t.scheduleOn,e]):e)}return this.parsedPos=s,new b(y.none,[],[],s-n)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&0==t[0].from&&t[0].to>=e}static get(){return ex}}function eQ(e,t,i){return I.applyChanges(e,[{fromA:t,toA:i,fromB:t,toB:i}])}class eb{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new eb(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=ek.create(e.facet(e$).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new eb(i)}}ed.state=f.QQ.define({create:eb.init,update(e,t){for(let e of t.effects)if(e.is(ed.setState))return e.value;return t.startState.facet(e$)!=t.state.facet(e$)?eb.init(t.state):e.apply(t)}});let ev=e=>{let t=setTimeout(()=>e(),500);return()=>clearTimeout(t)};"undefined"!=typeof requestIdleCallback&&(ev=e=>{let t=-1,i=setTimeout(()=>{t=requestIdleCallback(e,{timeout:400})},100);return()=>t<0?clearTimeout(i):cancelIdleCallback(t)});let eS="undefined"!=typeof navigator&&(null===(a=navigator.scheduling)||void 0===a?void 0:a.isInputPending)?()=>navigator.scheduling.isInputPending():null,ew=u.lg.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(ed.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(ed.state);t.tree==t.context.tree&&t.context.isDone(e.doc.length)||(this.working=ev(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:r}}=this.view,n=i.field(ed.state);if(n.tree==n.context.tree&&n.context.isDone(r+1e5))return;let s=Date.now()+Math.min(this.chunkBudget,100,e&&!eS?Math.max(25,e.timeRemaining()-5):1e9),o=n.context.treeLen<r&&i.doc.length>r+1e3,a=n.context.work(()=>eS&&eS()||Date.now()>s,r+(o?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(n.context.takeTree(),this.view.dispatch({effects:ed.setState.of(new eb(n.context))})),this.chunkBudget>0&&!(a&&!o)&&this.scheduleWork(),this.checkAsyncSchedule(n.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(e=>(0,u.OO)(this.view.state,e)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),e$=f.r$.define({combine:e=>e.length?e[0]:null,enables:e=>[ed.state,ew,u.tk.contentAttributes.compute([e],t=>{let i=t.facet(e);return i&&i.name?{"data-language":i.name}:{}})]}),eP=f.r$.define({combine:e=>{if(!e.length)return"  ";let t=e[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw Error("Invalid indent unit: "+JSON.stringify(e[0]));return t}});function eZ(e,t){let i=t.mapPos(e.from,1),r=t.mapPos(e.to,-1);return i>=r?void 0:{from:i,to:r}}let eT=f.Py.define({map:eZ}),e_=f.Py.define({map:eZ}),eX=f.QQ.define({create:()=>u.p.none,update(e,t){for(let i of(t.isUserEvent("delete")&&t.changes.iterChangedRanges((t,i)=>e=eC(e,t,i)),e=e.map(t.changes),t.effects))if(i.is(eT)&&!function(e,t,i){let r=!1;return e.between(t,t,(e,n)=>{e==t&&n==i&&(r=!0)}),r}(e,i.value.from,i.value.to)){let{preparePlaceholder:r}=t.state.facet(eR),n=r?u.p.replace({widget:new ej(r(t.state,i.value))}):eM;e=e.update({add:[n.range(i.value.from,i.value.to)]})}else i.is(e_)&&(e=e.update({filter:(e,t)=>i.value.from!=e||i.value.to!=t,filterFrom:i.value.from,filterTo:i.value.to}));return t.selection&&(e=eC(e,t.selection.main.head)),e},provide:e=>u.tk.decorations.from(e),toJSON(e,t){let i=[];return e.between(0,t.doc.length,(e,t)=>{i.push(e,t)}),i},fromJSON(e){if(!Array.isArray(e)||e.length%2)throw RangeError("Invalid JSON for fold state");let t=[];for(let i=0;i<e.length;){let r=e[i++],n=e[i++];if("number"!=typeof r||"number"!=typeof n)throw RangeError("Invalid JSON for fold state");t.push(eM.range(r,n))}return u.p.set(t,!0)}});function eC(e,t,i=t){let r=!1;return e.between(t,i,(e,n)=>{e<i&&n>t&&(r=!0)}),r?e.update({filterFrom:t,filterTo:i,filter:(e,r)=>e>=i||r<=t}):e}let eA={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},eR=f.r$.define({combine:e=>(0,f.BO)(e,eA)});function eq(e,t){let{state:i}=e,r=i.facet(eR),n=t=>{var i,r,n,s;let o,a=e.lineBlockAt(e.posAtDOM(t.target)),l=(i=e.state,r=a.from,n=a.to,o=null,null===(s=i.field(eX,!1))||void 0===s||s.between(r,n,(e,t)=>{(!o||o.from>e)&&(o={from:e,to:t})}),o);l&&e.dispatch({effects:e_.of(l)}),t.preventDefault()};if(r.placeholderDOM)return r.placeholderDOM(e,n,t);let s=document.createElement("span");return s.textContent=r.placeholderText,s.setAttribute("aria-label",i.phrase("folded code")),s.title=i.phrase("unfold"),s.className="cm-foldPlaceholder",s.onclick=n,s}let eM=u.p.replace({widget:new class extends u.l9{toDOM(e){return eq(e,null)}}});class ej extends u.l9{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return eq(e,this.value)}}class eY{constructor(e,t){let i;function r(e){let t=eu.V.newName();return(i||(i=Object.create(null)))["."+t]=e,t}this.specs=e;let n="string"==typeof t.all?t.all:t.all?r(t.all):void 0,s=t.scope;this.scope=s instanceof ed?e=>e.prop(ep)==s.data:s?e=>e==s:void 0,this.style=D(e.map(e=>({tag:e.tag,class:e.class||r(Object.assign({},e,{tag:null}))})),{all:n}).style,this.module=i?new eu.V(i):null,this.themeType=t.themeType}static define(e,t){return new eY(e,t||{})}}ef.meta,ef.link,ef.heading,ef.emphasis,ef.strong,ef.strikethrough,ef.keyword,ef.atom,ef.bool,ef.url,ef.contentSeparator,ef.labelName,ef.literal,ef.inserted,ef.string,ef.deleted,ef.regexp,ef.escape,ef.string,ef.variableName,ef.variableName,ef.typeName,ef.namespace,ef.className,ef.variableName,ef.macroName,ef.propertyName,ef.comment,ef.invalid;let eN=Object.create(null),eI=[y.none],eE=[],ez=Object.create(null),eV=Object.create(null);for(let[e,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])eV[e]=function(e,t){let i=[];for(let r of t.split(" ")){let t=[];for(let i of r.split(".")){let r=e[i]||ef[i];r?"function"==typeof r?t.length?t=t.map(r):eL(i,`Modifier ${i} used at start of tag`):t.length?eL(i,`Tag ${i} used as modifier`):t=Array.isArray(r)?r:[r]:eL(i,`Unknown highlighting tag ${i}`)}for(let e of t)i.push(e)}if(!i.length)return 0;let r=t.replace(/ /g,"_"),n=r+" "+i.map(e=>e.id),s=ez[n];if(s)return s.id;let o=ez[n]=y.define({id:eI.length,name:r,props:[function(e){let t=Object.create(null);for(let i in e){let r=e[i];for(let e of(Array.isArray(r)||(r=[r]),i.split(" ")))if(e){let i=[],n=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){n=1;break}let r=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!r)throw RangeError("Invalid path: "+e);if(i.push("*"==r[0]?"":'"'==r[0][0]?JSON.parse(r[0]):r[0]),(t+=r[0].length)==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){n=0;break}if("/"!=o)throw RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,a=i[o];if(!a)throw RangeError("Invalid path: "+e);let l=new U(r,n,o>0?i.slice(0,o):null);t[a]=l.sort(t[a])}}return B.add(t)}({[r]:i})]});return eI.push(o),o.id}(eN,t);function eL(e,t){eE.indexOf(e)>-1||(eE.push(e),console.warn(t))}u.Nm.RTL,u.Nm.LTR;let eW=f.q6.define(),eG=f.r$.define();class eB{constructor(e,t,i,r,n){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=r,this.selectionsAfter=n}setSelAfter(e){return new eB(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:null===(e=this.changes)||void 0===e?void 0:e.toJSON(),mapped:null===(t=this.mapped)||void 0===t?void 0:t.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(e=>e.toJSON())}}static fromJSON(e){return new eB(e.changes&&f.as.fromJSON(e.changes),[],e.mapped&&f.n0.fromJSON(e.mapped),e.startSelection&&f.jT.fromJSON(e.startSelection),e.selectionsAfter.map(f.jT.fromJSON))}static fromTransaction(e,t){let i=eJ;for(let t of e.startState.facet(eG)){let r=t(e);r.length&&(i=i.concat(r))}return!i.length&&e.changes.empty?null:new eB(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,eJ)}static selection(e){return new eB(void 0,eJ,void 0,void 0,e)}}function eU(e,t,i,r){let n=t+1>i+20?t-i-1:0,s=e.slice(n,t);return s.push(r),s}function eD(e,t){return e.length?t.length?e.concat(t):e:t}let eJ=[];function eF(e,t){if(!e.length)return e;let i=e.length,r=eJ;for(;i;){let n=function(e,t,i){let r=eD(e.selectionsAfter.length?e.selectionsAfter.map(e=>e.map(t)):eJ,i);if(!e.changes)return eB.selection(r);let n=e.changes.map(t),s=t.mapDesc(e.changes,!0),o=e.mapped?e.mapped.composeDesc(s):s;return new eB(n,f.Py.mapEffects(e.effects,t),o,e.startSelection.map(s),r)}(e[i-1],t,r);if(n.changes&&!n.changes.empty||n.effects.length){let t=e.slice(0,i);return t[i-1]=n,t}t=n.mapped,i--,r=n.selectionsAfter}return r.length?[eB.selection(r)]:eJ}let eK=/^(input\.type|delete)($|\.)/;class eH{constructor(e,t,i=0,r){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=r}isolate(){return this.prevTime?new eH(this.done,this.undone):this}addChanges(e,t,i,r,n){var s,o;let a,l,h=this.done,c=h[h.length-1];return h=c&&c.changes&&!c.changes.empty&&e.changes&&(!i||eK.test(i))&&(!c.selectionsAfter.length&&t-this.prevTime<r.newGroupDelay&&r.joinToEvent(n,(s=c.changes,o=e.changes,a=[],l=!1,s.iterChangedRanges((e,t)=>a.push(e,t)),o.iterChangedRanges((e,t,i,r)=>{for(let e=0;e<a.length;){let t=a[e++],n=a[e++];r>=t&&i<=n&&(l=!0)}}),l))||"input.type.compose"==i)?eU(h,h.length-1,r.minDepth,new eB(e.changes.compose(c.changes),eD(f.Py.mapEffects(e.effects,c.changes),c.effects),c.mapped,c.startSelection,eJ)):eU(h,h.length,r.minDepth,e),new eH(h,eJ,t,i)}addSelection(e,t,i,r){var n;let s=this.done.length?this.done[this.done.length-1].selectionsAfter:eJ;return s.length>0&&t-this.prevTime<r&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(n=s[s.length-1]).ranges.length==e.ranges.length&&0===n.ranges.filter((t,i)=>t.empty!=e.ranges[i].empty).length?this:new eH(function(e,t){if(!e.length)return[eB.selection([t])];{let i=e[e.length-1],r=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-200));return r.length&&r[r.length-1].eq(t)?e:(r.push(t),eU(e,e.length-1,1e9,i.setSelAfter(r)))}}(this.done,e),this.undone,t,i)}addMapping(e){return new eH(eF(this.done,e),eF(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let r=0==e?this.done:this.undone;if(0==r.length)return null;let n=r[r.length-1],s=n.selectionsAfter[0]||t.selection;if(i&&n.selectionsAfter.length){let i,o;return t.update({selection:n.selectionsAfter[n.selectionsAfter.length-1],annotations:eW.of({side:e,rest:(i=r[r.length-1],(o=r.slice())[r.length-1]=i.setSelAfter(i.selectionsAfter.slice(0,i.selectionsAfter.length-1)),o),selection:s}),userEvent:0==e?"select.undo":"select.redo",scrollIntoView:!0})}if(!n.changes)return null;{let i=1==r.length?eJ:r.slice(0,r.length-1);return n.mapped&&(i=eF(i,n.mapped)),t.update({changes:n.changes,selection:n.startSelection,effects:n.effects,annotations:eW.of({side:e,rest:i,selection:s}),filter:!1,userEvent:0==e?"undo":"redo",scrollIntoView:!0})}}}function e0(e,t){return f.jT.create(e.ranges.map(t),e.mainIndex)}function e1(e,t){return e.update({selection:t,scrollIntoView:!0,userEvent:"select"})}eH.empty=new eH(eJ,eJ),"undefined"!=typeof Intl&&Intl.Segmenter;function e2(e,t,i){if(e instanceof u.tk)for(let r of e.state.facet(u.tk.atomicRanges).map(t=>t(e)))r.between(t,t,(e,r)=>{e<t&&r>t&&(t=i?r:e)});return t}function e5(e,t){let i=-1;return e.changeByRange(r=>{let n=[];for(let s=r.from;s<=r.to;){let o=e.doc.lineAt(s);o.number>i&&(r.empty||r.to>o.from)&&(t(o,n,r),i=o.number),s=o.to+1}let s=e.changes(n);return{changes:n,range:f.jT.range(s.mapPos(r.anchor,1),s.mapPos(r.head,1))}})}let e4={key:"Tab",run:({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(e5(e,(t,i)=>{i.push({from:t.from,insert:e.facet(eP)})}),{userEvent:"input.indent"})),!0),shift:({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(e5(e,(t,i)=>{var r;let n,s=/^\s*/.exec(t.text)[0];if(!s)return;let o=(0,f.IS)(s,e.tabSize),a=0,l=function(e,t){let i="",r=e.tabSize,n=e.facet(eP)[0];if("	"==n){for(;t>=r;)i+="	",t-=r;n=" "}for(let e=0;e<t;e++)i+=n;return i}(e,Math.max(0,o-(9==(n=(r=e).facet(eP)).charCodeAt(0)?r.tabSize*n.length:n.length)));for(;a<s.length&&a<l.length&&s.charCodeAt(a)==l.charCodeAt(a);)a++;i.push({from:t.from+a,to:t.from+s.length,insert:l.slice(a)})}),{userEvent:"delete.dedent"})),!0)},e3=0;class e7{constructor(e,t){this.from=e,this.to=t}}class e9{constructor(e={}){this.id=e3++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=te.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}e9.closedBy=new e9({deserialize:e=>e.split(" ")}),e9.openedBy=new e9({deserialize:e=>e.split(" ")}),e9.group=new e9({deserialize:e=>e.split(" ")}),e9.isolate=new e9({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw RangeError("Invalid value for isolate: "+e);return e||"auto"}}),e9.contextHash=new e9({perNode:!0}),e9.lookAhead=new e9({perNode:!0}),e9.mounted=new e9({perNode:!0});class e6{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[e9.mounted.id]}}let e8=Object.create(null);class te{constructor(e,t,i,r=0){this.name=e,this.props=t,this.id=i,this.flags=r}static define(e){let t=e.props&&e.props.length?Object.create(null):e8,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),r=new te(e.name||"",t,e.id,i);if(e.props){for(let i of e.props)if(Array.isArray(i)||(i=i(r)),i){if(i[0].perNode)throw RangeError("Can't store a per-node prop on a node type");t[i[0].id]=i[1]}}return r}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(e9.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let r of i.split(" "))t[r]=e[i];return e=>{for(let i=e.prop(e9.group),r=-1;r<(i?i.length:0);r++){let n=t[r<0?e.name:i[r]];if(n)return n}}}}te.none=new te("",Object.create(null),0,8);class tt{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let r=null;for(let t of e){let e=t(i);e&&(r||(r=Object.assign({},i.props)),r[e[0].id]=e[1])}t.push(r?new te(i.name,r,i.id,i.flags):i)}return new tt(t)}}let ti=new WeakMap,tr=new WeakMap;(s=l||(l={}))[s.ExcludeBuffers=1]="ExcludeBuffers",s[s.IncludeAnonymous=2]="IncludeAnonymous",s[s.IgnoreMounts=4]="IgnoreMounts",s[s.IgnoreOverlays=8]="IgnoreOverlays";class tn{constructor(e,t,i,r,n){if(this.type=e,this.children=t,this.positions=i,this.length=r,this.props=null,n&&n.length)for(let[e,t]of(this.props=Object.create(null),n))this.props["number"==typeof e?e:e.id]=t}toString(){let e=e6.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let i=e.toString();i&&(t&&(t+=","),t+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new tm(this.topNode,e)}cursorAt(e,t=0,i=0){let r=ti.get(this)||this.topNode,n=new tm(r);return n.moveTo(e,t),ti.set(this,n._tree),n}get topNode(){return new tc(this,0,0,null)}resolve(e,t=0){let i=tl(ti.get(this)||this.topNode,e,t,!1);return ti.set(this,i),i}resolveInner(e,t=0){let i=tl(tr.get(this)||this.topNode,e,t,!0);return tr.set(this,i),i}resolveStack(e,t=0){return function(e,t,i){let r=e.resolveInner(t,i),n=null;for(let e=r instanceof tc?r:r.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(n||(n=[r])).push(s.resolve(t,i)),e=s}else{let s=e6.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new tc(s.tree,s.overlay[0].from+e.from,-1,e);(n||(n=[r])).push(tl(o,t,i,!1))}}return n?td(n):r}(this,e,t)}iterate(e){let{enter:t,leave:i,from:r=0,to:n=this.length}=e,s=e.mode||0,o=(s&l.IncludeAnonymous)>0;for(let e=this.cursor(s|l.IncludeAnonymous);;){let s=!1;if(e.from<=n&&e.to>=r&&(!o&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&i&&(o||!e.type.isAnonymous)&&i(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:tQ(te.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new tn(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new tn(te.none,e,t,i)))}static build(e){return function(e){var t;let{buffer:i,nodeSet:r,maxBufferLength:n=1024,reused:s=[],minRepeatType:o=r.types.length}=e,a=Array.isArray(i)?new ts(i,i.length):i,l=r.types,h=0,c=0;function f(e,t,i,n,s,o,a,l,h){let c=[],f=[];for(;e.length>n;)c.push(e.pop()),f.push(t.pop()+i-s);e.push(u(r.types[a],c,f,o-s,l-o,h)),t.push(s-i)}function u(e,t,i,r,n,s,o){if(s){let e=[e9.contextHash,s];o=o?[e].concat(o):[e]}if(n>25){let e=[e9.lookAhead,n];o=o?[e].concat(o):[e]}return new tn(e,t,i,r,o)}let p=[],O=[];for(;a.pos>0;)!function e(t,i,p,O,d,g){let{id:m,start:y,end:x,size:k}=a,Q=c,b=h;for(;k<0;){if(a.next(),-1==k){let e=s[m];p.push(e),O.push(y-t);return}if(-3==k){h=m;return}if(-4==k){c=m;return}throw RangeError(`Unrecognized record size: ${k}`)}let v=l[m],S,w,$=y-t;if(x-y<=n&&(w=function(e,t){let i=a.fork(),r=0,s=0,l=0,h=i.end-n,c={size:0,start:0,skip:0};e:for(let n=i.pos-e;i.pos>n;){let e=i.size;if(i.id==t&&e>=0){c.size=r,c.start=s,c.skip=l,l+=4,r+=4,i.next();continue}let a=i.pos-e;if(e<0||a<n||i.start<h)break;let f=i.id>=o?4:0,u=i.start;for(i.next();i.pos>a;){if(i.size<0){if(-3==i.size)f+=4;else break e}else i.id>=o&&(f+=4);i.next()}s=u,r+=e,l+=f}return(t<0||r==e)&&(c.size=r,c.start=s,c.skip=l),c.size>4?c:void 0}(a.pos-i,d))){let e=new Uint16Array(w.size-w.skip),i=a.pos-w.size,n=e.length;for(;a.pos>i;)n=function e(t,i,r){let{id:n,start:s,end:l,size:f}=a;if(a.next(),f>=0&&n<o){let o=r;if(f>4){let n=a.pos-(f-4);for(;a.pos>n;)r=e(t,i,r)}i[--r]=o,i[--r]=l-t,i[--r]=s-t,i[--r]=n}else -3==f?h=n:-4==f&&(c=n);return r}(w.start,e,n);S=new to(e,x-w.start,r),$=w.start-t}else{let t=a.pos-k;a.next();let i=[],s=[],l=m>=o?m:-1,h=0,c=x;for(;a.pos>t;)l>=0&&a.id==l&&a.size>=0?(a.end<=c-n&&(f(i,s,y,h,a.end,c,l,Q,b),h=i.length,c=a.end),a.next()):g>2500?function(e,t,i,s){let o=[],l=0,h=-1;for(;a.pos>t;){let{id:e,start:t,end:i,size:r}=a;if(r>4)a.next();else if(h>-1&&t<h)break;else h<0&&(h=i-n),o.push(e,t,i),l++,a.next()}if(l){let t=new Uint16Array(4*l),n=o[o.length-2];for(let e=o.length-3,i=0;e>=0;e-=3)t[i++]=o[e],t[i++]=o[e+1]-n,t[i++]=o[e+2]-n,t[i++]=i;i.push(new to(t,o[2]-n,r)),s.push(n-e)}}(y,t,i,s):e(y,t,i,s,l,g+1);if(l>=0&&h>0&&h<i.length&&f(i,s,y,h,y,c,l,Q,b),i.reverse(),s.reverse(),l>-1&&h>0){let e=function(e,t){return(i,r,n)=>{let s=0,o=i.length-1,a,l;if(o>=0&&(a=i[o])instanceof tn){if(!o&&a.type==e&&a.length==n)return a;(l=a.prop(e9.lookAhead))&&(s=r[o]+a.length+l)}return u(e,i,r,n,s,t)}}(v,b);S=tQ(v,i,s,0,i.length,0,x-y,e,e)}else S=u(v,i,s,x-y,Q-x,b)}p.push(S),O.push($)}(e.start||0,e.bufferStart||0,p,O,-1,0);let d=null!==(t=e.length)&&void 0!==t?t:p.length?O[0]+p[0].length:0;return new tn(l[e.topID],p.reverse(),O.reverse(),d)}(e)}}tn.empty=new tn(te.none,[],[],0);class ts{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new ts(this.buffer,this.index)}}class to{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return te.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],r=this.set.types[t],n=r.name;if(/\W/.test(n)&&!r.isError&&(n=JSON.stringify(n)),i==(e+=4))return n;let s=[];for(;e<i;)s.push(this.childString(e)),e=this.buffer[e+3];return n+"("+s.join(",")+")"}findChild(e,t,i,r,n){let{buffer:s}=this,o=-1;for(let a=e;a!=t&&(!ta(n,r,s[a+1],s[a+2])||(o=a,!(i>0)));a=s[a+3]);return o}slice(e,t,i){let r=this.buffer,n=new Uint16Array(t-e),s=0;for(let o=e,a=0;o<t;){n[a++]=r[o++],n[a++]=r[o++]-i;let t=n[a++]=r[o++]-i;n[a++]=r[o++]-e,s=Math.max(s,t)}return new to(n,s,this.set)}}function ta(e,t,i,r){switch(e){case -2:return i<t;case -1:return r>=t&&i<t;case 0:return i<t&&r>t;case 1:return i<=t&&r>t;case 2:return r>t;case 4:return!0}}function tl(e,t,i,r){for(var n;e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t);){let t=!r&&e instanceof tc&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=r?0:l.IgnoreOverlays;if(r)for(let r=e,o=r.parent;o;o=(r=o).parent)r instanceof tc&&r.index<0&&(null===(n=o.enter(t,i,s))||void 0===n?void 0:n.from)!=r.from&&(e=o);for(;;){let r=e.enter(t,i,s);if(!r)return e;e=r}}class th{cursor(e=0){return new tm(this,e)}getChild(e,t=null,i=null){let r=tf(this,e,t,i);return r.length?r[0]:null}getChildren(e,t=null,i=null){return tf(this,e,t,i)}resolve(e,t=0){return tl(this,e,t,!1)}resolveInner(e,t=0){return tl(this,e,t,!0)}matchContext(e){return tu(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(i=t,t=e.prevSibling):t=e}return i}get node(){return this}get next(){return this.parent}}class tc extends th{constructor(e,t,i,r){super(),this._tree=e,this.from=t,this.index=i,this._parent=r}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,r,n=0){for(let s=this;;){for(let{children:o,positions:a}=s._tree,h=t>0?o.length:-1;e!=h;e+=t){let h=o[e],c=a[e]+s.from;if(ta(r,i,c,c+h.length)){if(h instanceof to){if(n&l.ExcludeBuffers)continue;let o=h.findChild(0,h.buffer.length,t,i-c,r);if(o>-1)return new tO(new tp(s,h,e,c),null,o)}else if(n&l.IncludeAnonymous||!h.type.isAnonymous||ty(h)){let o;if(!(n&l.IgnoreMounts)&&(o=e6.get(h))&&!o.overlay)return new tc(o.tree,c,e,s);let a=new tc(h,c,e,s);return n&l.IncludeAnonymous||!a.type.isAnonymous?a:a.nextChild(t<0?h.children.length-1:0,t,i,r)}}}if(n&l.IncludeAnonymous||!s.type.isAnonymous||(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,!(s=s._parent)))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let r;if(!(i&l.IgnoreOverlays)&&(r=e6.get(this._tree))&&r.overlay){let i=e-this.from;for(let{from:e,to:n}of r.overlay)if((t>0?e<=i:e<i)&&(t<0?n>=i:n>i))return new tc(r.tree,r.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function tf(e,t,i,r){let n=e.cursor(),s=[];if(!n.firstChild())return s;if(null!=i){for(let e=!1;!e;)if(e=n.type.is(i),!n.nextSibling())return s}for(;;){if(null!=r&&n.type.is(r))return s;if(n.type.is(t)&&s.push(n.node),!n.nextSibling())return null==r?s:[]}}function tu(e,t,i=t.length-1){for(let r=e;i>=0;r=r.parent){if(!r)return!1;if(!r.type.isAnonymous){if(t[i]&&t[i]!=r.name)return!1;i--}}return!0}class tp{constructor(e,t,i,r){this.parent=e,this.buffer=t,this.index=i,this.start=r}}class tO extends th{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.context.start,i);return n<0?null:new tO(this.context,this,n)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&l.ExcludeBuffers)return null;let{buffer:r}=this.context,n=r.findChild(this.index+4,r.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return n<0?null:new tO(this.context,this,n)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new tO(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new tO(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,r=this.index+4,n=i.buffer[this.index+3];if(n>r){let s=i.buffer[this.index+1];e.push(i.slice(r,n,s)),t.push(0)}return new tn(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function td(e){if(!e.length)return null;let t=0,i=e[0];for(let r=1;r<e.length;r++){let n=e[r];(n.from>i.from||n.to<i.to)&&(i=n,t=r)}let r=i instanceof tc&&i.index<0?null:i.parent,n=e.slice();return r?n[t]=r:n.splice(t,1),new tg(n,i)}class tg{constructor(e,t){this.heads=e,this.node=t}get next(){return td(this.heads)}}class tm{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof tc)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:r}=this.buffer;return this.type=t||r.set.types[r.buffer[e]],this.from=i+r.buffer[e+1],this.to=i+r.buffer[e+2],!0}yield(e){return!!e&&(e instanceof tc?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:r}=this.buffer,n=r.findChild(this.index+4,r.buffer[this.index+3],e,t-this.buffer.start,i);return!(n<0)&&(this.stack.push(this.index),this.yieldBuf(n))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&l.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&l.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&l.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:r}=this;if(r){if(e>0){if(this.index<r.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(r.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=r)}else({index:t,_parent:i}=this._tree);for(;i;{index:t,_parent:i}=i)if(t>-1)for(let r=t+e,n=e<0?-1:i._tree.children.length;r!=n;r+=e){let e=i._tree.children[r];if(this.mode&l.IncludeAnonymous||e instanceof to||!e.type.isAnonymous||ty(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let r=this.index,n=this.stack.length;n>=0;){for(let s=e;s;s=s._parent)if(s.index==r){if(r==this.index)return s;t=s,i=n+1;break e}r=this.stack[--n]}for(let e=i;e<this.stack.length;e++)t=new tO(this.buffer,t,this.stack[e]);return this.bufferNode=new tO(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let r=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(r=!0)}for(;;){if(r&&t&&t(this),r=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,r=!0}}}matchContext(e){if(!this.buffer)return tu(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let r=e.length-1,n=this.stack.length-1;r>=0;n--){if(n<0)return tu(this._tree,e,r);let s=i[t.buffer[this.stack[n]]];if(!s.isAnonymous){if(e[r]&&e[r]!=s.name)return!1;r--}}return!0}}function ty(e){return e.children.some(e=>e instanceof to||!e.type.isAnonymous||ty(e))}let tx=new WeakMap;function tk(e,t){if(!e.isAnonymous||t instanceof to||t.type!=e)return 1;let i=tx.get(t);if(null==i){for(let r of(i=1,t.children)){if(r.type!=e||!(r instanceof tn)){i=1;break}i+=tk(e,r)}tx.set(t,i)}return i}function tQ(e,t,i,r,n,s,o,a,l){let h=0;for(let i=r;i<n;i++)h+=tk(e,t[i]);let c=Math.ceil(1.5*h/8),f=[],u=[];return!function t(i,r,n,o,a){for(let h=n;h<o;){let n=h,p=r[h],O=tk(e,i[h]);for(h++;h<o;h++){let t=tk(e,i[h]);if(O+t>=c)break;O+=t}if(h==n+1){if(O>c){let e=i[n];t(e.children,e.positions,0,e.children.length,r[n]+a);continue}f.push(i[n])}else{let t=r[h-1]+i[h-1].length-p;f.push(tQ(e,i,r,n,h,p,t,null,l))}u.push(p+a-s)}}(t,i,r,n,0),(a||l)(f,u,o)}class tb{constructor(e,t,i,r,n=!1,s=!1){this.from=e,this.to=t,this.tree=i,this.offset=r,this.open=(n?1:0)|(s?2:0)}get openStart(){return(1&this.open)>0}get openEnd(){return(2&this.open)>0}static addTree(e,t=[],i=!1){let r=[new tb(0,e.length,e,0,!1,i)];for(let i of t)i.to>e.length&&r.push(i);return r}static applyChanges(e,t,i=128){if(!t.length)return e;let r=[],n=1,s=e.length?e[0]:null;for(let o=0,a=0,l=0;;o++){let h=o<t.length?t[o]:null,c=h?h.fromA:1e9;if(c-a>=i)for(;s&&s.from<c;){let t=s;if(a>=t.from||c<=t.to||l){let e=Math.max(t.from,a)-l,i=Math.min(t.to,c)-l;t=e>=i?null:new tb(e,i,t.tree,t.offset+l,o>0,!!h)}if(t&&r.push(t),s.to>c)break;s=n<e.length?e[n++]:null}if(!h)break;a=h.toA,l=h.toA-h.toB}return r}}class tv{startParse(e,t,i){return"string"==typeof e&&(e=new tS(e)),i=i?i.length?i.map(e=>new e7(e.from,e.to)):[new e7(0,0)]:[new e7(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let r=this.startParse(e,t,i);for(;;){let e=r.advance();if(e)return e}}}class tS{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new e9({perNode:!0});let tw=0;class t${constructor(e,t,i,r){this.name=e,this.set=t,this.base=i,this.modified=r,this.id=tw++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i="string"==typeof e?e:"?";if(e instanceof t$&&(t=e),null==t?void 0:t.base)throw Error("Can not derive from a modified tag");let r=new t$(i,[],null,[]);if(r.set.push(r),t)for(let e of t.set)r.set.push(e);return r}static defineModifier(e){let t=new tZ(e);return e=>e.modified.indexOf(t)>-1?e:tZ.get(e.base||e,e.modified.concat(t).sort((e,t)=>e.id-t.id))}}let tP=0;class tZ{constructor(e){this.name=e,this.instances=[],this.id=tP++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>{var r;return i.base==e&&(r=i.modified,t.length==r.length&&t.every((e,t)=>e==r[t]))});if(i)return i;let r=[],n=new t$(e.name,r,e,t);for(let e of t)e.instances.push(n);let s=function(e){let t=[[]];for(let i=0;i<e.length;i++)for(let r=0,n=t.length;r<n;r++)t.push(t[r].concat(e[i]));return t.sort((e,t)=>t.length-e.length)}(t);for(let t of e.set)if(!t.modified.length)for(let e of s)r.push(tZ.get(t,e));return n}}let tT=new e9;class t_{constructor(e,t,i,r){this.tags=e,this.mode=t,this.context=i,this.next=r}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}function tX(e,t){let i=Object.create(null);for(let t of e)if(Array.isArray(t.tag))for(let e of t.tag)i[e.id]=t.class;else i[t.tag.id]=t.class;let{scope:r,all:n=null}=t||{};return{style:e=>{let t=n;for(let r of e)for(let e of r.set){let r=i[e.id];if(r){t=t?t+" "+r:r;break}}return t},scope:r}}t_.empty=new t_([],2,null);class tC{constructor(e,t,i){this.at=e,this.highlighters=t,this.span=i,this.class=""}startSpan(e,t){t!=this.class&&(this.flush(e),e>this.at&&(this.at=e),this.class=t)}flush(e){e>this.at&&this.class&&this.span(this.at,e,this.class)}highlightRange(e,t,i,r,n){let{type:s,from:o,to:a}=e;if(o>=i||a<=t)return;s.isTop&&(n=this.highlighters.filter(e=>!e.scope||e.scope(s)));let l=r,h=function(e){let t=e.type.prop(tT);for(;t&&t.context&&!e.matchContext(t.context);)t=t.next;return t||null}(e)||t_.empty,c=function(e,t){let i=null;for(let r of e){let e=r.style(t);e&&(i=i?i+" "+e:e)}return i}(n,h.tags);if(c&&(l&&(l+=" "),l+=c,1==h.mode&&(r+=(r?" ":"")+c)),this.startSpan(Math.max(t,o),l),h.opaque)return;let f=e.tree&&e.tree.prop(e9.mounted);if(f&&f.overlay){let s=e.node.enter(f.overlay[0].from+o,1),h=this.highlighters.filter(e=>!e.scope||e.scope(f.tree.type)),c=e.firstChild();for(let u=0,p=o;;u++){let O=u<f.overlay.length?f.overlay[u]:null,d=O?O.from+o:a,g=Math.max(t,p),m=Math.min(i,d);if(g<m&&c)for(;e.from<m&&(this.highlightRange(e,g,m,r,n),this.startSpan(Math.min(m,e.to),l),!(e.to>=d)&&e.nextSibling()););if(!O||d>i)break;(p=O.to+o)>t&&(this.highlightRange(s.cursor(),Math.max(t,O.from+o),Math.min(i,p),"",h),this.startSpan(Math.min(i,p),l))}c&&e.parent()}else if(e.firstChild()){f&&(r="");do{if(e.to<=t)continue;if(e.from>=i)break;this.highlightRange(e,t,i,r,n),this.startSpan(Math.min(i,e.to),l)}while(e.nextSibling());e.parent()}}}let tA=t$.define,tR=tA(),tq=tA(),tM=tA(tq),tj=tA(tq),tY=tA(),tN=tA(tY),tI=tA(tY),tE=tA(),tz=tA(tE),tV=tA(),tL=tA(),tW=tA(),tG=tA(tW),tB=tA(),tU={comment:tR,lineComment:tA(tR),blockComment:tA(tR),docComment:tA(tR),name:tq,variableName:tA(tq),typeName:tM,tagName:tA(tM),propertyName:tj,attributeName:tA(tj),className:tA(tq),labelName:tA(tq),namespace:tA(tq),macroName:tA(tq),literal:tY,string:tN,docString:tA(tN),character:tA(tN),attributeValue:tA(tN),number:tI,integer:tA(tI),float:tA(tI),bool:tA(tY),regexp:tA(tY),escape:tA(tY),color:tA(tY),url:tA(tY),keyword:tV,self:tA(tV),null:tA(tV),atom:tA(tV),unit:tA(tV),modifier:tA(tV),operatorKeyword:tA(tV),controlKeyword:tA(tV),definitionKeyword:tA(tV),moduleKeyword:tA(tV),operator:tL,derefOperator:tA(tL),arithmeticOperator:tA(tL),logicOperator:tA(tL),bitwiseOperator:tA(tL),compareOperator:tA(tL),updateOperator:tA(tL),definitionOperator:tA(tL),typeOperator:tA(tL),controlOperator:tA(tL),punctuation:tW,separator:tA(tW),bracket:tG,angleBracket:tA(tG),squareBracket:tA(tG),paren:tA(tG),brace:tA(tG),content:tE,heading:tz,heading1:tA(tz),heading2:tA(tz),heading3:tA(tz),heading4:tA(tz),heading5:tA(tz),heading6:tA(tz),contentSeparator:tA(tE),list:tA(tE),quote:tA(tE),emphasis:tA(tE),strong:tA(tE),link:tA(tE),monospace:tA(tE),strikethrough:tA(tE),inserted:tA(),deleted:tA(),changed:tA(),invalid:tA(),meta:tB,documentMeta:tA(tB),annotation:tA(tB),processingInstruction:tA(tB),definition:t$.defineModifier("definition"),constant:t$.defineModifier("constant"),function:t$.defineModifier("function"),standard:t$.defineModifier("standard"),local:t$.defineModifier("local"),special:t$.defineModifier("special")};for(let e in tU){let t=tU[e];t instanceof t$&&(t.name=e)}tX([{tag:tU.link,class:"tok-link"},{tag:tU.heading,class:"tok-heading"},{tag:tU.emphasis,class:"tok-emphasis"},{tag:tU.strong,class:"tok-strong"},{tag:tU.keyword,class:"tok-keyword"},{tag:tU.atom,class:"tok-atom"},{tag:tU.bool,class:"tok-bool"},{tag:tU.url,class:"tok-url"},{tag:tU.labelName,class:"tok-labelName"},{tag:tU.inserted,class:"tok-inserted"},{tag:tU.deleted,class:"tok-deleted"},{tag:tU.literal,class:"tok-literal"},{tag:tU.string,class:"tok-string"},{tag:tU.number,class:"tok-number"},{tag:[tU.regexp,tU.escape,tU.special(tU.string)],class:"tok-string2"},{tag:tU.variableName,class:"tok-variableName"},{tag:tU.local(tU.variableName),class:"tok-variableName tok-local"},{tag:tU.definition(tU.variableName),class:"tok-variableName tok-definition"},{tag:tU.special(tU.variableName),class:"tok-variableName2"},{tag:tU.definition(tU.propertyName),class:"tok-propertyName tok-definition"},{tag:tU.typeName,class:"tok-typeName"},{tag:tU.namespace,class:"tok-namespace"},{tag:tU.className,class:"tok-className"},{tag:tU.macroName,class:"tok-macroName"},{tag:tU.propertyName,class:"tok-propertyName"},{tag:tU.operator,class:"tok-operator"},{tag:tU.comment,class:"tok-comment"},{tag:tU.meta,class:"tok-meta"},{tag:tU.invalid,class:"tok-invalid"},{tag:tU.punctuation,class:"tok-punctuation"}]);let tD=new e9,tJ=new e9;class tF{constructor(e,t,i=[],r=""){this.data=e,this.name=r,f.yy.prototype.hasOwnProperty("tree")||Object.defineProperty(f.yy.prototype,"tree",{get(){return tH(this)}}),this.parser=t,this.extension=[t6.of(this),f.yy.languageData.of((e,t,i)=>{let r=tK(e,t,i),n=r.type.prop(tD);if(!n)return[];let s=e.facet(n),o=r.type.prop(tJ);if(o){let n=r.resolve(t-r.from,i);for(let t of o)if(t.test(n,e)){let i=e.facet(t.facet);return"replace"==t.type?i:i.concat(s)}}return s})].concat(i)}isActiveAt(e,t,i=-1){return tK(e,t,i).type.prop(tD)==this.data}findRegions(e){let t=e.facet(t6);if((null==t?void 0:t.data)==this.data)return[{from:0,to:e.doc.length}];if(!t||!t.allowsNesting)return[];let i=[],r=(e,t)=>{if(e.prop(tD)==this.data){i.push({from:t,to:t+e.length});return}let n=e.prop(e9.mounted);if(n){if(n.tree.prop(tD)==this.data){if(n.overlay)for(let e of n.overlay)i.push({from:e.from+t,to:e.to+t});else i.push({from:t,to:t+e.length});return}if(n.overlay){let e=i.length;if(r(n.tree,n.overlay[0].from+t),i.length>e)return}}for(let i=0;i<e.children.length;i++){let n=e.children[i];n instanceof tn&&r(n,e.positions[i]+t)}};return r(tH(e),0),i}get allowsNesting(){return!0}}function tK(e,t,i){let r=e.facet(t6),n=tH(e).topNode;if(!r||r.allowsNesting)for(let e=n;e;e=e.enter(t,i,l.ExcludeBuffers))e.type.isTop&&(n=e);return n}function tH(e){let t=e.field(tF.state,!1);return t?t.tree:tn.empty}tF.setState=f.Py.define();class t0{constructor(e){this.doc=e,this.cursorPos=0,this.string="",this.cursor=e.iter()}get length(){return this.doc.length}syncTo(e){return this.string=this.cursor.next(e-this.cursorPos).value,this.cursorPos=e+this.string.length,this.cursorPos-this.string.length}chunk(e){return this.syncTo(e),this.string}get lineChunks(){return!0}read(e,t){let i=this.cursorPos-this.string.length;return e<i||t>=this.cursorPos?this.doc.sliceString(e,t):this.string.slice(e-i,t-i)}}let t1=null;class t2{constructor(e,t,i=[],r,n,s,o,a){this.parser=e,this.state=t,this.fragments=i,this.tree=r,this.treeLen=n,this.viewport=s,this.skipped=o,this.scheduleOn=a,this.parse=null,this.tempSkipped=[]}static create(e,t,i){return new t2(e,t,[],tn.empty,0,i,[],null)}startParse(){return this.parser.startParse(new t0(this.state.doc),this.fragments)}work(e,t){return(null!=t&&t>=this.state.doc.length&&(t=void 0),this.tree!=tn.empty&&this.isDone(null!=t?t:this.state.doc.length))?(this.takeTree(),!0):this.withContext(()=>{var i;if("number"==typeof e){let t=Date.now()+e;e=()=>Date.now()>t}for(this.parse||(this.parse=this.startParse()),null!=t&&(null==this.parse.stoppedAt||this.parse.stoppedAt>t)&&t<this.state.doc.length&&this.parse.stopAt(t);;){let r=this.parse.advance();if(r){if(this.fragments=this.withoutTempSkipped(tb.addTree(r,this.fragments,null!=this.parse.stoppedAt)),this.treeLen=null!==(i=this.parse.stoppedAt)&&void 0!==i?i:this.state.doc.length,this.tree=r,this.parse=null,!(this.treeLen<(null!=t?t:this.state.doc.length)))return!0;this.parse=this.startParse()}if(e())return!1}})}takeTree(){let e,t;this.parse&&(e=this.parse.parsedPos)>=this.treeLen&&((null==this.parse.stoppedAt||this.parse.stoppedAt>e)&&this.parse.stopAt(e),this.withContext(()=>{for(;!(t=this.parse.advance()););}),this.treeLen=e,this.tree=t,this.fragments=this.withoutTempSkipped(tb.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(e){let t=t1;t1=this;try{return e()}finally{t1=t}}withoutTempSkipped(e){for(let t;t=this.tempSkipped.pop();)e=t5(e,t.from,t.to);return e}changes(e,t){let{fragments:i,tree:r,treeLen:n,viewport:s,skipped:o}=this;if(this.takeTree(),!e.empty){let t=[];if(e.iterChangedRanges((e,i,r,n)=>t.push({fromA:e,toA:i,fromB:r,toB:n})),i=tb.applyChanges(i,t),r=tn.empty,n=0,s={from:e.mapPos(s.from,-1),to:e.mapPos(s.to,1)},this.skipped.length)for(let t of(o=[],this.skipped)){let i=e.mapPos(t.from,1),r=e.mapPos(t.to,-1);i<r&&o.push({from:i,to:r})}}return new t2(this.parser,t,i,r,n,s,o,this.scheduleOn)}updateViewport(e){if(this.viewport.from==e.from&&this.viewport.to==e.to)return!1;this.viewport=e;let t=this.skipped.length;for(let t=0;t<this.skipped.length;t++){let{from:i,to:r}=this.skipped[t];i<e.to&&r>e.from&&(this.fragments=t5(this.fragments,i,r),this.skipped.splice(t--,1))}return!(this.skipped.length>=t)&&(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(e,t){this.skipped.push({from:e,to:t})}static getSkippingParser(e){return new class extends tv{createParse(t,i,r){let n=r[0].from,s=r[r.length-1].to;return{parsedPos:n,advance(){let t=t1;if(t){for(let e of r)t.tempSkipped.push(e);e&&(t.scheduleOn=t.scheduleOn?Promise.all([t.scheduleOn,e]):e)}return this.parsedPos=s,new tn(te.none,[],[],s-n)},stoppedAt:null,stopAt(){}}}}}isDone(e){e=Math.min(e,this.state.doc.length);let t=this.fragments;return this.treeLen>=e&&t.length&&0==t[0].from&&t[0].to>=e}static get(){return t1}}function t5(e,t,i){return tb.applyChanges(e,[{fromA:t,toA:i,fromB:t,toB:i}])}class t4{constructor(e){this.context=e,this.tree=e.tree}apply(e){if(!e.docChanged&&this.tree==this.context.tree)return this;let t=this.context.changes(e.changes,e.state),i=this.context.treeLen==e.startState.doc.length?void 0:Math.max(e.changes.mapPos(this.context.treeLen),t.viewport.to);return t.work(20,i)||t.takeTree(),new t4(t)}static init(e){let t=Math.min(3e3,e.doc.length),i=t2.create(e.facet(t6).parser,e,{from:0,to:t});return i.work(20,t)||i.takeTree(),new t4(i)}}tF.state=f.QQ.define({create:t4.init,update(e,t){for(let e of t.effects)if(e.is(tF.setState))return e.value;return t.startState.facet(t6)!=t.state.facet(t6)?t4.init(t.state):e.apply(t)}});let t3=e=>{let t=setTimeout(()=>e(),500);return()=>clearTimeout(t)};"undefined"!=typeof requestIdleCallback&&(t3=e=>{let t=-1,i=setTimeout(()=>{t=requestIdleCallback(e,{timeout:400})},100);return()=>t<0?clearTimeout(i):cancelIdleCallback(t)});let t7="undefined"!=typeof navigator&&(null===(h=navigator.scheduling)||void 0===h?void 0:h.isInputPending)?()=>navigator.scheduling.isInputPending():null,t9=u.lg.fromClass(class{constructor(e){this.view=e,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(e){let t=this.view.state.field(tF.state).context;(t.updateViewport(e.view.viewport)||this.view.viewport.to>t.treeLen)&&this.scheduleWork(),(e.docChanged||e.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(t)}scheduleWork(){if(this.working)return;let{state:e}=this.view,t=e.field(tF.state);t.tree==t.context.tree&&t.context.isDone(e.doc.length)||(this.working=t3(this.work))}work(e){this.working=null;let t=Date.now();if(this.chunkEnd<t&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=t+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:i,viewport:{to:r}}=this.view,n=i.field(tF.state);if(n.tree==n.context.tree&&n.context.isDone(r+1e5))return;let s=Date.now()+Math.min(this.chunkBudget,100,e&&!t7?Math.max(25,e.timeRemaining()-5):1e9),o=n.context.treeLen<r&&i.doc.length>r+1e3,a=n.context.work(()=>t7&&t7()||Date.now()>s,r+(o?0:1e5));this.chunkBudget-=Date.now()-t,(a||this.chunkBudget<=0)&&(n.context.takeTree(),this.view.dispatch({effects:tF.setState.of(new t4(n.context))})),this.chunkBudget>0&&!(a&&!o)&&this.scheduleWork(),this.checkAsyncSchedule(n.context)}checkAsyncSchedule(e){e.scheduleOn&&(this.workScheduled++,e.scheduleOn.then(()=>this.scheduleWork()).catch(e=>(0,u.OO)(this.view.state,e)).then(()=>this.workScheduled--),e.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),t6=f.r$.define({combine:e=>e.length?e[0]:null,enables:e=>[tF.state,t9,u.tk.contentAttributes.compute([e],t=>{let i=t.facet(e);return i&&i.name?{"data-language":i.name}:{}})]}),t8=f.r$.define(),ie=f.r$.define({combine:e=>{if(!e.length)return"  ";let t=e[0];if(!t||/\S/.test(t)||Array.from(t).some(e=>e!=t[0]))throw Error("Invalid indent unit: "+JSON.stringify(e[0]));return t}});function it(e){let t=e.facet(ie);return 9==t.charCodeAt(0)?e.tabSize*t.length:t.length}function ii(e,t){let i="",r=e.tabSize,n=e.facet(ie)[0];if("	"==n){for(;t>=r;)i+="	",t-=r;n=" "}for(let e=0;e<t;e++)i+=n;return i}function ir(e,t){for(let i of(e instanceof f.yy&&(e=new is(e)),e.state.facet(t8))){let r=i(e,t);if(void 0!==r)return r}let i=tH(e.state);return i.length>=t?function(e,t,i){let r=t.resolveStack(i),n=t.resolveInner(i,-1).resolve(i,0).enterUnfinishedNodesBefore(i);if(n!=r.node){let e=[];for(let t=n;t&&!(t.from<r.node.from||t.to>r.node.to||t.from==r.node.from&&t.type==r.node.type);t=t.parent)e.push(t);for(let t=e.length-1;t>=0;t--)r={node:e[t],next:r}}return ia(r,e,i)}(e,i,t):null}class is{constructor(e,t={}){this.state=e,this.options=t,this.unit=it(e)}lineAt(e,t=1){let i=this.state.doc.lineAt(e),{simulateBreak:r,simulateDoubleBreak:n}=this.options;return null!=r&&r>=i.from&&r<=i.to?n&&r==e?{text:"",from:e}:(t<0?r<e:r<=e)?{text:i.text.slice(r-i.from),from:r}:{text:i.text.slice(0,r-i.from),from:i.from}:i}textAfterPos(e,t=1){if(this.options.simulateDoubleBreak&&e==this.options.simulateBreak)return"";let{text:i,from:r}=this.lineAt(e,t);return i.slice(e-r,Math.min(i.length,e+100-r))}column(e,t=1){let{text:i,from:r}=this.lineAt(e,t),n=this.countColumn(i,e-r),s=this.options.overrideIndentation?this.options.overrideIndentation(r):-1;return s>-1&&(n+=s-this.countColumn(i,i.search(/\S|$/))),n}countColumn(e,t=e.length){return(0,f.IS)(e,this.state.tabSize,t)}lineIndent(e,t=1){let{text:i,from:r}=this.lineAt(e,t),n=this.options.overrideIndentation;if(n){let e=n(r);if(e>-1)return e}return this.countColumn(i,i.search(/\S|$/))}get simulatedBreak(){return this.options.simulateBreak||null}}let io=new e9;function ia(e,t,i){for(let r=e;r;r=r.next){let e=function(e){let t=e.type.prop(io);if(t)return t;let i=e.firstChild,r;if(i&&(r=i.type.prop(e9.closedBy))){let t=e.lastChild,i=t&&r.indexOf(t.name)>-1;return e=>(function(e,t,i,r,n){let s=e.textAfter,o=s.match(/^\s*/)[0].length,a=r&&s.slice(o,o+r.length)==r||n==e.pos+o,l=t?function(e){let t=e.node,i=t.childAfter(t.from),r=t.lastChild;if(!i)return null;let n=e.options.simulateBreak,s=e.state.doc.lineAt(i.from),o=null==n||n<=s.from?s.to:Math.min(s.to,n);for(let e=i.to;;){let n=t.childAfter(e);if(!n||n==r)return null;if(!n.type.isSkipped){if(n.from>=o)return null;let e=/^ */.exec(s.text.slice(i.to-s.from))[0].length;return{from:i.from,to:i.to+e}}e=n.to}}(e):null;return l?a?e.column(l.from):e.column(l.to):e.baseIndent+(a?0:1*e.unit)})(e,!0,0,void 0,i&&!(e.pos==e.options.simulateBreak&&e.options.simulateDoubleBreak)?t.from:void 0)}return null==e.parent?il:null}(r.node);if(e)return e(ih.create(t,i,r))}return 0}function il(){return 0}class ih extends is{constructor(e,t,i){super(e.state,e.options),this.base=e,this.pos=t,this.context=i}get node(){return this.context.node}static create(e,t,i){return new ih(e,t,i)}get textAfter(){return this.textAfterPos(this.pos)}get baseIndent(){return this.baseIndentFor(this.node)}baseIndentFor(e){let t=this.state.doc.lineAt(e.from);for(;;){let i=e.resolve(t.from);for(;i.parent&&i.parent.from==i.from;)i=i.parent;if(function(e,t){for(let i=t;i;i=i.parent)if(e==i)return!0;return!1}(i,e))break;t=this.state.doc.lineAt(i.from)}return this.lineIndent(t.from)}continue(){return ia(this.context.next,this.base,this.pos)}}let ic=f.r$.define(),iu=new e9;function ip(e,t,i){for(let r of e.facet(ic)){let n=r(e,t,i);if(n)return n}return function(e,t,i){let r=tH(e);if(r.length<i)return null;let n=r.resolveStack(i,1),s=null;for(let o=n;o;o=o.next){let n=o.node;if(n.to<=i||n.from>i)continue;if(s&&n.from<t)break;let a=n.type.prop(iu);if(a&&(n.to<r.length-50||r.length==e.doc.length||!function(e){let t=e.lastChild;return t&&t.to==e.to&&t.type.isError}(n))){let r=a(n,e);r&&r.from<=i&&r.from>=t&&r.to>i&&(s=r)}}return s}(e,t,i)}function iO(e,t){let i=t.mapPos(e.from,1),r=t.mapPos(e.to,-1);return i>=r?void 0:{from:i,to:r}}let id=f.Py.define({map:iO}),ig=f.Py.define({map:iO});function im(e){let t=[];for(let{head:i}of e.state.selection.ranges)t.some(e=>e.from<=i&&e.to>=i)||t.push(e.lineBlockAt(i));return t}let iy=f.QQ.define({create:()=>u.p.none,update(e,t){for(let i of(t.isUserEvent("delete")&&t.changes.iterChangedRanges((t,i)=>e=ix(e,t,i)),e=e.map(t.changes),t.effects))if(i.is(id)&&!function(e,t,i){let r=!1;return e.between(t,t,(e,n)=>{e==t&&n==i&&(r=!0)}),r}(e,i.value.from,i.value.to)){let{preparePlaceholder:r}=t.state.facet(iw),n=r?u.p.replace({widget:new iT(r(t.state,i.value))}):iZ;e=e.update({add:[n.range(i.value.from,i.value.to)]})}else i.is(ig)&&(e=e.update({filter:(e,t)=>i.value.from!=e||i.value.to!=t,filterFrom:i.value.from,filterTo:i.value.to}));return t.selection&&(e=ix(e,t.selection.main.head)),e},provide:e=>u.tk.decorations.from(e),toJSON(e,t){let i=[];return e.between(0,t.doc.length,(e,t)=>{i.push(e,t)}),i},fromJSON(e){if(!Array.isArray(e)||e.length%2)throw RangeError("Invalid JSON for fold state");let t=[];for(let i=0;i<e.length;){let r=e[i++],n=e[i++];if("number"!=typeof r||"number"!=typeof n)throw RangeError("Invalid JSON for fold state");t.push(iZ.range(r,n))}return u.p.set(t,!0)}});function ix(e,t,i=t){let r=!1;return e.between(t,i,(e,n)=>{e<i&&n>t&&(r=!0)}),r?e.update({filterFrom:t,filterTo:i,filter:(e,r)=>e>=i||r<=t}):e}function ik(e,t,i){var r;let n=null;return null===(r=e.field(iy,!1))||void 0===r||r.between(t,i,(e,t)=>{(!n||n.from>e)&&(n={from:e,to:t})}),n}function iQ(e,t){return e.field(iy,!1)?t:t.concat(f.Py.appendConfig.of(i$()))}function ib(e,t,i=!0){let r=e.state.doc.lineAt(t.from).number,n=e.state.doc.lineAt(t.to).number;return u.tk.announce.of(`${e.state.phrase(i?"Folded lines":"Unfolded lines")} ${r} ${e.state.phrase("to")} ${n}.`)}let iv=[{key:"Ctrl-Shift-[",mac:"Cmd-Alt-[",run:e=>{for(let t of im(e)){let i=ip(e.state,t.from,t.to);if(i)return e.dispatch({effects:iQ(e.state,[id.of(i),ib(e,i)])}),!0}return!1}},{key:"Ctrl-Shift-]",mac:"Cmd-Alt-]",run:e=>{if(!e.state.field(iy,!1))return!1;let t=[];for(let i of im(e)){let r=ik(e.state,i.from,i.to);r&&t.push(ig.of(r),ib(e,r,!1))}return t.length&&e.dispatch({effects:t}),t.length>0}},{key:"Ctrl-Alt-[",run:e=>{let{state:t}=e,i=[];for(let r=0;r<t.doc.length;){let n=e.lineBlockAt(r),s=ip(t,n.from,n.to);s&&i.push(id.of(s)),r=(s?e.lineBlockAt(s.to):n).to+1}return i.length&&e.dispatch({effects:iQ(e.state,i)}),!!i.length}},{key:"Ctrl-Alt-]",run:e=>{let t=e.state.field(iy,!1);if(!t||!t.size)return!1;let i=[];return t.between(0,e.state.doc.length,(e,t)=>{i.push(ig.of({from:e,to:t}))}),e.dispatch({effects:i}),!0}}],iS={placeholderDOM:null,preparePlaceholder:null,placeholderText:"…"},iw=f.r$.define({combine:e=>(0,f.BO)(e,iS)});function i$(e){let t=[iy,iC];return e&&t.push(iw.of(e)),t}function iP(e,t){let{state:i}=e,r=i.facet(iw),n=t=>{let i=e.lineBlockAt(e.posAtDOM(t.target)),r=ik(e.state,i.from,i.to);r&&e.dispatch({effects:ig.of(r)}),t.preventDefault()};if(r.placeholderDOM)return r.placeholderDOM(e,n,t);let s=document.createElement("span");return s.textContent=r.placeholderText,s.setAttribute("aria-label",i.phrase("folded code")),s.title=i.phrase("unfold"),s.className="cm-foldPlaceholder",s.onclick=n,s}let iZ=u.p.replace({widget:new class extends u.l9{toDOM(e){return iP(e,null)}}});class iT extends u.l9{constructor(e){super(),this.value=e}eq(e){return this.value==e.value}toDOM(e){return iP(e,this.value)}}let i_={openText:"⌄",closedText:"›",markerDOM:null,domEventHandlers:{},foldingChanged:()=>!1};class iX extends u.SJ{constructor(e,t){super(),this.config=e,this.open=t}eq(e){return this.config==e.config&&this.open==e.open}toDOM(e){if(this.config.markerDOM)return this.config.markerDOM(this.open);let t=document.createElement("span");return t.textContent=this.open?this.config.openText:this.config.closedText,t.title=e.state.phrase(this.open?"Fold line":"Unfold line"),t}}let iC=u.tk.baseTheme({".cm-foldPlaceholder":{backgroundColor:"#eee",border:"1px solid #ddd",color:"#888",borderRadius:".2em",margin:"0 1px",padding:"0 1px",cursor:"pointer"},".cm-foldGutter span":{padding:"0 1px",cursor:"pointer"}});class iA{constructor(e,t){let i;function r(e){let t=eu.V.newName();return(i||(i=Object.create(null)))["."+t]=e,t}this.specs=e;let n="string"==typeof t.all?t.all:t.all?r(t.all):void 0,s=t.scope;this.scope=s instanceof tF?e=>e.prop(tD)==s.data:s?e=>e==s:void 0,this.style=tX(e.map(e=>({tag:e.tag,class:e.class||r(Object.assign({},e,{tag:null}))})),{all:n}).style,this.module=i?new eu.V(i):null,this.themeType=t.themeType}static define(e,t){return new iA(e,t||{})}}let iR=f.r$.define(),iq=f.r$.define({combine:e=>e.length?[e[0]]:null});function iM(e){let t=e.facet(iR);return t.length?t:e.facet(iq)}let ij=f.Wl.high(u.lg.fromClass(class{constructor(e){this.markCache=Object.create(null),this.tree=tH(e.state),this.decorations=this.buildDeco(e,iM(e.state)),this.decoratedTo=e.viewport.to}update(e){let t=tH(e.state),i=iM(e.state),r=i!=iM(e.startState),{viewport:n}=e.view,s=e.changes.mapPos(this.decoratedTo,1);t.length<n.to&&!r&&t.type==this.tree.type&&s>=n.to?(this.decorations=this.decorations.map(e.changes),this.decoratedTo=s):(t!=this.tree||e.viewportChanged||r)&&(this.tree=t,this.decorations=this.buildDeco(e.view,i),this.decoratedTo=n.to)}buildDeco(e,t){if(!t||!this.tree.length)return u.p.none;let i=new f.f_;for(let{from:r,to:n}of e.visibleRanges)!function(e,t,i,r=0,n=e.length){let s=new tC(r,Array.isArray(t)?t:[t],i);s.highlightRange(e.cursor(),r,n,"",s.highlighters),s.flush(n)}(this.tree,t,(e,t,r)=>{i.add(e,t,this.markCache[r]||(this.markCache[r]=u.p.mark({class:r})))},r,n);return i.finish()}},{decorations:e=>e.decorations})),iY=iA.define([{tag:tU.meta,color:"#404740"},{tag:tU.link,textDecoration:"underline"},{tag:tU.heading,textDecoration:"underline",fontWeight:"bold"},{tag:tU.emphasis,fontStyle:"italic"},{tag:tU.strong,fontWeight:"bold"},{tag:tU.strikethrough,textDecoration:"line-through"},{tag:tU.keyword,color:"#708"},{tag:[tU.atom,tU.bool,tU.url,tU.contentSeparator,tU.labelName],color:"#219"},{tag:[tU.literal,tU.inserted],color:"#164"},{tag:[tU.string,tU.deleted],color:"#a11"},{tag:[tU.regexp,tU.escape,tU.special(tU.string)],color:"#e40"},{tag:tU.definition(tU.variableName),color:"#00f"},{tag:tU.local(tU.variableName),color:"#30a"},{tag:[tU.typeName,tU.namespace],color:"#085"},{tag:tU.className,color:"#167"},{tag:[tU.special(tU.variableName),tU.macroName],color:"#256"},{tag:tU.definition(tU.propertyName),color:"#00c"},{tag:tU.comment,color:"#940"},{tag:tU.invalid,color:"#f00"}]),iN=u.tk.baseTheme({"&.cm-focused .cm-matchingBracket":{backgroundColor:"#328c8252"},"&.cm-focused .cm-nonmatchingBracket":{backgroundColor:"#bb555544"}}),iI="()[]{}",iE=f.r$.define({combine:e=>(0,f.BO)(e,{afterCursor:!0,brackets:iI,maxScanDistance:1e4,renderMatch:iL})}),iz=u.p.mark({class:"cm-matchingBracket"}),iV=u.p.mark({class:"cm-nonmatchingBracket"});function iL(e){let t=[],i=e.matched?iz:iV;return t.push(i.range(e.start.from,e.start.to)),e.end&&t.push(i.range(e.end.from,e.end.to)),t}let iW=f.QQ.define({create:()=>u.p.none,update(e,t){if(!t.docChanged&&!t.selection)return e;let i=[],r=t.state.facet(iE);for(let e of t.state.selection.ranges){if(!e.empty)continue;let n=iJ(t.state,e.head,-1,r)||e.head>0&&iJ(t.state,e.head-1,1,r)||r.afterCursor&&(iJ(t.state,e.head,1,r)||e.head<t.state.doc.length&&iJ(t.state,e.head+1,-1,r));n&&(i=i.concat(r.renderMatch(n,t.state)))}return u.p.set(i,!0)},provide:e=>u.tk.decorations.from(e)}),iG=[iW,iN],iB=new e9;function iU(e,t,i){let r=e.prop(t<0?e9.openedBy:e9.closedBy);if(r)return r;if(1==e.name.length){let r=i.indexOf(e.name);if(r>-1&&r%2==(t<0?1:0))return[i[r+t]]}return null}function iD(e){let t=e.type.prop(iB);return t?t(e.node):e}function iJ(e,t,i,r={}){let n=r.maxScanDistance||1e4,s=r.brackets||iI,o=tH(e),a=o.resolveInner(t,i);for(let e=a;e;e=e.parent){let r=iU(e.type,i,s);if(r&&e.from<e.to){let n=iD(e);if(n&&(i>0?t>=n.from&&t<n.to:t>n.from&&t<=n.to))return function(e,t,i,r,n,s,o){let a=r.parent,l={from:n.from,to:n.to},h=0,c=null==a?void 0:a.cursor();if(c&&(i<0?c.childBefore(r.from):c.childAfter(r.to)))do if(i<0?c.to<=r.from:c.from>=r.to){if(0==h&&s.indexOf(c.type.name)>-1&&c.from<c.to){let e=iD(c);return{start:l,end:e?{from:e.from,to:e.to}:void 0,matched:!0}}if(iU(c.type,i,o))h++;else if(iU(c.type,-i,o)){if(0==h){let e=iD(c);return{start:l,end:e&&e.from<e.to?{from:e.from,to:e.to}:void 0,matched:!1}}h--}}while(i<0?c.prevSibling():c.nextSibling());return{start:l,matched:!1}}(0,0,i,e,n,r,s)}}return function(e,t,i,r,n,s,o){let a=i<0?e.sliceDoc(t-1,t):e.sliceDoc(t,t+1),l=o.indexOf(a);if(l<0||l%2==0!=i>0)return null;let h={from:i<0?t-1:t,to:i>0?t+1:t},c=e.doc.iterRange(t,i>0?e.doc.length:0),f=0;for(let e=0;!c.next().done&&e<=s;){let s=c.value;i<0&&(e+=s.length);let a=t+e*i;for(let e=i>0?0:s.length-1,t=i>0?s.length:-1;e!=t;e+=i){let t=o.indexOf(s[e]);if(!(t<0)&&r.resolveInner(a+e,1).type==n){if(t%2==0==i>0)f++;else{if(1==f)return{start:h,end:{from:a+e,to:a+e+1},matched:t>>1==l>>1};f--}}}i>0&&(e+=s.length)}return c.done?{start:h,matched:!1}:null}(e,t,i,o,a.type,n,s)}let iF=Object.create(null),iK=[te.none],iH=[],i0=Object.create(null),i1=Object.create(null);for(let[e,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])i1[e]=function(e,t){let i=[];for(let r of t.split(" ")){let t=[];for(let i of r.split(".")){let r=e[i]||tU[i];r?"function"==typeof r?t.length?t=t.map(r):i2(i,`Modifier ${i} used at start of tag`):t.length?i2(i,`Tag ${i} used as modifier`):t=Array.isArray(r)?r:[r]:i2(i,`Unknown highlighting tag ${i}`)}for(let e of t)i.push(e)}if(!i.length)return 0;let r=t.replace(/ /g,"_"),n=r+" "+i.map(e=>e.id),s=i0[n];if(s)return s.id;let o=i0[n]=te.define({id:iK.length,name:r,props:[function(e){let t=Object.create(null);for(let i in e){let r=e[i];for(let e of(Array.isArray(r)||(r=[r]),i.split(" ")))if(e){let i=[],n=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){n=1;break}let r=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!r)throw RangeError("Invalid path: "+e);if(i.push("*"==r[0]?"":'"'==r[0][0]?JSON.parse(r[0]):r[0]),(t+=r[0].length)==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){n=0;break}if("/"!=o)throw RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,a=i[o];if(!a)throw RangeError("Invalid path: "+e);let l=new t_(r,n,o>0?i.slice(0,o):null);t[a]=l.sort(t[a])}}return tT.add(t)}({[r]:i})]});return iK.push(o),o.id}(iF,t);function i2(e,t){iH.indexOf(e)>-1||(iH.push(e),console.warn(t))}function i5(e,t){return({state:i,dispatch:r})=>{if(i.readOnly)return!1;let n=e(t,i);return!!n&&(r(i.update(n)),!0)}}u.Nm.RTL,u.Nm.LTR;let i4=i5(function(e,t,i=t.selection.ranges){let r=[],n=-1;for(let{from:e,to:s}of i){let i=r.length,o=1e9,a=i9(t,e).line;if(a){for(let i=e;i<=s;){let l=t.doc.lineAt(i);if(l.from>n&&(e==s||s>l.from)){n=l.from;let e=/^\s*/.exec(l.text)[0].length,t=e==l.length,i=l.text.slice(e,e+a.length)==a?e:-1;e<l.text.length&&e<o&&(o=e),r.push({line:l,comment:i,token:a,indent:e,empty:t,single:!1})}i=l.to+1}if(o<1e9)for(let e=i;e<r.length;e++)r[e].indent<r[e].line.text.length&&(r[e].indent=o);r.length==i+1&&(r[i].single=!0)}}if(2!=e&&r.some(e=>e.comment<0&&(!e.empty||e.single))){let e=[];for(let{line:t,token:i,indent:n,empty:s,single:o}of r)(o||!s)&&e.push({from:t.from+n,insert:i+" "});let i=t.changes(e);return{changes:i,selection:t.selection.map(i,1)}}if(1!=e&&r.some(e=>e.comment>=0)){let e=[];for(let{line:t,comment:i,token:n}of r)if(i>=0){let r=t.from+i,s=r+n.length;" "==t.text[s-t.from]&&s++,e.push({from:r,to:s})}return{changes:e}}return null},0),i3=i5(i6,0),i7=i5((e,t)=>i6(e,t,function(e){let t=[];for(let i of e.selection.ranges){let r=e.doc.lineAt(i.from),n=i.to<=r.to?r:e.doc.lineAt(i.to);n.from>r.from&&n.from==i.to&&(n=i.to==r.to+1?r:e.doc.lineAt(i.to-1));let s=t.length-1;s>=0&&t[s].to>r.from?t[s].to=n.to:t.push({from:r.from+/^\s*/.exec(r.text)[0].length,to:n.to})}return t}(t)),0);function i9(e,t){let i=e.languageDataAt("commentTokens",t,1);return i.length?i[0]:{}}function i6(e,t,i=t.selection.ranges){let r=i.map(e=>i9(t,e.from).block);if(!r.every(e=>e))return null;let n=i.map((e,i)=>(function(e,{open:t,close:i},r,n){let s,o,a=e.sliceDoc(r-50,r),l=e.sliceDoc(n,n+50),h=/\s*$/.exec(a)[0].length,c=/^\s*/.exec(l)[0].length,f=a.length-h;if(a.slice(f-t.length,f)==t&&l.slice(c,c+i.length)==i)return{open:{pos:r-h,margin:h&&1},close:{pos:n+c,margin:c&&1}};n-r<=100?s=o=e.sliceDoc(r,n):(s=e.sliceDoc(r,r+50),o=e.sliceDoc(n-50,n));let u=/^\s*/.exec(s)[0].length,p=/\s*$/.exec(o)[0].length,O=o.length-p-i.length;return s.slice(u,u+t.length)==t&&o.slice(O,O+i.length)==i?{open:{pos:r+u+t.length,margin:/\s/.test(s.charAt(u+t.length))?1:0},close:{pos:n-p-i.length,margin:/\s/.test(o.charAt(O-1))?1:0}}:null})(t,r[i],e.from,e.to));if(2!=e&&!n.every(e=>e))return{changes:t.changes(i.map((e,t)=>n[t]?[]:[{from:e.from,insert:r[t].open+" "},{from:e.to,insert:" "+r[t].close}]))};if(1!=e&&n.some(e=>e)){let e=[];for(let t=0,i;t<n.length;t++)if(i=n[t]){let n=r[t],{open:s,close:o}=i;e.push({from:s.pos-n.open.length,to:s.pos+s.margin},{from:o.pos-o.margin,to:o.pos+n.close.length})}return{changes:e}}return null}let i8=f.q6.define(),re=f.q6.define(),rt=f.r$.define(),ri=f.r$.define({combine:e=>(0,f.BO)(e,{minDepth:100,newGroupDelay:500,joinToEvent:(e,t)=>t},{minDepth:Math.max,newGroupDelay:Math.min,joinToEvent:(e,t)=>(i,r)=>e(i,r)||t(i,r)})}),rr=f.QQ.define({create:()=>rg.empty,update(e,t){let i=t.state.facet(ri),r=t.annotation(i8);if(r){let n=rh.fromTransaction(t,r.selection),s=r.side,o=0==s?e.undone:e.done;return o=n?rc(o,o.length,i.minDepth,n):rp(o,t.startState.selection),new rg(0==s?r.rest:o,0==s?o:r.rest)}let n=t.annotation(re);if(("full"==n||"before"==n)&&(e=e.isolate()),!1===t.annotation(f.YW.addToHistory))return t.changes.empty?e:e.addMapping(t.changes.desc);let s=rh.fromTransaction(t),o=t.annotation(f.YW.time),a=t.annotation(f.YW.userEvent);return s?e=e.addChanges(s,o,a,i,t):t.selection&&(e=e.addSelection(t.startState.selection,o,a,i.newGroupDelay)),("full"==n||"after"==n)&&(e=e.isolate()),e},toJSON:e=>({done:e.done.map(e=>e.toJSON()),undone:e.undone.map(e=>e.toJSON())}),fromJSON:e=>new rg(e.done.map(rh.fromJSON),e.undone.map(rh.fromJSON))});function rn(e,t){return function({state:i,dispatch:r}){if(!t&&i.readOnly)return!1;let n=i.field(rr,!1);if(!n)return!1;let s=n.pop(e,i,t);return!!s&&(r(s),!0)}}let rs=rn(0,!1),ro=rn(1,!1),ra=rn(0,!0),rl=rn(1,!0);class rh{constructor(e,t,i,r,n){this.changes=e,this.effects=t,this.mapped=i,this.startSelection=r,this.selectionsAfter=n}setSelAfter(e){return new rh(this.changes,this.effects,this.mapped,this.startSelection,e)}toJSON(){var e,t,i;return{changes:null===(e=this.changes)||void 0===e?void 0:e.toJSON(),mapped:null===(t=this.mapped)||void 0===t?void 0:t.toJSON(),startSelection:null===(i=this.startSelection)||void 0===i?void 0:i.toJSON(),selectionsAfter:this.selectionsAfter.map(e=>e.toJSON())}}static fromJSON(e){return new rh(e.changes&&f.as.fromJSON(e.changes),[],e.mapped&&f.n0.fromJSON(e.mapped),e.startSelection&&f.jT.fromJSON(e.startSelection),e.selectionsAfter.map(f.jT.fromJSON))}static fromTransaction(e,t){let i=ru;for(let t of e.startState.facet(rt)){let r=t(e);r.length&&(i=i.concat(r))}return!i.length&&e.changes.empty?null:new rh(e.changes.invert(e.startState.doc),i,void 0,t||e.startState.selection,ru)}static selection(e){return new rh(void 0,ru,void 0,void 0,e)}}function rc(e,t,i,r){let n=t+1>i+20?t-i-1:0,s=e.slice(n,t);return s.push(r),s}function rf(e,t){return e.length?t.length?e.concat(t):e:t}let ru=[];function rp(e,t){if(!e.length)return[rh.selection([t])];{let i=e[e.length-1],r=i.selectionsAfter.slice(Math.max(0,i.selectionsAfter.length-200));return r.length&&r[r.length-1].eq(t)?e:(r.push(t),rc(e,e.length-1,1e9,i.setSelAfter(r)))}}function rO(e,t){if(!e.length)return e;let i=e.length,r=ru;for(;i;){let n=function(e,t,i){let r=rf(e.selectionsAfter.length?e.selectionsAfter.map(e=>e.map(t)):ru,i);if(!e.changes)return rh.selection(r);let n=e.changes.map(t),s=t.mapDesc(e.changes,!0),o=e.mapped?e.mapped.composeDesc(s):s;return new rh(n,f.Py.mapEffects(e.effects,t),o,e.startSelection.map(s),r)}(e[i-1],t,r);if(n.changes&&!n.changes.empty||n.effects.length){let t=e.slice(0,i);return t[i-1]=n,t}t=n.mapped,i--,r=n.selectionsAfter}return r.length?[rh.selection(r)]:ru}let rd=/^(input\.type|delete)($|\.)/;class rg{constructor(e,t,i=0,r){this.done=e,this.undone=t,this.prevTime=i,this.prevUserEvent=r}isolate(){return this.prevTime?new rg(this.done,this.undone):this}addChanges(e,t,i,r,n){var s,o;let a,l,h=this.done,c=h[h.length-1];return h=c&&c.changes&&!c.changes.empty&&e.changes&&(!i||rd.test(i))&&(!c.selectionsAfter.length&&t-this.prevTime<r.newGroupDelay&&r.joinToEvent(n,(s=c.changes,o=e.changes,a=[],l=!1,s.iterChangedRanges((e,t)=>a.push(e,t)),o.iterChangedRanges((e,t,i,r)=>{for(let e=0;e<a.length;){let t=a[e++],n=a[e++];r>=t&&i<=n&&(l=!0)}}),l))||"input.type.compose"==i)?rc(h,h.length-1,r.minDepth,new rh(e.changes.compose(c.changes),rf(f.Py.mapEffects(e.effects,c.changes),c.effects),c.mapped,c.startSelection,ru)):rc(h,h.length,r.minDepth,e),new rg(h,ru,t,i)}addSelection(e,t,i,r){var n;let s=this.done.length?this.done[this.done.length-1].selectionsAfter:ru;return s.length>0&&t-this.prevTime<r&&i==this.prevUserEvent&&i&&/^select($|\.)/.test(i)&&(n=s[s.length-1]).ranges.length==e.ranges.length&&0===n.ranges.filter((t,i)=>t.empty!=e.ranges[i].empty).length?this:new rg(rp(this.done,e),this.undone,t,i)}addMapping(e){return new rg(rO(this.done,e),rO(this.undone,e),this.prevTime,this.prevUserEvent)}pop(e,t,i){let r=0==e?this.done:this.undone;if(0==r.length)return null;let n=r[r.length-1],s=n.selectionsAfter[0]||t.selection;if(i&&n.selectionsAfter.length){let i,o;return t.update({selection:n.selectionsAfter[n.selectionsAfter.length-1],annotations:i8.of({side:e,rest:(i=r[r.length-1],(o=r.slice())[r.length-1]=i.setSelAfter(i.selectionsAfter.slice(0,i.selectionsAfter.length-1)),o),selection:s}),userEvent:0==e?"select.undo":"select.redo",scrollIntoView:!0})}if(!n.changes)return null;{let i=1==r.length?ru:r.slice(0,r.length-1);return n.mapped&&(i=rO(i,n.mapped)),t.update({changes:n.changes,selection:n.startSelection,effects:n.effects,annotations:i8.of({side:e,rest:i,selection:s}),filter:!1,userEvent:0==e?"undo":"redo",scrollIntoView:!0})}}}rg.empty=new rg(ru,ru);let rm=[{key:"Mod-z",run:rs,preventDefault:!0},{key:"Mod-y",mac:"Mod-Shift-z",run:ro,preventDefault:!0},{linux:"Ctrl-Shift-z",run:ro,preventDefault:!0},{key:"Mod-u",run:ra,preventDefault:!0},{key:"Alt-u",mac:"Mod-Shift-u",run:rl,preventDefault:!0}];function ry(e,t){return f.jT.create(e.ranges.map(t),e.mainIndex)}function rx(e,t){return e.update({selection:t,scrollIntoView:!0,userEvent:"select"})}function rk({state:e,dispatch:t},i){let r=ry(e.selection,i);return!r.eq(e.selection,!0)&&(t(rx(e,r)),!0)}function rQ(e,t){return f.jT.cursor(t?e.to:e.from)}function rb(e,t){return rk(e,i=>i.empty?e.moveByChar(i,t):rQ(i,t))}function rv(e){return e.textDirectionAt(e.state.selection.main.head)==u.Nm.LTR}let rS=e=>rb(e,!rv(e)),rw=e=>rb(e,rv(e));function r$(e,t){return rk(e,i=>i.empty?e.moveByGroup(i,t):rQ(i,t))}function rP(e,t,i){let r,n,s=tH(e).resolveInner(t.head),o=i?e9.closedBy:e9.openedBy;for(let r=t.head;;){let t=i?s.childAfter(r):s.childBefore(r);if(!t)break;!function(e,t,i){if(t.type.prop(i))return!0;let r=t.to-t.from;return r&&(r>2||/[^\s,.;:]/.test(e.sliceDoc(t.from,t.to)))||t.firstChild}(e,t,o)?r=i?t.to:t.from:s=t}return n=s.type.prop(o)&&(r=i?iJ(e,s.from,1):iJ(e,s.to,-1))&&r.matched?i?r.end.to:r.end.from:i?s.to:s.from,f.jT.cursor(n,i?-1:1)}function rZ(e,t){return rk(e,i=>{if(!i.empty)return rQ(i,t);let r=e.moveVertically(i,t);return r.head!=i.head?r:e.moveToLineBoundary(i,t)})}"undefined"!=typeof Intl&&Intl.Segmenter;let rT=e=>rZ(e,!1),r_=e=>rZ(e,!0);function rX(e){let t=e.scrollDOM.clientHeight<e.scrollDOM.scrollHeight-2,i=0,r=0,n;if(t){for(let t of e.state.facet(u.tk.scrollMargins)){let n=t(e);(null==n?void 0:n.top)&&(i=Math.max(null==n?void 0:n.top,i)),(null==n?void 0:n.bottom)&&(r=Math.max(null==n?void 0:n.bottom,r))}n=e.scrollDOM.clientHeight-i-r}else n=(e.dom.ownerDocument.defaultView||window).innerHeight;return{marginTop:i,marginBottom:r,selfScroll:t,height:Math.max(e.defaultLineHeight,n-5)}}function rC(e,t){let i,r=rX(e),{state:n}=e,s=ry(n.selection,i=>i.empty?e.moveVertically(i,t,r.height):rQ(i,t));if(s.eq(n.selection))return!1;if(r.selfScroll){let t=e.coordsAtPos(n.selection.main.head),o=e.scrollDOM.getBoundingClientRect(),a=o.top+r.marginTop,l=o.bottom-r.marginBottom;t&&t.top>a&&t.bottom<l&&(i=u.tk.scrollIntoView(s.main.head,{y:"start",yMargin:t.top-a}))}return e.dispatch(rx(n,s),{effects:i}),!0}let rA=e=>rC(e,!1),rR=e=>rC(e,!0);function rq(e,t,i){let r=e.lineBlockAt(t.head),n=e.moveToLineBoundary(t,i);if(n.head==t.head&&n.head!=(i?r.to:r.from)&&(n=e.moveToLineBoundary(t,i,!1)),!i&&n.head==r.from&&r.length){let i=/^\s*/.exec(e.state.sliceDoc(r.from,Math.min(r.from+100,r.to)))[0].length;i&&t.head!=r.from+i&&(n=f.jT.cursor(r.from+i))}return n}function rM(e,t){let i=ry(e.state.selection,e=>{let i=t(e);return f.jT.range(e.anchor,i.head,i.goalColumn,i.bidiLevel||void 0)});return!i.eq(e.state.selection)&&(e.dispatch(rx(e.state,i)),!0)}function rj(e,t){return rM(e,i=>e.moveByChar(i,t))}let rY=e=>rj(e,!rv(e)),rN=e=>rj(e,rv(e));function rI(e,t){return rM(e,i=>e.moveByGroup(i,t))}function rE(e,t){return rM(e,i=>e.moveVertically(i,t))}let rz=e=>rE(e,!1),rV=e=>rE(e,!0);function rL(e,t){return rM(e,i=>e.moveVertically(i,t,rX(e).height))}let rW=e=>rL(e,!1),rG=e=>rL(e,!0),rB=({state:e,dispatch:t})=>(t(rx(e,{anchor:0})),!0),rU=({state:e,dispatch:t})=>(t(rx(e,{anchor:e.doc.length})),!0),rD=({state:e,dispatch:t})=>(t(rx(e,{anchor:e.selection.main.anchor,head:0})),!0),rJ=({state:e,dispatch:t})=>(t(rx(e,{anchor:e.selection.main.anchor,head:e.doc.length})),!0);function rF(e,t){if(e.state.readOnly)return!1;let i="delete.selection",{state:r}=e,n=r.changeByRange(r=>{let{from:n,to:s}=r;if(n==s){let o=t(r);o<n?(i="delete.backward",o=rK(e,o,!1)):o>n&&(i="delete.forward",o=rK(e,o,!0)),n=Math.min(n,o),s=Math.max(s,o)}else n=rK(e,n,!1),s=rK(e,s,!0);return n==s?{range:r}:{changes:{from:n,to:s},range:f.jT.cursor(n,n<r.head?-1:1)}});return!n.changes.empty&&(e.dispatch(r.update(n,{scrollIntoView:!0,userEvent:i,effects:"delete.selection"==i?u.tk.announce.of(r.phrase("Selection deleted")):void 0})),!0)}function rK(e,t,i){if(e instanceof u.tk)for(let r of e.state.facet(u.tk.atomicRanges).map(t=>t(e)))r.between(t,t,(e,r)=>{e<t&&r>t&&(t=i?r:e)});return t}let rH=(e,t,i)=>rF(e,r=>{let n=r.from,{state:s}=e,o=s.doc.lineAt(n),a,l;if(i&&!t&&n>o.from&&n<o.from+200&&!/[^ \t]/.test(a=o.text.slice(0,n-o.from))){if("	"==a[a.length-1])return n-1;let e=(0,f.IS)(a,s.tabSize)%it(s)||it(s);for(let t=0;t<e&&" "==a[a.length-1-t];t++)n--;l=n}else(l=(0,f.cp)(o.text,n-o.from,t,t)+o.from)==n&&o.number!=(t?s.doc.lines:1)?l+=t?1:-1:!t&&/[\ufe00-\ufe0f]/.test(o.text.slice(l-o.from,n-o.from))&&(l=(0,f.cp)(o.text,l-o.from,!1,!1)+o.from);return l}),r0=e=>rH(e,!1,!0),r1=e=>rH(e,!0,!1),r2=(e,t)=>rF(e,i=>{let r=i.head,{state:n}=e,s=n.doc.lineAt(r),o=n.charCategorizer(r);for(let e=null;;){if(r==(t?s.to:s.from)){r==i.head&&s.number!=(t?n.doc.lines:1)&&(r+=t?1:-1);break}let a=(0,f.cp)(s.text,r-s.from,t)+s.from,l=s.text.slice(Math.min(r,a)-s.from,Math.max(r,a)-s.from),h=o(l);if(null!=e&&h!=e)break;(" "!=l||r!=i.head)&&(e=h),r=a}return r}),r5=e=>r2(e,!1);function r4(e){let t=[],i=-1;for(let r of e.selection.ranges){let n=e.doc.lineAt(r.from),s=e.doc.lineAt(r.to);if(r.empty||r.to!=s.from||(s=e.doc.lineAt(r.to-1)),i>=n.number){let e=t[t.length-1];e.to=s.to,e.ranges.push(r)}else t.push({from:n.from,to:s.to,ranges:[r]});i=s.number+1}return t}function r3(e,t,i){if(e.readOnly)return!1;let r=[],n=[];for(let t of r4(e)){if(i?t.to==e.doc.length:0==t.from)continue;let s=e.doc.lineAt(i?t.to+1:t.from-1),o=s.length+1;if(i)for(let i of(r.push({from:t.to,to:s.to},{from:t.from,insert:s.text+e.lineBreak}),t.ranges))n.push(f.jT.range(Math.min(e.doc.length,i.anchor+o),Math.min(e.doc.length,i.head+o)));else for(let i of(r.push({from:s.from,to:t.from},{from:t.to,insert:e.lineBreak+s.text}),t.ranges))n.push(f.jT.range(i.anchor-o,i.head-o))}return!!r.length&&(t(e.update({changes:r,scrollIntoView:!0,selection:f.jT.create(n,e.selection.mainIndex),userEvent:"move.line"})),!0)}function r7(e,t,i){if(e.readOnly)return!1;let r=[];for(let t of r4(e))i?r.push({from:t.from,insert:e.doc.slice(t.from,t.to)+e.lineBreak}):r.push({from:t.to,insert:e.lineBreak+e.doc.slice(t.from,t.to)});return t(e.update({changes:r,scrollIntoView:!0,userEvent:"input.copyline"})),!0}let r9=r8(!1),r6=r8(!0);function r8(e){return({state:t,dispatch:i})=>{if(t.readOnly)return!1;let r=t.changeByRange(i=>{let{from:r,to:n}=i,s=t.doc.lineAt(r),o=!e&&r==n&&function(e,t){if(/\(\)|\[\]|\{\}/.test(e.sliceDoc(t-1,t+1)))return{from:t,to:t};let i=tH(e).resolveInner(t),r=i.childBefore(t),n=i.childAfter(t),s;return r&&n&&r.to<=t&&n.from>=t&&(s=r.type.prop(e9.closedBy))&&s.indexOf(n.name)>-1&&e.doc.lineAt(r.to).from==e.doc.lineAt(n.from).from&&!/\S/.test(e.sliceDoc(r.to,n.from))?{from:r.to,to:n.from}:null}(t,r);e&&(r=n=(n<=s.to?s:t.doc.lineAt(n)).to);let a=new is(t,{simulateBreak:r,simulateDoubleBreak:!!o}),l=ir(a,r);for(null==l&&(l=(0,f.IS)(/^\s*/.exec(t.doc.lineAt(r).text)[0],t.tabSize));n<s.to&&/\s/.test(s.text[n-s.from]);)n++;o?{from:r,to:n}=o:r>s.from&&r<s.from+100&&!/\S/.test(s.text.slice(0,r))&&(r=s.from);let h=["",ii(t,l)];return o&&h.push(ii(t,a.lineIndent(s.from,-1))),{changes:{from:r,to:n,insert:f.xv.of(h)},range:f.jT.cursor(r+1+h[1].length)}});return i(t.update(r,{scrollIntoView:!0,userEvent:"input"})),!0}}function ne(e,t){let i=-1;return e.changeByRange(r=>{let n=[];for(let s=r.from;s<=r.to;){let o=e.doc.lineAt(s);o.number>i&&(r.empty||r.to>o.from)&&(t(o,n,r),i=o.number),s=o.to+1}let s=e.changes(n);return{changes:n,range:f.jT.range(s.mapPos(r.anchor,1),s.mapPos(r.head,1))}})}let nt=[{key:"Ctrl-b",run:rS,shift:rY,preventDefault:!0},{key:"Ctrl-f",run:rw,shift:rN},{key:"Ctrl-p",run:rT,shift:rz},{key:"Ctrl-n",run:r_,shift:rV},{key:"Ctrl-a",run:e=>rk(e,t=>f.jT.cursor(e.lineBlockAt(t.head).from,1)),shift:e=>rM(e,t=>f.jT.cursor(e.lineBlockAt(t.head).from))},{key:"Ctrl-e",run:e=>rk(e,t=>f.jT.cursor(e.lineBlockAt(t.head).to,-1)),shift:e=>rM(e,t=>f.jT.cursor(e.lineBlockAt(t.head).to))},{key:"Ctrl-d",run:r1},{key:"Ctrl-h",run:r0},{key:"Ctrl-k",run:e=>rF(e,t=>{let i=e.lineBlockAt(t.head).to;return t.head<i?i:Math.min(e.state.doc.length,t.head+1)})},{key:"Ctrl-Alt-h",run:r5},{key:"Ctrl-o",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(e=>({changes:{from:e.from,to:e.to,insert:f.xv.of(["",""])},range:f.jT.cursor(e.from)}));return t(e.update(i,{scrollIntoView:!0,userEvent:"input"})),!0}},{key:"Ctrl-t",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=e.changeByRange(t=>{if(!t.empty||0==t.from||t.from==e.doc.length)return{range:t};let i=t.from,r=e.doc.lineAt(i),n=i==r.from?i-1:(0,f.cp)(r.text,i-r.from,!1)+r.from,s=i==r.to?i+1:(0,f.cp)(r.text,i-r.from,!0)+r.from;return{changes:{from:n,to:s,insert:e.doc.slice(i,s).append(e.doc.slice(n,i))},range:f.jT.cursor(s)}});return!i.changes.empty&&(t(e.update(i,{scrollIntoView:!0,userEvent:"move.character"})),!0)}},{key:"Ctrl-v",run:rR}],ni=[{key:"ArrowLeft",run:rS,shift:rY,preventDefault:!0},{key:"Mod-ArrowLeft",mac:"Alt-ArrowLeft",run:e=>r$(e,!rv(e)),shift:e=>rI(e,!rv(e)),preventDefault:!0},{mac:"Cmd-ArrowLeft",run:e=>rk(e,t=>rq(e,t,!rv(e))),shift:e=>rM(e,t=>rq(e,t,!rv(e))),preventDefault:!0},{key:"ArrowRight",run:rw,shift:rN,preventDefault:!0},{key:"Mod-ArrowRight",mac:"Alt-ArrowRight",run:e=>r$(e,rv(e)),shift:e=>rI(e,rv(e)),preventDefault:!0},{mac:"Cmd-ArrowRight",run:e=>rk(e,t=>rq(e,t,rv(e))),shift:e=>rM(e,t=>rq(e,t,rv(e))),preventDefault:!0},{key:"ArrowUp",run:rT,shift:rz,preventDefault:!0},{mac:"Cmd-ArrowUp",run:rB,shift:rD},{mac:"Ctrl-ArrowUp",run:rA,shift:rW},{key:"ArrowDown",run:r_,shift:rV,preventDefault:!0},{mac:"Cmd-ArrowDown",run:rU,shift:rJ},{mac:"Ctrl-ArrowDown",run:rR,shift:rG},{key:"PageUp",run:rA,shift:rW},{key:"PageDown",run:rR,shift:rG},{key:"Home",run:e=>rk(e,t=>rq(e,t,!1)),shift:e=>rM(e,t=>rq(e,t,!1)),preventDefault:!0},{key:"Mod-Home",run:rB,shift:rD},{key:"End",run:e=>rk(e,t=>rq(e,t,!0)),shift:e=>rM(e,t=>rq(e,t,!0)),preventDefault:!0},{key:"Mod-End",run:rU,shift:rJ},{key:"Enter",run:r9,shift:r9},{key:"Mod-a",run:({state:e,dispatch:t})=>(t(e.update({selection:{anchor:0,head:e.doc.length},userEvent:"select"})),!0)},{key:"Backspace",run:r0,shift:r0},{key:"Delete",run:r1},{key:"Mod-Backspace",mac:"Alt-Backspace",run:r5},{key:"Mod-Delete",mac:"Alt-Delete",run:e=>r2(e,!0)},{mac:"Mod-Backspace",run:e=>rF(e,t=>{let i=e.moveToLineBoundary(t,!1).head;return t.head>i?i:Math.max(0,t.head-1)})},{mac:"Mod-Delete",run:e=>rF(e,t=>{let i=e.moveToLineBoundary(t,!0).head;return t.head<i?i:Math.min(e.state.doc.length,t.head+1)})}].concat(nt.map(e=>({mac:e.key,run:e.run,shift:e.shift}))),nr=[{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:e=>rk(e,t=>rP(e.state,t,!rv(e))),shift:e=>rM(e,t=>rP(e.state,t,!rv(e)))},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:e=>rk(e,t=>rP(e.state,t,rv(e))),shift:e=>rM(e,t=>rP(e.state,t,rv(e)))},{key:"Alt-ArrowUp",run:({state:e,dispatch:t})=>r3(e,t,!1)},{key:"Shift-Alt-ArrowUp",run:({state:e,dispatch:t})=>r7(e,t,!1)},{key:"Alt-ArrowDown",run:({state:e,dispatch:t})=>r3(e,t,!0)},{key:"Shift-Alt-ArrowDown",run:({state:e,dispatch:t})=>r7(e,t,!0)},{key:"Escape",run:({state:e,dispatch:t})=>{let i=e.selection,r=null;return i.ranges.length>1?r=f.jT.create([i.main]):i.main.empty||(r=f.jT.create([f.jT.cursor(i.main.head)])),!!r&&(t(rx(e,r)),!0)}},{key:"Mod-Enter",run:r6},{key:"Alt-l",mac:"Ctrl-l",run:({state:e,dispatch:t})=>{let i=r4(e).map(({from:t,to:i})=>f.jT.range(t,Math.min(i+1,e.doc.length)));return t(e.update({selection:f.jT.create(i),userEvent:"select"})),!0}},{key:"Mod-i",run:({state:e,dispatch:t})=>{let i=ry(e.selection,t=>{let i=tH(e),r=i.resolveStack(t.from,1);if(t.empty){let e=i.resolveStack(t.from,-1);e.node.from>=r.node.from&&e.node.to<=r.node.to&&(r=e)}for(let e=r;e;e=e.next){let{node:i}=e;if((i.from<t.from&&i.to>=t.to||i.to>t.to&&i.from<=t.from)&&e.next)return f.jT.range(i.to,i.from)}return t});return!i.eq(e.selection)&&(t(rx(e,i)),!0)},preventDefault:!0},{key:"Mod-[",run:({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(ne(e,(t,i)=>{let r=/^\s*/.exec(t.text)[0];if(!r)return;let n=(0,f.IS)(r,e.tabSize),s=0,o=ii(e,Math.max(0,n-it(e)));for(;s<r.length&&s<o.length&&r.charCodeAt(s)==o.charCodeAt(s);)s++;i.push({from:t.from+s,to:t.from+r.length,insert:o.slice(s)})}),{userEvent:"delete.dedent"})),!0)},{key:"Mod-]",run:({state:e,dispatch:t})=>!e.readOnly&&(t(e.update(ne(e,(t,i)=>{i.push({from:t.from,insert:e.facet(ie)})}),{userEvent:"input.indent"})),!0)},{key:"Mod-Alt-\\",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=Object.create(null),r=new is(e,{overrideIndentation:e=>{let t=i[e];return null==t?-1:t}}),n=ne(e,(t,n,s)=>{let o=ir(r,t.from);if(null==o)return;/\S/.test(t.text)||(o=0);let a=/^\s*/.exec(t.text)[0],l=ii(e,o);(a!=l||s.from<t.from+a.length)&&(i[t.from]=o,n.push({from:t.from,to:t.from+a.length,insert:l}))});return n.changes.empty||t(e.update(n,{userEvent:"indent"})),!0}},{key:"Shift-Mod-k",run:e=>{if(e.state.readOnly)return!1;let{state:t}=e,i=t.changes(r4(t).map(({from:e,to:i})=>(e>0?e--:i<t.doc.length&&i++,{from:e,to:i}))),r=ry(t.selection,t=>{let i;if(e.lineWrapping){let r=e.lineBlockAt(t.head),n=e.coordsAtPos(t.head,t.assoc||1);n&&(i=r.bottom+e.documentTop-n.bottom+e.defaultLineHeight/2)}return e.moveVertically(t,!0,i)}).map(i);return e.dispatch({changes:i,selection:r,scrollIntoView:!0,userEvent:"delete.line"}),!0}},{key:"Shift-Mod-\\",run:({state:e,dispatch:t})=>{let i,r;return i=!1,r=ry(e.selection,t=>{let r=iJ(e,t.head,-1)||iJ(e,t.head,1)||t.head>0&&iJ(e,t.head-1,1)||t.head<e.doc.length&&iJ(e,t.head+1,-1);if(!r||!r.end)return t;i=!0;let n=r.start.from==t.head?r.end.to:r.end.from;return f.jT.cursor(n)}),!!i&&(t(rx(e,r)),!0)}},{key:"Mod-/",run:e=>{let{state:t}=e,i=t.doc.lineAt(t.selection.main.from),r=i9(e.state,i.from);return r.line?i4(e):!!r.block&&i7(e)}},{key:"Alt-A",run:i3},{key:"Ctrl-m",mac:"Shift-Alt-m",run:e=>(e.setTabFocusMode(),!0)}].concat(ni);function nn(){var e=arguments[0];"string"==typeof e&&(e=document.createElement(e));var t=1,i=arguments[1];if(i&&"object"==typeof i&&null==i.nodeType&&!Array.isArray(i)){for(var r in i)if(Object.prototype.hasOwnProperty.call(i,r)){var n=i[r];"string"==typeof n?e.setAttribute(r,n):null!=n&&(e[r]=n)}t++}for(;t<arguments.length;t++)!function e(t,i){if("string"==typeof i)t.appendChild(document.createTextNode(i));else if(null==i);else if(null!=i.nodeType)t.appendChild(i);else if(Array.isArray(i))for(var r=0;r<i.length;r++)e(t,i[r]);else throw RangeError("Unsupported child node: "+i)}(e,arguments[t]);return e}let ns="function"==typeof String.prototype.normalize?e=>e.normalize("NFKD"):e=>e;class no{constructor(e,t,i=0,r=e.length,n,s){this.test=s,this.value={from:0,to:0},this.done=!1,this.matches=[],this.buffer="",this.bufferPos=0,this.iter=e.iterRange(i,r),this.bufferStart=i,this.normalize=n?e=>n(ns(e)):ns,this.query=this.normalize(t)}peek(){if(this.bufferPos==this.buffer.length){if(this.bufferStart+=this.buffer.length,this.iter.next(),this.iter.done)return -1;this.bufferPos=0,this.buffer=this.iter.value}return(0,f.gm)(this.buffer,this.bufferPos)}next(){for(;this.matches.length;)this.matches.pop();return this.nextOverlapping()}nextOverlapping(){for(;;){let e=this.peek();if(e<0)return this.done=!0,this;let t=(0,f.bg)(e),i=this.bufferStart+this.bufferPos;this.bufferPos+=(0,f.nZ)(e);let r=this.normalize(t);if(r.length)for(let e=0,n=i;;e++){let s=r.charCodeAt(e),o=this.match(s,n,this.bufferPos+this.bufferStart);if(e==r.length-1){if(o)return this.value=o,this;break}n==i&&e<t.length&&t.charCodeAt(e)==s&&n++}}}match(e,t,i){let r=null;for(let t=0;t<this.matches.length;t+=2){let n=this.matches[t],s=!1;this.query.charCodeAt(n)==e&&(n==this.query.length-1?r={from:this.matches[t+1],to:i}:(this.matches[t]++,s=!0)),s||(this.matches.splice(t,2),t-=2)}return this.query.charCodeAt(0)==e&&(1==this.query.length?r={from:t,to:i}:this.matches.push(1,t)),r&&this.test&&!this.test(r.from,r.to,this.buffer,this.bufferStart)&&(r=null),r}}"undefined"!=typeof Symbol&&(no.prototype[Symbol.iterator]=function(){return this});let na={from:-1,to:-1,match:/.*/.exec("")},nl="gm"+(null==/x/.unicode?"":"u");class nh{constructor(e,t,i,r=0,n=e.length){if(this.text=e,this.to=n,this.curLine="",this.done=!1,this.value=na,/\\[sWDnr]|\n|\r|\[\^/.test(t))return new nu(e,t,i,r,n);this.re=new RegExp(t,nl+((null==i?void 0:i.ignoreCase)?"i":"")),this.test=null==i?void 0:i.test,this.iter=e.iter();let s=e.lineAt(r);this.curLineStart=s.from,this.matchPos=np(e,r),this.getLine(this.curLineStart)}getLine(e){this.iter.next(e),this.iter.lineBreak?this.curLine="":(this.curLine=this.iter.value,this.curLineStart+this.curLine.length>this.to&&(this.curLine=this.curLine.slice(0,this.to-this.curLineStart)),this.iter.next())}nextLine(){this.curLineStart=this.curLineStart+this.curLine.length+1,this.curLineStart>this.to?this.curLine="":this.getLine(0)}next(){for(let e=this.matchPos-this.curLineStart;;){this.re.lastIndex=e;let t=this.matchPos<=this.to&&this.re.exec(this.curLine);if(t){let i=this.curLineStart+t.index,r=i+t[0].length;if(this.matchPos=np(this.text,r+(i==r?1:0)),i==this.curLineStart+this.curLine.length&&this.nextLine(),(i<r||i>this.value.to)&&(!this.test||this.test(i,r,t)))return this.value={from:i,to:r,match:t},this;e=this.matchPos-this.curLineStart}else{if(!(this.curLineStart+this.curLine.length<this.to))return this.done=!0,this;this.nextLine(),e=0}}}}let nc=new WeakMap;class nf{constructor(e,t){this.from=e,this.text=t}get to(){return this.from+this.text.length}static get(e,t,i){let r=nc.get(e);if(!r||r.from>=i||r.to<=t){let r=new nf(t,e.sliceString(t,i));return nc.set(e,r),r}if(r.from==t&&r.to==i)return r;let{text:n,from:s}=r;return s>t&&(n=e.sliceString(t,s)+n,s=t),r.to<i&&(n+=e.sliceString(r.to,i)),nc.set(e,new nf(s,n)),new nf(t,n.slice(t-s,i-s))}}class nu{constructor(e,t,i,r,n){this.text=e,this.to=n,this.done=!1,this.value=na,this.matchPos=np(e,r),this.re=new RegExp(t,nl+((null==i?void 0:i.ignoreCase)?"i":"")),this.test=null==i?void 0:i.test,this.flat=nf.get(e,r,this.chunkEnd(r+5e3))}chunkEnd(e){return e>=this.to?this.to:this.text.lineAt(e).to}next(){for(;;){let e=this.re.lastIndex=this.matchPos-this.flat.from,t=this.re.exec(this.flat.text);if(t&&!t[0]&&t.index==e&&(this.re.lastIndex=e+1,t=this.re.exec(this.flat.text)),t){let e=this.flat.from+t.index,i=e+t[0].length;if((this.flat.to>=this.to||t.index+t[0].length<=this.flat.text.length-10)&&(!this.test||this.test(e,i,t)))return this.value={from:e,to:i,match:t},this.matchPos=np(this.text,i+(e==i?1:0)),this}if(this.flat.to==this.to)return this.done=!0,this;this.flat=nf.get(this.text,this.flat.from,this.chunkEnd(this.flat.from+2*this.flat.text.length))}}}function np(e,t){if(t>=e.length)return t;let i=e.lineAt(t),r;for(;t<i.to&&(r=i.text.charCodeAt(t-i.from))>=56320&&r<57344;)t++;return t}function nO(e){let t=nn("input",{class:"cm-textfield",name:"line",value:String(e.state.doc.lineAt(e.state.selection.main.head).number)});function i(){let i=/^([+-])?(\d+)?(:\d+)?(%)?$/.exec(t.value);if(!i)return;let{state:r}=e,n=r.doc.lineAt(r.selection.main.head),[,s,o,a,l]=i,h=a?+a.slice(1):0,c=o?+o:n.number;if(o&&l){let e=c/100;s&&(e=e*("-"==s?-1:1)+n.number/r.doc.lines),c=Math.round(r.doc.lines*e)}else o&&s&&(c=c*("-"==s?-1:1)+n.number);let p=r.doc.line(Math.max(1,Math.min(r.doc.lines,c))),O=f.jT.cursor(p.from+Math.max(0,Math.min(h,p.length)));e.dispatch({effects:[nd.of(!1),u.tk.scrollIntoView(O.from,{y:"center"})],selection:O}),e.focus()}return{dom:nn("form",{class:"cm-gotoLine",onkeydown:t=>{27==t.keyCode?(t.preventDefault(),e.dispatch({effects:nd.of(!1)}),e.focus()):13==t.keyCode&&(t.preventDefault(),i())},onsubmit:e=>{e.preventDefault(),i()}},nn("label",e.state.phrase("Go to line"),": ",t)," ",nn("button",{class:"cm-button",type:"submit"},e.state.phrase("go")),nn("button",{name:"close",onclick:()=>{e.dispatch({effects:nd.of(!1)}),e.focus()},"aria-label":e.state.phrase("close"),type:"button"},["\xd7"]))}}"undefined"!=typeof Symbol&&(nh.prototype[Symbol.iterator]=nu.prototype[Symbol.iterator]=function(){return this});let nd=f.Py.define(),ng=f.QQ.define({create:()=>!0,update(e,t){for(let i of t.effects)i.is(nd)&&(e=i.value);return e},provide:e=>u.mH.from(e,e=>e?nO:null)}),nm=u.tk.baseTheme({".cm-panel.cm-gotoLine":{padding:"2px 6px 4px",position:"relative","& label":{fontSize:"80%"},"& [name=close]":{position:"absolute",top:"0",bottom:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:"0"}}}),ny={highlightWordAroundCursor:!1,minSelectionLength:1,maxMatches:100,wholeWords:!1},nx=f.r$.define({combine:e=>(0,f.BO)(e,ny,{highlightWordAroundCursor:(e,t)=>e||t,minSelectionLength:Math.min,maxMatches:Math.min})}),nk=u.p.mark({class:"cm-selectionMatch"}),nQ=u.p.mark({class:"cm-selectionMatch cm-selectionMatch-main"});function nb(e,t,i,r){return(0==i||e(t.sliceDoc(i-1,i))!=f.D0.Word)&&(r==t.doc.length||e(t.sliceDoc(r,r+1))!=f.D0.Word)}let nv=u.lg.fromClass(class{constructor(e){this.decorations=this.getDeco(e)}update(e){(e.selectionSet||e.docChanged||e.viewportChanged)&&(this.decorations=this.getDeco(e.view))}getDeco(e){let t=e.state.facet(nx),{state:i}=e,r=i.selection;if(r.ranges.length>1)return u.p.none;let n=r.main,s,o=null;if(n.empty){if(!t.highlightWordAroundCursor)return u.p.none;let e=i.wordAt(n.head);if(!e)return u.p.none;o=i.charCategorizer(n.head),s=i.sliceDoc(e.from,e.to)}else{let e=n.to-n.from;if(e<t.minSelectionLength||e>200)return u.p.none;if(t.wholeWords){var a,l,h;if(s=i.sliceDoc(n.from,n.to),!(nb(o=i.charCategorizer(n.head),i,n.from,n.to)&&(a=o,l=n.from,h=n.to,a(i.sliceDoc(l,l+1))==f.D0.Word&&a(i.sliceDoc(h-1,h))==f.D0.Word)))return u.p.none}else if(!(s=i.sliceDoc(n.from,n.to)))return u.p.none}let c=[];for(let r of e.visibleRanges){let e=new no(i.doc,s,r.from,r.to);for(;!e.next().done;){let{from:r,to:s}=e.value;if((!o||nb(o,i,r,s))&&(n.empty&&r<=n.from&&s>=n.to?c.push(nQ.range(r,s)):(r>=n.to||s<=n.from)&&c.push(nk.range(r,s)),c.length>t.maxMatches))return u.p.none}}return u.p.set(c)}},{decorations:e=>e.decorations}),nS=u.tk.baseTheme({".cm-selectionMatch":{backgroundColor:"#99ff7780"},".cm-searchMatch .cm-selectionMatch":{backgroundColor:"transparent"}}),nw=({state:e,dispatch:t})=>{let{selection:i}=e,r=f.jT.create(i.ranges.map(t=>e.wordAt(t.head)||f.jT.cursor(t.head)),i.mainIndex);return!r.eq(i)&&(t(e.update({selection:r})),!0)},n$=f.r$.define({combine:e=>(0,f.BO)(e,{top:!1,caseSensitive:!1,literal:!1,regexp:!1,wholeWord:!1,createPanel:e=>new n1(e),scrollToMatch:e=>u.tk.scrollIntoView(e)})});class nP{constructor(e){this.search=e.search,this.caseSensitive=!!e.caseSensitive,this.literal=!!e.literal,this.regexp=!!e.regexp,this.replace=e.replace||"",this.valid=!!this.search&&(!this.regexp||function(e){try{return new RegExp(e,nl),!0}catch(e){return!1}}(this.search)),this.unquoted=this.unquote(this.search),this.wholeWord=!!e.wholeWord}unquote(e){return this.literal?e:e.replace(/\\([nrt\\])/g,(e,t)=>"n"==t?"\n":"r"==t?"\r":"t"==t?"	":"\\")}eq(e){return this.search==e.search&&this.replace==e.replace&&this.caseSensitive==e.caseSensitive&&this.regexp==e.regexp&&this.wholeWord==e.wholeWord}create(){return this.regexp?new nR(this):new n_(this)}getCursor(e,t=0,i){let r=e.doc?e:f.yy.create({doc:e});return null==i&&(i=r.doc.length),this.regexp?nX(this,r,t,i):nT(this,r,t,i)}}class nZ{constructor(e){this.spec=e}}function nT(e,t,i,r){var n,s;return new no(t.doc,e.unquoted,i,r,e.caseSensitive?void 0:e=>e.toLowerCase(),e.wholeWord?(n=t.doc,s=t.charCategorizer(t.selection.main.head),(e,t,i,r)=>((r>e||r+i.length<t)&&(r=Math.max(0,e-2),i=n.sliceString(r,Math.min(n.length,t+2))),(s(nC(i,e-r))!=f.D0.Word||s(nA(i,e-r))!=f.D0.Word)&&(s(nA(i,t-r))!=f.D0.Word||s(nC(i,t-r))!=f.D0.Word))):void 0)}class n_ extends nZ{constructor(e){super(e)}nextMatch(e,t,i){let r=nT(this.spec,e,i,e.doc.length).nextOverlapping();if(r.done){let i=Math.min(e.doc.length,t+this.spec.unquoted.length);r=nT(this.spec,e,0,i).nextOverlapping()}return r.done||r.value.from==t&&r.value.to==i?null:r.value}prevMatchInRange(e,t,i){for(let r=i;;){let i=Math.max(t,r-1e4-this.spec.unquoted.length),n=nT(this.spec,e,i,r),s=null;for(;!n.nextOverlapping().done;)s=n.value;if(s)return s;if(i==t)return null;r-=1e4}}prevMatch(e,t,i){let r=this.prevMatchInRange(e,0,t);return r||(r=this.prevMatchInRange(e,Math.max(0,i-this.spec.unquoted.length),e.doc.length)),r&&(r.from!=t||r.to!=i)?r:null}getReplacement(e){return this.spec.unquote(this.spec.replace)}matchAll(e,t){let i=nT(this.spec,e,0,e.doc.length),r=[];for(;!i.next().done;){if(r.length>=t)return null;r.push(i.value)}return r}highlight(e,t,i,r){let n=nT(this.spec,e,Math.max(0,t-this.spec.unquoted.length),Math.min(i+this.spec.unquoted.length,e.doc.length));for(;!n.next().done;)r(n.value.from,n.value.to)}}function nX(e,t,i,r){var n;return new nh(t.doc,e.search,{ignoreCase:!e.caseSensitive,test:e.wholeWord?(n=t.charCategorizer(t.selection.main.head),(e,t,i)=>!i[0].length||(n(nC(i.input,i.index))!=f.D0.Word||n(nA(i.input,i.index))!=f.D0.Word)&&(n(nA(i.input,i.index+i[0].length))!=f.D0.Word||n(nC(i.input,i.index+i[0].length))!=f.D0.Word)):void 0},i,r)}function nC(e,t){return e.slice((0,f.cp)(e,t,!1),t)}function nA(e,t){return e.slice(t,(0,f.cp)(e,t))}class nR extends nZ{nextMatch(e,t,i){let r=nX(this.spec,e,i,e.doc.length).next();return r.done&&(r=nX(this.spec,e,0,t).next()),r.done?null:r.value}prevMatchInRange(e,t,i){for(let r=1;;r++){let n=Math.max(t,i-1e4*r),s=nX(this.spec,e,n,i),o=null;for(;!s.next().done;)o=s.value;if(o&&(n==t||o.from>n+10))return o;if(n==t)return null}}prevMatch(e,t,i){return this.prevMatchInRange(e,0,t)||this.prevMatchInRange(e,i,e.doc.length)}getReplacement(e){return this.spec.unquote(this.spec.replace).replace(/\$([$&]|\d+)/g,(t,i)=>{if("&"==i)return e.match[0];if("$"==i)return"$";for(let t=i.length;t>0;t--){let r=+i.slice(0,t);if(r>0&&r<e.match.length)return e.match[r]+i.slice(t)}return t})}matchAll(e,t){let i=nX(this.spec,e,0,e.doc.length),r=[];for(;!i.next().done;){if(r.length>=t)return null;r.push(i.value)}return r}highlight(e,t,i,r){let n=nX(this.spec,e,Math.max(0,t-250),Math.min(i+250,e.doc.length));for(;!n.next().done;)r(n.value.from,n.value.to)}}let nq=f.Py.define(),nM=f.Py.define(),nj=f.QQ.define({create:e=>new nY(nD(e).create(),null),update(e,t){for(let i of t.effects)i.is(nq)?e=new nY(i.value.create(),e.panel):i.is(nM)&&(e=new nY(e.query,i.value?nU:null));return e},provide:e=>u.mH.from(e,e=>e.panel)});class nY{constructor(e,t){this.query=e,this.panel=t}}let nN=u.p.mark({class:"cm-searchMatch"}),nI=u.p.mark({class:"cm-searchMatch cm-searchMatch-selected"}),nE=u.lg.fromClass(class{constructor(e){this.view=e,this.decorations=this.highlight(e.state.field(nj))}update(e){let t=e.state.field(nj);(t!=e.startState.field(nj)||e.docChanged||e.selectionSet||e.viewportChanged)&&(this.decorations=this.highlight(t))}highlight({query:e,panel:t}){if(!t||!e.spec.valid)return u.p.none;let{view:i}=this,r=new f.f_;for(let t=0,n=i.visibleRanges,s=n.length;t<s;t++){let{from:o,to:a}=n[t];for(;t<s-1&&a>n[t+1].from-500;)a=n[++t].to;e.highlight(i.state,o,a,(e,t)=>{let n=i.state.selection.ranges.some(i=>i.from==e&&i.to==t);r.add(e,t,n?nI:nN)})}return r.finish()}},{decorations:e=>e.decorations});function nz(e){return t=>{let i=t.state.field(nj,!1);return i&&i.query.spec.valid?e(t,i):nK(t)}}let nV=nz((e,{query:t})=>{let{to:i}=e.state.selection.main,r=t.nextMatch(e.state,i,i);if(!r)return!1;let n=f.jT.single(r.from,r.to),s=e.state.facet(n$);return e.dispatch({selection:n,effects:[n4(e,r),s.scrollToMatch(n.main,e)],userEvent:"select.search"}),nF(e),!0}),nL=nz((e,{query:t})=>{let{state:i}=e,{from:r}=i.selection.main,n=t.prevMatch(i,r,r);if(!n)return!1;let s=f.jT.single(n.from,n.to),o=e.state.facet(n$);return e.dispatch({selection:s,effects:[n4(e,n),o.scrollToMatch(s.main,e)],userEvent:"select.search"}),nF(e),!0}),nW=nz((e,{query:t})=>{let i=t.matchAll(e.state,1e3);return!!i&&!!i.length&&(e.dispatch({selection:f.jT.create(i.map(e=>f.jT.range(e.from,e.to))),userEvent:"select.search.matches"}),!0)}),nG=nz((e,{query:t})=>{let{state:i}=e,{from:r,to:n}=i.selection.main;if(i.readOnly)return!1;let s=t.nextMatch(i,r,r);if(!s)return!1;let o=s,a=[],l,h,c=[];o.from==r&&o.to==n&&(h=i.toText(t.getReplacement(o)),a.push({from:o.from,to:o.to,insert:h}),o=t.nextMatch(i,o.from,o.to),c.push(u.tk.announce.of(i.phrase("replaced match on line $",i.doc.lineAt(r).number)+".")));let p=e.state.changes(a);return o&&(l=f.jT.single(o.from,o.to).map(p),c.push(n4(e,o)),c.push(i.facet(n$).scrollToMatch(l.main,e))),e.dispatch({changes:p,selection:l,effects:c,userEvent:"input.replace"}),!0}),nB=nz((e,{query:t})=>{if(e.state.readOnly)return!1;let i=t.matchAll(e.state,1e9).map(e=>{let{from:i,to:r}=e;return{from:i,to:r,insert:t.getReplacement(e)}});if(!i.length)return!1;let r=e.state.phrase("replaced $ matches",i.length)+".";return e.dispatch({changes:i,effects:u.tk.announce.of(r),userEvent:"input.replace.all"}),!0});function nU(e){return e.state.facet(n$).createPanel(e)}function nD(e,t){var i,r,n,s,o;let a=e.selection.main,l=a.empty||a.to>a.from+100?"":e.sliceDoc(a.from,a.to);if(t&&!l)return t;let h=e.facet(n$);return new nP({search:(null!==(i=null==t?void 0:t.literal)&&void 0!==i?i:h.literal)?l:l.replace(/\n/g,"\\n"),caseSensitive:null!==(r=null==t?void 0:t.caseSensitive)&&void 0!==r?r:h.caseSensitive,literal:null!==(n=null==t?void 0:t.literal)&&void 0!==n?n:h.literal,regexp:null!==(s=null==t?void 0:t.regexp)&&void 0!==s?s:h.regexp,wholeWord:null!==(o=null==t?void 0:t.wholeWord)&&void 0!==o?o:h.wholeWord})}function nJ(e){let t=(0,u.Sd)(e,nU);return t&&t.dom.querySelector("[main-field]")}function nF(e){let t=nJ(e);t&&t==e.root.activeElement&&t.select()}let nK=e=>{let t=e.state.field(nj,!1);if(t&&t.panel){let i=nJ(e);if(i&&i!=e.root.activeElement){let r=nD(e.state,t.query.spec);r.valid&&e.dispatch({effects:nq.of(r)}),i.focus(),i.select()}}else e.dispatch({effects:[nM.of(!0),t?nq.of(nD(e.state,t.query.spec)):f.Py.appendConfig.of(n7)]});return!0},nH=e=>{let t=e.state.field(nj,!1);if(!t||!t.panel)return!1;let i=(0,u.Sd)(e,nU);return i&&i.dom.contains(e.root.activeElement)&&e.focus(),e.dispatch({effects:nM.of(!1)}),!0},n0=[{key:"Mod-f",run:nK,scope:"editor search-panel"},{key:"F3",run:nV,shift:nL,scope:"editor search-panel",preventDefault:!0},{key:"Mod-g",run:nV,shift:nL,scope:"editor search-panel",preventDefault:!0},{key:"Escape",run:nH,scope:"editor search-panel"},{key:"Mod-Shift-l",run:({state:e,dispatch:t})=>{let i=e.selection;if(i.ranges.length>1||i.main.empty)return!1;let{from:r,to:n}=i.main,s=[],o=0;for(let t=new no(e.doc,e.sliceDoc(r,n));!t.next().done;){if(s.length>1e3)return!1;t.value.from==r&&(o=s.length),s.push(f.jT.range(t.value.from,t.value.to))}return t(e.update({selection:f.jT.create(s,o),userEvent:"select.search.matches"})),!0}},{key:"Mod-Alt-g",run:e=>{let t=(0,u.Sd)(e,nO);if(!t){let i=[nd.of(!0)];null==e.state.field(ng,!1)&&i.push(f.Py.appendConfig.of([ng,nm])),e.dispatch({effects:i}),t=(0,u.Sd)(e,nO)}return t&&t.dom.querySelector("input").select(),!0}},{key:"Mod-d",run:({state:e,dispatch:t})=>{let{ranges:i}=e.selection;if(i.some(e=>e.from===e.to))return nw({state:e,dispatch:t});let r=e.sliceDoc(i[0].from,i[0].to);if(e.selection.ranges.some(t=>e.sliceDoc(t.from,t.to)!=r))return!1;let n=function(e,t){let{main:i,ranges:r}=e.selection,n=e.wordAt(i.head),s=n&&n.from==i.from&&n.to==i.to;for(let i=!1,n=new no(e.doc,t,r[r.length-1].to);;)if(n.next(),n.done){if(i)return null;n=new no(e.doc,t,0,Math.max(0,r[r.length-1].from-1)),i=!0}else{if(i&&r.some(e=>e.from==n.value.from))continue;if(s){let t=e.wordAt(n.value.from);if(!t||t.from!=n.value.from||t.to!=n.value.to)continue}return n.value}}(e,r);return!!n&&(t(e.update({selection:e.selection.addRange(f.jT.range(n.from,n.to),!1),effects:u.tk.scrollIntoView(n.to)})),!0)},preventDefault:!0}];class n1{constructor(e){this.view=e;let t=this.query=e.state.field(nj).query.spec;function i(e,t,i){return nn("button",{class:"cm-button",name:e,onclick:t,type:"button"},i)}this.commit=this.commit.bind(this),this.searchField=nn("input",{value:t.search,placeholder:n2(e,"Find"),"aria-label":n2(e,"Find"),class:"cm-textfield",name:"search",form:"","main-field":"true",onchange:this.commit,onkeyup:this.commit}),this.replaceField=nn("input",{value:t.replace,placeholder:n2(e,"Replace"),"aria-label":n2(e,"Replace"),class:"cm-textfield",name:"replace",form:"",onchange:this.commit,onkeyup:this.commit}),this.caseField=nn("input",{type:"checkbox",name:"case",form:"",checked:t.caseSensitive,onchange:this.commit}),this.reField=nn("input",{type:"checkbox",name:"re",form:"",checked:t.regexp,onchange:this.commit}),this.wordField=nn("input",{type:"checkbox",name:"word",form:"",checked:t.wholeWord,onchange:this.commit}),this.dom=nn("div",{onkeydown:e=>this.keydown(e),class:"cm-search"},[this.searchField,i("next",()=>nV(e),[n2(e,"next")]),i("prev",()=>nL(e),[n2(e,"previous")]),i("select",()=>nW(e),[n2(e,"all")]),nn("label",null,[this.caseField,n2(e,"match case")]),nn("label",null,[this.reField,n2(e,"regexp")]),nn("label",null,[this.wordField,n2(e,"by word")]),...e.state.readOnly?[]:[nn("br"),this.replaceField,i("replace",()=>nG(e),[n2(e,"replace")]),i("replaceAll",()=>nB(e),[n2(e,"replace all")])],nn("button",{name:"close",onclick:()=>nH(e),"aria-label":n2(e,"close"),type:"button"},["\xd7"])])}commit(){let e=new nP({search:this.searchField.value,caseSensitive:this.caseField.checked,regexp:this.reField.checked,wholeWord:this.wordField.checked,replace:this.replaceField.value});e.eq(this.query)||(this.query=e,this.view.dispatch({effects:nq.of(e)}))}keydown(e){(0,u.$1)(this.view,e,"search-panel")?e.preventDefault():13==e.keyCode&&e.target==this.searchField?(e.preventDefault(),(e.shiftKey?nL:nV)(this.view)):13==e.keyCode&&e.target==this.replaceField&&(e.preventDefault(),nG(this.view))}update(e){for(let t of e.transactions)for(let e of t.effects)e.is(nq)&&!e.value.eq(this.query)&&this.setQuery(e.value)}setQuery(e){this.query=e,this.searchField.value=e.search,this.replaceField.value=e.replace,this.caseField.checked=e.caseSensitive,this.reField.checked=e.regexp,this.wordField.checked=e.wholeWord}mount(){this.searchField.select()}get pos(){return 80}get top(){return this.view.state.facet(n$).top}}function n2(e,t){return e.state.phrase(t)}let n5=/[\s\.,:;?!]/;function n4(e,{from:t,to:i}){let r=e.state.doc.lineAt(t),n=e.state.doc.lineAt(i).to,s=Math.max(r.from,t-30),o=Math.min(n,i+30),a=e.state.sliceDoc(s,o);if(s!=r.from){for(let e=0;e<30;e++)if(!n5.test(a[e+1])&&n5.test(a[e])){a=a.slice(e);break}}if(o!=n){for(let e=a.length-1;e>a.length-30;e--)if(!n5.test(a[e-1])&&n5.test(a[e])){a=a.slice(0,e);break}}return u.tk.announce.of(`${e.state.phrase("current match")}. ${a} ${e.state.phrase("on line")} ${r.number}.`)}let n3=u.tk.baseTheme({".cm-panel.cm-search":{padding:"2px 6px 4px",position:"relative","& [name=close]":{position:"absolute",top:"0",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",padding:0,margin:0},"& input, & button, & label":{margin:".2em .6em .2em 0"},"& input[type=checkbox]":{marginRight:".2em"},"& label":{fontSize:"80%",whiteSpace:"pre"}},"&light .cm-searchMatch":{backgroundColor:"#ffff0054"},"&dark .cm-searchMatch":{backgroundColor:"#00ffff8a"},"&light .cm-searchMatch-selected":{backgroundColor:"#ff6a0054"},"&dark .cm-searchMatch-selected":{backgroundColor:"#ff00ff8a"}}),n7=[nj,f.Wl.low(nE),n3];class n9{constructor(e,t,i,r){this.state=e,this.pos=t,this.explicit=i,this.view=r,this.abortListeners=[],this.abortOnDocChange=!1}tokenBefore(e){let t=tH(this.state).resolveInner(this.pos,-1);for(;t&&0>e.indexOf(t.name);)t=t.parent;return t?{from:t.from,to:this.pos,text:this.state.sliceDoc(t.from,this.pos),type:t.type}:null}matchBefore(e){let t=this.state.doc.lineAt(this.pos),i=Math.max(t.from,this.pos-250),r=t.text.slice(i-t.from,this.pos-t.from),n=r.search(st(e,!1));return n<0?null:{from:i+n,to:this.pos,text:r.slice(n)}}get aborted(){return null==this.abortListeners}addEventListener(e,t,i){"abort"==e&&this.abortListeners&&(this.abortListeners.push(t),i&&i.onDocChange&&(this.abortOnDocChange=!0))}}function n6(e){let t=Object.keys(e).join(""),i=/\w/.test(t);return i&&(t=t.replace(/\w/g,"")),`[${i?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}class n8{constructor(e,t,i,r){this.completion=e,this.source=t,this.match=i,this.score=r}}function se(e){return e.selection.main.from}function st(e,t){var i;let{source:r}=e,n=t&&"^"!=r[0],s="$"!=r[r.length-1];return n||s?RegExp(`${n?"^":""}(?:${r})${s?"$":""}`,null!==(i=e.flags)&&void 0!==i?i:e.ignoreCase?"i":""):e}let si=f.q6.define(),sr=new WeakMap;function sn(e){if(!Array.isArray(e))return e;let t=sr.get(e);return t||sr.set(e,t=function(e){let t=e.map(e=>"string"==typeof e?{label:e}:e),[i,r]=t.every(e=>/^\w+$/.test(e.label))?[/\w*$/,/\w+$/]:function(e){let t=Object.create(null),i=Object.create(null);for(let{label:r}of e){t[r[0]]=!0;for(let e=1;e<r.length;e++)i[r[e]]=!0}let r=n6(t)+n6(i)+"*$";return[RegExp("^"+r),new RegExp(r)]}(t);return e=>{let n=e.matchBefore(r);return n||e.explicit?{from:n?n.from:e.pos,options:t,validFor:i}:null}}(e)),t}let ss=f.Py.define(),so=f.Py.define();class sa{constructor(e){this.pattern=e,this.chars=[],this.folded=[],this.any=[],this.precise=[],this.byWord=[],this.score=0,this.matched=[];for(let t=0;t<e.length;){let i=(0,f.gm)(e,t),r=(0,f.nZ)(i);this.chars.push(i);let n=e.slice(t,t+r),s=n.toUpperCase();this.folded.push((0,f.gm)(s==n?n.toLowerCase():s,0)),t+=r}this.astral=e.length!=this.chars.length}ret(e,t){return this.score=e,this.matched=t,this}match(e){if(0==this.pattern.length)return this.ret(-100,[]);if(e.length<this.pattern.length)return null;let{chars:t,folded:i,any:r,precise:n,byWord:s}=this;if(1==t.length){let r=(0,f.gm)(e,0),n=(0,f.nZ)(r),s=n==e.length?0:-100;if(r==t[0]);else{if(r!=i[0])return null;s+=-200}return this.ret(s,[0,n])}let o=e.indexOf(this.pattern);if(0==o)return this.ret(e.length==this.pattern.length?0:-100,[0,this.pattern.length]);let a=t.length,l=0;if(o<0){for(let n=0,s=Math.min(e.length,200);n<s&&l<a;){let s=(0,f.gm)(e,n);(s==t[l]||s==i[l])&&(r[l++]=n),n+=(0,f.nZ)(s)}if(l<a)return null}let h=0,c=0,u=!1,p=0,O=-1,d=-1,g=/[a-z]/.test(e),m=!0;for(let r=0,l=Math.min(e.length,200),y=0;r<l&&c<a;){let l=(0,f.gm)(e,r);o<0&&(h<a&&l==t[h]&&(n[h++]=r),p<a&&(l==t[p]||l==i[p]?(0==p&&(O=r),d=r+1,p++):p=0));let x,k=l<255?l>=48&&l<=57||l>=97&&l<=122?2:l>=65&&l<=90?1:0:(x=(0,f.bg)(l))!=x.toLowerCase()?1:x!=x.toUpperCase()?2:0;(!r||1==k&&g||0==y&&0!=k)&&(t[c]==l||i[c]==l&&(u=!0)?s[c++]=r:s.length&&(m=!1)),y=k,r+=(0,f.nZ)(l)}return c==a&&0==s[0]&&m?this.result(-100+(u?-200:0),s,e):p==a&&0==O?this.ret(-200-e.length+(d==e.length?0:-100),[0,d]):o>-1?this.ret(-700-e.length,[o,o+this.pattern.length]):p==a?this.ret(-900-e.length,[O,d]):c==a?this.result(-100+(u?-200:0)+-700+(m?0:-1100),s,e):2==t.length?null:this.result((r[0]?-700:0)+-200+-1100,r,e)}result(e,t,i){let r=[],n=0;for(let e of t){let t=e+(this.astral?(0,f.nZ)((0,f.gm)(i,e)):1);n&&r[n-1]==e?r[n-1]=t:(r[n++]=e,r[n++]=t)}return this.ret(e-i.length,r)}}class sl{constructor(e){this.pattern=e,this.matched=[],this.score=0,this.folded=e.toLowerCase()}match(e){if(e.length<this.pattern.length)return null;let t=e.slice(0,this.pattern.length),i=t==this.pattern?0:t.toLowerCase()==this.folded?-200:null;return null==i?null:(this.matched=[0,t.length],this.score=i+(e.length==this.pattern.length?0:-100),this)}}let sh=f.r$.define({combine:e=>(0,f.BO)(e,{activateOnTyping:!0,activateOnCompletion:()=>!1,activateOnTypingDelay:100,selectOnOpen:!0,override:null,closeOnBlur:!0,maxRenderedOptions:100,defaultKeymap:!0,tooltipClass:()=>"",optionClass:()=>"",aboveCursor:!1,icons:!0,addToOptions:[],positionInfo:sf,filterStrict:!1,compareCompletions:(e,t)=>e.label.localeCompare(t.label),interactionDelay:75,updateSyncTime:100},{defaultKeymap:(e,t)=>e&&t,closeOnBlur:(e,t)=>e&&t,icons:(e,t)=>e&&t,tooltipClass:(e,t)=>i=>sc(e(i),t(i)),optionClass:(e,t)=>i=>sc(e(i),t(i)),addToOptions:(e,t)=>e.concat(t),filterStrict:(e,t)=>e||t})});function sc(e,t){return e?t?e+" "+t:e:t}function sf(e,t,i,r,n,s){let o=e.textDirection==u.Nm.RTL,a=o,l=!1,h="top",c,f,p=t.left-n.left,O=n.right-t.right,d=r.right-r.left,g=r.bottom-r.top;if(a&&p<Math.min(d,O)?a=!1:!a&&O<Math.min(d,p)&&(a=!0),d<=(a?p:O))c=Math.max(n.top,Math.min(i.top,n.bottom-g))-t.top,f=Math.min(400,a?p:O);else{l=!0,f=Math.min(400,(o?t.right:n.right-t.left)-30);let e=n.bottom-t.bottom;e>=g||e>t.top?c=i.bottom-t.top:(h="bottom",c=t.bottom-i.top)}return{style:`${h}: ${c/((t.bottom-t.top)/s.offsetHeight)}px; max-width: ${f/((t.right-t.left)/s.offsetWidth)}px`,class:"cm-completionInfo-"+(l?o?"left-narrow":"right-narrow":a?"left":"right")}}function su(e,t,i){if(e<=i)return{from:0,to:e};if(t<0&&(t=0),t<=e>>1){let e=Math.floor(t/i);return{from:e*i,to:(e+1)*i}}let r=Math.floor((e-t)/i);return{from:e-(r+1)*i,to:e-r*i}}class sp{constructor(e,t,i){let r;this.view=e,this.stateField=t,this.applyCompletion=i,this.info=null,this.infoDestroy=null,this.placeInfoReq={read:()=>this.measureInfo(),write:e=>this.placeInfo(e),key:this},this.space=null,this.currentClass="";let n=e.state.field(t),{options:s,selected:o}=n.open,a=e.state.facet(sh);this.optionContent=(r=a.addToOptions.slice(),a.icons&&r.push({render(e){let t=document.createElement("div");return t.classList.add("cm-completionIcon"),e.type&&t.classList.add(...e.type.split(/\s+/g).map(e=>"cm-completionIcon-"+e)),t.setAttribute("aria-hidden","true"),t},position:20}),r.push({render(e,t,i,r){let n=document.createElement("span");n.className="cm-completionLabel";let s=e.displayLabel||e.label,o=0;for(let e=0;e<r.length;){let t=r[e++],i=r[e++];t>o&&n.appendChild(document.createTextNode(s.slice(o,t)));let a=n.appendChild(document.createElement("span"));a.appendChild(document.createTextNode(s.slice(t,i))),a.className="cm-completionMatchedText",o=i}return o<s.length&&n.appendChild(document.createTextNode(s.slice(o))),n},position:50},{render(e){if(!e.detail)return null;let t=document.createElement("span");return t.className="cm-completionDetail",t.textContent=e.detail,t},position:80}),r.sort((e,t)=>e.position-t.position).map(e=>e.render)),this.optionClass=a.optionClass,this.tooltipClass=a.tooltipClass,this.range=su(s.length,o,a.maxRenderedOptions),this.dom=document.createElement("div"),this.dom.className="cm-tooltip-autocomplete",this.updateTooltipClass(e.state),this.dom.addEventListener("mousedown",i=>{let{options:r}=e.state.field(t).open;for(let t=i.target,n;t&&t!=this.dom;t=t.parentNode)if("LI"==t.nodeName&&(n=/-(\d+)$/.exec(t.id))&&+n[1]<r.length){this.applyCompletion(e,r[+n[1]]),i.preventDefault();return}}),this.dom.addEventListener("focusout",t=>{let i=e.state.field(this.stateField,!1);i&&i.tooltip&&e.state.facet(sh).closeOnBlur&&t.relatedTarget!=e.contentDOM&&e.dispatch({effects:so.of(null)})}),this.showOptions(s,n.id)}mount(){this.updateSel()}showOptions(e,t){this.list&&this.list.remove(),this.list=this.dom.appendChild(this.createListBox(e,t,this.range)),this.list.addEventListener("scroll",()=>{this.info&&this.view.requestMeasure(this.placeInfoReq)})}update(e){var t;let i=e.state.field(this.stateField),r=e.startState.field(this.stateField);if(this.updateTooltipClass(e.state),i!=r){let{options:n,selected:s,disabled:o}=i.open;r.open&&r.open.options==n||(this.range=su(n.length,s,e.state.facet(sh).maxRenderedOptions),this.showOptions(n,i.id)),this.updateSel(),o!=(null===(t=r.open)||void 0===t?void 0:t.disabled)&&this.dom.classList.toggle("cm-tooltip-autocomplete-disabled",!!o)}}updateTooltipClass(e){let t=this.tooltipClass(e);if(t!=this.currentClass){for(let e of this.currentClass.split(" "))e&&this.dom.classList.remove(e);for(let e of t.split(" "))e&&this.dom.classList.add(e);this.currentClass=t}}positioned(e){this.space=e,this.info&&this.view.requestMeasure(this.placeInfoReq)}updateSel(){let e=this.view.state.field(this.stateField),t=e.open;if((t.selected>-1&&t.selected<this.range.from||t.selected>=this.range.to)&&(this.range=su(t.options.length,t.selected,this.view.state.facet(sh).maxRenderedOptions),this.showOptions(t.options,e.id)),this.updateSelectedOption(t.selected)){this.destroyInfo();let{completion:i}=t.options[t.selected],{info:r}=i;if(!r)return;let n="string"==typeof r?document.createTextNode(r):r(i);if(!n)return;"then"in n?n.then(t=>{t&&this.view.state.field(this.stateField,!1)==e&&this.addInfoPane(t,i)}).catch(e=>(0,u.OO)(this.view.state,e,"completion info")):this.addInfoPane(n,i)}}addInfoPane(e,t){this.destroyInfo();let i=this.info=document.createElement("div");if(i.className="cm-tooltip cm-completionInfo",null!=e.nodeType)i.appendChild(e),this.infoDestroy=null;else{let{dom:t,destroy:r}=e;i.appendChild(t),this.infoDestroy=r||null}this.dom.appendChild(i),this.view.requestMeasure(this.placeInfoReq)}updateSelectedOption(e){var t,i;let r,n,s,o=null;for(let t=this.list.firstChild,i=this.range.from;t;t=t.nextSibling,i++)"LI"==t.nodeName&&t.id?i==e?t.hasAttribute("aria-selected")||(t.setAttribute("aria-selected","true"),o=t):t.hasAttribute("aria-selected")&&t.removeAttribute("aria-selected"):i--;return o&&(t=this.list,i=o,r=t.getBoundingClientRect(),n=i.getBoundingClientRect(),s=r.height/t.offsetHeight,n.top<r.top?t.scrollTop-=(r.top-n.top)/s:n.bottom>r.bottom&&(t.scrollTop+=(n.bottom-r.bottom)/s)),o}measureInfo(){let e=this.dom.querySelector("[aria-selected]");if(!e||!this.info)return null;let t=this.dom.getBoundingClientRect(),i=this.info.getBoundingClientRect(),r=e.getBoundingClientRect(),n=this.space;if(!n){let e=this.dom.ownerDocument.documentElement;n={left:0,top:0,right:e.clientWidth,bottom:e.clientHeight}}return r.top>Math.min(n.bottom,t.bottom)-10||r.bottom<Math.max(n.top,t.top)+10?null:this.view.state.facet(sh).positionInfo(this.view,t,r,i,n,this.dom)}placeInfo(e){this.info&&(e?(e.style&&(this.info.style.cssText=e.style),this.info.className="cm-tooltip cm-completionInfo "+(e.class||"")):this.info.style.cssText="top: -1e6px")}createListBox(e,t,i){let r=document.createElement("ul");r.id=t,r.setAttribute("role","listbox"),r.setAttribute("aria-expanded","true"),r.setAttribute("aria-label",this.view.state.phrase("Completions")),r.addEventListener("mousedown",e=>{e.target==r&&e.preventDefault()});let n=null;for(let s=i.from;s<i.to;s++){let{completion:o,match:a}=e[s],{section:l}=o;if(l){let e="string"==typeof l?l:l.name;e!=n&&(s>i.from||0==i.from)&&(n=e,"string"!=typeof l&&l.header?r.appendChild(l.header(l)):r.appendChild(document.createElement("completion-section")).textContent=e)}let h=r.appendChild(document.createElement("li"));h.id=t+"-"+s,h.setAttribute("role","option");let c=this.optionClass(o);for(let e of(c&&(h.className=c),this.optionContent)){let t=e(o,this.view.state,this.view,a);t&&h.appendChild(t)}}return i.from&&r.classList.add("cm-completionListIncompleteTop"),i.to<e.length&&r.classList.add("cm-completionListIncompleteBottom"),r}destroyInfo(){this.info&&(this.infoDestroy&&this.infoDestroy(),this.info.remove(),this.info=null)}destroy(){this.destroyInfo()}}function sO(e){return 100*(e.boost||0)+(e.apply?10:0)+(e.info?5:0)+(e.type?1:0)}class sd{constructor(e,t,i,r,n,s){this.options=e,this.attrs=t,this.tooltip=i,this.timestamp=r,this.selected=n,this.disabled=s}setSelected(e,t){return e==this.selected||e>=this.options.length?this:new sd(this.options,sx(t,e),this.tooltip,this.timestamp,e,this.disabled)}static build(e,t,i,r,n,s){if(r&&!s&&e.some(e=>e.isPending))return r.setDisabled();let o=function(e,t){let i=[],r=null,n=e=>{i.push(e);let{section:t}=e.completion;if(t){r||(r=[]);let e="string"==typeof t?t:t.name;r.some(t=>t.name==e)||r.push("string"==typeof t?{name:e}:t)}},s=t.facet(sh);for(let r of e)if(r.hasResult()){let e=r.result.getMatch;if(!1===r.result.filter)for(let t of r.result.options)n(new n8(t,r.source,e?e(t):[],1e9-i.length));else{let i=t.sliceDoc(r.from,r.to),o,a=s.filterStrict?new sl(i):new sa(i);for(let t of r.result.options)if(o=a.match(t.label)){let i=t.displayLabel?e?e(t,o.matched):[]:o.matched;n(new n8(t,r.source,i,o.score+(t.boost||0)))}}}if(r){let e=Object.create(null),t=0;for(let i of r.sort((e,t)=>{var i,r;return(null!==(i=e.rank)&&void 0!==i?i:1e9)-(null!==(r=t.rank)&&void 0!==r?r:1e9)||(e.name<t.name?-1:1)}))t-=1e5,e[i.name]=t;for(let t of i){let{section:i}=t.completion;i&&(t.score+=e["string"==typeof i?i:i.name])}}let o=[],a=null,l=s.compareCompletions;for(let e of i.sort((e,t)=>t.score-e.score||l(e.completion,t.completion))){let t=e.completion;a&&a.label==t.label&&a.detail==t.detail&&(null==a.type||null==t.type||a.type==t.type)&&a.apply==t.apply&&a.boost==t.boost?sO(e.completion)>sO(a)&&(o[o.length-1]=e):o.push(e),a=e.completion}return o}(e,t);if(!o.length)return r&&e.some(e=>e.isPending)?r.setDisabled():null;let a=t.facet(sh).selectOnOpen?0:-1;if(r&&r.selected!=a&&-1!=r.selected){let e=r.options[r.selected].completion;for(let t=0;t<o.length;t++)if(o[t].completion==e){a=t;break}}return new sd(o,sx(i,a),{pos:e.reduce((e,t)=>t.hasResult()?Math.min(e,t.from):e,1e8),create:sZ,above:n.aboveCursor},r?r.timestamp:Date.now(),a,!1)}map(e){return new sd(this.options,this.attrs,Object.assign(Object.assign({},this.tooltip),{pos:e.mapPos(this.tooltip.pos)}),this.timestamp,this.selected,this.disabled)}setDisabled(){return new sd(this.options,this.attrs,this.tooltip,this.timestamp,this.selected,!0)}}class sg{constructor(e,t,i){this.active=e,this.id=t,this.open=i}static start(){return new sg(sk,"cm-ac-"+Math.floor(2e6*Math.random()).toString(36),null)}update(e){let{state:t}=e,i=t.facet(sh),r=(i.override||t.languageDataAt("autocomplete",se(t)).map(sn)).map(t=>(this.active.find(e=>e.source==t)||new sb(t,this.active.some(e=>0!=e.state)?1:0)).update(e,i));r.length==this.active.length&&r.every((e,t)=>e==this.active[t])&&(r=this.active);let n=this.open,s=e.effects.some(e=>e.is(sS));for(let o of(n&&e.docChanged&&(n=n.map(e.changes)),e.selection||r.some(t=>t.hasResult()&&e.changes.touchesRange(t.from,t.to))||!function(e,t){if(e==t)return!0;for(let i=0,r=0;;){for(;i<e.length&&!e[i].hasResult();)i++;for(;r<t.length&&!t[r].hasResult();)r++;let n=i==e.length,s=r==t.length;if(n||s)return n==s;if(e[i++].result!=t[r++].result)return!1}}(r,this.active)||s?n=sd.build(r,t,this.id,n,i,s):n&&n.disabled&&!r.some(e=>e.isPending)&&(n=null),!n&&r.every(e=>!e.isPending)&&r.some(e=>e.hasResult())&&(r=r.map(e=>e.hasResult()?new sb(e.source,0):e)),e.effects))o.is(sw)&&(n=n&&n.setSelected(o.value,this.id));return r==this.active&&n==this.open?this:new sg(r,this.id,n)}get tooltip(){return this.open?this.open.tooltip:null}get attrs(){return this.open?this.open.attrs:this.active.length?sm:sy}}let sm={"aria-autocomplete":"list"},sy={};function sx(e,t){let i={"aria-autocomplete":"list","aria-haspopup":"listbox","aria-controls":e};return t>-1&&(i["aria-activedescendant"]=e+"-"+t),i}let sk=[];function sQ(e,t){if(e.isUserEvent("input.complete")){let i=e.annotation(si);if(i&&t.activateOnCompletion(i))return 12}let i=e.isUserEvent("input.type");return i&&t.activateOnTyping?5:i?1:e.isUserEvent("delete.backward")?2:e.selection?8:e.docChanged?16:0}class sb{constructor(e,t,i=!1){this.source=e,this.state=t,this.explicit=i}hasResult(){return!1}get isPending(){return 1==this.state}update(e,t){let i=sQ(e,t),r=this;for(let t of((8&i||16&i&&this.touches(e))&&(r=new sb(r.source,0)),4&i&&0==r.state&&(r=new sb(this.source,1)),r=r.updateFor(e,i),e.effects))if(t.is(ss))r=new sb(r.source,1,t.value);else if(t.is(so))r=new sb(r.source,0);else if(t.is(sS))for(let e of t.value)e.source==r.source&&(r=e);return r}updateFor(e,t){return this.map(e.changes)}map(e){return this}touches(e){return e.changes.touchesRange(se(e.state))}}class sv extends sb{constructor(e,t,i,r,n,s){super(e,3,t),this.limit=i,this.result=r,this.from=n,this.to=s}hasResult(){return!0}updateFor(e,t){var i;if(!(3&t))return this.map(e.changes);let r=this.result;r.map&&!e.changes.empty&&(r=r.map(r,e.changes));let n=e.changes.mapPos(this.from),s=e.changes.mapPos(this.to,1),o=se(e.state);if(o>s||!r||2&t&&(se(e.startState)==this.from||o<this.limit))return new sb(this.source,4&t?1:0);let a=e.changes.mapPos(this.limit);return!function(e,t,i,r){if(!e)return!1;let n=t.sliceDoc(i,r);return"function"==typeof e?e(n,i,r,t):st(e,!0).test(n)}(r.validFor,e.state,n,s)?r.update&&(r=r.update(r,n,s,new n9(e.state,o,!1)))?new sv(this.source,this.explicit,a,r,r.from,null!==(i=r.to)&&void 0!==i?i:se(e.state)):new sb(this.source,1,this.explicit):new sv(this.source,this.explicit,a,r,n,s)}map(e){return e.empty?this:(this.result.map?this.result.map(this.result,e):this.result)?new sv(this.source,this.explicit,e.mapPos(this.limit),this.result,e.mapPos(this.from),e.mapPos(this.to,1)):new sb(this.source,0)}touches(e){return e.changes.touchesRange(this.from,this.to)}}let sS=f.Py.define({map:(e,t)=>e.map(e=>e.map(t))}),sw=f.Py.define(),s$=f.QQ.define({create:()=>sg.start(),update:(e,t)=>e.update(t),provide:e=>[u.hJ.from(e,e=>e.tooltip),u.tk.contentAttributes.from(e,e=>e.attrs)]});function sP(e,t){let i=t.completion.apply||t.completion.label,r=e.state.field(s$).active.find(e=>e.source==t.source);return r instanceof sv&&("string"==typeof i?e.dispatch(Object.assign(Object.assign({},function(e,t,i,r){let{main:n}=e.selection,s=i-n.from,o=r-n.from;return Object.assign(Object.assign({},e.changeByRange(a=>{if(a!=n&&i!=r&&e.sliceDoc(a.from+s,a.from+o)!=e.sliceDoc(i,r))return{range:a};let l=e.toText(t);return{changes:{from:a.from+s,to:r==n.from?a.to:a.from+o,insert:l},range:f.jT.cursor(a.from+s+l.length)}})),{scrollIntoView:!0,userEvent:"input.complete"})}(e.state,i,r.from,r.to)),{annotations:si.of(t.completion)})):i(e,t.completion,r.from,r.to),!0)}let sZ=e=>new sp(e,s$,sP);function sT(e,t="option"){return i=>{let r=i.state.field(s$,!1);if(!r||!r.open||r.open.disabled||Date.now()-r.open.timestamp<i.state.facet(sh).interactionDelay)return!1;let n=1,s;"page"==t&&(s=(0,u.gB)(i,r.open.tooltip))&&(n=Math.max(2,Math.floor(s.dom.offsetHeight/s.dom.querySelector("li").offsetHeight)-1));let{length:o}=r.open.options,a=r.open.selected>-1?r.open.selected+n*(e?1:-1):e?0:o-1;return a<0?a="page"==t?0:o-1:a>=o&&(a="page"==t?o-1:0),i.dispatch({effects:sw.of(a)}),!0}}let s_=e=>!!e.state.field(s$,!1)&&(e.dispatch({effects:ss.of(!0)}),!0);class sX{constructor(e,t){this.active=e,this.context=t,this.time=Date.now(),this.updates=[],this.done=void 0}}let sC=u.lg.fromClass(class{constructor(e){for(let t of(this.view=e,this.debounceUpdate=-1,this.running=[],this.debounceAccept=-1,this.pendingStart=!1,this.composing=0,e.state.field(s$).active))t.isPending&&this.startQuery(t)}update(e){let t=e.state.field(s$),i=e.state.facet(sh);if(!e.selectionSet&&!e.docChanged&&e.startState.field(s$)==t)return;let r=e.transactions.some(e=>{let t=sQ(e,i);return 8&t||(e.selection||e.docChanged)&&!(3&t)});for(let t=0;t<this.running.length;t++){let i=this.running[t];if(r||i.context.abortOnDocChange&&e.docChanged||i.updates.length+e.transactions.length>50&&Date.now()-i.time>1e3){for(let e of i.context.abortListeners)try{e()}catch(e){(0,u.OO)(this.view.state,e)}i.context.abortListeners=null,this.running.splice(t--,1)}else i.updates.push(...e.transactions)}this.debounceUpdate>-1&&clearTimeout(this.debounceUpdate),e.transactions.some(e=>e.effects.some(e=>e.is(ss)))&&(this.pendingStart=!0);let n=this.pendingStart?50:i.activateOnTypingDelay;if(this.debounceUpdate=t.active.some(e=>e.isPending&&!this.running.some(t=>t.active.source==e.source))?setTimeout(()=>this.startUpdate(),n):-1,0!=this.composing)for(let t of e.transactions)t.isUserEvent("input.type")?this.composing=2:2==this.composing&&t.selection&&(this.composing=3)}startUpdate(){this.debounceUpdate=-1,this.pendingStart=!1;let{state:e}=this.view,t=e.field(s$);for(let e of t.active)e.isPending&&!this.running.some(t=>t.active.source==e.source)&&this.startQuery(e);this.running.length&&t.open&&t.open.disabled&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(sh).updateSyncTime))}startQuery(e){let{state:t}=this.view,i=se(t),r=new n9(t,i,e.explicit,this.view),n=new sX(e,r);this.running.push(n),Promise.resolve(e.source(r)).then(e=>{n.context.aborted||(n.done=e||null,this.scheduleAccept())},e=>{this.view.dispatch({effects:so.of(null)}),(0,u.OO)(this.view.state,e)})}scheduleAccept(){this.running.every(e=>void 0!==e.done)?this.accept():this.debounceAccept<0&&(this.debounceAccept=setTimeout(()=>this.accept(),this.view.state.facet(sh).updateSyncTime))}accept(){var e;this.debounceAccept>-1&&clearTimeout(this.debounceAccept),this.debounceAccept=-1;let t=[],i=this.view.state.facet(sh),r=this.view.state.field(s$);for(let n=0;n<this.running.length;n++){let s=this.running[n];if(void 0===s.done)continue;if(this.running.splice(n--,1),s.done){let r=se(s.updates.length?s.updates[0].startState:this.view.state),n=Math.min(r,s.done.from+(s.active.explicit?0:1)),o=new sv(s.active.source,s.active.explicit,n,s.done,s.done.from,null!==(e=s.done.to)&&void 0!==e?e:r);for(let e of s.updates)o=o.update(e,i);if(o.hasResult()){t.push(o);continue}}let o=r.active.find(e=>e.source==s.active.source);if(o&&o.isPending){if(null==s.done){let e=new sb(s.active.source,0);for(let t of s.updates)e=e.update(t,i);e.isPending||t.push(e)}else this.startQuery(o)}}(t.length||r.open&&r.open.disabled)&&this.view.dispatch({effects:sS.of(t)})}},{eventHandlers:{blur(e){let t=this.view.state.field(s$,!1);if(t&&t.tooltip&&this.view.state.facet(sh).closeOnBlur){let i=t.open&&(0,u.gB)(this.view,t.open.tooltip);i&&i.dom.contains(e.relatedTarget)||setTimeout(()=>this.view.dispatch({effects:so.of(null)}),10)}},compositionstart(){this.composing=1},compositionend(){3==this.composing&&setTimeout(()=>this.view.dispatch({effects:ss.of(!1)}),20),this.composing=0}}}),sA="object"==typeof navigator&&/Win/.test(navigator.platform),sR=f.Wl.highest(u.tk.domEventHandlers({keydown(e,t){let i=t.state.field(s$,!1);if(!i||!i.open||i.open.disabled||i.open.selected<0||e.key.length>1||e.ctrlKey&&!(sA&&e.altKey)||e.metaKey)return!1;let r=i.open.options[i.open.selected],n=i.active.find(e=>e.source==r.source),s=r.completion.commitCharacters||n.result.commitCharacters;return s&&s.indexOf(e.key)>-1&&sP(t,r),!1}})),sq=u.tk.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"\xb7\xb7\xb7"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'\uD835\uDC65'"}},".cm-completionIcon-constant":{"&:after":{content:"'\uD835\uDC36'"}},".cm-completionIcon-type":{"&:after":{content:"'\uD835\uDC61'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'\uD83D\uDD11︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}}),sM=u.p.widget({widget:new class extends u.l9{toDOM(){let e=document.createElement("span");return e.className="cm-snippetFieldPosition",e}ignoreEvent(){return!1}}}),sj=u.p.mark({class:"cm-snippetField"});class sY{constructor(e,t){this.ranges=e,this.active=t,this.deco=u.p.set(e.map(e=>(e.from==e.to?sM:sj).range(e.from,e.to)))}map(e){let t=[];for(let i of this.ranges){let r=i.map(e);if(!r)return null;t.push(r)}return new sY(t,this.active)}selectionInsideField(e){return e.ranges.every(e=>this.ranges.some(t=>t.field==this.active&&t.from<=e.from&&t.to>=e.to))}}let sN=f.Py.define({map:(e,t)=>e&&e.map(t)}),sI=f.Py.define(),sE=f.QQ.define({create:()=>null,update(e,t){for(let i of t.effects){if(i.is(sN))return i.value;if(i.is(sI)&&e)return new sY(e.ranges,i.value)}return e&&t.docChanged&&(e=e.map(t.changes)),e&&t.selection&&!e.selectionInsideField(t.selection)&&(e=null),e},provide:e=>u.tk.decorations.from(e,e=>e?e.deco:u.p.none)});function sz(e){return({state:t,dispatch:i})=>{var r;let n=t.field(sE,!1);if(!n||e<0&&0==n.active)return!1;let s=n.active+e,o=e>0&&!n.ranges.some(t=>t.field==s+e);return i(t.update({selection:(r=n.ranges,f.jT.create(r.filter(e=>e.field==s).map(e=>f.jT.range(e.from,e.to)))),effects:sN.of(o?null:new sY(n.ranges,s)),scrollIntoView:!0})),!0}}let sV=sz(1),sL=sz(-1),sW={brackets:["(","[","{","'",'"'],before:")]}:;>",stringPrefixes:[]},sG=f.Py.define({map(e,t){let i=t.mapPos(e,-1,f.gc.TrackAfter);return null==i?void 0:i}}),sB=new class extends f.uU{};sB.startSide=1,sB.endSide=-1;let sU=f.QQ.define({create:()=>f.Xs.empty,update(e,t){if(e=e.map(t.changes),t.selection){let i=t.state.doc.lineAt(t.selection.main.head);e=e.update({filter:e=>e>=i.from&&e<=i.to})}for(let i of t.effects)i.is(sG)&&(e=e.update({add:[sB.range(i.value,i.value+1)]}));return e}}),sD="()[]{}<>\xab\xbb\xbb\xab［］｛｝";function sJ(e){for(let t=0;t<sD.length;t+=2)if(sD.charCodeAt(t)==e)return sD.charAt(t+1);return(0,f.bg)(e<128?e:e+1)}function sF(e,t){return e.languageDataAt("closeBrackets",t)[0]||sW}let sK="object"==typeof navigator&&/Android\b/.test(navigator.userAgent),sH=u.tk.inputHandler.of((e,t,i,r)=>{if((sK?e.composing:e.compositionStarted)||e.state.readOnly)return!1;let n=e.state.selection.main;if(r.length>2||2==r.length&&1==(0,f.nZ)((0,f.gm)(r,0))||t!=n.from||i!=n.to)return!1;let s=function(e,t){let i=sF(e,e.selection.main.head),r=i.brackets||sW.brackets;for(let n of r){let s=sJ((0,f.gm)(n,0));if(t==n)return s==n?function(e,t,i,r){let n=r.stringPrefixes||sW.stringPrefixes,s=null,o=e.changeByRange(r=>{if(!r.empty)return{changes:[{insert:t,from:r.from},{insert:t,from:r.to}],effects:sG.of(r.to+t.length),range:f.jT.range(r.anchor+t.length,r.head+t.length)};let o=r.head,a=s2(e.doc,o),l;if(a==t){if(s5(e,o))return{changes:{insert:t+t,from:o},effects:sG.of(o+t.length),range:f.jT.cursor(o+t.length)};if(s1(e,o)){let r=i&&e.sliceDoc(o,o+3*t.length)==t+t+t?t+t+t:t;return{changes:{from:o,to:o+r.length,insert:r},range:f.jT.cursor(o+r.length)}}}else if(i&&e.sliceDoc(o-2*t.length,o)==t+t&&(l=s4(e,o-2*t.length,n))>-1&&s5(e,l))return{changes:{insert:t+t+t+t,from:o},effects:sG.of(o+t.length),range:f.jT.cursor(o+t.length)};else if(e.charCategorizer(o)(a)!=f.D0.Word&&s4(e,o,n)>-1&&!function(e,t,i,r){let n=tH(e).resolveInner(t,-1),s=r.reduce((e,t)=>Math.max(e,t.length),0);for(let o=0;o<5;o++){let o=e.sliceDoc(n.from,Math.min(n.to,n.from+i.length+s)),a=o.indexOf(i);if(!a||a>-1&&r.indexOf(o.slice(0,a))>-1){let t=n.firstChild;for(;t&&t.from==n.from&&t.to-t.from>i.length+a;){if(e.sliceDoc(t.to-i.length,t.to)==i)return!1;t=t.firstChild}return!0}let l=n.to==t&&n.parent;if(!l)break;n=l}return!1}(e,o,t,n))return{changes:{insert:t+t,from:o},effects:sG.of(o+t.length),range:f.jT.cursor(o+t.length)};return{range:s=r}});return s?null:e.update(o,{scrollIntoView:!0,userEvent:"input.type"})}(e,n,r.indexOf(n+n+n)>-1,i):function(e,t,i,r){let n=null,s=e.changeByRange(s=>{if(!s.empty)return{changes:[{insert:t,from:s.from},{insert:i,from:s.to}],effects:sG.of(s.to+t.length),range:f.jT.range(s.anchor+t.length,s.head+t.length)};let o=s2(e.doc,s.head);return!o||/\s/.test(o)||r.indexOf(o)>-1?{changes:{insert:t+i,from:s.head},effects:sG.of(s.head+t.length),range:f.jT.cursor(s.head+t.length)}:{range:n=s}});return n?null:e.update(s,{scrollIntoView:!0,userEvent:"input.type"})}(e,n,s,i.before||sW.before);if(t==s&&s1(e,e.selection.main.from))return function(e,t,i){let r=null,n=e.changeByRange(t=>t.empty&&s2(e.doc,t.head)==i?{changes:{from:t.head,to:t.head+i.length,insert:i},range:f.jT.cursor(t.head+i.length)}:r={range:t});return r?null:e.update(n,{scrollIntoView:!0,userEvent:"input.type"})}(e,0,s)}return null}(e.state,r);return!!s&&(e.dispatch(s),!0)}),s0=[{key:"Backspace",run:({state:e,dispatch:t})=>{if(e.readOnly)return!1;let i=sF(e,e.selection.main.head).brackets||sW.brackets,r=null,n=e.changeByRange(t=>{if(t.empty){var n,s;let r;let o=(n=e.doc,s=t.head,r=n.sliceString(s-2,s),(0,f.nZ)((0,f.gm)(r,0))==r.length?r:r.slice(1));for(let r of i)if(r==o&&s2(e.doc,t.head)==sJ((0,f.gm)(r,0)))return{changes:{from:t.head-r.length,to:t.head+r.length},range:f.jT.cursor(t.head-r.length)}}return{range:r=t}});return r||t(e.update(n,{scrollIntoView:!0,userEvent:"delete.backward"})),!r}}];function s1(e,t){let i=!1;return e.field(sU).between(0,e.doc.length,e=>{e==t&&(i=!0)}),i}function s2(e,t){let i=e.sliceString(t,t+2);return i.slice(0,(0,f.nZ)((0,f.gm)(i,0)))}function s5(e,t){let i=tH(e).resolveInner(t+1);return i.parent&&i.from==t}function s4(e,t,i){let r=e.charCategorizer(t);if(r(e.sliceDoc(t-1,t))!=f.D0.Word)return t;for(let n of i){let i=t-n.length;if(e.sliceDoc(i,t)==n&&r(e.sliceDoc(i-1,i))!=f.D0.Word)return i}return -1}let s3=[{key:"Ctrl-Space",run:s_},{mac:"Alt-`",run:s_},{key:"Escape",run:e=>{let t=e.state.field(s$,!1);return!!(t&&t.active.some(e=>0!=e.state))&&(e.dispatch({effects:so.of(null)}),!0)}},{key:"ArrowDown",run:sT(!0)},{key:"ArrowUp",run:sT(!1)},{key:"PageDown",run:sT(!0,"page")},{key:"PageUp",run:sT(!1,"page")},{key:"Enter",run:e=>{let t=e.state.field(s$,!1);return!(e.state.readOnly||!t||!t.open||t.open.selected<0||t.open.disabled||Date.now()-t.open.timestamp<e.state.facet(sh).interactionDelay)&&sP(e,t.open.options[t.open.selected])}}],s7=f.Wl.highest(u.$f.computeN([sh],e=>e.facet(sh).defaultKeymap?[s3]:[]));class s9{constructor(e,t,i){this.from=e,this.to=t,this.diagnostic=i}}class s6{constructor(e,t,i){this.diagnostics=e,this.panel=t,this.selected=i}static init(e,t,i){let r=i.facet(ol).markerFilter;r&&(e=r(e,i));let n=e.slice().sort((e,t)=>e.from-t.from||e.to-t.to),s=new f.f_,o=[],a=0;for(let e=0;;){let t,r,l=e==n.length?null:n[e];if(!l&&!o.length)break;for(o.length?(t=a,r=o.reduce((e,t)=>Math.min(e,t.to),l&&l.from>t?l.from:1e8)):(t=l.from,r=l.to,o.push(l),e++);e<n.length;){let i=n[e];if(i.from==t&&(i.to>i.from||i.to==t))o.push(i),e++,r=Math.min(i.to,r);else{r=Math.min(i.from,r);break}}let h=function(e){let t="hint",i=1;for(let n of e){var r;let e="error"==(r=n.severity)?4:"warning"==r?3:"info"==r?2:1;e>i&&(i=e,t=n.severity)}return t}(o);if(o.some(e=>e.from==e.to||e.from==e.to-1&&i.doc.lineAt(e.from).to==e.from))s.add(t,t,u.p.widget({widget:new of(h),diagnostics:o.slice()}));else{let e=o.reduce((e,t)=>t.markClass?e+" "+t.markClass:e,"");s.add(t,r,u.p.mark({class:"cm-lintRange cm-lintRange-"+h+e,diagnostics:o.slice(),inclusiveEnd:o.some(e=>e.to>r)}))}a=r;for(let e=0;e<o.length;e++)o[e].to<=a&&o.splice(e--,1)}let l=s.finish();return new s6(l,t,s8(l))}}function s8(e,t=null,i=0){let r=null;return e.between(i,1e9,(e,i,{spec:n})=>{if(!(t&&0>n.diagnostics.indexOf(t))){if(r){if(0>n.diagnostics.indexOf(r.diagnostic))return!1;r=new s9(r.from,i,r.diagnostic)}else r=new s9(e,i,t||n.diagnostics[0])}}),r}function oe(e,t){let i=t.pos,r=t.end||i,n=e.state.facet(ol).hideOn(e,i,r);if(null!=n)return n;let s=e.startState.doc.lineAt(t.pos);return!!(e.effects.some(e=>e.is(ot))||e.changes.touchesRange(s.from,Math.max(s.to,r)))}let ot=f.Py.define(),oi=f.Py.define(),or=f.Py.define(),on=f.QQ.define({create:()=>new s6(u.p.none,null,null),update(e,t){if(t.docChanged&&e.diagnostics.size){let i=e.diagnostics.map(t.changes),r=null,n=e.panel;if(e.selected){let n=t.changes.mapPos(e.selected.from,1);r=s8(i,e.selected.diagnostic,n)||s8(i,null,n)}!i.size&&n&&t.state.facet(ol).autoPanel&&(n=null),e=new s6(i,n,r)}for(let i of t.effects)if(i.is(ot)){let r=t.state.facet(ol).autoPanel?i.value.length?op.open:null:e.panel;e=s6.init(i.value,r,t.state)}else i.is(oi)?e=new s6(e.diagnostics,i.value?op.open:null,e.selected):i.is(or)&&(e=new s6(e.diagnostics,e.panel,i.value));return e},provide:e=>[u.mH.from(e,e=>e.panel),u.tk.decorations.from(e,e=>e.diagnostics)]}),os=u.p.mark({class:"cm-lintRange cm-lintRange-active"}),oo=e=>{let t=e.state.field(on,!1);return!!t&&!!t.panel&&(e.dispatch({effects:oi.of(!1)}),!0)},oa=[{key:"Mod-Shift-m",run:e=>{var t,i;let r=e.state.field(on,!1);r&&r.panel||e.dispatch({effects:(t=e.state,i=[oi.of(!0)],t.field(on,!1)?i:i.concat(f.Py.appendConfig.of(om)))});let n=(0,u.Sd)(e,op.open);return n&&n.dom.querySelector(".cm-panel-lint ul").focus(),!0},preventDefault:!0},{key:"F8",run:e=>{let t=e.state.field(on,!1);if(!t)return!1;let i=e.state.selection.main,r=t.diagnostics.iter(i.to+1);return(!!r.value||!!(r=t.diagnostics.iter(0)).value&&(r.from!=i.from||r.to!=i.to))&&(e.dispatch({selection:{anchor:r.from,head:r.to},scrollIntoView:!0}),!0)}}],ol=f.r$.define({combine:e=>Object.assign({sources:e.map(e=>e.source).filter(e=>null!=e)},(0,f.BO)(e.map(e=>e.config),{delay:750,markerFilter:null,tooltipFilter:null,needsRefresh:null,hideOn:()=>null},{needsRefresh:(e,t)=>e?t?i=>e(i)||t(i):e:t}))});function oh(e){let t=[];if(e)r:for(let{name:i}of e){for(let e=0;e<i.length;e++){let r=i[e];if(/[a-zA-Z]/.test(r)&&!t.some(e=>e.toLowerCase()==r.toLowerCase())){t.push(r);continue r}}t.push("")}return t}function oc(e,t,i){var r;let n=i?oh(t.actions):[];return nn("li",{class:"cm-diagnostic cm-diagnostic-"+t.severity},nn("span",{class:"cm-diagnosticText"},t.renderMessage?t.renderMessage(e):t.message),null===(r=t.actions)||void 0===r?void 0:r.map((i,r)=>{let s=!1,o=r=>{if(r.preventDefault(),s)return;s=!0;let n=s8(e.state.field(on).diagnostics,t);n&&i.apply(e,n.from,n.to)},{name:a}=i,l=n[r]?a.indexOf(n[r]):-1,h=l<0?a:[a.slice(0,l),nn("u",a.slice(l,l+1)),a.slice(l+1)];return nn("button",{type:"button",class:"cm-diagnosticAction",onclick:o,onmousedown:o,"aria-label":` Action: ${a}${l<0?"":` (access key "${n[r]})"`}.`},h)}),t.source&&nn("div",{class:"cm-diagnosticSource"},t.source))}class of extends u.l9{constructor(e){super(),this.sev=e}eq(e){return e.sev==this.sev}toDOM(){return nn("span",{class:"cm-lintPoint cm-lintPoint-"+this.sev})}}class ou{constructor(e,t){this.diagnostic=t,this.id="item_"+Math.floor(4294967295*Math.random()).toString(16),this.dom=oc(e,t,!0),this.dom.id=this.id,this.dom.setAttribute("role","option")}}class op{constructor(e){this.view=e,this.items=[],this.list=nn("ul",{tabIndex:0,role:"listbox","aria-label":this.view.state.phrase("Diagnostics"),onkeydown:t=>{if(27==t.keyCode)oo(this.view),this.view.focus();else if(38==t.keyCode||33==t.keyCode)this.moveSelection((this.selectedIndex-1+this.items.length)%this.items.length);else if(40==t.keyCode||34==t.keyCode)this.moveSelection((this.selectedIndex+1)%this.items.length);else if(36==t.keyCode)this.moveSelection(0);else if(35==t.keyCode)this.moveSelection(this.items.length-1);else if(13==t.keyCode)this.view.focus();else{if(!(t.keyCode>=65)||!(t.keyCode<=90)||!(this.selectedIndex>=0))return;let{diagnostic:i}=this.items[this.selectedIndex],r=oh(i.actions);for(let n=0;n<r.length;n++)if(r[n].toUpperCase().charCodeAt(0)==t.keyCode){let t=s8(this.view.state.field(on).diagnostics,i);t&&i.actions[n].apply(e,t.from,t.to)}}t.preventDefault()},onclick:e=>{for(let t=0;t<this.items.length;t++)this.items[t].dom.contains(e.target)&&this.moveSelection(t)}}),this.dom=nn("div",{class:"cm-panel-lint"},this.list,nn("button",{type:"button",name:"close","aria-label":this.view.state.phrase("close"),onclick:()=>oo(this.view)},"\xd7")),this.update()}get selectedIndex(){let e=this.view.state.field(on).selected;if(!e)return -1;for(let t=0;t<this.items.length;t++)if(this.items[t].diagnostic==e.diagnostic)return t;return -1}update(){let{diagnostics:e,selected:t}=this.view.state.field(on),i=0,r=!1,n=null,s=new Set;for(e.between(0,this.view.state.doc.length,(e,o,{spec:a})=>{for(let e of a.diagnostics){if(s.has(e))continue;s.add(e);let o=-1,a;for(let t=i;t<this.items.length;t++)if(this.items[t].diagnostic==e){o=t;break}o<0?(a=new ou(this.view,e),this.items.splice(i,0,a),r=!0):(a=this.items[o],o>i&&(this.items.splice(i,o-i),r=!0)),t&&a.diagnostic==t.diagnostic?a.dom.hasAttribute("aria-selected")||(a.dom.setAttribute("aria-selected","true"),n=a):a.dom.hasAttribute("aria-selected")&&a.dom.removeAttribute("aria-selected"),i++}});i<this.items.length&&!(1==this.items.length&&this.items[0].diagnostic.from<0);)r=!0,this.items.pop();0==this.items.length&&(this.items.push(new ou(this.view,{from:-1,to:-1,severity:"info",message:this.view.state.phrase("No diagnostics")})),r=!0),n?(this.list.setAttribute("aria-activedescendant",n.id),this.view.requestMeasure({key:this,read:()=>({sel:n.dom.getBoundingClientRect(),panel:this.list.getBoundingClientRect()}),write:({sel:e,panel:t})=>{let i=t.height/this.list.offsetHeight;e.top<t.top?this.list.scrollTop-=(t.top-e.top)/i:e.bottom>t.bottom&&(this.list.scrollTop+=(e.bottom-t.bottom)/i)}})):this.selectedIndex<0&&this.list.removeAttribute("aria-activedescendant"),r&&this.sync()}sync(){let e=this.list.firstChild;function t(){let t=e;e=t.nextSibling,t.remove()}for(let i of this.items)if(i.dom.parentNode==this.list){for(;e!=i.dom;)t();e=i.dom.nextSibling}else this.list.insertBefore(i.dom,e);for(;e;)t()}moveSelection(e){if(this.selectedIndex<0)return;let t=s8(this.view.state.field(on).diagnostics,this.items[e].diagnostic);t&&this.view.dispatch({selection:{anchor:t.from,head:t.to},scrollIntoView:!0,effects:or.of(t)})}static open(e){return new op(e)}}function oO(e){return function(e,t='viewBox="0 0 40 40"'){return`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" ${t}>${encodeURIComponent(e)}</svg>')`}(`<path d="m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0" stroke="${e}" fill="none" stroke-width=".7"/>`,'width="6" height="3"')}let od=u.tk.baseTheme({".cm-diagnostic":{padding:"3px 6px 3px 8px",marginLeft:"-1px",display:"block",whiteSpace:"pre-wrap"},".cm-diagnostic-error":{borderLeft:"5px solid #d11"},".cm-diagnostic-warning":{borderLeft:"5px solid orange"},".cm-diagnostic-info":{borderLeft:"5px solid #999"},".cm-diagnostic-hint":{borderLeft:"5px solid #66d"},".cm-diagnosticAction":{font:"inherit",border:"none",padding:"2px 4px",backgroundColor:"#444",color:"white",borderRadius:"3px",marginLeft:"8px",cursor:"pointer"},".cm-diagnosticSource":{fontSize:"70%",opacity:.7},".cm-lintRange":{backgroundPosition:"left bottom",backgroundRepeat:"repeat-x",paddingBottom:"0.7px"},".cm-lintRange-error":{backgroundImage:oO("#d11")},".cm-lintRange-warning":{backgroundImage:oO("orange")},".cm-lintRange-info":{backgroundImage:oO("#999")},".cm-lintRange-hint":{backgroundImage:oO("#66d")},".cm-lintRange-active":{backgroundColor:"#ffdd9980"},".cm-tooltip-lint":{padding:0,margin:0},".cm-lintPoint":{position:"relative","&:after":{content:'""',position:"absolute",bottom:0,left:"-2px",borderLeft:"3px solid transparent",borderRight:"3px solid transparent",borderBottom:"4px solid #d11"}},".cm-lintPoint-warning":{"&:after":{borderBottomColor:"orange"}},".cm-lintPoint-info":{"&:after":{borderBottomColor:"#999"}},".cm-lintPoint-hint":{"&:after":{borderBottomColor:"#66d"}},".cm-panel.cm-panel-lint":{position:"relative","& ul":{maxHeight:"100px",overflowY:"auto","& [aria-selected]":{backgroundColor:"#ddd","& u":{textDecoration:"underline"}},"&:focus [aria-selected]":{background_fallback:"#bdf",backgroundColor:"Highlight",color_fallback:"white",color:"HighlightText"},"& u":{textDecoration:"none"},padding:0,margin:0},"& [name=close]":{position:"absolute",top:"0",right:"2px",background:"inherit",border:"none",font:"inherit",padding:0,margin:0}}});u.SJ;let og=f.Py.define(),om=[on,u.tk.decorations.compute([on],e=>{let{selected:t,panel:i}=e.field(on);return t&&i&&t.from!=t.to?u.p.set([os.range(t.from,t.to)]):u.p.none}),(0,u.bF)(function(e,t,i){let{diagnostics:r}=e.state.field(on),n,s=-1,o=-1;r.between(t-(i<0?1:0),t+(i>0?1:0),(e,r,{spec:a})=>{if(t>=e&&t<=r&&(e==r||(t>e||i>0)&&(t<r||i<0)))return n=a.diagnostics,s=e,o=r,!1});let a=e.state.facet(ol).tooltipFilter;return(n&&a&&(n=a(n,e.state)),n)?{pos:s,end:o,above:e.state.doc.lineAt(s).to<o,create:()=>{var t;return{dom:(t=e,nn("ul",{class:"cm-tooltip-lint"},n.map(e=>oc(t,e,!1))))}}}:null},{hideOn:oe}),od];var oy=function(e){void 0===e&&(e={});var t,i,{crosshairCursor:r=!1}=e,n=[];!1!==e.closeBracketsKeymap&&(n=n.concat(s0)),!1!==e.defaultKeymap&&(n=n.concat(nr)),!1!==e.searchKeymap&&(n=n.concat(n0)),!1!==e.historyKeymap&&(n=n.concat(rm)),!1!==e.foldKeymap&&(n=n.concat(iv)),!1!==e.completionKeymap&&(n=n.concat(s3)),!1!==e.lintKeymap&&(n=n.concat(oa));var s=[];if(!1!==e.lineNumbers&&s.push((0,u.Eu)()),!1!==e.highlightActiveLineGutter&&s.push((0,u.HQ)()),!1!==e.highlightSpecialChars&&s.push((0,u.AE)()),!1!==e.history&&s.push(function(e={}){return[rr,ri.of(e),u.tk.domEventHandlers({beforeinput(e,t){let i="historyUndo"==e.inputType?rs:"historyRedo"==e.inputType?ro:null;return!!i&&(e.preventDefault(),i(t))}})]}()),!1!==e.foldGutter&&s.push(function(e={}){let t={...i_,...e},i=new iX(t,!0),r=new iX(t,!1),n=u.lg.fromClass(class{constructor(e){this.from=e.viewport.from,this.markers=this.buildMarkers(e)}update(e){(e.docChanged||e.viewportChanged||e.startState.facet(t6)!=e.state.facet(t6)||e.startState.field(iy,!1)!=e.state.field(iy,!1)||tH(e.startState)!=tH(e.state)||t.foldingChanged(e))&&(this.markers=this.buildMarkers(e.view))}buildMarkers(e){let t=new f.f_;for(let n of e.viewportLineBlocks){let s=ik(e.state,n.from,n.to)?r:ip(e.state,n.from,n.to)?i:null;s&&t.add(n.from,n.from,s)}return t.finish()}}),{domEventHandlers:s}=t;return[n,(0,u.v5)({class:"cm-foldGutter",markers(e){var t;return(null===(t=e.plugin(n))||void 0===t?void 0:t.markers)||f.Xs.empty},initialSpacer:()=>new iX(t,!1),domEventHandlers:{...s,click:(e,t,i)=>{if(s.click&&s.click(e,t,i))return!0;let r=ik(e.state,t.from,t.to);if(r)return e.dispatch({effects:ig.of(r)}),!0;let n=ip(e.state,t.from,t.to);return!!n&&(e.dispatch({effects:id.of(n)}),!0)}}}),i$()]}()),!1!==e.drawSelection&&s.push((0,u.Uw)()),!1!==e.dropCursor&&s.push((0,u.qr)()),!1!==e.allowMultipleSelections&&s.push(f.yy.allowMultipleSelections.of(!0)),!1!==e.indentOnInput&&s.push(f.yy.transactionFilter.of(e=>{if(!e.docChanged||!e.isUserEvent("input.type")&&!e.isUserEvent("input.complete"))return e;let t=e.startState.languageDataAt("indentOnInput",e.startState.selection.main.head);if(!t.length)return e;let i=e.newDoc,{head:r}=e.newSelection.main,n=i.lineAt(r);if(r>n.from+200)return e;let s=i.sliceString(n.from,r);if(!t.some(e=>e.test(s)))return e;let{state:o}=e,a=-1,l=[];for(let{head:e}of o.selection.ranges){let t=o.doc.lineAt(e);if(t.from==a)continue;a=t.from;let i=ir(o,t.from);if(null==i)continue;let r=/^\s*/.exec(t.text)[0],n=ii(o,i);r!=n&&l.push({from:t.from,to:t.from+r.length,insert:n})}return l.length?[e,{changes:l,sequential:!0}]:e})),!1!==e.syntaxHighlighting){let e,i;s.push((t={fallback:!0},e=[ij],iY instanceof iA&&(iY.module&&e.push(u.tk.styleModule.of(iY.module)),i=iY.themeType),(null==t?void 0:t.fallback)?e.push(iq.of(iY)):i?e.push(iR.computeN([u.tk.darkTheme],e=>e.facet(u.tk.darkTheme)==("dark"==i)?[iY]:[])):e.push(iR.of(iY)),e))}if(!1!==e.bracketMatching&&s.push(function(e={}){return[iE.of(e),iG]}()),!1!==e.closeBrackets&&s.push([sH,sU]),!1!==e.autocompletion&&s.push(function(e={}){return[sR,s$,sh.of(e),sC,s7,sq]}()),!1!==e.rectangularSelection&&s.push((0,u.Zs)()),!1!==r&&s.push((0,u.S2)()),!1!==e.highlightActiveLine&&s.push((0,u.ZO)()),!1!==e.highlightSelectionMatches){let e;s.push((e=[nS,nv],i&&e.push(nx.of(i)),e))}return e.tabSize&&"number"==typeof e.tabSize&&s.push(ie.of(" ".repeat(e.tabSize))),s.concat([u.$f.of(n.flat())]).filter(Boolean)},ox=i(8996),ok=u.tk.theme({"&":{backgroundColor:"#fff"}},{dark:!1}),oQ=function(e){void 0===e&&(e={});var{indentWithTab:t=!0,editable:i=!0,readOnly:r=!1,theme:n="light",placeholder:s="",basicSetup:o=!0}=e,a=[];switch(t&&a.unshift(u.$f.of([e4])),o&&("boolean"==typeof o?a.unshift(oy()):a.unshift(oy(o))),s&&a.unshift((0,u.W$)(s)),n){case"light":a.push(ok);break;case"dark":a.push(ox.vk);break;case"none":break;default:a.push(n)}return!1===i&&a.push(u.tk.editable.of(!1)),r&&a.push(f.yy.readOnly.of(!0)),[...a]},ob=e=>({line:e.state.doc.lineAt(e.state.selection.main.from),lineCount:e.state.doc.lines,lineBreak:e.state.lineBreak,length:e.state.doc.length,readOnly:e.state.readOnly,tabSize:e.state.tabSize,selection:e.state.selection,selectionAsSingle:e.state.selection.asSingle().main,ranges:e.state.selection.ranges,selectionCode:e.state.sliceDoc(e.state.selection.main.from,e.state.selection.main.to),selections:e.state.selection.ranges.map(t=>e.state.sliceDoc(t.from,t.to)),selectedText:e.state.selection.ranges.some(e=>!e.empty)});class ov{constructor(e,t){this.timeLeftMS=void 0,this.timeoutMS=void 0,this.isCancelled=!1,this.isTimeExhausted=!1,this.callbacks=[],this.timeLeftMS=t,this.timeoutMS=t,this.callbacks.push(e)}tick(){if(!this.isCancelled&&!this.isTimeExhausted&&(this.timeLeftMS--,this.timeLeftMS<=0)){this.isTimeExhausted=!0;var e=this.callbacks.slice();this.callbacks.length=0,e.forEach(e=>{try{e()}catch(e){console.error("TimeoutLatch callback error:",e)}})}}cancel(){this.isCancelled=!0,this.callbacks.length=0}reset(){this.timeLeftMS=this.timeoutMS,this.isCancelled=!1,this.isTimeExhausted=!1}get isDone(){return this.isCancelled||this.isTimeExhausted}}class oS{constructor(){this.interval=null,this.latches=new Set}add(e){this.latches.add(e),this.start()}remove(e){this.latches.delete(e),0===this.latches.size&&this.stop()}start(){null===this.interval&&(this.interval=setInterval(()=>{this.latches.forEach(e=>{e.tick(),e.isDone&&this.remove(e)})},1))}stop(){null!==this.interval&&(clearInterval(this.interval),this.interval=null)}}var ow=null,o$=()=>"undefined"==typeof window?new oS:(ow||(ow=new oS),ow),oP=f.q6.define(),oZ=[],oT=i(7573),o_=["className","value","selection","extensions","onChange","onStatistics","onCreateEditor","onUpdate","autoFocus","theme","height","minHeight","maxHeight","width","minWidth","maxWidth","basicSetup","placeholder","indentWithTab","editable","readOnly","root","initialState"],oX=(0,c.forwardRef)((e,t)=>{var{className:i,value:n="",selection:s,extensions:o=[],onChange:a,onStatistics:l,onCreateEditor:h,onUpdate:p,autoFocus:O,theme:d="light",height:g,minHeight:m,maxHeight:y,width:x,minWidth:k,maxWidth:Q,basicSetup:b,placeholder:v,indentWithTab:S,editable:w,readOnly:$,root:P,initialState:Z}=e,T=function(e,t){if(null==e)return{};var i={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;i[r]=e[r]}return i}(e,o_),_=(0,c.useRef)(null),{state:X,view:C,container:A,setContainer:R}=function(e){var{value:t,selection:i,onChange:r,onStatistics:n,onCreateEditor:s,onUpdate:o,extensions:a=oZ,autoFocus:l,theme:h="light",height:p=null,minHeight:O=null,maxHeight:d=null,width:g=null,minWidth:m=null,maxWidth:y=null,placeholder:x="",editable:k=!0,readOnly:Q=!1,indentWithTab:b=!0,basicSetup:v=!0,root:S,initialState:w}=e,[$,P]=(0,c.useState)(),[Z,T]=(0,c.useState)(),[_,X]=(0,c.useState)(),C=(0,c.useState)(()=>({current:null}))[0],A=(0,c.useState)(()=>({current:null}))[0],R=u.tk.theme({"&":{height:p,minHeight:O,maxHeight:d,width:g,minWidth:m,maxWidth:y},"& .cm-scroller":{height:"100% !important"}}),q=[u.tk.updateListener.of(e=>{e.docChanged&&"function"==typeof r&&!e.transactions.some(e=>e.annotation(oP))&&(C.current?C.current.reset():(C.current=new ov(()=>{if(A.current){var e=A.current;A.current=null,e()}C.current=null},200),o$().add(C.current)),r(e.state.doc.toString(),e)),n&&n(ob(e))}),R,...oQ({theme:h,editable:k,readOnly:Q,placeholder:x,indentWithTab:b,basicSetup:v})];return o&&"function"==typeof o&&q.push(u.tk.updateListener.of(o)),q=q.concat(a),(0,c.useLayoutEffect)(()=>{if($&&!_){var e={doc:t,selection:i,extensions:q},r=w?f.yy.fromJSON(w.json,e,w.fields):f.yy.create(e);if(X(r),!Z){var n=new u.tk({state:r,parent:$,root:S});T(n),s&&s(n,r)}}return()=>{Z&&(X(void 0),T(void 0))}},[$,_]),(0,c.useEffect)(()=>{e.container&&P(e.container)},[e.container]),(0,c.useEffect)(()=>()=>{Z&&(Z.destroy(),T(void 0)),C.current&&(C.current.cancel(),C.current=null)},[Z]),(0,c.useEffect)(()=>{l&&Z&&Z.focus()},[l,Z]),(0,c.useEffect)(()=>{Z&&Z.dispatch({effects:f.Py.reconfigure.of(q)})},[h,a,p,O,d,g,m,y,x,k,Q,b,v,r,o]),(0,c.useEffect)(()=>{if(void 0!==t){var e=Z?Z.state.doc.toString():"";if(Z&&t!==e){var i=C.current&&!C.current.isDone,r=()=>{Z&&t!==Z.state.doc.toString()&&Z.dispatch({changes:{from:0,to:Z.state.doc.toString().length,insert:t||""},annotations:[oP.of(!0)]})};i?A.current=r:r()}}},[t,Z]),{state:_,setState:X,view:Z,setView:T,container:$,setContainer:P}}({root:P,value:n,autoFocus:O,theme:d,height:g,minHeight:m,maxHeight:y,width:x,minWidth:k,maxWidth:Q,basicSetup:b,placeholder:v,indentWithTab:S,editable:w,readOnly:$,selection:s,onChange:a,onStatistics:l,onCreateEditor:h,onUpdate:p,extensions:o,initialState:Z});(0,c.useImperativeHandle)(t,()=>({editor:_.current,state:X,view:C}),[_,A,X,C]);var q=(0,c.useCallback)(e=>{_.current=e,R(e)},[R]);if("string"!=typeof n)throw Error("value must be typeof string but got "+typeof n);var M="string"==typeof d?"cm-theme-"+d:"cm-theme";return(0,oT.jsx)("div",r({ref:q,className:""+M+(i?" "+i:"")},T))});oX.displayName="CodeMirror";var oC=oX},3237:function(e,t,i){i.d(t,{V:function(){return o}});let r="undefined"==typeof Symbol?"__ͼ":Symbol.for("ͼ"),n="undefined"==typeof Symbol?"__styleSet"+Math.floor(1e8*Math.random()):Symbol("styleSet"),s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:{};class o{constructor(e,t){this.rules=[];let{finish:i}=t||{};function r(e){return/^@/.test(e)?[e]:e.split(/,\s*/)}for(let t in e)!function e(t,n,s,o){let a=[],l=/^@(\w+)\b/.exec(t[0]),h=l&&"keyframes"==l[1];if(l&&null==n)return s.push(t[0]+";");for(let i in n){let o=n[i];if(/&/.test(i))e(i.split(/,\s*/).map(e=>t.map(t=>e.replace(/&/,t))).reduce((e,t)=>e.concat(t)),o,s);else if(o&&"object"==typeof o){if(!l)throw RangeError("The value of a property ("+i+") should be a primitive value.");e(r(i),o,a,h)}else null!=o&&a.push(i.replace(/_.*/,"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())+": "+o+";")}(a.length||h)&&s.push((!i||l||o?t:t.map(i)).join(", ")+" {"+a.join(" ")+"}")}(r(t),e[t],this.rules)}getRules(){return this.rules.join("\n")}static newName(){let e=s[r]||1;return s[r]=e+1,"ͼ"+e.toString(36)}static mount(e,t,i){let r=e[n],s=i&&i.nonce;r?s&&r.setNonce(s):r=new l(e,s),r.mount(Array.isArray(t)?t:[t],e)}}let a=new Map;class l{constructor(e,t){let i=e.ownerDocument||e,r=i.defaultView;if(!e.head&&e.adoptedStyleSheets&&r.CSSStyleSheet){let t=a.get(i);if(t)return e[n]=t;this.sheet=new r.CSSStyleSheet,a.set(i,this)}else this.styleTag=i.createElement("style"),t&&this.styleTag.setAttribute("nonce",t);this.modules=[],e[n]=this}mount(e,t){let i=this.sheet,r=0,n=0;for(let t=0;t<e.length;t++){let s=e[t],o=this.modules.indexOf(s);if(o<n&&o>-1&&(this.modules.splice(o,1),n--,o=-1),-1==o){if(this.modules.splice(n++,0,s),i)for(let e=0;e<s.rules.length;e++)i.insertRule(s.rules[e],r++)}else{for(;n<o;)r+=this.modules[n++].rules.length;r+=s.rules.length,n++}}if(i)0>t.adoptedStyleSheets.indexOf(this.sheet)&&(t.adoptedStyleSheets=[this.sheet,...t.adoptedStyleSheets]);else{let e="";for(let t=0;t<this.modules.length;t++)e+=this.modules[t].getRules()+"\n";this.styleTag.textContent=e;let i=t.head||t;this.styleTag.parentNode!=i&&i.insertBefore(this.styleTag,i.firstChild)}}setNonce(e){this.styleTag&&this.styleTag.getAttribute("nonce")!=e&&this.styleTag.setAttribute("nonce",e)}}},1934:function(e,t,i){i.d(t,{YG:function(){return h},uY:function(){return n},ue:function(){return r}});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},n={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},s="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),o="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),a=0;a<10;a++)r[48+a]=r[96+a]=String(a);for(var a=1;a<=24;a++)r[a+111]="F"+a;for(var a=65;a<=90;a++)r[a]=String.fromCharCode(a+32),n[a]=String.fromCharCode(a);for(var l in r)n.hasOwnProperty(l)||(n[l]=r[l]);function h(e){var t=!(s&&e.metaKey&&e.shiftKey&&!e.ctrlKey&&!e.altKey||o&&e.shiftKey&&e.key&&1==e.key.length||"Unidentified"==e.key)&&e.key||(e.shiftKey?n:r)[e.keyCode]||e.key||"Unidentified";return"Esc"==t&&(t="Escape"),"Del"==t&&(t="Delete"),"Left"==t&&(t="ArrowLeft"),"Up"==t&&(t="ArrowUp"),"Right"==t&&(t="ArrowRight"),"Down"==t&&(t="ArrowDown"),t}}}]);