"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[11],{6434:function(e,t,r){r.d(t,{Z:function(){return a}});var n=r(9213);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},7808:function(e,t,r){r.d(t,{VY:function(){return th},ck:function(){return tv},fC:function(){return tf},Z0:function(){return tN},Tr:function(){return tM},tu:function(){return tC},fF:function(){return tE},xz:function(){return tm}});var n=r(7653),a=r(8344),o=r(1082),s=r(8556),i=r(4036),l=r(7840),d=r(8671),u=r(1171),c=r(7205),f=r(8646),m=r(6010),p=r(9555),h=r(6303),g=r(3184),v=r(2268),w=r(7575),y=r(306),b=r(432),x=r(6418),M=r(7146),E=r(4397),C=r(7573),N=["Enter"," "],S=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...S],D={ltr:[...N,"ArrowRight"],rtl:[...N,"ArrowLeft"]},I={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[R,j,_]=(0,u.B)(T),[P,B]=(0,i.b)(T,[_,g.D7,y.Pc]),A=(0,g.D7)(),O=(0,y.Pc)(),[z,L]=P(T),[V,F]=P(T),W=e=>{let{__scopeMenu:t,open:r=!1,children:a,dir:o,onOpenChange:s,modal:i=!0}=e,l=A(t),[d,u]=n.useState(null),f=n.useRef(!1),m=(0,x.W)(s),p=(0,c.gm)(o);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(g.fC,{...l,children:(0,C.jsx)(z,{scope:t,open:r,onOpenChange:m,content:d,onContentChange:u,children:(0,C.jsx)(V,{scope:t,onClose:n.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:f,dir:p,modal:i,children:a})})})};W.displayName=T;var K=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,a=A(r);return(0,C.jsx)(g.ee,{...a,...n,ref:t})});K.displayName="MenuAnchor";var Y="MenuPortal",[$,H]=P(Y,{forceMount:void 0}),G=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:a}=e,o=L(Y,t);return(0,C.jsx)($,{scope:t,forceMount:r,children:(0,C.jsx)(w.z,{present:r||o.open,children:(0,C.jsx)(v.h,{asChild:!0,container:a,children:n})})})};G.displayName=Y;var U="MenuContent",[X,J]=P(U),q=n.forwardRef((e,t)=>{let r=H(U,e.__scopeMenu),{forceMount:n=r.forceMount,...a}=e,o=L(U,e.__scopeMenu),s=F(U,e.__scopeMenu);return(0,C.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(w.z,{present:n||o.open,children:(0,C.jsx)(R.Slot,{scope:e.__scopeMenu,children:s.modal?(0,C.jsx)(Z,{...a,ref:t}):(0,C.jsx)(Q,{...a,ref:t})})})})}),Z=n.forwardRef((e,t)=>{let r=L(U,e.__scopeMenu),a=n.useRef(null),i=(0,s.e)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,M.Ry)(e)},[]),(0,C.jsx)(et,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=L(U,e.__scopeMenu);return(0,C.jsx)(et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ee=(0,b.Z8)("MenuContent.ScrollLock"),et=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:d,disableOutsidePointerEvents:u,onEntryFocus:c,onEscapeKeyDown:h,onPointerDownOutside:v,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:M,...N}=e,D=L(U,r),I=F(U,r),T=A(r),R=O(r),_=j(r),[P,B]=n.useState(null),z=n.useRef(null),V=(0,s.e)(t,z,D.onContentChange),W=n.useRef(0),K=n.useRef(""),Y=n.useRef(0),$=n.useRef(null),H=n.useRef("right"),G=n.useRef(0),J=M?E.Z:n.Fragment,q=M?{as:ee,allowPinchZoom:!0}:void 0,Z=e=>{let t=K.current+e,r=_().filter(e=>!e.disabled),n=document.activeElement,a=r.find(e=>e.ref.current===n)?.textValue,o=r.map(e=>e.textValue),s=function(e,t,r){var n;let a=t.length>1&&Array.from(t).every(e=>e===t[0]),o=a?t[0]:t,s=r?e.indexOf(r):-1,i=(n=Math.max(s,0),e.map((t,r)=>e[(n+r)%e.length])),l=1===o.length;l&&(i=i.filter(e=>e!==r));let d=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return d!==r?d:void 0}(o,t,a),i=r.find(e=>e.textValue===s)?.ref.current;!function e(t){K.current=t,window.clearTimeout(W.current),""!==t&&(W.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(W.current),[]),(0,m.EW)();let Q=n.useCallback(e=>{let t=H.current===$.current?.side;return t&&function(e,t){if(!t)return!1;let r={x:e.clientX,y:e.clientY};return function(e,t){let{x:r,y:n}=e,a=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],i=t[o],l=s.x,d=s.y,u=i.x,c=i.y,f=d>n!=c>n&&r<(u-l)*(n-d)/(c-d)+l;f&&(a=!a)}return a}(r,t)}(e,$.current?.area)},[]);return(0,C.jsx)(X,{scope:r,searchRef:K,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{Q(e)||(z.current?.focus(),B(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:Y,onPointerGraceIntentChange:n.useCallback(e=>{$.current=e},[]),children:(0,C.jsx)(J,{...q,children:(0,C.jsx)(p.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.M)(l,e=>{e.preventDefault(),z.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,C.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:u,onEscapeKeyDown:h,onPointerDownOutside:v,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,C.jsx)(y.fC,{asChild:!0,...R,dir:I.dir,orientation:"vertical",loop:a,currentTabStopId:P,onCurrentTabStopIdChange:B,onEntryFocus:(0,o.M)(c,e=>{I.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(g.VY,{role:"menu","aria-orientation":"vertical","data-state":eI(D.open),"data-radix-menu-content":"",dir:I.dir,...T,...N,ref:V,style:{outline:"none",...N.style},onKeyDown:(0,o.M)(N.onKeyDown,e=>{let t=e.target,r=t.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,a=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&a&&Z(e.key));let o=z.current;if(e.target!==o||!k.includes(e.key))return;e.preventDefault();let s=_().filter(e=>!e.disabled),i=s.map(e=>e.ref.current);S.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(W.current),K.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{let t=e.target,r=G.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>G.current?"right":"left";H.current=t,G.current=e.clientX}}))})})})})})})});q.displayName=U;var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,C.jsx)(d.WV.div,{role:"group",...n,ref:t})});er.displayName="MenuGroup";var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,C.jsx)(d.WV.div,{...n,ref:t})});en.displayName="MenuLabel";var ea="MenuItem",eo="menu.itemSelect",es=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...i}=e,l=n.useRef(null),u=F(ea,e.__scopeMenu),c=J(ea,e.__scopeMenu),f=(0,s.e)(t,l),m=n.useRef(!1);return(0,C.jsx)(ei,{...i,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=l.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>a?.(e),{once:!0}),(0,d.jH)(e,t),t.defaultPrevented?m.current=!1:u.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),m.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{m.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!r&&(!t||" "!==e.key)&&N.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});es.displayName=ea;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:i,...l}=e,u=J(ea,r),c=O(r),f=n.useRef(null),m=(0,s.e)(t,f),[p,h]=n.useState(!1),[g,v]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&v((e.textContent??"").trim())},[l.children]),(0,C.jsx)(R.ItemSlot,{scope:r,disabled:a,textValue:i??g,children:(0,C.jsx)(y.ck,{asChild:!0,...c,focusable:!a,children:(0,C.jsx)(d.WV.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...l,ref:m,onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{if(a)u.onItemLeave(e);else if(u.onItemEnter(e),!e.defaultPrevented){let t=e.currentTarget;t.focus({preventScroll:!0})}})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>u.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>h(!0)),onBlur:(0,o.M)(e.onBlur,()=>h(!1))})})})}),el=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,C.jsx)(eg,{scope:e.__scopeMenu,checked:r,children:(0,C.jsx)(es,{role:"menuitemcheckbox","aria-checked":eT(r)?"mixed":r,...a,ref:t,"data-state":eR(r),onSelect:(0,o.M)(a.onSelect,()=>n?.(!!eT(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ed="MenuRadioGroup",[eu,ec]=P(ed,{value:void 0,onValueChange:()=>{}}),ef=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...a}=e,o=(0,x.W)(n);return(0,C.jsx)(eu,{scope:e.__scopeMenu,value:r,onValueChange:o,children:(0,C.jsx)(er,{...a,ref:t})})});ef.displayName=ed;var em="MenuRadioItem",ep=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ec(em,e.__scopeMenu),s=r===a.value;return(0,C.jsx)(eg,{scope:e.__scopeMenu,checked:s,children:(0,C.jsx)(es,{role:"menuitemradio","aria-checked":s,...n,ref:t,"data-state":eR(s),onSelect:(0,o.M)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ep.displayName=em;var eh="MenuItemIndicator",[eg,ev]=P(eh,{checked:!1}),ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...a}=e,o=ev(eh,r);return(0,C.jsx)(w.z,{present:n||eT(o.checked)||!0===o.checked,children:(0,C.jsx)(d.WV.span,{...a,ref:t,"data-state":eR(o.checked)})})});ew.displayName=eh;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,C.jsx)(d.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ey.displayName="MenuSeparator";var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,a=A(r);return(0,C.jsx)(g.Eh,{...a,...n,ref:t})});eb.displayName="MenuArrow";var ex="MenuSub",[eM,eE]=P(ex),eC=e=>{let{__scopeMenu:t,children:r,open:a=!1,onOpenChange:o}=e,s=L(ex,t),i=A(t),[l,d]=n.useState(null),[u,c]=n.useState(null),f=(0,x.W)(o);return n.useEffect(()=>(!1===s.open&&f(!1),()=>f(!1)),[s.open,f]),(0,C.jsx)(g.fC,{...i,children:(0,C.jsx)(z,{scope:t,open:a,onOpenChange:f,content:u,onContentChange:c,children:(0,C.jsx)(eM,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:l,onTriggerChange:d,children:r})})})};eC.displayName=ex;var eN="MenuSubTrigger",eS=n.forwardRef((e,t)=>{let r=L(eN,e.__scopeMenu),a=F(eN,e.__scopeMenu),i=eE(eN,e.__scopeMenu),l=J(eN,e.__scopeMenu),d=n.useRef(null),{pointerGraceTimerRef:u,onPointerGraceIntentChange:c}=l,f={__scopeMenu:e.__scopeMenu},m=n.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return n.useEffect(()=>m,[m]),n.useEffect(()=>{let e=u.current;return()=>{window.clearTimeout(e),c(null)}},[u,c]),(0,C.jsx)(K,{asChild:!0,...f,children:(0,C.jsx)(ei,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eI(r.open),...e,ref:(0,s.F)(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,ej(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||d.current||(l.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{r.onOpenChange(!0),m()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>{m();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,a="right"===n,o=t[a?"left":"right"],s=t[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:o,y:t.top},{x:s,y:t.top},{x:s,y:t.bottom},{x:o,y:t.bottom}],side:n}),window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&D[a.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eS.displayName=eN;var ek="MenuSubContent",eD=n.forwardRef((e,t)=>{let r=H(U,e.__scopeMenu),{forceMount:a=r.forceMount,...i}=e,l=L(U,e.__scopeMenu),d=F(U,e.__scopeMenu),u=eE(ek,e.__scopeMenu),c=n.useRef(null),f=(0,s.e)(t,c);return(0,C.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(w.z,{present:a||l.open,children:(0,C.jsx)(R.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(et,{id:u.contentId,"aria-labelledby":u.triggerId,...i,ref:f,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{d.isUsingKeyboardRef.current&&c.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==u.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=I[d.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),u.trigger?.focus(),e.preventDefault())})})})})})});function eI(e){return e?"open":"closed"}function eT(e){return"indeterminate"===e}function eR(e){return eT(e)?"indeterminate":e?"checked":"unchecked"}function ej(e){return t=>"mouse"===t.pointerType?e(t):void 0}eD.displayName=ek;var e_="DropdownMenu",[eP,eB]=(0,i.b)(e_,[B]),eA=B(),[eO,ez]=eP(e_),eL=e=>{let{__scopeDropdownMenu:t,children:r,dir:a,open:o,defaultOpen:s,onOpenChange:i,modal:d=!0}=e,u=eA(t),c=n.useRef(null),[f,m]=(0,l.T)({prop:o,defaultProp:s??!1,onChange:i,caller:e_});return(0,C.jsx)(eO,{scope:t,triggerId:(0,h.M)(),triggerRef:c,contentId:(0,h.M)(),open:f,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),modal:d,children:(0,C.jsx)(W,{...u,open:f,onOpenChange:m,dir:a,modal:d,children:r})})};eL.displayName=e_;var eV="DropdownMenuTrigger",eF=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...a}=e,i=ez(eV,r),l=eA(r);return(0,C.jsx)(K,{asChild:!0,...l,children:(0,C.jsx)(d.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,s.F)(t,i.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eV;var eW=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eA(t);return(0,C.jsx)(G,{...n,...r})};eW.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,s=ez(eK,r),i=eA(r),l=n.useRef(!1);return(0,C.jsx)(q,{id:s.contentId,"aria-labelledby":s.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{l.current||s.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!s.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eY.displayName=eK;var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(er,{...a,...n,ref:t})});e$.displayName="DropdownMenuGroup";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(en,{...a,...n,ref:t})});eH.displayName="DropdownMenuLabel";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(es,{...a,...n,ref:t})});eG.displayName="DropdownMenuItem";var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(el,{...a,...n,ref:t})});eU.displayName="DropdownMenuCheckboxItem";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(ef,{...a,...n,ref:t})});eX.displayName="DropdownMenuRadioGroup";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(ep,{...a,...n,ref:t})});eJ.displayName="DropdownMenuRadioItem";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(ew,{...a,...n,ref:t})});eq.displayName="DropdownMenuItemIndicator";var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(ey,{...a,...n,ref:t})});eZ.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(eb,{...a,...n,ref:t})}).displayName="DropdownMenuArrow";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(eS,{...a,...n,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var e0=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,a=eA(r);return(0,C.jsx)(eD,{...a,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e0.displayName="DropdownMenuSubContent";var e1=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:a,defaultOpen:o}=e,s=eA(t),[i,d]=(0,l.T)({prop:n,defaultProp:o??!1,onChange:a,caller:"DropdownMenuSub"});return(0,C.jsx)(eC,{...s,open:i,onOpenChange:d,children:r})},e2=r(9148),e5=r(2741),e4=r(4494);let e7={...e5.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"1",responsive:!0},...e4.I,scrollbars:{type:"enum",values:["vertical","horizontal","both"],default:"both"}};var e3=r(1032),e8=r(2808),e6=r(5159);let e9=e6.E.m.values;var te=r(3589);let tt=n.forwardRef((e,t)=>{let{rest:r,...o}=function(e){let{m:t,mx:r,my:n,mt:a,mr:o,mb:s,ml:i,...l}=e;return{m:t,mx:r,my:n,mt:a,mr:o,mb:s,ml:i,rest:l}}(e),[s,i]=function(e){let[t,r]=(0,e3.uq)({className:"rt-r-m",customProperties:["--margin"],propValues:e9,value:e.m}),[n,o]=(0,e3.uq)({className:"rt-r-mx",customProperties:["--margin-left","--margin-right"],propValues:e9,value:e.mx}),[s,i]=(0,e3.uq)({className:"rt-r-my",customProperties:["--margin-top","--margin-bottom"],propValues:e9,value:e.my}),[l,d]=(0,e3.uq)({className:"rt-r-mt",customProperties:["--margin-top"],propValues:e9,value:e.mt}),[u,c]=(0,e3.uq)({className:"rt-r-mr",customProperties:["--margin-right"],propValues:e9,value:e.mr}),[f,m]=(0,e3.uq)({className:"rt-r-mb",customProperties:["--margin-bottom"],propValues:e9,value:e.mb}),[p,h]=(0,e3.uq)({className:"rt-r-ml",customProperties:["--margin-left"],propValues:e9,value:e.ml});return[a(t,n,s,l,u,f,p),(0,e8.y)(r,o,i,d,c,m,h)]}(o),{asChild:l,children:d,className:u,style:c,type:f,scrollHideDelay:m="scroll"!==f?0:void 0,dir:p,size:h=e7.size.default,radius:g=e7.radius.default,scrollbars:v=e7.scrollbars.default,...w}=r;return n.createElement(e2.fC,{type:f,scrollHideDelay:m,className:a("rt-ScrollAreaRoot",s,u),style:(0,e8.y)(i,c),asChild:l},(0,te.x)({asChild:l,children:d},e=>n.createElement(n.Fragment,null,n.createElement(e2.l_,{...w,ref:t,className:"rt-ScrollAreaViewport"},e),n.createElement("div",{className:"rt-ScrollAreaViewportFocusRing"}),"vertical"!==v?n.createElement(e2.LW,{"data-radius":g,orientation:"horizontal",className:a("rt-ScrollAreaScrollbar",(0,e3.RE)({className:"rt-r-size",value:h,propValues:e7.size.values}))},n.createElement(e2.bU,{className:"rt-ScrollAreaThumb"})):null,"horizontal"!==v?n.createElement(e2.LW,{"data-radius":g,orientation:"vertical",className:a("rt-ScrollAreaScrollbar",(0,e3.RE)({className:"rt-r-size",value:h,propValues:e7.size.values}))},n.createElement(e2.bU,{className:"rt-ScrollAreaThumb"})):null,"both"===v?n.createElement(e2.Ns,{className:"rt-ScrollAreaCorner"}):null)))});tt.displayName="ScrollArea";var tr=r(605),tn=r(3579);let ta={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"solid"},...tr.EG,...tn.K},to={...e5.C,...tr.EG,shortcut:{type:"string"}},ts={...tr.EG,shortcut:{type:"string"}},ti={...tr.EG};var tl=r(2123),td=r(1511),tu=r(5236),tc=r(8373);let tf=e=>n.createElement(eL,{...e});tf.displayName="DropdownMenu.Root";let tm=n.forwardRef(({children:e,...t},r)=>n.createElement(eF,{...t,ref:r,asChild:!0},(0,tc.O)(e)));tm.displayName="DropdownMenu.Trigger";let tp=n.createContext({}),th=n.forwardRef((e,t)=>{let r=(0,tl.TC)(),{size:o=ta.size.default,variant:s=ta.variant.default,highContrast:i=ta.highContrast.default}=e,{className:l,children:d,color:u,container:c,forceMount:f,...m}=(0,tu.y)(e,ta),p=u||r.accentColor;return n.createElement(eW,{container:c,forceMount:f},n.createElement(tl.Q2,{asChild:!0},n.createElement(eY,{"data-accent-color":p,align:"start",sideOffset:4,collisionPadding:10,...m,asChild:!1,ref:t,className:a("rt-PopperContent","rt-BaseMenuContent","rt-DropdownMenuContent",l)},n.createElement(tt,{type:"auto"},n.createElement("div",{className:a("rt-BaseMenuViewport","rt-DropdownMenuViewport")},n.createElement(tp.Provider,{value:n.useMemo(()=>({size:o,variant:s,color:p,highContrast:i}),[o,s,p,i])},d))))))});th.displayName="DropdownMenu.Content";let tg=n.forwardRef(({className:e,...t},r)=>n.createElement(eH,{...t,asChild:!1,ref:r,className:a("rt-BaseMenuLabel","rt-DropdownMenuLabel",e)}));tg.displayName="DropdownMenu.Label";let tv=n.forwardRef((e,t)=>{let{className:r,children:o,color:s=to.color.default,shortcut:i,...l}=e;return n.createElement(eG,{"data-accent-color":s,...l,ref:t,className:a("rt-reset","rt-BaseMenuItem","rt-DropdownMenuItem",r)},n.createElement(b.A4,null,o),i&&n.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},i))});tv.displayName="DropdownMenu.Item";let tw=n.forwardRef(({className:e,...t},r)=>n.createElement(e$,{...t,asChild:!1,ref:r,className:a("rt-BaseMenuGroup","rt-DropdownMenuGroup",e)}));tw.displayName="DropdownMenu.Group";let ty=n.forwardRef(({className:e,...t},r)=>n.createElement(eX,{...t,asChild:!1,ref:r,className:a("rt-BaseMenuRadioGroup","rt-DropdownMenuRadioGroup",e)}));ty.displayName="DropdownMenu.RadioGroup";let tb=n.forwardRef((e,t)=>{let{children:r,className:o,color:s=ti.color.default,...i}=e;return n.createElement(eJ,{...i,asChild:!1,ref:t,"data-accent-color":s,className:a("rt-BaseMenuItem","rt-BaseMenuRadioItem","rt-DropdownMenuItem","rt-DropdownMenuRadioItem",o)},r,n.createElement(eq,{className:"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator"},n.createElement(td.dc,{className:"rt-BaseMenuItemIndicatorIcon rt-DropdownMenuItemIndicatorIcon"})))});tb.displayName="DropdownMenu.RadioItem";let tx=n.forwardRef((e,t)=>{let{children:r,className:o,shortcut:s,color:i=ts.color.default,...l}=e;return n.createElement(eU,{...l,asChild:!1,ref:t,"data-accent-color":i,className:a("rt-BaseMenuItem","rt-BaseMenuCheckboxItem","rt-DropdownMenuItem","rt-DropdownMenuCheckboxItem",o)},r,n.createElement(eq,{className:"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator"},n.createElement(td.dc,{className:"rt-BaseMenuItemIndicatorIcon rt-ContextMenuItemIndicatorIcon"})),s&&n.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},s))});tx.displayName="DropdownMenu.CheckboxItem";let tM=e=>n.createElement(e1,{...e});tM.displayName="DropdownMenu.Sub";let tE=n.forwardRef((e,t)=>{let{className:r,children:o,...s}=e;return n.createElement(eQ,{...s,asChild:!1,ref:t,className:a("rt-BaseMenuItem","rt-BaseMenuSubTrigger","rt-DropdownMenuItem","rt-DropdownMenuSubTrigger",r)},o,n.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},n.createElement(td.OW,{className:"rt-BaseMenuSubTriggerIcon rt-DropdownMenuSubtriggerIcon"})))});tE.displayName="DropdownMenu.SubTrigger";let tC=n.forwardRef((e,t)=>{let{size:r,variant:o,color:s,highContrast:i}=n.useContext(tp),{className:l,children:d,container:u,forceMount:c,...f}=(0,tu.y)({size:r,variant:o,color:s,highContrast:i,...e},ta);return n.createElement(eW,{container:u,forceMount:c},n.createElement(tl.Q2,{asChild:!0},n.createElement(e0,{"data-accent-color":s,alignOffset:-(4*Number(r)),sideOffset:1,collisionPadding:10,...f,asChild:!1,ref:t,className:a("rt-PopperContent","rt-BaseMenuContent","rt-BaseMenuSubContent","rt-DropdownMenuContent","rt-DropdownMenuSubContent",l)},n.createElement(tt,{type:"auto"},n.createElement("div",{className:a("rt-BaseMenuViewport","rt-DropdownMenuViewport")},d)))))});tC.displayName="DropdownMenu.SubContent";let tN=n.forwardRef(({className:e,...t},r)=>n.createElement(eZ,{...t,asChild:!1,ref:r,className:a("rt-BaseMenuSeparator","rt-DropdownMenuSeparator",e)}));tN.displayName="DropdownMenu.Separator"},4594:function(e,t,r){r.d(t,{KO:function(){return p},zt:function(){return l}});var n=r(7653),a=r(2732),o=r(5640);let s=(0,n.createContext)(void 0);function i(e){let t=(0,n.useContext)(s);return(null==e?void 0:e.store)||t||(0,a.K7)()}function l({children:e,store:t}){let r=(0,n.useRef)(void 0);return t||r.current||(r.current=(0,a.MT)()),(0,n.createElement)(s.Provider,{value:t||r.current},e)}let d=e=>"function"==typeof(null==e?void 0:e.then),u=e=>{e.status||(e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}))},c=n.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw u(e),e}),f=new WeakMap,m=(e,t)=>{let r=f.get(e);return r||(r=new Promise((n,a)=>{let s=e,i=e=>t=>{s===e&&n(t)},l=e=>t=>{s===e&&a(t)},u=()=>{try{let e=t();d(e)?(f.set(e,r),s=e,e.then(i(e),l(e)),(0,o.XB)(e,u)):n(e)}catch(e){a(e)}};e.then(i(e),l(e)),(0,o.XB)(e,u)}),f.set(e,r)),r};function p(e,t){return[function(e,t){let{delay:r,unstable_promiseStatus:a=!n.use}=t||{},o=i(t),[[s,l,f],p]=(0,n.useReducer)(t=>{let r=o.get(e);return Object.is(t[0],r)&&t[1]===o&&t[2]===e?t:[r,o,e]},void 0,()=>[o.get(e),o,e]),h=s;if((l!==o||f!==e)&&(p(),h=o.get(e)),(0,n.useEffect)(()=>{let t=o.sub(e,()=>{if(a)try{let t=o.get(e);d(t)&&u(m(t,()=>o.get(e)))}catch(e){}if("number"==typeof r){setTimeout(p,r);return}p()});return p(),t},[o,e,r,a]),(0,n.useDebugValue)(h),d(h)){let t=m(h,()=>o.get(e));return a&&u(t),c(t)}return h}(e,t),function(e,t){let r=i(t),a=(0,n.useCallback)((...t)=>{if(!("write"in e))throw Error("not writable atom");return r.set(e,...t)},[r,e]);return a}(e,t)]}},2732:function(e,t,r){let n,a;r.d(t,{K7:function(){return f},MT:function(){return c},cn:function(){return i}});var o=r(5640);let s=0;function i(e,t){let r=`atom${++s}`,n={toString(){return this.debugLabel?r+":"+this.debugLabel:r}};return"function"==typeof e?n.read=e:(n.init=e,n.read=l,n.write=d),t&&(n.write=t),n}function l(e){return e(this)}function d(e,t,r){return t(this,"function"==typeof r?r(e(this)):r)}let u=()=>{let e=0,t=(0,o.YN)({}),r=new WeakMap,n=new WeakMap,a=(0,o.k_)(r,n,void 0,void 0,void 0,void 0,t,void 0,(t,r,n,...a)=>e?n(t,...a):t.write(r,n,...a)),s=new Set;return t.m.add(void 0,e=>{s.add(e);let t=r.get(e);t.m=n.get(e)}),t.u.add(void 0,e=>{s.delete(e);let t=r.get(e);delete t.m}),Object.assign(a,{dev4_get_internal_weak_map:()=>(console.log("Deprecated: Use devstore from the devtools library"),r),dev4_get_mounted_atoms:()=>s,dev4_restore_atoms:t=>{a.set({read:()=>null,write:(r,n)=>{++e;try{for(let[e,r]of t)"init"in e&&n(e,r)}finally{--e}}})}})};function c(){return n?n():u()}function f(){return a||(a=c(),globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=a),globalThis.__JOTAI_DEFAULT_STORE__!==a&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044")),a}},5640:function(e,t,r){r.d(t,{XB:function(){return x},YN:function(){return b},k_:function(){return y}});let n=(e,t)=>e.unstable_is?e.unstable_is(t):t===e,a=e=>"init"in e,o=e=>!!e.write,s=e=>"v"in e||"e"in e,i=e=>{if("e"in e)throw e.e;if(!("v"in e))throw Error("[Bug] atom state is not initialized");return e.v},l=new WeakMap,d=e=>{var t;return f(e)&&!!(null==(t=l.get(e))?void 0:t[0])},u=e=>{let t=l.get(e);(null==t?void 0:t[0])&&(t[0]=!1,t[1].forEach(e=>e()))},c=(e,t)=>{let r=l.get(e);if(!r){r=[!0,new Set],l.set(e,r);let t=()=>{r[0]=!1};e.then(t,t)}r[1].add(t)},f=e=>"function"==typeof(null==e?void 0:e.then),m=(e,t,r)=>{r.p.has(e)||(r.p.add(e),t.then(()=>{r.p.delete(e)},()=>{r.p.delete(e)}))},p=(e,t,r)=>{let n=r(e),a="v"in n,o=n.v;if(f(t))for(let a of n.d.keys())m(e,t,r(a));n.v=t,delete n.e,a&&Object.is(o,n.v)||(++n.n,f(o)&&u(o))},h=(e,t,r)=>{var n;let a=new Set;for(let t of(null==(n=r.get(e))?void 0:n.t)||[])r.has(t)&&a.add(t);for(let e of t.p)a.add(e);return a},g=()=>{let e=new Set,t=()=>{e.forEach(e=>e())};return t.add=t=>(e.add(t),()=>{e.delete(t)}),t},v=()=>{let e={},t=new WeakMap,r=r=>{var n,a;null==(n=t.get(e))||n.forEach(e=>e(r)),null==(a=t.get(r))||a.forEach(e=>e())};return r.add=(r,n)=>{let a=r||e,o=(t.has(a)?t:t.set(a,new Set)).get(a);return o.add(n),()=>{null==o||o.delete(n),o.size||t.delete(a)}},r},w=Symbol(),y=(e=new WeakMap,t=new WeakMap,r=new WeakMap,l=new Set,u=new Set,g=new Set,v={},y=(e,...t)=>e.read(...t),b=(e,...t)=>e.write(...t),x=(e,t)=>{var r;return null==(r=e.unstable_onInit)?void 0:r.call(e,t)},M=(e,t)=>{var r;return null==(r=e.onMount)?void 0:r.call(e,t)},...E)=>{let C=E[0]||(t=>{if(!t)throw Error("Atom is undefined or null");let r=e.get(t);return r||(r={d:new Map,p:new Set,n:0},e.set(t,r),null==x||x(t,P)),r}),N=E[1]||(()=>{let e=[],r=t=>{try{t()}catch(t){e.push(t)}};do{v.f&&r(v.f);let e=new Set,n=e.add.bind(e);l.forEach(e=>{var r;return null==(r=t.get(e))?void 0:r.l.forEach(n)}),l.clear(),g.forEach(n),g.clear(),u.forEach(n),u.clear(),e.forEach(r),l.size&&S()}while(l.size||g.size||u.size);if(e.length)throw AggregateError(e)}),S=E[2]||(()=>{let e=[],n=new WeakSet,a=new WeakSet,o=Array.from(l);for(;o.length;){let s=o[o.length-1],i=C(s);if(a.has(s)){o.pop();continue}if(n.has(s)){if(r.get(s)===i.n)e.push([s,i]);else if(r.has(s))throw Error("[Bug] invalidated atom exists");a.add(s),o.pop();continue}for(let e of(n.add(s),h(s,i,t)))n.has(e)||o.push(e)}for(let t=e.length-1;t>=0;--t){let[n,a]=e[t],o=!1;for(let e of a.d.keys())if(e!==n&&l.has(e)){o=!0;break}o&&(k(n),T(n)),r.delete(n)}}),k=E[3]||(e=>{var u;let h,g;let w=C(e);if(s(w)&&(t.has(e)&&r.get(e)!==w.n||Array.from(w.d).every(([e,t])=>k(e).n===t)))return w;w.d.clear();let b=!0,x=()=>{t.has(e)&&(T(e),S(),N())},M=w.n;try{let r=y(e,r=>{var o;if(n(e,r)){let e=C(r);if(!s(e)){if(a(r))p(r,r.init,C);else throw Error("no atom init")}return i(e)}let l=k(r);try{return i(l)}finally{w.d.set(r,l.n),d(w.v)&&m(e,w.v,l),null==(o=t.get(r))||o.t.add(e),b||x()}},{get signal(){return h||(h=new AbortController),h.signal},get setSelf(){return o(e)||console.warn("setSelf function cannot be used with read-only atom"),!g&&o(e)&&(g=(...t)=>{if(b&&console.warn("setSelf function cannot be called in sync"),!b)try{return I(e,...t)}finally{S(),N()}}),g}});return p(e,r,C),f(r)&&(c(r,()=>null==h?void 0:h.abort()),r.then(x,x)),w}catch(e){return delete w.v,w.e=e,++w.n,w}finally{b=!1,M!==w.n&&r.get(e)===M&&(r.set(e,w.n),l.add(e),null==(u=v.c)||u.call(v,e))}}),D=E[4]||(e=>{let n=[e];for(;n.length;){let e=n.pop(),a=C(e);for(let o of h(e,a,t)){let e=C(o);r.set(o,e.n),n.push(o)}}}),I=E[5]||((e,...t)=>{let r=!0;try{return b(e,e=>i(k(e)),(t,...o)=>{var s;let i=C(t);try{if(!n(e,t))return I(t,...o);{if(!a(t))throw Error("atom not writable");let e=i.n,r=o[0];p(t,r,C),T(t),e!==i.n&&(l.add(t),null==(s=v.c)||s.call(v,t),D(t));return}}finally{r||(S(),N())}},...t)}finally{r=!1}}),T=E[6]||(e=>{var r;let n=C(e),a=t.get(e);if(a&&!d(n.v)){for(let[t,o]of n.d)if(!a.d.has(t)){let n=C(t),s=R(t);s.t.add(e),a.d.add(t),o!==n.n&&(l.add(t),null==(r=v.c)||r.call(v,t),D(t))}for(let t of a.d||[])if(!n.d.has(t)){a.d.delete(t);let r=j(t);null==r||r.t.delete(e)}}}),R=E[7]||(e=>{var r;let n=C(e),a=t.get(e);if(!a){for(let t of(k(e),n.d.keys())){let r=R(t);r.t.add(e)}a={l:new Set,d:new Set(n.d.keys()),t:new Set},t.set(e,a),null==(r=v.m)||r.call(v,e),o(e)&&u.add(()=>{let t=!0;try{let r=M(e,(...r)=>{try{return I(e,...r)}finally{t||(S(),N())}});r&&(a.u=()=>{t=!0;try{r()}finally{t=!1}})}finally{t=!1}})}return a}),j=E[8]||(e=>{var r;let n=C(e),a=t.get(e);if(a&&!a.l.size&&!Array.from(a.t).some(r=>{var n;return null==(n=t.get(r))?void 0:n.d.has(e)})){for(let o of(a.u&&g.add(a.u),a=void 0,t.delete(e),null==(r=v.u)||r.call(v,e),n.d.keys())){let t=j(o);null==t||t.t.delete(e)}return}return a}),_=[e,t,r,l,u,g,v,y,b,x,M,C,N,S,k,D,I,T,R,j],P={get:e=>i(k(e)),set:(e,...t)=>{try{return I(e,...t)}finally{S(),N()}},sub:(e,t)=>{let r=R(e),n=r.l;return n.add(t),N(),()=>{n.delete(t),j(e),N()}}};return Object.defineProperty(P,w,{value:_}),P},b=e=>(e.c||(e.c=v()),e.m||(e.m=v()),e.u||(e.u=v()),e.f||(e.f=g()),e),x=c},8935:function(e,t,r){r.d(t,{O4:function(){return i}});var n=r(2732);let a=Symbol("RESET"),o=e=>"function"==typeof(null==e?void 0:e.then),s=function(e=()=>{try{return window.localStorage}catch(e){"undefined"!=typeof window&&console.warn(e);return}},t){var r,n;let a,s,i;let l={getItem:(r,n)=>{var i,l;let d=e=>{if(a!==(e=e||"")){try{s=JSON.parse(e,null==t?void 0:t.reviver)}catch(e){return n}a=e}return s},u=null!=(l=null==(i=e())?void 0:i.getItem(r))?l:null;return o(u)?u.then(d):d(u)},setItem:(r,n)=>{var a;return null==(a=e())?void 0:a.setItem(r,JSON.stringify(n,null==t?void 0:t.replacer))},removeItem:t=>{var r;return null==(r=e())?void 0:r.removeItem(t)}};try{i=null==(r=e())?void 0:r.subscribe}catch(e){}return!i&&"undefined"!=typeof window&&"function"==typeof window.addEventListener&&window.Storage&&(i=(t,r)=>{if(!(e() instanceof window.Storage))return()=>{};let n=n=>{n.storageArea===e()&&n.key===t&&r(n.newValue)};return window.addEventListener("storage",n),()=>{window.removeEventListener("storage",n)}}),i&&(l.subscribe=(n=i,(e,t,r)=>n(e,e=>{let n;try{n=JSON.parse(e||"")}catch(e){n=r}t(n)}))),l}();function i(e,t,r=s,i){let l=null==i?void 0:i.getOnInit,d=(0,n.cn)(l?r.getItem(e,t):t);d.debugPrivate=!0,d.onMount=n=>{let a;return n(r.getItem(e,t)),r.subscribe&&(a=r.subscribe(e,n,t)),a};let u=(0,n.cn)(e=>e(d),(n,s,i)=>{let l="function"==typeof i?i(n(d)):i;return l===a?(s(d,t),r.removeItem(e)):o(l)?l.then(t=>(s(d,t),r.setItem(e,t))):(s(d,l),r.setItem(e,l))});return u}},8255:function(e,t,r){r.d(t,{F:function(){return u},f:function(){return c}});var n=r(7653),a=(e,t,r,n,a,o,s,i)=>{let l=document.documentElement,d=["light","dark"];function u(t){(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&o?a.map(e=>o[e]||e):a;r?(l.classList.remove(...n),l.classList.add(o&&o[t]?o[t]:t)):l.setAttribute(e,t)}),i&&d.includes(t)&&(l.style.colorScheme=t)}if(n)u(n);else try{let e=localStorage.getItem(t)||r,n=s&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;u(n)}catch(e){}},o=["light","dark"],s="(prefers-color-scheme: dark)",i="undefined"==typeof window,l=n.createContext(void 0),d={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(l))?e:d},c=e=>n.useContext(l)?n.createElement(n.Fragment,null,e.children):n.createElement(m,{...e}),f=["light","dark"],m=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:a=!0,storageKey:i="theme",themes:d=f,defaultTheme:u=r?"system":"light",attribute:c="data-theme",value:m,children:w,nonce:y,scriptProps:b})=>{let[x,M]=n.useState(()=>h(i,u)),[E,C]=n.useState(()=>"system"===x?v():x),N=m?Object.values(m):d,S=n.useCallback(e=>{let n=e;if(!n)return;"system"===e&&r&&(n=v());let s=m?m[n]:n,i=t?g(y):null,l=document.documentElement,d=e=>{"class"===e?(l.classList.remove(...N),s&&l.classList.add(s)):e.startsWith("data-")&&(s?l.setAttribute(e,s):l.removeAttribute(e))};if(Array.isArray(c)?c.forEach(d):d(c),a){let e=o.includes(u)?u:null,t=o.includes(n)?n:e;l.style.colorScheme=t}null==i||i()},[y]),k=n.useCallback(e=>{let t="function"==typeof e?e(x):e;M(t);try{localStorage.setItem(i,t)}catch(e){}},[x]),D=n.useCallback(t=>{C(v(t)),"system"===x&&r&&!e&&S("system")},[x,e]);n.useEffect(()=>{let e=window.matchMedia(s);return e.addListener(D),D(e),()=>e.removeListener(D)},[D]),n.useEffect(()=>{let e=e=>{e.key===i&&(e.newValue?M(e.newValue):k(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[k]),n.useEffect(()=>{S(null!=e?e:x)},[e,x]);let I=n.useMemo(()=>({theme:x,setTheme:k,forcedTheme:e,resolvedTheme:"system"===x?E:x,themes:r?[...d,"system"]:d,systemTheme:r?E:void 0}),[x,k,e,E,r,d]);return n.createElement(l.Provider,{value:I},n.createElement(p,{forcedTheme:e,storageKey:i,attribute:c,enableSystem:r,enableColorScheme:a,defaultTheme:u,value:m,themes:d,nonce:y,scriptProps:b}),w)},p=n.memo(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:o,enableColorScheme:s,defaultTheme:i,value:l,themes:d,nonce:u,scriptProps:c})=>{let f=JSON.stringify([r,t,i,e,d,l,o,s]).slice(1,-1);return n.createElement("script",{...c,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?u:"",dangerouslySetInnerHTML:{__html:`(${a.toString()})(${f})`}})}),h=(e,t)=>{let r;if(!i){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},g=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(s)),e.matches?"dark":"light")},3588:function(e,t,r){r.d(t,{Am:function(){return v},x7:function(){return M}});var n=r(7653),a=r(1036),o=e=>{switch(e){case"success":return l;case"info":return u;case"warning":return d;case"error":return c;default:return null}},s=Array(12).fill(0),i=({visible:e,className:t})=>n.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},n.createElement("div",{className:"sonner-spinner"},s.map((e,t)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),l=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),m=()=>{let[e,t]=n.useState(document.hidden);return n.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},p=1,h=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,a="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:p++,o=this.toasts.find(e=>e.id===a),s=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),o?this.toasts=this.toasts.map(t=>t.id===a?(this.publish({...t,...e,id:a,title:r}),{...t,...e,id:a,dismissible:s,title:r}):t):this.addToast({title:r,...n,dismissible:s,id:a}),a},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r;if(!t)return;void 0!==t.loading&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let n=e instanceof Promise?e:e(),a=void 0!==r,o,s=n.then(async e=>{if(o=["resolve",e],re.isValidElement(e))a=!1,this.create({id:r,type:"default",message:e});else if(g(e)&&!e.ok){a=!1;let n="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,o="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:r,type:"error",message:n,description:o})}else if(void 0!==t.success){a=!1;let n="function"==typeof t.success?await t.success(e):t.success,o="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"success",message:n,description:o})}}).catch(async e=>{if(o=["reject",e],void 0!==t.error){a=!1;let n="function"==typeof t.error?await t.error(e):t.error,o="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"error",message:n,description:o})}}).finally(()=>{var e;a&&(this.dismiss(r),r=void 0),null==(e=t.finally)||e.call(t)}),i=()=>new Promise((e,t)=>s.then(()=>"reject"===o[0]?t(o[1]):e(o[1])).catch(t));return"string"!=typeof r&&"number"!=typeof r?{unwrap:i}:Object.assign(r,{unwrap:i})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||p++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},g=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,v=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||p++;return h.addToast({title:e,...t,id:r}),r},{success:h.success,info:h.info,warning:h.warning,error:h.error,custom:h.custom,message:h.message,promise:h.promise,dismiss:h.dismiss,loading:h.loading},{getHistory:()=>h.toasts,getToasts:()=>h.getActiveToasts()});function w(e){return void 0!==e.label}function y(...e){return e.filter(Boolean).join(" ")}!function(e,{insertAt:t}={}){if(!e||"undefined"==typeof document)return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===t&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var b=e=>{var t,r,a,s,l,d,u,c,p,h,g,v,b,x;let{invert:M,toast:E,unstyled:C,interacting:N,setHeights:S,visibleToasts:k,heights:D,index:I,toasts:T,expanded:R,removeToast:j,defaultRichColors:_,closeButton:P,style:B,cancelButtonStyle:A,actionButtonStyle:O,className:z="",descriptionClassName:L="",duration:V,position:F,gap:W,loadingIcon:K,expandByDefault:Y,classNames:$,icons:H,closeButtonAriaLabel:G="Close toast",pauseWhenPageIsHidden:U}=e,[X,J]=n.useState(null),[q,Z]=n.useState(null),[Q,ee]=n.useState(!1),[et,er]=n.useState(!1),[en,ea]=n.useState(!1),[eo,es]=n.useState(!1),[ei,el]=n.useState(!1),[ed,eu]=n.useState(0),[ec,ef]=n.useState(0),em=n.useRef(E.duration||V||4e3),ep=n.useRef(null),eh=n.useRef(null),eg=E.type,ev=!1!==E.dismissible,ew=E.className||"",ey=E.descriptionClassName||"",eb=n.useMemo(()=>D.findIndex(e=>e.toastId===E.id)||0,[D,E.id]),ex=n.useMemo(()=>{var e;return null!=(e=E.closeButton)?e:P},[E.closeButton,P]),eM=n.useMemo(()=>E.duration||V||4e3,[E.duration,V]),eE=n.useRef(0),eC=n.useRef(0),eN=n.useRef(0),eS=n.useRef(null),[ek,eD]=F.split("-"),eI=n.useMemo(()=>D.reduce((e,t,r)=>r>=eb?e:e+t.height,0),[D,eb]),eT=m(),eR=E.invert||M,ej="loading"===eg;eC.current=n.useMemo(()=>eb*W+eI,[eb,eI]),n.useEffect(()=>{em.current=eM},[eM]),n.useEffect(()=>{ee(!0)},[]),n.useEffect(()=>{let e=eh.current;if(e){let t=e.getBoundingClientRect().height;return ef(t),S(e=>[{toastId:E.id,height:t,position:E.position},...e]),()=>S(e=>e.filter(e=>e.toastId!==E.id))}},[S,E.id]),n.useLayoutEffect(()=>{if(!Q)return;let e=eh.current,t=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=t,ef(r),S(e=>e.find(e=>e.toastId===E.id)?e.map(e=>e.toastId===E.id?{...e,height:r}:e):[{toastId:E.id,height:r,position:E.position},...e])},[Q,E.title,E.description,S,E.id]);let e_=n.useCallback(()=>{er(!0),eu(eC.current),S(e=>e.filter(e=>e.toastId!==E.id)),setTimeout(()=>{j(E)},200)},[E,j,S,eC]);return n.useEffect(()=>{let e;if((!E.promise||"loading"!==eg)&&E.duration!==1/0&&"loading"!==E.type)return R||N||U&&eT?(()=>{if(eN.current<eE.current){let e=new Date().getTime()-eE.current;em.current=em.current-e}eN.current=new Date().getTime()})():em.current!==1/0&&(eE.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=E.onAutoClose)||e.call(E,E),e_()},em.current)),()=>clearTimeout(e)},[R,N,E,eg,U,eT,e_]),n.useEffect(()=>{E.delete&&e_()},[e_,E.delete]),n.createElement("li",{tabIndex:0,ref:eh,className:y(z,ew,null==$?void 0:$.toast,null==(t=null==E?void 0:E.classNames)?void 0:t.toast,null==$?void 0:$.default,null==$?void 0:$[eg],null==(r=null==E?void 0:E.classNames)?void 0:r[eg]),"data-sonner-toast":"","data-rich-colors":null!=(a=E.richColors)?a:_,"data-styled":!(E.jsx||E.unstyled||C),"data-mounted":Q,"data-promise":!!E.promise,"data-swiped":ei,"data-removed":et,"data-visible":I+1<=k,"data-y-position":ek,"data-x-position":eD,"data-index":I,"data-front":0===I,"data-swiping":en,"data-dismissible":ev,"data-type":eg,"data-invert":eR,"data-swipe-out":eo,"data-swipe-direction":q,"data-expanded":!!(R||Y&&Q),style:{"--index":I,"--toasts-before":I,"--z-index":T.length-I,"--offset":`${et?ed:eC.current}px`,"--initial-height":Y?"auto":`${ec}px`,...B,...E.style},onDragEnd:()=>{ea(!1),J(null),eS.current=null},onPointerDown:e=>{ej||!ev||(ep.current=new Date,eu(eC.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(ea(!0),eS.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,r,n;if(eo||!ev)return;eS.current=null;let a=Number((null==(e=eh.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),o=Number((null==(t=eh.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),s=new Date().getTime()-(null==(r=ep.current)?void 0:r.getTime()),i="x"===X?a:o;if(Math.abs(i)>=20||Math.abs(i)/s>.11){eu(eC.current),null==(n=E.onDismiss)||n.call(E,E),Z("x"===X?a>0?"right":"left":o>0?"down":"up"),e_(),es(!0),el(!1);return}ea(!1),J(null)},onPointerMove:t=>{var r,n,a,o;if(!eS.current||!ev||(null==(r=window.getSelection())?void 0:r.toString().length)>0)return;let s=t.clientY-eS.current.y,i=t.clientX-eS.current.x,l=null!=(n=e.swipeDirections)?n:function(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}(F);!X&&(Math.abs(i)>1||Math.abs(s)>1)&&J(Math.abs(i)>Math.abs(s)?"x":"y");let d={x:0,y:0};"y"===X?(l.includes("top")||l.includes("bottom"))&&(l.includes("top")&&s<0||l.includes("bottom")&&s>0)&&(d.y=s):"x"===X&&(l.includes("left")||l.includes("right"))&&(l.includes("left")&&i<0||l.includes("right")&&i>0)&&(d.x=i),(Math.abs(d.x)>0||Math.abs(d.y)>0)&&el(!0),null==(a=eh.current)||a.style.setProperty("--swipe-amount-x",`${d.x}px`),null==(o=eh.current)||o.style.setProperty("--swipe-amount-y",`${d.y}px`)}},ex&&!E.jsx?n.createElement("button",{"aria-label":G,"data-disabled":ej,"data-close-button":!0,onClick:ej||!ev?()=>{}:()=>{var e;e_(),null==(e=E.onDismiss)||e.call(E,E)},className:y(null==$?void 0:$.closeButton,null==(s=null==E?void 0:E.classNames)?void 0:s.closeButton)},null!=(l=null==H?void 0:H.close)?l:f):null,E.jsx||(0,n.isValidElement)(E.title)?E.jsx?E.jsx:"function"==typeof E.title?E.title():E.title:n.createElement(n.Fragment,null,eg||E.icon||E.promise?n.createElement("div",{"data-icon":"",className:y(null==$?void 0:$.icon,null==(d=null==E?void 0:E.classNames)?void 0:d.icon)},E.promise||"loading"===E.type&&!E.icon?E.icon||(null!=H&&H.loading?n.createElement("div",{className:y(null==$?void 0:$.loader,null==(v=null==E?void 0:E.classNames)?void 0:v.loader,"sonner-loader"),"data-visible":"loading"===eg},H.loading):K?n.createElement("div",{className:y(null==$?void 0:$.loader,null==(b=null==E?void 0:E.classNames)?void 0:b.loader,"sonner-loader"),"data-visible":"loading"===eg},K):n.createElement(i,{className:y(null==$?void 0:$.loader,null==(x=null==E?void 0:E.classNames)?void 0:x.loader),visible:"loading"===eg})):null,"loading"!==E.type?E.icon||(null==H?void 0:H[eg])||o(eg):null):null,n.createElement("div",{"data-content":"",className:y(null==$?void 0:$.content,null==(u=null==E?void 0:E.classNames)?void 0:u.content)},n.createElement("div",{"data-title":"",className:y(null==$?void 0:$.title,null==(c=null==E?void 0:E.classNames)?void 0:c.title)},"function"==typeof E.title?E.title():E.title),E.description?n.createElement("div",{"data-description":"",className:y(L,ey,null==$?void 0:$.description,null==(p=null==E?void 0:E.classNames)?void 0:p.description)},"function"==typeof E.description?E.description():E.description):null),(0,n.isValidElement)(E.cancel)?E.cancel:E.cancel&&w(E.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:E.cancelButtonStyle||A,onClick:e=>{var t,r;w(E.cancel)&&ev&&(null==(r=(t=E.cancel).onClick)||r.call(t,e),e_())},className:y(null==$?void 0:$.cancelButton,null==(h=null==E?void 0:E.classNames)?void 0:h.cancelButton)},E.cancel.label):null,(0,n.isValidElement)(E.action)?E.action:E.action&&w(E.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:E.actionButtonStyle||O,onClick:e=>{var t,r;w(E.action)&&(null==(r=(t=E.action).onClick)||r.call(t,e),e.defaultPrevented||e_())},className:y(null==$?void 0:$.actionButton,null==(g=null==E?void 0:E.classNames)?void 0:g.actionButton)},E.action.label):null))};function x(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}var M=(0,n.forwardRef)(function(e,t){let{invert:r,position:o="bottom-right",hotkey:s=["altKey","KeyT"],expand:i,closeButton:l,className:d,offset:u,mobileOffset:c,theme:f="light",richColors:m,duration:p,style:g,visibleToasts:v=3,toastOptions:w,dir:y=x(),gap:M=14,loadingIcon:E,icons:C,containerAriaLabel:N="Notifications",pauseWhenPageIsHidden:S}=e,[k,D]=n.useState([]),I=n.useMemo(()=>Array.from(new Set([o].concat(k.filter(e=>e.position).map(e=>e.position)))),[k,o]),[T,R]=n.useState([]),[j,_]=n.useState(!1),[P,B]=n.useState(!1),[A,O]=n.useState("system"!==f?f:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),z=n.useRef(null),L=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),V=n.useRef(null),F=n.useRef(!1),W=n.useCallback(e=>{D(t=>{var r;return null!=(r=t.find(t=>t.id===e.id))&&r.delete||h.dismiss(e.id),t.filter(({id:t})=>t!==e.id)})},[]);return n.useEffect(()=>h.subscribe(e=>{if(e.dismiss){D(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));return}setTimeout(()=>{a.flushSync(()=>{D(t=>{let r=t.findIndex(t=>t.id===e.id);return -1!==r?[...t.slice(0,r),{...t[r],...e},...t.slice(r+1)]:[e,...t]})})})}),[]),n.useEffect(()=>{if("system"!==f){O(f);return}if("system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?O("dark"):O("light")),"undefined"==typeof window)return;let e=window.matchMedia("(prefers-color-scheme: dark)");try{e.addEventListener("change",({matches:e})=>{O(e?"dark":"light")})}catch(t){e.addListener(({matches:e})=>{try{O(e?"dark":"light")}catch(e){console.error(e)}})}},[f]),n.useEffect(()=>{k.length<=1&&_(!1)},[k]),n.useEffect(()=>{let e=e=>{var t,r;s.every(t=>e[t]||e.code===t)&&(_(!0),null==(t=z.current)||t.focus()),"Escape"===e.code&&(document.activeElement===z.current||null!=(r=z.current)&&r.contains(document.activeElement))&&_(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[s]),n.useEffect(()=>{if(z.current)return()=>{V.current&&(V.current.focus({preventScroll:!0}),V.current=null,F.current=!1)}},[z.current]),n.createElement("section",{ref:t,"aria-label":`${N} ${L}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},I.map((t,a)=>{var o;let s;let[f,h]=t.split("-");return k.length?n.createElement("ol",{key:t,dir:"auto"===y?x():y,tabIndex:-1,ref:z,className:d,"data-sonner-toaster":!0,"data-theme":A,"data-y-position":f,"data-lifted":j&&k.length>1&&!i,"data-x-position":h,style:{"--front-toast-height":`${(null==(o=T[0])?void 0:o.height)||0}px`,"--width":"356px","--gap":`${M}px`,...g,...(s={},[u,c].forEach((e,t)=>{let r=1===t,n=r?"--mobile-offset":"--offset",a=r?"16px":"32px";function o(e){["top","right","bottom","left"].forEach(t=>{s[`${n}-${t}`]="number"==typeof e?`${e}px`:e})}"number"==typeof e||"string"==typeof e?o(e):"object"==typeof e?["top","right","bottom","left"].forEach(t=>{void 0===e[t]?s[`${n}-${t}`]=a:s[`${n}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]}):o(a)}),s)},onBlur:e=>{F.current&&!e.currentTarget.contains(e.relatedTarget)&&(F.current=!1,V.current&&(V.current.focus({preventScroll:!0}),V.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||F.current||(F.current=!0,V.current=e.relatedTarget)},onMouseEnter:()=>_(!0),onMouseMove:()=>_(!0),onMouseLeave:()=>{P||_(!1)},onDragEnd:()=>_(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||B(!0)},onPointerUp:()=>B(!1)},k.filter(e=>!e.position&&0===a||e.position===t).map((a,o)=>{var s,d;return n.createElement(b,{key:a.id,icons:C,index:o,toast:a,defaultRichColors:m,duration:null!=(s=null==w?void 0:w.duration)?s:p,className:null==w?void 0:w.className,descriptionClassName:null==w?void 0:w.descriptionClassName,invert:r,visibleToasts:v,closeButton:null!=(d=null==w?void 0:w.closeButton)?d:l,interacting:P,position:t,style:null==w?void 0:w.style,unstyled:null==w?void 0:w.unstyled,classNames:null==w?void 0:w.classNames,cancelButtonStyle:null==w?void 0:w.cancelButtonStyle,actionButtonStyle:null==w?void 0:w.actionButtonStyle,removeToast:W,toasts:k.filter(e=>e.position==a.position),heights:T.filter(e=>e.position==a.position),setHeights:R,expandByDefault:i,gap:M,loadingIcon:E,expanded:j,pauseWhenPageIsHidden:S,swipeDirections:e.swipeDirections})})):null}))})}}]);