"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[906],{188:function(t,e,i){let s;i.d(e,{$1:function(){return iL},$f:function(){return iE},AE:function(){return i4},Eu:function(){return s0},HQ:function(){return s3},Nm:function(){return tb},OO:function(){return tG},S2:function(){return sc},SJ:function(){return sP},Sd:function(){return sO},Uw:function(){return ij},W$:function(){return sn},ZO:function(){return se},Zs:function(){return sl},bF:function(){return sk},gB:function(){return sA},hJ:function(){return sy},l9:function(){return tn},lg:function(){return tZ},mH:function(){return sL},p:function(){return tl},qr:function(){return iJ},tk:function(){return iS},v5:function(){return sF}});var o,n,r,l=i(7474),h=i(3237),a=i(1934);function c(t){return(11==t.nodeType?t.getSelection?t:t.ownerDocument:t).getSelection()}function d(t,e){return!!e&&(t==e||t.contains(1!=e.nodeType?e.parentNode:e))}function u(t,e){if(!e.anchorNode)return!1;try{return d(t,e.anchorNode)}catch(t){return!1}}function f(t){return 3==t.nodeType?C(t,0,t.nodeValue.length).getClientRects():1==t.nodeType?t.getClientRects():[]}function p(t,e,i,s){return!!i&&(v(t,e,i,s,-1)||v(t,e,i,s,1))}function g(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e}function m(t){return 1==t.nodeType&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(t.nodeName)}function v(t,e,i,s,o){for(;;){if(t==i&&e==s)return!0;if(e==(o<0?0:w(t))){if("DIV"==t.nodeName)return!1;let i=t.parentNode;if(!i||1!=i.nodeType)return!1;e=g(t)+(o<0?0:1),t=i}else{if(1!=t.nodeType||1==(t=t.childNodes[e+(o<0?-1:0)]).nodeType&&"false"==t.contentEditable)return!1;e=o<0?w(t):0}}}function w(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function b(t,e){let i=e?t.left:t.right;return{left:i,right:i,top:t.top,bottom:t.bottom}}function y(t,e){let i=e.width/t.offsetWidth,s=e.height/t.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||1>Math.abs(e.width-t.offsetWidth))&&(i=1),(s>.995&&s<1.005||!isFinite(s)||1>Math.abs(e.height-t.offsetHeight))&&(s=1),{scaleX:i,scaleY:s}}class x{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:e,focusNode:i}=t;this.set(e,Math.min(t.anchorOffset,e?w(e):0),i,Math.min(t.focusOffset,i?w(i):0))}set(t,e,i,s){this.anchorNode=t,this.anchorOffset=e,this.focusNode=i,this.focusOffset=s}}let S=null;function M(t){if(t.setActive)return t.setActive();if(S)return t.focus(S);let e=[];for(let i=t;i&&(e.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(t.focus(null==S?{get preventScroll(){return S={preventScroll:!0},!0}}:void 0),!S){S=!1;for(let t=0;t<e.length;){let i=e[t++],s=e[t++],o=e[t++];i.scrollTop!=s&&(i.scrollTop=s),i.scrollLeft!=o&&(i.scrollLeft=o)}}}function C(t,e,i=e){let o=s||(s=document.createRange());return o.setEnd(t,i),o.setStart(t,e),o}function k(t,e,i,s){let o={key:e,code:e,keyCode:i,which:i,cancelable:!0};s&&({altKey:o.altKey,ctrlKey:o.ctrlKey,shiftKey:o.shiftKey,metaKey:o.metaKey}=s);let n=new KeyboardEvent("keydown",o);n.synthetic=!0,t.dispatchEvent(n);let r=new KeyboardEvent("keyup",o);return r.synthetic=!0,t.dispatchEvent(r),n.defaultPrevented||r.defaultPrevented}function A(t){for(;t.attributes.length;)t.removeAttributeNode(t.attributes[0])}function D(t){return t.scrollTop>Math.max(1,t.scrollHeight-t.clientHeight-4)}function T(t,e){for(let i=t,s=e;;){if(3==i.nodeType&&s>0)return{node:i,offset:s};if(1==i.nodeType&&s>0){if("false"==i.contentEditable)return null;s=w(i=i.childNodes[s-1])}else{if(!i.parentNode||m(i))return null;s=g(i),i=i.parentNode}}}function O(t,e){for(let i=t,s=e;;){if(3==i.nodeType&&s<i.nodeValue.length)return{node:i,offset:s};if(1==i.nodeType&&s<i.childNodes.length){if("false"==i.contentEditable)return null;i=i.childNodes[s],s=0}else{if(!i.parentNode||m(i))return null;s=g(i)+1,i=i.parentNode}}}class E{constructor(t,e,i=!0){this.node=t,this.offset=e,this.precise=i}static before(t,e){return new E(t.parentNode,g(t),e)}static after(t,e){return new E(t.parentNode,g(t)+1,e)}}let R=[];class B{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let e=this.posAtStart;for(let i of this.children){if(i==t)return e;e+=i.length+i.breakAfter}throw RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,e){if(2&this.flags){let i=this.dom,s=null,o;for(let n of this.children){if(7&n.flags){if(!n.dom&&(o=s?s.nextSibling:i.firstChild)){let t=B.get(o);(!t||!t.parent&&t.canReuseDOM(n))&&n.reuseDOM(o)}n.sync(t,e),n.flags&=-8}if(o=s?s.nextSibling:i.firstChild,e&&!e.written&&e.node==i&&o!=n.dom&&(e.written=!0),n.dom.parentNode==i)for(;o&&o!=n.dom;)o=L(o);else i.insertBefore(n.dom,o);s=n.dom}for((o=s?s.nextSibling:i.firstChild)&&e&&e.node==i&&(e.written=!0);o;)o=L(o)}else if(1&this.flags)for(let i of this.children)7&i.flags&&(i.sync(t,e),i.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,e){let i;if(t==this.dom)i=this.dom.childNodes[e];else{let s=0==w(t)?0:0==e?-1:1;for(;;){let e=t.parentNode;if(e==this.dom)break;0==s&&e.firstChild!=e.lastChild&&(s=t==e.firstChild?-1:1),t=e}i=s<0?t:t.nextSibling}if(i==this.dom.firstChild)return 0;for(;i&&!B.get(i);)i=i.nextSibling;if(!i)return this.length;for(let t=0,e=0;;t++){let s=this.children[t];if(s.dom==i)return e;e+=s.length+s.breakAfter}}domBoundsAround(t,e,i=0){let s=-1,o=-1,n=-1,r=-1;for(let l=0,h=i,a=i;l<this.children.length;l++){let i=this.children[l],c=h+i.length;if(h<t&&c>e)return i.domBoundsAround(t,e,h);if(c>=t&&-1==s&&(s=l,o=h),h>e&&i.dom.parentNode==this.dom){n=l,r=a;break}a=c,h=c+i.breakAfter}return{from:o,to:r<0?i+this.length:r,startDOM:(s?this.children[s-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:n<this.children.length&&n>=0?this.children[n].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let e=this.parent;e;e=e.parent){if(t&&(e.flags|=2),1&e.flags)return;e.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,7&this.flags&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let e=t.parent;if(!e)return t;t=e}}replaceChildren(t,e,i=R){this.markDirty();for(let s=t;s<e;s++){let t=this.children[s];t.parent==this&&0>i.indexOf(t)&&t.destroy()}i.length<250?this.children.splice(t,e-t,...i):this.children=[].concat(this.children.slice(0,t),i,this.children.slice(e));for(let t=0;t<i.length;t++)i[t].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new P(this.children,t,this.children.length)}childPos(t,e=1){return this.childCursor().findPos(t,e)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+("Text"==t?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,e,i,s,o,n){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}function L(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}B.prototype.breakAfter=0;class P{constructor(t,e,i){this.children=t,this.pos=e,this.i=i,this.off=0}findPos(t,e=1){for(;;){if(t>this.pos||t==this.pos&&(e>0||0==this.i||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let i=this.children[--this.i];this.pos-=i.length+i.breakAfter}}}function H(t,e,i,s,o,n,r,l,h){let{children:a}=t,c=a.length?a[e]:null,d=n.length?n[n.length-1]:null,u=d?d.breakAfter:r;if(!(e==s&&c&&!r&&!u&&n.length<2&&c.merge(i,o,n.length?d:null,0==i,l,h))){if(s<a.length){let t=a[s];t&&(o<t.length||t.breakAfter&&(null==d?void 0:d.breakAfter))?(e==s&&(t=t.split(o),o=0),!u&&d&&t.merge(0,o,d,!0,0,h)?n[n.length-1]=t:((o||t.children.length&&!t.children[0].length)&&t.merge(0,o,null,!1,0,h),n.push(t))):(null==t?void 0:t.breakAfter)&&(d?d.breakAfter=1:r=1),s++}for(c&&(c.breakAfter=r,i>0&&(!r&&n.length&&c.merge(i,c.length,n[0],!1,l,0)?c.breakAfter=n.shift().breakAfter:(i<c.length||c.children.length&&0==c.children[c.children.length-1].length)&&c.merge(i,c.length,null,!1,l,0),e++));e<s&&n.length;)if(a[s-1].become(n[n.length-1]))s--,n.pop(),h=n.length?0:l;else if(a[e].become(n[0]))e++,n.shift(),l=n.length?0:h;else break;!n.length&&e&&s<a.length&&!a[e-1].breakAfter&&a[s].merge(0,0,a[e-1],!1,l,h)&&e--,(e<s||n.length)&&t.replaceChildren(e,s,n)}}function V(t,e,i,s,o,n){let r=t.childCursor(),{i:l,off:h}=r.findPos(i,1),{i:a,off:c}=r.findPos(e,-1),d=e-i;for(let t of s)d+=t.length;t.length+=d,H(t,a,c,l,h,s,0,o,n)}let N="undefined"!=typeof navigator?navigator:{userAgent:"",vendor:"",platform:""},W="undefined"!=typeof document?document:{documentElement:{style:{}}},F=/Edge\/(\d+)/.exec(N.userAgent),z=/MSIE \d/.test(N.userAgent),I=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(N.userAgent),q=!!(z||I||F),K=!q&&/gecko\/(\d+)/i.test(N.userAgent),j=!q&&/Chrome\/(\d+)/.exec(N.userAgent),$="webkitFontSmoothing"in W.documentElement.style,X=!q&&/Apple Computer/.test(N.vendor),Y=X&&(/Mobile\/\w+/.test(N.userAgent)||N.maxTouchPoints>2);var G={mac:Y||/Mac/.test(N.platform),windows:/Win/.test(N.platform),linux:/Linux|X11/.test(N.platform),ie:q,ie_version:z?W.documentMode||6:I?+I[1]:F?+F[1]:0,gecko:K,gecko_version:K?+(/Firefox\/(\d+)/.exec(N.userAgent)||[0,0])[1]:0,chrome:!!j,chrome_version:j?+j[1]:0,ios:Y,android:/Android\b/.test(N.userAgent),webkit:$,safari:X,webkit_version:$?+(/\bAppleWebKit\/(\d+)/.exec(N.userAgent)||[0,0])[1]:0,tabSize:null!=W.documentElement.style.tabSize?"tab-size":"-moz-tab-size"};class _ extends B{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,e){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(e&&e.node==this.dom&&(e.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){3==t.nodeType&&this.createDOM(t)}merge(t,e,i){return!(8&this.flags)&&(!i||i instanceof _&&!(this.length-(e-t)+i.length>256)&&!(8&i.flags))&&(this.text=this.text.slice(0,t)+(i?i.text:"")+this.text.slice(e),this.markDirty(),!0)}split(t){let e=new _(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),e.flags|=8&this.flags,e}localPosFromDOM(t,e){return t==this.dom?e:e?this.text.length:0}domAtPos(t){return new E(this.dom,t)}domBoundsAround(t,e,i){return{from:i,to:i+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,e){return function(t,e,i){let s=t.nodeValue.length;e>s&&(e=s);let o=e,n=e,r=0;0==e&&i<0||e==s&&i>=0?!(G.chrome||G.gecko)&&(e?(o--,r=1):n<s&&(n++,r=-1)):i<0?o--:n<s&&n++;let l=C(t,o,n).getClientRects();if(!l.length)return null;let h=l[(r?r<0:i>=0)?0:l.length-1];return G.safari&&!r&&0==h.width&&(h=Array.prototype.find.call(l,t=>t.width)||h),r?b(h,r<0):h||null}(this.dom,t,e)}}class U extends B{constructor(t,e=[],i=0){for(let s of(super(),this.mark=t,this.children=e,this.length=i,e))s.setParent(this)}setAttrs(t){if(A(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let e in this.mark.attrs)t.setAttribute(e,this.mark.attrs[e]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,e){this.dom?4&this.flags&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,e)}merge(t,e,i,s,o,n){return(!i||!!(i instanceof U&&i.mark.eq(this.mark))&&(!t||!(o<=0))&&(!(e<this.length)||!(n<=0)))&&(V(this,t,e,i?i.children.slice():[],o-1,n-1),this.markDirty(),!0)}split(t){let e=[],i=0,s=-1,o=0;for(let n of this.children){let r=i+n.length;r>t&&e.push(i<t?n.split(t-i):n),s<0&&i>=t&&(s=o),i=r,o++}let n=this.length-t;return this.length=t,s>-1&&(this.children.length=s,this.markDirty()),new U(this.mark,e,n)}domAtPos(t){return J(this,t)}coordsAt(t,e){return tt(this,t,e)}}class Q extends B{static create(t,e,i){return new Q(t,e,i)}constructor(t,e,i){super(),this.widget=t,this.length=e,this.side=i,this.prevWidget=null}split(t){let e=Q.create(this.widget,this.length-t,this.side);return this.length-=t,e}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,e,i,s,o,n){return(!i||i instanceof Q&&!!this.widget.compare(i.widget)&&(!(t>0)||!(o<=0))&&(!(e<this.length)||!(n<=0)))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}become(t){return t instanceof Q&&t.side==this.side&&this.widget.constructor==t.widget.constructor&&(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(0==this.length)return l.xv.empty;let t=this;for(;t.parent;)t=t.parent;let{view:e}=t,i=e&&e.state.doc,s=this.posAtStart;return i?i.slice(s,s+this.length):l.xv.empty}domAtPos(t){return(this.length?0==t:this.side>0)?E.before(this.dom):E.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,e){let i=this.widget.coordsAt(this.dom,t,e);if(i)return i;let s=this.dom.getClientRects(),o=null;if(!s.length)return null;let n=this.side?this.side<0:t>0;for(let e=n?s.length-1:0;o=s[e],t>0?0!=e:e!=s.length-1&&!(o.top<o.bottom);e+=n?-1:1);return b(o,!n)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class Z extends B{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof Z&&t.side==this.side}split(){return new Z(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?E.before(this.dom):E.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return l.xv.empty}get isHidden(){return!0}}function J(t,e){let i=t.dom,{children:s}=t,o=0;for(let t=0;o<s.length;o++){let n=s[o],r=t+n.length;if(!(r==t&&0>=n.getSide())){if(e>t&&e<r&&n.dom.parentNode==i)return n.domAtPos(e-t);if(e<=t)break;t=r}}for(let t=o;t>0;t--){let e=s[t-1];if(e.dom.parentNode==i)return e.domAtPos(e.length)}for(let t=o;t<s.length;t++){let e=s[t];if(e.dom.parentNode==i)return e.domAtPos(0)}return new E(i,0)}function tt(t,e,i){let s=null,o=-1,n=null,r=-1;!function t(e,l){for(let h=0,a=0;h<e.children.length&&a<=l;h++){let c=e.children[h],d=a+c.length;d>=l&&(c.children.length?t(c,l-a):(!n||n.isHidden&&(i>0||function(t,e){let i=t.coordsAt(0,1),s=e.coordsAt(0,1);return i&&s&&s.top<i.bottom}(n,c)))&&(d>l||a==d&&c.getSide()>0)?(n=c,r=l-a):(a<l||a==d&&0>c.getSide()&&!c.isHidden)&&(s=c,o=l-a)),a=d}}(t,e);let l=(i<0?s:n)||s||n;return l?l.coordsAt(Math.max(0,l==s?o:r),i):function(t){let e=t.dom.lastChild;if(!e)return t.dom.getBoundingClientRect();let i=f(e);return i[i.length-1]||null}(t)}function te(t,e){for(let i in t)"class"==i&&e.class?e.class+=" "+t.class:"style"==i&&e.style?e.style+=";"+t.style:e[i]=t[i];return e}_.prototype.children=Q.prototype.children=Z.prototype.children=R;let ti=Object.create(null);function ts(t,e,i){if(t==e)return!0;t||(t=ti),e||(e=ti);let s=Object.keys(t),o=Object.keys(e);if(s.length-(i&&s.indexOf(i)>-1?1:0)!=o.length-(i&&o.indexOf(i)>-1?1:0))return!1;for(let n of s)if(n!=i&&(-1==o.indexOf(n)||t[n]!==e[n]))return!1;return!0}function to(t,e,i){let s=!1;if(e)for(let o in e)i&&o in i||(s=!0,"style"==o?t.style.cssText="":t.removeAttribute(o));if(i)for(let o in i)e&&e[o]==i[o]||(s=!0,"style"==o?t.style.cssText=i[o]:t.setAttribute(o,i[o]));return s}class tn{eq(t){return!1}updateDOM(t,e){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return -1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,e,i){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var tr=((o=tr||(tr={}))[o.Text=0]="Text",o[o.WidgetBefore=1]="WidgetBefore",o[o.WidgetAfter=2]="WidgetAfter",o[o.WidgetRange=3]="WidgetRange",o);class tl extends l.uU{constructor(t,e,i,s){super(),this.startSide=t,this.endSide=e,this.widget=i,this.spec=s}get heightRelevant(){return!1}static mark(t){return new th(t)}static widget(t){let e=Math.max(-1e4,Math.min(1e4,t.side||0)),i=!!t.block;return e+=i&&!t.inlineOrder?e>0?3e8:-4e8:e>0?1e8:-1e8,new tc(t,e,e,i,t.widget||null,!1)}static replace(t){let e=!!t.block,i,s;if(t.isBlockGap)i=-5e8,s=4e8;else{let{start:o,end:n}=td(t,e);i=(o?e?-3e8:-1:5e8)-1,s=(n?e?2e8:1:-6e8)+1}return new tc(t,i,s,e,t.widget||null,!0)}static line(t){return new ta(t)}static set(t,e=!1){return l.Xs.of(t,e)}hasHeight(){return!!this.widget&&this.widget.estimatedHeight>-1}}tl.none=l.Xs.empty;class th extends tl{constructor(t){let{start:e,end:i}=td(t);super(e?-1:5e8,i?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var e,i;return this==t||t instanceof th&&this.tagName==t.tagName&&(this.class||(null===(e=this.attrs)||void 0===e?void 0:e.class))==(t.class||(null===(i=t.attrs)||void 0===i?void 0:i.class))&&ts(this.attrs,t.attrs,"class")}range(t,e=t){if(t>=e)throw RangeError("Mark decorations may not be empty");return super.range(t,e)}}th.prototype.point=!1;class ta extends tl{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof ta&&this.spec.class==t.spec.class&&ts(this.spec.attributes,t.spec.attributes)}range(t,e=t){if(e!=t)throw RangeError("Line decoration ranges must be zero-length");return super.range(t,e)}}ta.prototype.mapMode=l.gc.TrackBefore,ta.prototype.point=!0;class tc extends tl{constructor(t,e,i,s,o,n){super(e,i,o,t),this.block=s,this.isReplace=n,this.mapMode=s?e<=0?l.gc.TrackBefore:l.gc.TrackAfter:l.gc.TrackDel}get type(){return this.startSide!=this.endSide?tr.WidgetRange:this.startSide<=0?tr.WidgetBefore:tr.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){var e,i;return t instanceof tc&&((e=this.widget)==(i=t.widget)||!!(e&&i&&e.compare(i)))&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,e=t){if(this.isReplace&&(t>e||t==e&&this.startSide>0&&this.endSide<=0))throw RangeError("Invalid range for replacement decoration");if(!this.isReplace&&e!=t)throw RangeError("Widget decorations can only have zero-length ranges");return super.range(t,e)}}function td(t,e=!1){let{inclusiveStart:i,inclusiveEnd:s}=t;return null==i&&(i=t.inclusive),null==s&&(s=t.inclusive),{start:null!=i?i:e,end:null!=s?s:e}}function tu(t,e,i,s=0){let o=i.length-1;o>=0&&i[o]+s>=t?i[o]=Math.max(i[o],e):i.push(t,e)}tc.prototype.point=!0;class tf extends B{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,e,i,s,o,n){if(i){if(!(i instanceof tf))return!1;this.dom||i.transferDOM(this)}return s&&this.setDeco(i?i.attrs:null),V(this,t,e,i?i.children.slice():[],o,n),!0}split(t){let e=new tf;if(e.breakAfter=this.breakAfter,0==this.length)return e;let{i,off:s}=this.childPos(t);s&&(e.append(this.children[i].split(s),0),this.children[i].merge(s,this.children[i].length,null,!1,0,0),i++);for(let t=i;t<this.children.length;t++)e.append(this.children[t],0);for(;i>0&&0==this.children[i-1].length;)this.children[--i].destroy();return this.children.length=i,this.markDirty(),this.length=t,e}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=void 0===this.prevAttrs?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){ts(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,e){!function t(e,i,s){let o,{children:n}=e;s>0&&i instanceof U&&n.length&&(o=n[n.length-1])instanceof U&&o.mark.eq(i.mark)?t(o,i.children[0],s-1):(n.push(i),i.setParent(e)),e.length+=i.length}(this,t,e)}addLineDeco(t){let e=t.spec.attributes,i=t.spec.class;e&&(this.attrs=te(e,this.attrs||{})),i&&(this.attrs=te({class:i},this.attrs||{}))}domAtPos(t){return J(this,t)}reuseDOM(t){"DIV"==t.nodeName&&(this.setDOM(t),this.flags|=6)}sync(t,e){var i;this.dom?4&this.flags&&(A(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),void 0!==this.prevAttrs&&(to(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,e);let s=this.dom.lastChild;for(;s&&B.get(s) instanceof U;)s=s.lastChild;if(!s||!this.length||"BR"!=s.nodeName&&(null===(i=B.get(s))||void 0===i?void 0:i.isEditable)==!1&&(!G.ios||!this.children.some(t=>t instanceof _))){let t=document.createElement("BR");t.cmIgnore=!0,this.dom.appendChild(t)}}measureTextSize(){if(0==this.children.length||this.length>20)return null;let t=0,e;for(let i of this.children){if(!(i instanceof _)||/[^ -~]/.test(i.text))return null;let s=f(i.dom);if(1!=s.length)return null;t+=s[0].width,e=s[0].height}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:e}:null}coordsAt(t,e){let i=tt(this,t,e);if(!this.children.length&&i&&this.parent){let{heightOracle:t}=this.parent.view.viewState,e=i.bottom-i.top;if(2>Math.abs(e-t.lineHeight)&&t.textHeight<e){let s=(e-t.textHeight)/2;return{top:i.top+s,bottom:i.bottom-s,left:i.left,right:i.left}}}return i}become(t){return t instanceof tf&&0==this.children.length&&0==t.children.length&&ts(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,e){for(let i=0,s=0;i<t.children.length;i++){let o=t.children[i],n=s+o.length;if(n>=e){if(o instanceof tf)return o;if(n>e)break}s=n+o.breakAfter}return null}}class tp extends B{constructor(t,e,i){super(),this.widget=t,this.length=e,this.deco=i,this.breakAfter=0,this.prevWidget=null}merge(t,e,i,s,o,n){return(!i||i instanceof tp&&!!this.widget.compare(i.widget)&&(!(t>0)||!(o<=0))&&(!(e<this.length)||!(n<=0)))&&(this.length=t+(i?i.length:0)+(this.length-e),!0)}domAtPos(t){return 0==t?E.before(this.dom):E.after(this.dom,t==this.length)}split(t){let e=this.length-t;this.length=t;let i=new tp(this.widget,e,this.deco);return i.breakAfter=this.breakAfter,i}get children(){return R}sync(t){this.dom&&this.widget.updateDOM(this.dom,t)||(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):l.xv.empty}domBoundsAround(){return null}become(t){return t instanceof tp&&t.widget.constructor==this.widget.constructor&&(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0)}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,e){return this.widget.coordsAt(this.dom,t,e)||(this.widget instanceof tg?null:b(this.dom.getBoundingClientRect(),this.length?0==t:e<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:e,endSide:i}=this.deco;return e!=i&&(t<0?e<0:i>0)}}class tg extends tn{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class tm{constructor(t,e,i,s){this.doc=t,this.pos=e,this.end=i,this.disallowBlockEffectsFor=s,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=e}posCovered(){if(0==this.content.length)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof tp&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new tf),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(tv(new Z(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,this.posCovered()||t&&this.content.length&&this.content[this.content.length-1]instanceof tp||this.getLine()}buildText(t,e,i){for(;t>0;){if(this.textOff==this.text.length){let{value:e,lineBreak:i,done:s}=this.cursor.next(this.skip);if(this.skip=0,s)throw Error("Ran out of text content when drawing inline views");if(i){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}this.text=e,this.textOff=0}let s=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(e.slice(e.length-i)),this.getLine().append(tv(new _(this.text.slice(this.textOff,this.textOff+s)),e),i),this.atCursorPos=!0,this.textOff+=s,t-=s,i=0}}span(t,e,i,s){this.buildText(e-t,i,s),this.pos=e,this.openStart<0&&(this.openStart=s)}point(t,e,i,s,o,n){if(this.disallowBlockEffectsFor[n]&&i instanceof tc){if(i.block)throw RangeError("Block decorations may not be specified via plugins");if(e>this.doc.lineAt(this.pos).to)throw RangeError("Decorations that replace line breaks may not be specified via plugins")}let r=e-t;if(i instanceof tc){if(i.block)i.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new tp(i.widget||tw.block,r,i));else{let n=Q.create(i.widget||tw.inline,r,r?0:i.startSide),l=this.atCursorPos&&!n.isEditable&&o<=s.length&&(t<e||i.startSide>0),h=!n.isEditable&&(t<e||o>s.length||i.startSide<=0),a=this.getLine();2!=this.pendingBuffer||l||n.isEditable||(this.pendingBuffer=0),this.flushBuffer(s),l&&(a.append(tv(new Z(1),s),o),o=s.length+Math.max(0,o-s.length)),a.append(tv(n,s),o),this.atCursorPos=h,this.pendingBuffer=h?t<e||o>s.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=s.slice())}}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(i);r&&(this.textOff+r<=this.text.length?this.textOff+=r:(this.skip+=r-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=e),this.openStart<0&&(this.openStart=o)}static build(t,e,i,s,o){let n=new tm(t,e,i,o);return n.openEnd=l.Xs.spans(s,e,i,n),n.openStart<0&&(n.openStart=n.openEnd),n.finish(n.openEnd),n}}function tv(t,e){for(let i of e)t=new U(i,[t],t.length);return t}class tw extends tn{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}tw.inline=new tw("span"),tw.block=new tw("div");var tb=((n=tb||(tb={}))[n.LTR=0]="LTR",n[n.RTL=1]="RTL",n);let ty=tb.LTR,tx=tb.RTL;function tS(t){let e=[];for(let i=0;i<t.length;i++)e.push(1<<+t[i]);return e}let tM=tS("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),tC=tS("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),tk=Object.create(null),tA=[];for(let t of["()","[]","{}"]){let e=t.charCodeAt(0),i=t.charCodeAt(1);tk[e]=i,tk[i]=-e}function tD(t){return t<=247?tM[t]:1424<=t&&t<=1524?2:1536<=t&&t<=1785?tC[t-1536]:1774<=t&&t<=2220?4:8192<=t&&t<=8204?256:64336<=t&&t<=65023?4:1}let tT=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class tO{get dir(){return this.level%2?tx:ty}constructor(t,e,i){this.from=t,this.to=e,this.level=i}side(t,e){return this.dir==e==t?this.to:this.from}forward(t,e){return t==(this.dir==e)}static find(t,e,i,s){let o=-1;for(let n=0;n<t.length;n++){let r=t[n];if(r.from<=e&&r.to>=e){if(r.level==i)return n;(o<0||(0!=s?s<0?r.from<e:r.to>e:t[o].level>r.level))&&(o=n)}}if(o<0)throw RangeError("Index out of range");return o}}let tE=[];function tR(t){return[new tO(0,t,0)]}let tB="",tL=l.r$.define(),tP=l.r$.define(),tH=l.r$.define(),tV=l.r$.define(),tN=l.r$.define(),tW=l.r$.define(),tF=l.r$.define(),tz=l.r$.define(),tI=l.r$.define(),tq=l.r$.define({combine:t=>t.some(t=>t)}),tK=l.r$.define({combine:t=>t.some(t=>t)}),tj=l.r$.define();class t${constructor(t,e="nearest",i="nearest",s=5,o=5,n=!1){this.range=t,this.y=e,this.x=i,this.yMargin=s,this.xMargin=o,this.isSnapshot=n}map(t){return t.empty?this:new t$(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new t$(l.jT.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}let tX=l.Py.define({map:(t,e)=>t.map(e)}),tY=l.Py.define();function tG(t,e,i){let s=t.facet(tV);s.length?s[0](e):window.onerror&&window.onerror(String(e),i,void 0,void 0,e)||(i?console.error(i+":",e):console.error(e))}let t_=l.r$.define({combine:t=>!t.length||t[0]}),tU=0,tQ=l.r$.define({combine:t=>t.filter((e,i)=>{for(let s=0;s<i;s++)if(t[s].plugin==e.plugin)return!1;return!0})});class tZ{constructor(t,e,i,s,o){this.id=t,this.create=e,this.domEventHandlers=i,this.domEventObservers=s,this.baseExtensions=o(this),this.extension=this.baseExtensions.concat(tQ.of({plugin:this,arg:void 0}))}of(t){return this.baseExtensions.concat(tQ.of({plugin:this,arg:t}))}static define(t,e){let{eventHandlers:i,eventObservers:s,provide:o,decorations:n}=e||{};return new tZ(tU++,t,i,s,t=>{let e=[];return n&&e.push(t2.of(e=>{let i=e.plugin(t);return i?n(i):tl.none})),o&&e.push(o(t)),e})}static fromClass(t,e){return tZ.define((e,i)=>new t(e,i),e)}}class tJ{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}get plugin(){return this.spec&&this.spec.plugin}update(t){if(this.value){if(this.mustUpdate){let t=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(t)}catch(e){if(tG(t.state,e,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch(t){}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.plugin.create(t,this.spec.arg)}catch(e){tG(t.state,e,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var e;if(null===(e=this.value)||void 0===e?void 0:e.destroy)try{this.value.destroy()}catch(e){tG(t.state,e,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}let t0=l.r$.define(),t1=l.r$.define(),t2=l.r$.define(),t8=l.r$.define(),t3=l.r$.define(),t9=l.r$.define();function t5(t,e){let i=t.state.facet(t9);if(!i.length)return i;let s=i.map(e=>e instanceof Function?e(t):e),o=[];return l.Xs.spans(s,e.from,e.to,{point(){},span(t,i,s,n){let r=t-e.from,l=i-e.from,h=o;for(let t=s.length-1;t>=0;t--,n--){let i=s[t].spec.bidiIsolate,o;if(null==i&&(i=function(t,e,i){for(let s=e;s<i;s++){let e=tD(t.charCodeAt(s));if(1==e)break;if(2==e||4==e)return tx}return ty}(e.text,r,l)),n>0&&h.length&&(o=h[h.length-1]).to==r&&o.direction==i)o.to=l,h=o.inner;else{let t={from:r,to:l,direction:i,inner:[]};h.push(t),h=t.inner}}}}),o}let t4=l.r$.define();function t6(t){let e=0,i=0,s=0,o=0;for(let n of t.state.facet(t4)){let r=n(t);r&&(null!=r.left&&(e=Math.max(e,r.left)),null!=r.right&&(i=Math.max(i,r.right)),null!=r.top&&(s=Math.max(s,r.top)),null!=r.bottom&&(o=Math.max(o,r.bottom)))}return{left:e,right:i,top:s,bottom:o}}let t7=l.r$.define();class et{constructor(t,e,i,s){this.fromA=t,this.toA=e,this.fromB=i,this.toB=s}join(t){return new et(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let e=t.length,i=this;for(;e>0;e--){let s=t[e-1];if(!(s.fromA>i.toA)){if(s.toA<i.fromA)break;i=i.join(s),t.splice(e-1,1)}}return t.splice(e,0,i),t}static extendWithRanges(t,e){if(0==e.length)return t;let i=[];for(let s=0,o=0,n=0,r=0;;s++){let l=s==t.length?null:t[s],h=n-r,a=l?l.fromB:1e9;for(;o<e.length&&e[o]<a;){let t=e[o],s=e[o+1],n=Math.max(r,t),l=Math.min(a,s);if(n<=l&&new et(n+h,l+h,n,l).addToSet(i),s>a)break;o+=2}if(!l)return i;new et(l.fromA,l.toA,l.fromB,l.toB).addToSet(i),n=l.toA,r=l.toB}}}class ee{constructor(t,e,i){for(let s of(this.view=t,this.state=e,this.transactions=i,this.flags=0,this.startState=t.state,this.changes=l.as.empty(this.startState.doc.length),i))this.changes=this.changes.compose(s.changes);let s=[];this.changes.iterChangedRanges((t,e,i,o)=>s.push(new et(t,e,i,o))),this.changedRanges=s}static create(t,e,i){return new ee(t,e,i)}get viewportChanged(){return(4&this.flags)>0}get viewportMoved(){return(8&this.flags)>0}get heightChanged(){return(2&this.flags)>0}get geometryChanged(){return this.docChanged||(18&this.flags)>0}get focusChanged(){return(1&this.flags)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return 0==this.flags&&0==this.transactions.length}}class ei extends B{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=tl.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new tf],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new et(0,0,0,t.state.doc.length)],0,null)}update(t){var e,i,s,o,n,r;let h,a;let c=t.changedRanges;this.minWidth>0&&c.length&&(c.every(({fromA:t,toA:e})=>e<this.minWidthFrom||t>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let d=-1;!(this.view.inputState.composing>=0)||this.view.observer.editContext||((null===(e=this.domChanged)||void 0===e?void 0:e.newSel)?d=this.domChanged.newSel.head:(i=t.changes,s=this.hasComposition,h=!1,s&&i.iterChangedRanges((t,e)=>{t<s.to&&e>s.from&&(h=!0)}),h||t.selectionSet||(d=t.state.selection.main.head)));let u=d>-1?function(t,e,i){let s=es(t,i);if(!s)return null;let{node:o,from:n,to:r}=s,l=o.nodeValue;if(/[\n\r]/.test(l)||t.state.doc.sliceString(s.from,s.to)!=l)return null;let h=e.invertedDesc,a=new et(h.mapPos(n),h.mapPos(r),n,r),c=[];for(let e=o.parentNode;;e=e.parentNode){let i=B.get(e);if(i instanceof U)c.push({node:e,deco:i.mark});else{if(i instanceof tf||"DIV"==e.nodeName&&e.parentNode==t.contentDOM)return{range:a,text:o,marks:c,line:e};if(e==t.contentDOM)return null;c.push({node:e,deco:new th({inclusive:!0,attributes:function(t){let e=Object.create(null);for(let i=0;i<t.attributes.length;i++){let s=t.attributes[i];e[s.name]=s.value}return e}(e),tagName:e.tagName.toLowerCase()})})}}}(this.view,t.changes,d):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:e,to:i}=this.hasComposition;c=new et(e,i,t.changes.mapPos(e,-1),t.changes.mapPos(i,1)).addToSet(c.slice())}this.hasComposition=u?{from:u.range.fromB,to:u.range.toB}:null,(G.ie||G.chrome)&&!u&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let f=(o=this.decorations,n=this.updateDeco(),r=t.changes,a=new eo,l.Xs.compare(o,n,r,a),a.changes);return c=et.extendWithRanges(c,f),(!!(7&this.flags)||0!=c.length)&&(this.updateInner(c,t.startState.doc.length,u),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,e,i){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,e,i);let{observer:s}=this.view;s.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let t=G.chrome||G.ios?{node:s.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,t),this.flags&=-8,t&&(t.written||s.selectionRange.focusNode!=t.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(t=>t.flags&=-9);let o=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let t of this.children)t instanceof tp&&t.widget instanceof tg&&o.push(t.dom);s.updateGaps(o)}updateChildren(t,e,i){let s=i?i.range.addToSet(t.slice()):t,o=this.childCursor(e);for(let t=s.length-1;;t--){let e=t>=0?s[t]:null;if(!e)break;let{fromA:n,toA:r,fromB:l,toB:h}=e,a,c,d,u;if(i&&i.range.fromB<h&&i.range.toB>l){let t=tm.build(this.view.state.doc,l,i.range.fromB,this.decorations,this.dynamicDecorationMap),e=tm.build(this.view.state.doc,i.range.toB,h,this.decorations,this.dynamicDecorationMap);c=t.breakAtStart,d=t.openStart,u=e.openEnd;let s=this.compositionView(i);e.breakAtStart?s.breakAfter=1:e.content.length&&s.merge(s.length,s.length,e.content[0],!1,e.openStart,0)&&(s.breakAfter=e.content[0].breakAfter,e.content.shift()),t.content.length&&s.merge(0,0,t.content[t.content.length-1],!0,0,t.openEnd)&&t.content.pop(),a=t.content.concat(s).concat(e.content)}else({content:a,breakAtStart:c,openStart:d,openEnd:u}=tm.build(this.view.state.doc,l,h,this.decorations,this.dynamicDecorationMap));let{i:f,off:p}=o.findPos(r,1),{i:g,off:m}=o.findPos(n,-1);H(this,g,m,f,p,a,c,d,u)}i&&this.fixCompositionDOM(i)}updateEditContextFormatting(t){for(let e of(this.editContextFormatting=this.editContextFormatting.map(t.changes),t.transactions))for(let t of e.effects)t.is(tY)&&(this.editContextFormatting=t.value)}compositionView(t){let e=new _(t.text.nodeValue);for(let{deco:i}of(e.flags|=8,t.marks))e=new U(i,[e],e.length);let i=new tf;return i.append(e,0),i}fixCompositionDOM(t){let e=(t,e)=>{e.flags|=8|(e.children.some(t=>7&t.flags)?1:0),this.markedForComposition.add(e);let i=B.get(t);i&&i!=e&&(i.dom=null),e.setDOM(t)},i=this.childPos(t.range.fromB,1),s=this.children[i.i];e(t.line,s);for(let o=t.marks.length-1;o>=-1;o--)i=s.childPos(i.off,1),s=s.children[i.i],e(o>=0?t.marks[o].node:t.text,s)}updateSelection(t=!1,e=!1){var i;(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let s=this.view.root.activeElement,o=s==this.dom,n=!o&&!(this.view.state.facet(t_)||this.dom.tabIndex>-1)&&u(this.dom,this.view.observer.selectionRange)&&!(s&&this.dom.contains(s));if(!(o||e||n))return;let r=this.forceSelection;this.forceSelection=!1;let l=this.view.state.selection.main,h=this.moveToLine(this.domAtPos(l.anchor)),a=l.empty?h:this.moveToLine(this.domAtPos(l.head));if(G.gecko&&l.empty&&!this.hasComposition&&1==(i=h).node.nodeType&&i.node.firstChild&&(0==i.offset||"false"==i.node.childNodes[i.offset-1].contentEditable)&&(i.offset==i.node.childNodes.length||"false"==i.node.childNodes[i.offset].contentEditable)){let t=document.createTextNode("");this.view.observer.ignore(()=>h.node.insertBefore(t,h.node.childNodes[h.offset]||null)),h=a=new E(t,0),r=!0}let d=this.view.observer.selectionRange;!r&&d.focusNode&&(p(h.node,h.offset,d.anchorNode,d.anchorOffset)&&p(a.node,a.offset,d.focusNode,d.focusOffset)||this.suppressWidgetCursorChange(d,l))||(this.view.observer.ignore(()=>{G.android&&G.chrome&&this.dom.contains(d.focusNode)&&function(t,e){for(let i=t;i&&i!=e;i=i.assignedSlot||i.parentNode)if(1==i.nodeType&&"false"==i.contentEditable)return!0;return!1}(d.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let t=c(this.view.root);if(t){if(l.empty){if(G.gecko){var e,i;let t=(e=h.node,i=h.offset,1!=e.nodeType?0:(i&&"false"==e.childNodes[i-1].contentEditable?1:0)|(i<e.childNodes.length&&"false"==e.childNodes[i].contentEditable?2:0));if(t&&3!=t){let e=(1==t?T:O)(h.node,h.offset);e&&(h=new E(e.node,e.offset))}}t.collapse(h.node,h.offset),null!=l.bidiLevel&&void 0!==t.caretBidiLevel&&(t.caretBidiLevel=l.bidiLevel)}else if(t.extend){t.collapse(h.node,h.offset);try{t.extend(a.node,a.offset)}catch(t){}}else{let e=document.createRange();l.anchor>l.head&&([h,a]=[a,h]),e.setEnd(a.node,a.offset),e.setStart(h.node,h.offset),t.removeAllRanges(),t.addRange(e)}}n&&this.view.root.activeElement==this.dom&&(this.dom.blur(),s&&s.focus())}),this.view.observer.setSelectionRange(h,a)),this.impreciseAnchor=h.precise?null:new E(d.anchorNode,d.anchorOffset),this.impreciseHead=a.precise?null:new E(d.focusNode,d.focusOffset)}suppressWidgetCursorChange(t,e){return this.hasComposition&&e.empty&&p(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==e.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,e=t.state.selection.main,i=c(t.root),{anchorNode:s,anchorOffset:o}=t.observer.selectionRange;if(!i||!e.empty||!e.assoc||!i.modify)return;let n=tf.find(this,e.head);if(!n)return;let r=n.posAtStart;if(e.head==r||e.head==r+n.length)return;let l=this.coordsAt(e.head,-1),h=this.coordsAt(e.head,1);if(!l||!h||l.bottom>h.top)return;let a=this.domAtPos(e.head+e.assoc);i.collapse(a.node,a.offset),i.modify("move",e.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let d=t.observer.selectionRange;t.docView.posFromDOM(d.anchorNode,d.anchorOffset)!=e.from&&i.collapse(s,o)}moveToLine(t){let e=this.dom,i;if(t.node!=e)return t;for(let s=t.offset;!i&&s<e.childNodes.length;s++){let t=B.get(e.childNodes[s]);t instanceof tf&&(i=t.domAtPos(0))}for(let s=t.offset-1;!i&&s>=0;s--){let t=B.get(e.childNodes[s]);t instanceof tf&&(i=t.domAtPos(t.length))}return i?new E(i.node,i.offset,!0):t}nearest(t){for(let e=t;e;){let t=B.get(e);if(t&&t.rootView==this)return t;e=e.parentNode}return null}posFromDOM(t,e){let i=this.nearest(t);if(!i)throw RangeError("Trying to find position for a DOM position outside of the document");return i.localPosFromDOM(t,e)+i.posAtStart}domAtPos(t){let{i:e,off:i}=this.childCursor().findPos(t,-1);for(;e<this.children.length-1;){let t=this.children[e];if(i<t.length||t instanceof tf)break;e++,i=0}return this.children[e].domAtPos(i)}coordsAt(t,e){let i=null,s=0;for(let o=this.length,n=this.children.length-1;n>=0;n--){let r=this.children[n],l=o-r.breakAfter,h=l-r.length;if(l<t)break;if(h<=t&&(h<t||r.covers(-1))&&(l>t||r.covers(1))&&(!i||r instanceof tf&&!(i instanceof tf&&e>=0)))i=r,s=h;else if(i&&h==t&&l==t&&r instanceof tp&&2>Math.abs(e)){if(r.deco.startSide<0)break;n&&(i=null)}o=h}return i?i.coordsAt(t-s,e):null}coordsForChar(t){let{i:e,off:i}=this.childPos(t,1),s=this.children[e];if(!(s instanceof tf))return null;for(;s.children.length;){let{i:t,off:e}=s.childPos(i,1);for(;;t++){if(t==s.children.length)return null;if((s=s.children[t]).length)break}i=e}if(!(s instanceof _))return null;let o=(0,l.cp)(s.text,i);if(o==i)return null;let n=C(s.dom,i,o).getClientRects();for(let t=0;t<n.length;t++){let e=n[t];if(t==n.length-1||e.top<e.bottom&&e.left<e.right)return e}return null}measureVisibleLineHeights(t){let e=[],{from:i,to:s}=t,o=this.view.contentDOM.clientWidth,n=o>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,r=-1,l=this.view.textDirection==tb.LTR;for(let t=0,h=0;h<this.children.length;h++){let a=this.children[h],c=t+a.length;if(c>s)break;if(t>=i){let i=a.dom.getBoundingClientRect();if(e.push(i.height),n){let e=a.dom.lastChild,s=e?f(e):[];if(s.length){let e=s[s.length-1],n=l?e.right-i.left:i.right-e.left;n>r&&(r=n,this.minWidth=o,this.minWidthFrom=t,this.minWidthTo=c)}}}t=c+a.breakAfter}return e}textDirectionAt(t){let{i:e}=this.childPos(t,1);return"rtl"==getComputedStyle(this.children[e].dom).direction?tb.RTL:tb.LTR}measureTextSize(){for(let t of this.children)if(t instanceof tf){let e=t.measureTextSize();if(e)return e}let t=document.createElement("div"),e,i,s;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let o=f(t.firstChild)[0];e=t.getBoundingClientRect().height,i=o?o.width/27:7,s=o?o.height:e,t.remove()}),{lineHeight:e,charWidth:i,textHeight:s}}childCursor(t=this.length){let e=this.children.length;return e&&(t-=this.children[--e].length),new P(this.children,t,e)}computeBlockGapDeco(){let t=[],e=this.view.viewState;for(let i=0,s=0;;s++){let o=s==e.viewports.length?null:e.viewports[s],n=o?o.from-1:this.length;if(n>i){let s=(e.lineBlockAt(n).bottom-e.lineBlockAt(i).top)/this.view.scaleY;t.push(tl.replace({widget:new tg(s),block:!0,inclusive:!0,isBlockGap:!0}).range(i,n))}if(!o)break;i=o.to+1}return tl.set(t)}updateDeco(){let t=1,e=this.view.state.facet(t2).map(e=>(this.dynamicDecorationMap[t++]="function"==typeof e)?e(this.view):e),i=!1,s=this.view.state.facet(t8).map((t,e)=>{let s="function"==typeof t;return s&&(i=!0),s?t(this.view):t});for(s.length&&(this.dynamicDecorationMap[t++]=i,e.push(l.Xs.join(s))),this.decorations=[this.editContextFormatting,...e,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let e=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=e.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let e of this.view.state.facet(tj))try{if(e(this.view,t.range,t))return!0}catch(t){tG(this.view.state,t,"scroll handler")}let{range:e}=t,i=this.coordsAt(e.head,e.empty?e.assoc:e.head>e.anchor?-1:1),s;if(!i)return;!e.empty&&(s=this.coordsAt(e.anchor,e.anchor>e.head?-1:1))&&(i={left:Math.min(i.left,s.left),top:Math.min(i.top,s.top),right:Math.max(i.right,s.right),bottom:Math.max(i.bottom,s.bottom)});let o=t6(this.view),n={left:i.left-o.left,top:i.top-o.top,right:i.right+o.right,bottom:i.bottom+o.bottom},{offsetWidth:r,offsetHeight:l}=this.view.scrollDOM;!function(t,e,i,s,o,n,r,l){let h=t.ownerDocument,a=h.defaultView||window;for(let c=t,d=!1;c&&!d;)if(1==c.nodeType){let t,u=c==h.body,f=1,p=1;if(u)t=function(t){let e=t.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.innerWidth,top:0,bottom:t.innerHeight}}(a);else{if(/^(fixed|sticky)$/.test(getComputedStyle(c).position)&&(d=!0),c.scrollHeight<=c.clientHeight&&c.scrollWidth<=c.clientWidth){c=c.assignedSlot||c.parentNode;continue}let e=c.getBoundingClientRect();({scaleX:f,scaleY:p}=y(c,e)),t={left:e.left,right:e.left+c.clientWidth*f,top:e.top,bottom:e.top+c.clientHeight*p}}let g=0,m=0;if("nearest"==o)e.top<t.top?(m=e.top-(t.top+r),i>0&&e.bottom>t.bottom+m&&(m=e.bottom-t.bottom+r)):e.bottom>t.bottom&&(m=e.bottom-t.bottom+r,i<0&&e.top-m<t.top&&(m=e.top-(t.top+r)));else{let s=e.bottom-e.top,n=t.bottom-t.top;m=("center"==o&&s<=n?e.top+s/2-n/2:"start"==o||"center"==o&&i<0?e.top-r:e.bottom-n+r)-t.top}if("nearest"==s?e.left<t.left?(g=e.left-(t.left+n),i>0&&e.right>t.right+g&&(g=e.right-t.right+n)):e.right>t.right&&(g=e.right-t.right+n,i<0&&e.left<t.left+g&&(g=e.left-(t.left+n))):g=("center"==s?e.left+(e.right-e.left)/2-(t.right-t.left)/2:"start"==s==l?e.left-n:e.right-(t.right-t.left)+n)-t.left,g||m){if(u)a.scrollBy(g,m);else{let t=0,i=0;if(m){let t=c.scrollTop;c.scrollTop+=m/p,i=(c.scrollTop-t)*p}if(g){let e=c.scrollLeft;c.scrollLeft+=g/f,t=(c.scrollLeft-e)*f}e={left:e.left-t,top:e.top-i,right:e.right-t,bottom:e.bottom-i},t&&1>Math.abs(t-g)&&(s="nearest"),i&&1>Math.abs(i-m)&&(o="nearest")}}if(u)break;(e.top<t.top||e.bottom>t.bottom||e.left<t.left||e.right>t.right)&&(e={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)}),c=c.assignedSlot||c.parentNode}else if(11==c.nodeType)c=c.host;else break}(this.view.scrollDOM,n,e.head<e.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,r),-r),Math.max(Math.min(t.yMargin,l),-l),this.view.textDirection==tb.LTR)}}function es(t,e){let i=t.observer.selectionRange;if(!i.focusNode)return null;let s=T(i.focusNode,i.focusOffset),o=O(i.focusNode,i.focusOffset),n=s||o;if(o&&s&&o.node!=s.node){let e=B.get(o.node);if(!e||e instanceof _&&e.text!=o.node.nodeValue)n=o;else if(t.docView.lastCompositionAfterCursor){let t=B.get(s.node);!t||t instanceof _&&t.text!=s.node.nodeValue||(n=o)}}if(t.docView.lastCompositionAfterCursor=n!=s,!n)return null;let r=e-n.offset;return{from:r,to:r+n.node.nodeValue.length,node:n.node}}let eo=class{constructor(){this.changes=[]}compareRange(t,e){tu(t,e,this.changes)}comparePoint(t,e){tu(t,e,this.changes)}boundChange(t){tu(t,t,this.changes)}};function en(t,e){return t.top<e.bottom-1&&t.bottom>e.top+1}function er(t,e){return e<t.top?{top:e,left:t.left,right:t.right,bottom:t.bottom}:t}function el(t,e){return e>t.bottom?{top:t.top,left:t.left,right:t.right,bottom:e}:t}function eh(t,e,i){let s=t.nodeValue.length,o=-1,n=1e9,r=0;for(let l=0;l<s;l++){let s=C(t,l,l+1).getClientRects();for(let h=0;h<s.length;h++){let a=s[h];if(a.top==a.bottom)continue;r||(r=e-a.left);let c=(a.top>i?a.top-i:i-a.bottom)-1;if(a.left-1<=e&&a.right+1>=e&&c<n){let i=e>=(a.left+a.right)/2,s=i;if((G.chrome||G.gecko)&&C(t,l).getBoundingClientRect().left==a.right&&(s=!i),c<=0)return{node:t,offset:l+(s?1:0)};o=l+(s?1:0),n=c}}}return{node:t,offset:o>-1?o:r>0?t.nodeValue.length:0}}function ea(t,e,i,s=-1){var o,n;let r=t.contentDOM.getBoundingClientRect(),l=r.top+t.viewState.paddingTop,h,{docHeight:a}=t.viewState,{x:c,y:d}=e,u=d-l;if(u<0)return 0;if(u>a)return t.state.doc.length;for(let e=t.viewState.heightOracle.textHeight/2,o=!1;(h=t.elementAtHeight(u)).type!=tr.Text;)for(;!((u=s>0?h.bottom+e:h.top-e)>=0)||!(u<=a);){if(o)return i?null:0;o=!0,s=-s}d=l+u;let p=h.from;if(p<t.viewport.from)return 0==t.viewport.from?0:i?null:ec(t,r,h,c,d);if(p>t.viewport.to)return t.viewport.to==t.state.doc.length?t.state.doc.length:i?null:ec(t,r,h,c,d);let g=t.dom.ownerDocument,m=t.root.elementFromPoint?t.root:g,v=m.elementFromPoint(c,d);v&&!t.contentDOM.contains(v)&&(v=null),!v&&(c=Math.max(r.left+1,Math.min(r.right-1,c)),(v=m.elementFromPoint(c,d))&&!t.contentDOM.contains(v)&&(v=null));let b,y=-1;if(v&&(null===(o=t.docView.nearest(v))||void 0===o?void 0:o.isEditable)!=!1){if(g.caretPositionFromPoint){let t=g.caretPositionFromPoint(c,d);t&&({offsetNode:b,offset:y}=t)}else if(g.caretRangeFromPoint){let e=g.caretRangeFromPoint(c,d);e&&({startContainer:b,startOffset:y}=e,(!t.contentDOM.contains(b)||G.safari&&function(t,e,i){let s,o=t;if(3!=t.nodeType||e!=(s=t.nodeValue.length))return!1;for(;;){let t=o.nextSibling;if(t){if("BR"==t.nodeName)break;return!1}{let t=o.parentNode;if(!t||"DIV"==t.nodeName)break;o=t}}return C(t,s-1,s).getBoundingClientRect().right>i}(b,y,c)||G.chrome&&function(t,e,i){if(0!=e)return!1;for(let e=t;;){let t=e.parentNode;if(!t||1!=t.nodeType||t.firstChild!=e)return!1;if(t.classList.contains("cm-line"))break;e=t}return i-(1==t.nodeType?t.getBoundingClientRect():C(t,0,Math.max(t.nodeValue.length,1)).getBoundingClientRect()).left>5}(b,y,c))&&(b=void 0))}b&&(y=Math.min(w(b),y))}if(!b||!t.docView.dom.contains(b)){let e=tf.find(t.docView,p);if(!e)return u>h.top+h.height/2?h.to:h.from;({node:b,offset:y}=function t(e,i,s){let o,n,r,l,h,a,c,d,u=!1;for(let m=e.firstChild;m;m=m.nextSibling){let e=f(m);for(let f=0;f<e.length;f++){var p,g;let v=e[f];a&&en(a,v)&&(v=er(el(v,a.bottom),a.top));let w=(p=v).left>i?p.left-i:Math.max(0,i-p.right),b=(g=v).top>s?g.top-s:Math.max(0,s-g.bottom);if(0==w&&0==b)return 3==m.nodeType?eh(m,i,s):t(m,i,s);(!h||d>b||d==b&&c>w)&&(h=m,a=v,c=w,d=b,u=!w||(i<v.left?f>0:f<e.length-1)),0==w?s>v.bottom&&(!r||r.bottom<v.bottom)?(o=m,r=v):s<v.top&&(!l||l.top>v.top)&&(n=m,l=v):r&&en(r,v)?r=el(r,v.bottom):l&&en(l,v)&&(l=er(l,v.top))}}if(r&&r.bottom>=s?(h=o,a=r):l&&l.top<=s&&(h=n,a=l),!h)return{node:e,offset:0};let m=Math.max(a.left,Math.min(a.right,i));if(3==h.nodeType)return eh(h,m,s);if(u&&"false"!=h.contentEditable)return t(h,m,s);let v=Array.prototype.indexOf.call(e.childNodes,h)+(i>=(a.left+a.right)/2?1:0);return{node:e,offset:v}}(e.dom,c,d))}let x=t.docView.nearest(b);if(!x)return null;if(!x.isWidget||(null===(n=x.dom)||void 0===n?void 0:n.nodeType)!=1)return x.localPosFromDOM(b,y)+x.posAtStart;{let t=x.dom.getBoundingClientRect();return e.y<t.top||e.y<=t.bottom&&e.x<=(t.left+t.right)/2?x.posAtStart:x.posAtEnd}}function ec(t,e,i,s,o){let n=Math.round((s-e.left)*t.defaultCharacterWidth);if(t.lineWrapping&&i.height>1.5*t.defaultLineHeight){let e=t.viewState.heightOracle.textHeight;n+=Math.floor((o-i.top-(t.defaultLineHeight-e)*.5)/e)*t.viewState.heightOracle.lineLength}let r=t.state.sliceDoc(i.from,i.to);return i.from+(0,l.Gz)(r,n,t.state.tabSize)}function ed(t,e,i){let s=t.lineBlockAt(e);if(Array.isArray(s.type)){let t;for(let o of s.type){if(o.from>e)break;if(!(o.to<e)){if(o.from<e&&o.to>e)return o;(!t||o.type==tr.Text&&(t.type!=o.type||(i<0?o.from<e:o.to>e)))&&(t=o)}}return t||s}return s}function eu(t,e,i,s){let o=t.state.doc.lineAt(e.head),n=t.bidiSpans(o),r=t.textDirectionAt(o.from);for(let h=e,a=null;;){let e=function(t,e,i,s,o){var n;let r=s.head-t.from,h=tO.find(e,r,null!==(n=s.bidiLevel)&&void 0!==n?n:-1,s.assoc),a=e[h],c=a.side(o,i);if(r==c){let t=h+=o?1:-1;if(t<0||t>=e.length)return null;r=(a=e[h=t]).side(!o,i),c=a.side(o,i)}let d=(0,l.cp)(t.text,r,a.forward(o,i));(d<a.from||d>a.to)&&(d=c),tB=t.text.slice(Math.min(r,d),Math.max(r,d));let u=h==(o?e.length-1:0)?null:e[h+(o?1:-1)];return u&&d==c&&u.level+(o?0:1)<a.level?l.jT.cursor(u.side(!o,i)+t.from,u.forward(o,i)?1:-1,u.level):l.jT.cursor(d+t.from,a.forward(o,i)?-1:1,a.level)}(o,n,r,h,i),c=tB;if(!e){if(o.number==(i?t.state.doc.lines:1))return h;c="\n",o=t.state.doc.line(o.number+(i?1:-1)),n=t.bidiSpans(o),e=t.visualLineSide(o,!i)}if(a){if(!a(c))return h}else{if(!s)return e;a=s(c)}h=e}}function ef(t,e,i){for(;;){let s=0;for(let o of t)o.between(e-1,e+1,(t,o,n)=>{if(e>t&&e<o){let n=s||i||(e-t<o-e?-1:1);e=n<0?t:o,s=n}});if(!s)return e}}function ep(t,e,i){let s=ef(t.state.facet(t3).map(e=>e(t)),i.from,e.head>i.from?-1:1);return s==i.from?i:l.jT.cursor(s,s<i.from?1:-1)}class eg{constructor(t,e){this.points=t,this.text="",this.lineSeparator=e.facet(l.yy.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+="￿"}readRange(t,e){if(!t)return this;let i=t.parentNode;for(let s=t;;){this.findPointBefore(i,s);let t=this.text.length;this.readNode(s);let o=s.nextSibling;if(o==e)break;let n=B.get(s),r=B.get(o);(n&&r?n.breakAfter:(n?n.breakAfter:m(s))||m(o)&&("BR"!=s.nodeName||s.cmIgnore)&&this.text.length>t)&&this.lineBreak(),s=o}return this.findPointBefore(i,e),this}readTextNode(t){let e=t.nodeValue;for(let i of this.points)i.node==t&&(i.pos=this.text.length+Math.min(i.offset,e.length));for(let i=0,s=this.lineSeparator?null:/\r\n?|\n/g;;){let o=-1,n=1,r;if(this.lineSeparator?(o=e.indexOf(this.lineSeparator,i),n=this.lineSeparator.length):(r=s.exec(e))&&(o=r.index,n=r[0].length),this.append(e.slice(i,o<0?e.length:o)),o<0)break;if(this.lineBreak(),n>1)for(let e of this.points)e.node==t&&e.pos>this.text.length&&(e.pos-=n-1);i=o+n}}readNode(t){if(t.cmIgnore)return;let e=B.get(t),i=e&&e.overrideDOMText;if(null!=i){this.findPointInside(t,i.length);for(let t=i.iter();!t.next().done;)t.lineBreak?this.lineBreak():this.append(t.value)}else 3==t.nodeType?this.readTextNode(t):"BR"==t.nodeName?t.nextSibling&&this.lineBreak():1==t.nodeType&&this.readRange(t.firstChild,null)}findPointBefore(t,e){for(let i of this.points)i.node==t&&t.childNodes[i.offset]==e&&(i.pos=this.text.length)}findPointInside(t,e){for(let i of this.points)(3==t.nodeType?i.node==t:t.contains(i.node))&&(i.pos=this.text.length+(!function(t,e,i){for(;;){if(!e||i<w(e))return!1;if(e==t)return!0;i=g(e)+1,e=e.parentNode}}(t,i.node,i.offset)?0:e))}}class em{constructor(t,e){this.node=t,this.offset=e,this.pos=-1}}class ev{constructor(t,e,i,s){this.typeOver=s,this.bounds=null,this.text="",this.domChanged=e>-1;let{impreciseHead:o,impreciseAnchor:n}=t.docView;if(t.state.readOnly&&e>-1)this.newSel=null;else if(e>-1&&(this.bounds=t.docView.domBoundsAround(e,i,0))){let e=o||n?[]:function(t){let e=[];if(t.root.activeElement!=t.contentDOM)return e;let{anchorNode:i,anchorOffset:s,focusNode:o,focusOffset:n}=t.observer.selectionRange;return i&&(e.push(new em(i,s)),(o!=i||n!=s)&&e.push(new em(o,n))),e}(t),i=new eg(e,t.state);i.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=i.text,this.newSel=function(t,e){if(0==t.length)return null;let i=t[0].pos,s=2==t.length?t[1].pos:i;return i>-1&&s>-1?l.jT.single(i+e,s+e):null}(e,this.bounds.from)}else{let e=t.observer.selectionRange,i=o&&o.node==e.focusNode&&o.offset==e.focusOffset||!d(t.contentDOM,e.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(e.focusNode,e.focusOffset),s=n&&n.node==e.anchorNode&&n.offset==e.anchorOffset||!d(t.contentDOM,e.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(e.anchorNode,e.anchorOffset),r=t.viewport;if((G.ios||G.chrome)&&t.state.selection.main.empty&&i!=s&&(r.from>0||r.to<t.state.doc.length)){let e=Math.min(i,s),o=Math.max(i,s),n=r.from-e,l=r.to-o;(0==n||1==n||0==e)&&(0==l||-1==l||o==t.state.doc.length)&&(i=0,s=t.state.doc.length)}this.newSel=l.jT.single(s,i)}}}function ew(t,e){let i;let{newSel:s}=e,o=t.state.selection.main,n=t.inputState.lastKeyTime>Date.now()-100?t.inputState.lastKeyCode:-1;if(e.bounds){let{from:s,to:r}=e.bounds,h=o.from,a=null;(8===n||G.android&&e.text.length<r-s)&&(h=o.to,a="end");let c=function(t,e,i,s){let o=Math.min(t.length,e.length),n=0;for(;n<o&&t.charCodeAt(n)==e.charCodeAt(n);)n++;if(n==o&&t.length==e.length)return null;let r=t.length,l=e.length;for(;r>0&&l>0&&t.charCodeAt(r-1)==e.charCodeAt(l-1);)r--,l--;if("end"==s){let t=Math.max(0,n-Math.min(r,l));i-=r+t-n}if(r<n&&t.length<e.length){let t=i<=n&&i>=r?n-i:0;n-=t,l=n+(l-r),r=n}else if(l<n){let t=i<=n&&i>=l?n-i:0;n-=t,r=n+(r-l),l=n}return{from:n,toA:r,toB:l}}(t.state.doc.sliceString(s,r,"￿"),e.text,h-s,a);c&&(G.chrome&&13==n&&c.toB==c.from+2&&"￿￿"==e.text.slice(c.from,c.toB)&&c.toB--,i={from:s+c.from,to:s+c.toA,insert:l.xv.of(e.text.slice(c.from,c.toB).split("￿"))})}else s&&(!t.hasFocus&&t.state.facet(t_)||s.main.eq(o))&&(s=null);if(!i&&!s)return!1;if(!i&&e.typeOver&&!o.empty&&s&&s.main.empty?i={from:o.from,to:o.to,insert:t.state.doc.slice(o.from,o.to)}:(G.mac||G.android)&&i&&i.from==i.to&&i.from==o.head-1&&/^\. ?$/.test(i.insert.toString())&&"off"==t.contentDOM.getAttribute("autocorrect")?(s&&2==i.insert.length&&(s=l.jT.single(s.main.anchor-1,s.main.head-1)),i={from:i.from,to:i.to,insert:l.xv.of([i.insert.toString().replace("."," ")])}):i&&i.from>=o.from&&i.to<=o.to&&(i.from!=o.from||i.to!=o.to)&&o.to-o.from-(i.to-i.from)<=4?i={from:o.from,to:o.to,insert:t.state.doc.slice(o.from,i.from).append(i.insert).append(t.state.doc.slice(i.to,o.to))}:G.chrome&&i&&i.from==i.to&&i.from==o.head&&"\n "==i.insert.toString()&&t.lineWrapping&&(s&&(s=l.jT.single(s.main.anchor-1,s.main.head-1)),i={from:o.from,to:o.to,insert:l.xv.of([" "])}),i)return eb(t,i,s,n);if(!s||s.main.eq(o))return!1;{let e=!1,i="select";return t.inputState.lastSelectionTime>Date.now()-50&&("select"==t.inputState.lastSelectionOrigin&&(e=!0),i=t.inputState.lastSelectionOrigin),t.dispatch({selection:s,scrollIntoView:e,userEvent:i}),!0}}function eb(t,e,i,s=-1){let o;if(G.ios&&t.inputState.flushIOSKey(e))return!0;let n=t.state.selection.main;if(G.android&&(e.to==n.to&&(e.from==n.from||e.from==n.from-1&&" "==t.state.sliceDoc(e.from,n.from))&&1==e.insert.length&&2==e.insert.lines&&k(t.contentDOM,"Enter",13)||(e.from==n.from-1&&e.to==n.to&&0==e.insert.length||8==s&&e.insert.length<e.to-e.from&&e.to>n.head)&&k(t.contentDOM,"Backspace",8)||e.from==n.from&&e.to==n.to+1&&0==e.insert.length&&k(t.contentDOM,"Delete",46)))return!0;let r=e.insert.toString();t.inputState.composing>=0&&t.inputState.composing++;let h=()=>o||(o=function(t,e,i){let s,o=t.state,n=o.selection.main;if(e.from>=n.from&&e.to<=n.to&&e.to-e.from>=(n.to-n.from)/3&&(!i||i.main.empty&&i.main.from==e.from+e.insert.length)&&t.inputState.composing<0){let i=n.from<e.from?o.sliceDoc(n.from,e.from):"",r=n.to>e.to?o.sliceDoc(e.to,n.to):"";s=o.replaceSelection(t.state.toText(i+e.insert.sliceString(0,void 0,t.state.lineBreak)+r))}else{let r=o.changes(e),h=i&&i.main.to<=r.newLength?i.main:void 0;if(o.selection.ranges.length>1&&t.inputState.composing>=0&&e.to<=n.to&&e.to>=n.to-10){let a=t.state.sliceDoc(e.from,e.to),c,d=i&&es(t,i.main.head);if(d){let t=e.insert.length-(e.to-e.from);c={from:d.from,to:d.to-t}}else c=t.state.doc.lineAt(n.head);let u=n.to-e.to,f=n.to-n.from;s=o.changeByRange(i=>{if(i.from==n.from&&i.to==n.to)return{changes:r,range:h||i.map(r)};let s=i.to-u,d=s-a.length;if(i.to-i.from!=f||t.state.sliceDoc(d,s)!=a||i.to>=c.from&&i.from<=c.to)return{range:i};let p=o.changes({from:d,to:s,insert:e.insert}),g=i.to-n.to;return{changes:p,range:h?l.jT.range(Math.max(0,h.anchor+g),Math.max(0,h.head+g)):i.map(p)}})}else s={changes:r,selection:h&&o.selection.replaceRange(h)}}let r="input.type";return(t.composing||t.inputState.compositionPendingChange&&t.inputState.compositionEndedAt>Date.now()-50)&&(t.inputState.compositionPendingChange=!1,r+=".compose",t.inputState.compositionFirstChange&&(r+=".start",t.inputState.compositionFirstChange=!1)),o.update(s,{userEvent:r,scrollIntoView:!0})}(t,e,i));return t.state.facet(tW).some(i=>i(t,e.from,e.to,r,h))||t.dispatch(h()),!0}class ey{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){var e;this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,G.safari&&t.contentDOM.addEventListener("input",()=>null),G.gecko&&(e=t.contentDOM.ownerDocument,e$.has(e)||(e$.add(e),e.addEventListener("copy",()=>{}),e.addEventListener("cut",()=>{})))}handleEvent(t){!(!function(t,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let i=e.target,s;i!=t.contentDOM;i=i.parentNode)if(!i||11==i.nodeType||(s=B.get(i))&&s.ignoreEvent(e))return!1;return!0}(this.view,t)||this.ignoreDuringComposition(t))&&("keydown"==t.type&&this.keydown(t)||(0!=this.view.updateState?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t)))}runHandlers(t,e){let i=this.handlers[t];if(i){for(let t of i.observers)t(this.view,e);for(let t of i.handlers){if(e.defaultPrevented)break;if(t(this.view,e)){e.preventDefault();break}}}}ensureHandlers(t){let e=function(t){let e=Object.create(null);function i(t){return e[t]||(e[t]={observers:[],handlers:[]})}for(let e of t){let t=e.spec,s=t&&t.plugin.domEventHandlers,o=t&&t.plugin.domEventObservers;if(s)for(let t in s){let o=s[t];o&&i(t).handlers.push(ex(e.value,o))}if(o)for(let t in o){let s=o[t];s&&i(t).observers.push(ex(e.value,s))}}for(let t in eD)i(t).handlers.push(eD[t]);for(let t in eT)i(t).observers.push(eT[t]);return e}(t),i=this.handlers,s=this.view.contentDOM;for(let t in e)if("scroll"!=t){let o=!e[t].handlers.length,n=i[t];n&&!n.handlers.length!=o&&(s.removeEventListener(t,this.handleEvent),n=null),n||s.addEventListener(t,this.handleEvent,{passive:o})}for(let t in i)"scroll"==t||e[t]||s.removeEventListener(t,this.handleEvent);this.handlers=e}keydown(t){let e;return this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),!!(9==t.keyCode&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))||((this.tabFocusMode>0&&27!=t.keyCode&&0>eC.indexOf(t.keyCode)&&(this.tabFocusMode=-1),G.android&&G.chrome&&!t.synthetic&&(13==t.keyCode||8==t.keyCode))?(this.view.observer.delayAndroidKey(t.key,t.keyCode),!0):G.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((e=eS.find(e=>e.keyCode==t.keyCode))&&!t.ctrlKey||eM.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=e||t,setTimeout(()=>this.flushIOSKey(),250),!0):(229!=t.keyCode&&this.view.observer.forceFlush(),!1))}flushIOSKey(t){let e=this.pendingIOSKey;return!(!e||"Enter"==e.key&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString()))&&(this.pendingIOSKey=void 0,k(this.view.contentDOM,e.key,e.keyCode,e instanceof KeyboardEvent?e:void 0))}ignoreDuringComposition(t){return!!/^key/.test(t.type)&&(this.composing>0||!!(G.safari&&!G.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100)&&(this.compositionPendingKey=!1,!0))}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function ex(t,e){return(i,s)=>{try{return e.call(t,s,i)}catch(t){tG(i.state,t)}}}let eS=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],eM="dthko",eC=[16,17,18,20,91,92,224,225];function ek(t){return .7*Math.max(0,t)+8}class eA{constructor(t,e,i,s){let o;this.view=t,this.startEvent=e,this.style=i,this.mustSelect=s,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=e,this.scrollParents=function(t){let e=t.ownerDocument,i,s;for(let o=t.parentNode;o&&o!=e.body&&(!i||!s);)if(1==o.nodeType)!s&&o.scrollHeight>o.clientHeight&&(s=o),!i&&o.scrollWidth>o.clientWidth&&(i=o),o=o.assignedSlot||o.parentNode;else if(11==o.nodeType)o=o.host;else break;return{x:i,y:s}}(t.contentDOM),this.atoms=t.state.facet(t3).map(e=>e(t));let n=t.contentDOM.ownerDocument;n.addEventListener("mousemove",this.move=this.move.bind(this)),n.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=e.shiftKey,this.multiple=t.state.facet(l.yy.allowMultipleSelections)&&((o=t.state.facet(tL)).length?o[0](e):G.mac?e.metaKey:e.ctrlKey),this.dragging=!!function(t,e){let{main:i}=t.state.selection;if(i.empty)return!1;let s=c(t.root);if(!s||0==s.rangeCount)return!0;let o=s.getRangeAt(0).getClientRects();for(let t=0;t<o.length;t++){let i=o[t];if(i.left<=e.clientX&&i.right>=e.clientX&&i.top<=e.clientY&&i.bottom>=e.clientY)return!0}return!1}(t,e)&&1==eF(e)&&null}start(t){!1===this.dragging&&this.select(t)}move(t){var e;if(0==t.buttons)return this.destroy();if(this.dragging||null==this.dragging&&10>Math.max(Math.abs((e=this.startEvent).clientX-t.clientX),Math.abs(e.clientY-t.clientY)))return;this.select(this.lastEvent=t);let i=0,s=0,o=0,n=0,r=this.view.win.innerWidth,l=this.view.win.innerHeight;this.scrollParents.x&&({left:o,right:r}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:n,bottom:l}=this.scrollParents.y.getBoundingClientRect());let h=t6(this.view);t.clientX-h.left<=o+6?i=-ek(o-t.clientX):t.clientX+h.right>=r-6&&(i=ek(t.clientX-r)),t.clientY-h.top<=n+6?s=-ek(n-t.clientY):t.clientY+h.bottom>=l-6&&(s=ek(t.clientY-l)),this.setScrollSpeed(i,s)}up(t){null==this.dragging&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,e){this.scrollSpeed={x:t,y:e},t||e?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:e}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),e&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=e,e=0),(t||e)&&this.view.win.scrollBy(t,e),!1===this.dragging&&this.select(this.lastEvent)}skipAtoms(t){let e=null;for(let i=0;i<t.ranges.length;i++){let s=t.ranges[i],o=null;if(s.empty){let t=ef(this.atoms,s.from,0);t!=s.from&&(o=l.jT.cursor(t,-1))}else{let t=ef(this.atoms,s.from,-1),e=ef(this.atoms,s.to,1);(t!=s.from||e!=s.to)&&(o=l.jT.range(s.from==s.anchor?t:e,s.from==s.head?t:e))}o&&(e||(e=t.ranges.slice()),e[i]=o)}return e?l.jT.create(e,t.mainIndex):t}select(t){let{view:e}=this,i=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!i.eq(e.state.selection,!1===this.dragging))&&this.view.dispatch({selection:i,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(t=>t.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}let eD=Object.create(null),eT=Object.create(null),eO=G.ie&&G.ie_version<15||G.ios&&G.webkit_version<604;function eE(t,e,i){for(let s of t.facet(e))i=s(i,t);return i}function eR(t,e){e=eE(t.state,tz,e);let{state:i}=t,s,o=1,n=i.toText(e),r=n.lines==i.selection.ranges.length;if(null!=eI&&i.selection.ranges.every(t=>t.empty)&&eI==n.toString()){let t=-1;s=i.changeByRange(s=>{let h=i.doc.lineAt(s.from);if(h.from==t)return{range:s};t=h.from;let a=i.toText((r?n.line(o++).text:e)+i.lineBreak);return{changes:{from:h.from,insert:a},range:l.jT.cursor(s.from+a.length)}})}else s=r?i.changeByRange(t=>{let e=n.line(o++);return{changes:{from:t.from,to:t.to,insert:e.text},range:l.jT.cursor(t.from+e.length)}}):i.replaceSelection(n);t.dispatch(s,{userEvent:"input.paste",scrollIntoView:!0})}function eB(t,e,i,s){if(1==s)return l.jT.cursor(e,i);if(2==s)return function(t,e,i=1){let s=t.charCategorizer(e),o=t.doc.lineAt(e),n=e-o.from;if(0==o.length)return l.jT.cursor(e);0==n?i=1:n==o.length&&(i=-1);let r=n,h=n;i<0?r=(0,l.cp)(o.text,n,!1):h=(0,l.cp)(o.text,n);let a=s(o.text.slice(r,h));for(;r>0;){let t=(0,l.cp)(o.text,r,!1);if(s(o.text.slice(t,r))!=a)break;r=t}for(;h<o.length;){let t=(0,l.cp)(o.text,h);if(s(o.text.slice(h,t))!=a)break;h=t}return l.jT.range(r+o.from,h+o.from)}(t.state,e,i);{let i=tf.find(t.docView,e),s=t.state.doc.lineAt(i?i.posAtEnd:e),o=i?i.posAtStart:s.from,n=i?i.posAtEnd:s.to;return n<t.state.doc.length&&n==s.to&&n++,l.jT.range(o,n)}}eT.scroll=t=>{t.inputState.lastScrollTop=t.scrollDOM.scrollTop,t.inputState.lastScrollLeft=t.scrollDOM.scrollLeft},eD.keydown=(t,e)=>(t.inputState.setSelectionOrigin("select"),27==e.keyCode&&0!=t.inputState.tabFocusMode&&(t.inputState.tabFocusMode=Date.now()+2e3),!1),eT.touchstart=(t,e)=>{t.inputState.lastTouchTime=Date.now(),t.inputState.setSelectionOrigin("select.pointer")},eT.touchmove=t=>{t.inputState.setSelectionOrigin("select.pointer")},eD.mousedown=(t,e)=>{let i,s,o;if(t.observer.flush(),t.inputState.lastTouchTime>Date.now()-2e3)return!1;let n=null;for(let i of t.state.facet(tH))if(n=i(t,e))break;if(n||0!=e.button||(i=eP(t,e),s=eF(e),o=t.state.selection,n={update(t){t.docChanged&&(i.pos=t.changes.mapPos(i.pos),o=o.map(t.changes))},get(e,n,r){let h=eP(t,e),a,c=eB(t,h.pos,h.bias,s);if(i.pos!=h.pos&&!n){let e=eB(t,i.pos,i.bias,s),o=Math.min(e.from,c.from),n=Math.max(e.to,c.to);c=o<c.from?l.jT.range(o,n):l.jT.range(n,o)}return n?o.replaceRange(o.main.extend(c.from,c.to)):r&&1==s&&o.ranges.length>1&&(a=function(t,e){for(let i=0;i<t.ranges.length;i++){let{from:s,to:o}=t.ranges[i];if(s<=e&&o>=e)return l.jT.create(t.ranges.slice(0,i).concat(t.ranges.slice(i+1)),t.mainIndex==i?0:t.mainIndex-(t.mainIndex>i?1:0))}return null}(o,h.pos))?a:r?o.addRange(c):l.jT.create([c])}}),n){let i=!t.hasFocus;t.inputState.startMouseSelection(new eA(t,e,n,i)),i&&t.observer.ignore(()=>{M(t.contentDOM);let e=t.root.activeElement;e&&!e.contains(t.contentDOM)&&e.blur()});let s=t.inputState.mouseSelection;if(s)return s.start(e),!1===s.dragging}return!1};let eL=(t,e,i)=>e>=i.top&&e<=i.bottom&&t>=i.left&&t<=i.right;function eP(t,e){let i=t.posAtCoords({x:e.clientX,y:e.clientY},!1);return{pos:i,bias:function(t,e,i,s){let o=tf.find(t.docView,e);if(!o)return 1;let n=e-o.posAtStart;if(0==n)return 1;if(n==o.length)return -1;let r=o.coordsAt(n,-1);if(r&&eL(i,s,r))return -1;let l=o.coordsAt(n,1);return l&&eL(i,s,l)?1:r&&r.bottom>=s?-1:1}(t,i,e.clientX,e.clientY)}}let eH=G.ie&&G.ie_version<=11,eV=null,eN=0,eW=0;function eF(t){if(!eH)return t.detail;let e=eV,i=eW;return eV=t,eW=Date.now(),eN=!e||i>Date.now()-400&&2>Math.abs(e.clientX-t.clientX)&&2>Math.abs(e.clientY-t.clientY)?(eN+1)%3:1}function ez(t,e,i,s){let o;if(!(i=eE(t.state,tz,i)))return;let n=t.posAtCoords({x:e.clientX,y:e.clientY},!1),{draggedContent:r}=t.inputState,l=s&&r&&((o=t.state.facet(tP)).length?o[0](e):G.mac?!e.altKey:!e.ctrlKey)?{from:r.from,to:r.to}:null,h={from:n,insert:i},a=t.state.changes(l?[l,h]:h);t.focus(),t.dispatch({changes:a,selection:{anchor:a.mapPos(n,-1),head:a.mapPos(n,1)},userEvent:l?"move.drop":"input.drop"}),t.inputState.draggedContent=null}eD.dragstart=(t,e)=>{let{selection:{main:i}}=t.state;if(e.target.draggable){let s=t.docView.nearest(e.target);if(s&&s.isWidget){let t=s.posAtStart,e=t+s.length;(t>=i.to||e<=i.from)&&(i=l.jT.range(t,e))}}let{inputState:s}=t;return s.mouseSelection&&(s.mouseSelection.dragging=!0),s.draggedContent=i,e.dataTransfer&&(e.dataTransfer.setData("Text",eE(t.state,tI,t.state.sliceDoc(i.from,i.to))),e.dataTransfer.effectAllowed="copyMove"),!1},eD.dragend=t=>(t.inputState.draggedContent=null,!1),eD.drop=(t,e)=>{if(!e.dataTransfer)return!1;if(t.state.readOnly)return!0;let i=e.dataTransfer.files;if(i&&i.length){let s=Array(i.length),o=0,n=()=>{++o==i.length&&ez(t,e,s.filter(t=>null!=t).join(t.state.lineBreak),!1)};for(let t=0;t<i.length;t++){let e=new FileReader;e.onerror=n,e.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(e.result)||(s[t]=e.result),n()},e.readAsText(i[t])}return!0}{let i=e.dataTransfer.getData("Text");if(i)return ez(t,e,i,!0),!0}return!1},eD.paste=(t,e)=>{if(t.state.readOnly)return!0;t.observer.flush();let i=eO?null:e.clipboardData;return i?(eR(t,i.getData("text/plain")||i.getData("text/uri-list")),!0):(function(t){let e=t.dom.parentNode;if(!e)return;let i=e.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout(()=>{t.focus(),i.remove(),eR(t,i.value)},50)}(t),!1)};let eI=null;eD.copy=eD.cut=(t,e)=>{let{text:i,ranges:s,linewise:o}=function(t){let e=[],i=[],s=!1;for(let s of t.selection.ranges)s.empty||(e.push(t.sliceDoc(s.from,s.to)),i.push(s));if(!e.length){let o=-1;for(let{from:s}of t.selection.ranges){let n=t.doc.lineAt(s);n.number>o&&(e.push(n.text),i.push({from:n.from,to:Math.min(t.doc.length,n.to+1)})),o=n.number}s=!0}return{text:eE(t,tI,e.join(t.lineBreak)),ranges:i,linewise:s}}(t.state);if(!i&&!o)return!1;eI=o?i:null,"cut"!=e.type||t.state.readOnly||t.dispatch({changes:s,scrollIntoView:!0,userEvent:"delete.cut"});let n=eO?null:e.clipboardData;return n?(n.clearData(),n.setData("text/plain",i),!0):(!function(t,e){let i=t.dom.parentNode;if(!i)return;let s=i.appendChild(document.createElement("textarea"));s.style.cssText="position: fixed; left: -10000px; top: 10px",s.value=e,s.focus(),s.selectionEnd=e.length,s.selectionStart=0,setTimeout(()=>{s.remove(),t.focus()},50)}(t,i),!1)};let eq=l.q6.define();function eK(t,e){let i=[];for(let s of t.facet(tF)){let o=s(t,e);o&&i.push(o)}return i.length?t.update({effects:i,annotations:eq.of(!0)}):null}function ej(t){setTimeout(()=>{let e=t.hasFocus;if(e!=t.inputState.notifiedFocused){let i=eK(t.state,e);i?t.dispatch(i):t.update([])}},10)}eT.focus=t=>{t.inputState.lastFocusTime=Date.now(),!t.scrollDOM.scrollTop&&(t.inputState.lastScrollTop||t.inputState.lastScrollLeft)&&(t.scrollDOM.scrollTop=t.inputState.lastScrollTop,t.scrollDOM.scrollLeft=t.inputState.lastScrollLeft),ej(t)},eT.blur=t=>{t.observer.clearSelectionRange(),ej(t)},eT.compositionstart=eT.compositionupdate=t=>{!t.observer.editContext&&(null==t.inputState.compositionFirstChange&&(t.inputState.compositionFirstChange=!0),t.inputState.composing<0&&(t.inputState.composing=0))},eT.compositionend=t=>{t.observer.editContext||(t.inputState.composing=-1,t.inputState.compositionEndedAt=Date.now(),t.inputState.compositionPendingKey=!0,t.inputState.compositionPendingChange=t.observer.pendingRecords().length>0,t.inputState.compositionFirstChange=null,G.chrome&&G.android?t.observer.flushSoon():t.inputState.compositionPendingChange?Promise.resolve().then(()=>t.observer.flush()):setTimeout(()=>{t.inputState.composing<0&&t.docView.hasComposition&&t.update([])},50))},eT.contextmenu=t=>{t.inputState.lastContextMenu=Date.now()},eD.beforeinput=(t,e)=>{var i,s;let o;if("insertReplacementText"==e.inputType&&t.observer.editContext){let s=null===(i=e.dataTransfer)||void 0===i?void 0:i.getData("text/plain"),o=e.getTargetRanges();if(s&&o.length){let e=o[0],i=t.posAtDOM(e.startContainer,e.startOffset),n=t.posAtDOM(e.endContainer,e.endOffset);return eb(t,{from:i,to:n,insert:t.state.toText(s)},null),!0}}if(G.chrome&&G.android&&(o=eS.find(t=>t.inputType==e.inputType))&&(t.observer.delayAndroidKey(o.key,o.keyCode),"Backspace"==o.key||"Delete"==o.key)){let e=(null===(s=window.visualViewport)||void 0===s?void 0:s.height)||0;setTimeout(()=>{var i;((null===(i=window.visualViewport)||void 0===i?void 0:i.height)||0)>e+10&&t.hasFocus&&(t.contentDOM.blur(),t.focus())},100)}return G.ios&&"deleteContentForward"==e.inputType&&t.observer.flushSoon(),G.safari&&"insertText"==e.inputType&&t.inputState.composing>=0&&setTimeout(()=>eT.compositionend(t,e),20),!1};let e$=new Set,eX=["pre-wrap","normal","pre-line","break-spaces"],eY=!1;class eG{constructor(t){this.lineWrapping=t,this.doc=l.xv.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,e){let i=this.doc.lineAt(e).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(i+=Math.max(0,Math.ceil((e-t-i*this.lineLength*.5)/this.lineLength))),this.lineHeight*i}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/Math.max(1,this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return eX.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let e=!1;for(let i=0;i<t.length;i++){let s=t[i];s<0?i++:this.heightSamples[Math.floor(10*s)]||(e=!0,this.heightSamples[Math.floor(10*s)]=!0)}return e}refresh(t,e,i,s,o,n){let r=eX.indexOf(t)>-1,l=Math.round(e)!=Math.round(this.lineHeight)||this.lineWrapping!=r;if(this.lineWrapping=r,this.lineHeight=e,this.charWidth=i,this.textHeight=s,this.lineLength=o,l){this.heightSamples={};for(let t=0;t<n.length;t++){let e=n[t];e<0?t++:this.heightSamples[Math.floor(10*e)]=!0}}return l}}class e_{constructor(t,e){this.from=t,this.heights=e,this.index=0}get more(){return this.index<this.heights.length}}class eU{constructor(t,e,i,s,o){this.from=t,this.length=e,this.top=i,this.height=s,this._content=o}get type(){return"number"==typeof this._content?tr.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof tc?this._content.widget:null}get widgetLineBreaks(){return"number"==typeof this._content?this._content:0}join(t){let e=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new eU(this.from,this.length+t.length,this.top,this.height+t.height,e)}}var eQ=((r=eQ||(eQ={}))[r.ByPos=0]="ByPos",r[r.ByHeight=1]="ByHeight",r[r.ByPosNoHeight=2]="ByPosNoHeight",r);class eZ{constructor(t,e,i=2){this.length=t,this.height=e,this.flags=i}get outdated(){return(2&this.flags)>0}set outdated(t){this.flags=(t?2:0)|-3&this.flags}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>.001&&(eY=!0),this.height=t)}replace(t,e,i){return eZ.of(i)}decomposeLeft(t,e){e.push(this)}decomposeRight(t,e){e.push(this)}applyChanges(t,e,i,s){let o=this,n=i.doc;for(let r=s.length-1;r>=0;r--){let{fromA:l,toA:h,fromB:a,toB:c}=s[r],d=o.lineAt(l,eQ.ByPosNoHeight,i.setDoc(e),0,0),u=d.to>=h?d:o.lineAt(h,eQ.ByPosNoHeight,i,0,0);for(c+=u.to-h,h=u.to;r>0&&d.from<=s[r-1].toA;)l=s[r-1].fromA,a=s[r-1].fromB,r--,l<d.from&&(d=o.lineAt(l,eQ.ByPosNoHeight,i,0,0));a+=d.from-l,l=d.from;let f=e9.build(i.setDoc(n),t,a,c);o=eJ(o,o.replace(l,h,f))}return o.updateHeight(i,0)}static empty(){return new e1(0,0)}static of(t){if(1==t.length)return t[0];let e=0,i=t.length,s=0,o=0;for(;;)if(e==i){if(s>2*o){let o=t[e-1];o.break?t.splice(--e,1,o.left,null,o.right):t.splice(--e,1,o.left,o.right),i+=1+o.break,s-=o.size}else if(o>2*s){let e=t[i];e.break?t.splice(i,1,e.left,null,e.right):t.splice(i,1,e.left,e.right),i+=2+e.break,o-=e.size}else break}else if(s<o){let i=t[e++];i&&(s+=i.size)}else{let e=t[--i];e&&(o+=e.size)}let n=0;return null==t[e-1]?(n=1,e--):null==t[e]&&(n=1,i++),new e8(eZ.of(t.slice(0,e)),n,eZ.of(t.slice(i)))}}function eJ(t,e){return t==e?t:(t.constructor!=e.constructor&&(eY=!0),e)}eZ.prototype.size=1;class e0 extends eZ{constructor(t,e,i){super(t,e),this.deco=i}blockAt(t,e,i,s){return new eU(s,this.length,i,this.height,this.deco||0)}lineAt(t,e,i,s,o){return this.blockAt(0,i,s,o)}forEachLine(t,e,i,s,o,n){t<=o+this.length&&e>=o&&n(this.blockAt(0,i,s,o))}updateHeight(t,e=0,i=!1,s){return s&&s.from<=e&&s.more&&this.setHeight(s.heights[s.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class e1 extends e0{constructor(t,e){super(t,e,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,e,i,s){return new eU(s,this.length,i,this.height,this.breaks)}replace(t,e,i){let s=i[0];return 1==i.length&&(s instanceof e1||s instanceof e2&&4&s.flags)&&10>Math.abs(this.length-s.length)?(s instanceof e2?s=new e1(s.length,this.height):s.height=this.height,this.outdated||(s.outdated=!1),s):eZ.of(i)}updateHeight(t,e=0,i=!1,s){return s&&s.from<=e&&s.more?this.setHeight(s.heights[s.index++]):(i||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class e2 extends eZ{constructor(t){super(t,0)}heightMetrics(t,e){let i=t.doc.lineAt(e).number,s=t.doc.lineAt(e+this.length).number,o=s-i+1,n,r=0;if(t.lineWrapping){let e=Math.min(this.height,t.lineHeight*o);n=e/o,this.length>o+1&&(r=(this.height-e)/(this.length-o-1))}else n=this.height/o;return{firstLine:i,lastLine:s,perLine:n,perChar:r}}blockAt(t,e,i,s){let{firstLine:o,lastLine:n,perLine:r,perChar:l}=this.heightMetrics(e,s);if(e.lineWrapping){let o=s+(t<e.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-i)/this.height))*this.length)),n=e.doc.lineAt(o),h=r+n.length*l,a=Math.max(i,t-h/2);return new eU(n.from,n.length,a,h,0)}{let s=Math.max(0,Math.min(n-o,Math.floor((t-i)/r))),{from:l,length:h}=e.doc.line(o+s);return new eU(l,h,i+r*s,r,0)}}lineAt(t,e,i,s,o){if(e==eQ.ByHeight)return this.blockAt(t,i,s,o);if(e==eQ.ByPosNoHeight){let{from:e,to:s}=i.doc.lineAt(t);return new eU(e,s-e,0,0,0)}let{firstLine:n,perLine:r,perChar:l}=this.heightMetrics(i,o),h=i.doc.lineAt(t),a=r+h.length*l,c=h.number-n,d=s+r*c+l*(h.from-o-c);return new eU(h.from,h.length,Math.max(s,Math.min(d,s+this.height-a)),a,0)}forEachLine(t,e,i,s,o,n){t=Math.max(t,o),e=Math.min(e,o+this.length);let{firstLine:r,perLine:l,perChar:h}=this.heightMetrics(i,o);for(let a=t,c=s;a<=e;){let e=i.doc.lineAt(a);if(a==t){let i=e.number-r;c+=l*i+h*(t-o-i)}let s=l+h*e.length;n(new eU(e.from,e.length,c,s,0)),c+=s,a=e.to+1}}replace(t,e,i){let s=this.length-e;if(s>0){let t=i[i.length-1];t instanceof e2?i[i.length-1]=new e2(t.length+s):i.push(null,new e2(s-1))}if(t>0){let e=i[0];e instanceof e2?i[0]=new e2(t+e.length):i.unshift(new e2(t-1),null)}return eZ.of(i)}decomposeLeft(t,e){e.push(new e2(t-1),null)}decomposeRight(t,e){e.push(null,new e2(this.length-t-1))}updateHeight(t,e=0,i=!1,s){let o=e+this.length;if(s&&s.from<=e+this.length&&s.more){let i=[],n=Math.max(e,s.from),r=-1;for(s.from>e&&i.push(new e2(s.from-e-1).updateHeight(t,e));n<=o&&s.more;){let e=t.doc.lineAt(n).length;i.length&&i.push(null);let o=s.heights[s.index++];-1==r?r=o:Math.abs(o-r)>=.001&&(r=-2);let l=new e1(e,o);l.outdated=!1,i.push(l),n+=e+1}n<=o&&i.push(null,new e2(o-n).updateHeight(t,n));let l=eZ.of(i);return(r<0||Math.abs(l.height-this.height)>=.001||Math.abs(r-this.heightMetrics(t,e).perLine)>=.001)&&(eY=!0),eJ(this,l)}return(i||this.outdated)&&(this.setHeight(t.heightForGap(e,e+this.length)),this.outdated=!1),this}toString(){return`gap(${this.length})`}}class e8 extends eZ{constructor(t,e,i){super(t.length+e+i.length,t.height+i.height,e|(t.outdated||i.outdated?2:0)),this.left=t,this.right=i,this.size=t.size+i.size}get break(){return 1&this.flags}blockAt(t,e,i,s){let o=i+this.left.height;return t<o?this.left.blockAt(t,e,i,s):this.right.blockAt(t,e,o,s+this.left.length+this.break)}lineAt(t,e,i,s,o){let n=s+this.left.height,r=o+this.left.length+this.break,l=e==eQ.ByHeight?t<n:t<r,h=l?this.left.lineAt(t,e,i,s,o):this.right.lineAt(t,e,i,n,r);if(this.break||(l?h.to<r:h.from>r))return h;let a=e==eQ.ByPosNoHeight?eQ.ByPosNoHeight:eQ.ByPos;return l?h.join(this.right.lineAt(r,a,i,n,r)):this.left.lineAt(r,a,i,s,o).join(h)}forEachLine(t,e,i,s,o,n){let r=s+this.left.height,l=o+this.left.length+this.break;if(this.break)t<l&&this.left.forEachLine(t,e,i,s,o,n),e>=l&&this.right.forEachLine(t,e,i,r,l,n);else{let h=this.lineAt(l,eQ.ByPos,i,s,o);t<h.from&&this.left.forEachLine(t,h.from-1,i,s,o,n),h.to>=t&&h.from<=e&&n(h),e>h.to&&this.right.forEachLine(h.to+1,e,i,r,l,n)}}replace(t,e,i){let s=this.left.length+this.break;if(e<s)return this.balanced(this.left.replace(t,e,i),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-s,e-s,i));let o=[];t>0&&this.decomposeLeft(t,o);let n=o.length;for(let t of i)o.push(t);if(t>0&&e3(o,n-1),e<this.length){let t=o.length;this.decomposeRight(e,o),e3(o,t)}return eZ.of(o)}decomposeLeft(t,e){let i=this.left.length;if(t<=i)return this.left.decomposeLeft(t,e);e.push(this.left),this.break&&t>=++i&&e.push(null),t>i&&this.right.decomposeLeft(t-i,e)}decomposeRight(t,e){let i=this.left.length,s=i+this.break;if(t>=s)return this.right.decomposeRight(t-s,e);t<i&&this.left.decomposeRight(t,e),this.break&&t<s&&e.push(null),e.push(this.right)}balanced(t,e){return t.size>2*e.size||e.size>2*t.size?eZ.of(this.break?[t,null,e]:[t,e]):(this.left=eJ(this.left,t),this.right=eJ(this.right,e),this.setHeight(t.height+e.height),this.outdated=t.outdated||e.outdated,this.size=t.size+e.size,this.length=t.length+this.break+e.length,this)}updateHeight(t,e=0,i=!1,s){let{left:o,right:n}=this,r=e+o.length+this.break,l=null;return(s&&s.from<=e+o.length&&s.more?l=o=o.updateHeight(t,e,i,s):o.updateHeight(t,e,i),s&&s.from<=r+n.length&&s.more?l=n=n.updateHeight(t,r,i,s):n.updateHeight(t,r,i),l)?this.balanced(o,n):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function e3(t,e){let i,s;null==t[e]&&(i=t[e-1])instanceof e2&&(s=t[e+1])instanceof e2&&t.splice(e-1,3,new e2(i.length+1+s.length))}class e9{constructor(t,e){this.pos=t,this.oracle=e,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,e){if(this.lineStart>-1){let t=Math.min(e,this.lineEnd),i=this.nodes[this.nodes.length-1];i instanceof e1?i.length+=t-this.pos:(t>this.pos||!this.isCovered)&&this.nodes.push(new e1(t-this.pos,-1)),this.writtenTo=t,e>t&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=e}point(t,e,i){if(t<e||i.heightRelevant){let s=i.widget?i.widget.estimatedHeight:0,o=i.widget?i.widget.lineBreaks:0;s<0&&(s=this.oracle.lineHeight);let n=e-t;i.block?this.addBlock(new e0(n,s,i)):(n||o||s>=5)&&this.addLineDeco(s,o,n)}else e>t&&this.span(t,e);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:e}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=e,this.writtenTo<t&&((this.writtenTo<t-1||null==this.nodes[this.nodes.length-1])&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new e1(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,e){let i=new e2(e-t);return this.oracle.doc.lineAt(t).to==e&&(i.flags|=4),i}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof e1)return t;let e=new e1(0,-1);return this.nodes.push(e),e}addBlock(t){this.enterLine();let e=t.deco;e&&e.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,e&&e.endSide>0&&(this.covering=t)}addLineDeco(t,e,i){let s=this.ensureLine();s.length+=i,s.collapsed+=i,s.widgetHeight=Math.max(s.widgetHeight,t),s.breaks+=e,this.writtenTo=this.pos=this.pos+i}finish(t){let e=0==this.nodes.length?null:this.nodes[this.nodes.length-1];!(this.lineStart>-1)||e instanceof e1||this.isCovered?(this.writtenTo<this.pos||null==e)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos)):this.nodes.push(new e1(0,-1));let i=t;for(let t of this.nodes)t instanceof e1&&t.updateHeight(this.oracle,i),i+=t?t.length:1;return this.nodes}static build(t,e,i,s){let o=new e9(i,t);return l.Xs.spans(e,i,s,o,0),o.finish(i)}}class e5{constructor(){this.changes=[]}compareRange(){}comparePoint(t,e,i,s){(t<e||i&&i.heightRelevant||s&&s.heightRelevant)&&tu(t,e,this.changes,5)}}class e4{constructor(t,e,i,s){this.from=t,this.to=e,this.size=i,this.displaySize=s}static same(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++){let s=t[i],o=e[i];if(s.from!=o.from||s.to!=o.to||s.size!=o.size)return!1}return!0}draw(t,e){return tl.replace({widget:new e6(this.displaySize*(e?t.scaleY:t.scaleX),e)}).range(this.from,this.to)}}class e6 extends tn{constructor(t,e){super(),this.size=t,this.vertical=e}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class e7{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=is,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=tb.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let e=t.facet(t1).some(t=>"function"!=typeof t&&"cm-lineWrapping"==t.class);this.heightOracle=new eG(e),this.stateDeco=t.facet(t2).filter(t=>"function"!=typeof t),this.heightMap=eZ.empty().applyChanges(this.stateDeco,l.xv.empty,this.heightOracle.setDoc(t.doc),[new et(0,0,0,t.doc.length)]);for(let t=0;t<2&&(this.viewport=this.getViewport(0,null),this.updateForViewport());t++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=tl.set(this.lineGaps.map(t=>t.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:e}=this.state.selection;for(let i=0;i<=1;i++){let s=i?e.head:e.anchor;if(!t.some(({from:t,to:e})=>s>=t&&s<=e)){let{from:e,to:i}=this.lineBlockAt(s);t.push(new it(e,i))}}return this.viewports=t.sort((t,e)=>t.from-e.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?is:new io(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(ir(t,this.scaler))})}update(t,e=null){var i,s;let o;this.state=t.state;let n=this.stateDeco;this.stateDeco=this.state.facet(t2).filter(t=>"function"!=typeof t);let r=t.changedRanges,h=et.extendWithRanges(r,(i=this.stateDeco,s=t?t.changes:l.as.empty(this.state.doc.length),o=new e5,l.Xs.compare(n,i,s,o,0),o.changes)),a=this.heightMap.height,c=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);eY=!1,this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),h),(this.heightMap.height!=a||eY)&&(t.flags|=2),c?(this.scrollAnchorPos=t.changes.mapPos(c.from,-1),this.scrollAnchorHeight=c.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=a);let d=h.length?this.mapViewport(this.viewport,t.changes):this.viewport;(e&&(e.range.head<d.from||e.range.head>d.to)||!this.viewportIsAppropriate(d))&&(d=this.getViewport(0,e));let u=d.from!=this.viewport.from||d.to!=this.viewport.to;this.viewport=d,t.flags|=this.updateForViewport(),(u||!t.changes.empty||2&t.flags)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),e&&(this.scrollTarget=e),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(tK)&&(this.mustEnforceCursorAssoc=!0)}measure(t){var e;let i,s,o=t.contentDOM,n=window.getComputedStyle(o),r=this.heightOracle,h=n.whiteSpace;this.defaultTextDirection="rtl"==n.direction?tb.RTL:tb.LTR;let a=this.heightOracle.mustRefreshForWrapping(h),c=o.getBoundingClientRect(),d=a||this.mustMeasureContent||this.contentDOMHeight!=c.height;this.contentDOMHeight=c.height,this.mustMeasureContent=!1;let u=0,f=0;if(c.width&&c.height){let{scaleX:t,scaleY:e}=y(o,c);(t>.005&&Math.abs(this.scaleX-t)>.005||e>.005&&Math.abs(this.scaleY-e)>.005)&&(this.scaleX=t,this.scaleY=e,u|=16,a=d=!0)}let p=(parseInt(n.paddingTop)||0)*this.scaleY,g=(parseInt(n.paddingBottom)||0)*this.scaleY;(this.paddingTop!=p||this.paddingBottom!=g)&&(this.paddingTop=p,this.paddingBottom=g,u|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(r.lineWrapping&&(d=!0),this.editorWidth=t.scrollDOM.clientWidth,u|=16);let m=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=m&&(this.scrollAnchorHeight=-1,this.scrollTop=m),this.scrolledToBottom=D(t.scrollDOM);let v=(this.printing?function(t,e){let i=t.getBoundingClientRect();return{left:0,right:i.right-i.left,top:e,bottom:i.bottom-(i.top+e)}}:function(t,e){let i=t.getBoundingClientRect(),s=t.ownerDocument,o=s.defaultView||window,n=Math.max(0,i.left),r=Math.min(o.innerWidth,i.right),l=Math.max(0,i.top),h=Math.min(o.innerHeight,i.bottom);for(let e=t.parentNode;e&&e!=s.body;)if(1==e.nodeType){let i=e,s=window.getComputedStyle(i);if((i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth)&&"visible"!=s.overflow){let s=i.getBoundingClientRect();n=Math.max(n,s.left),r=Math.min(r,s.right),l=Math.max(l,s.top),h=Math.min(e==t.parentNode?o.innerHeight:h,s.bottom)}e="absolute"==s.position||"fixed"==s.position?i.offsetParent:i.parentNode}else if(11==e.nodeType)e=e.host;else break;return{left:n-i.left,right:Math.max(n,r)-i.left,top:l-(i.top+e),bottom:Math.max(l,h)-(i.top+e)}})(o,this.paddingTop),w=v.top-this.pixelViewport.top,b=v.bottom-this.pixelViewport.bottom;this.pixelViewport=v;let x=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(x!=this.inView&&(this.inView=x,x&&(d=!0)),!this.inView&&!this.scrollTarget&&(i=(e=t.dom).getBoundingClientRect(),s=e.ownerDocument.defaultView||window,!(i.left<s.innerWidth)||!(i.right>0)||!(i.top<s.innerHeight)||!(i.bottom>0)))return 0;let S=c.width;if((this.contentDOMWidth!=S||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=c.width,this.editorHeight=t.scrollDOM.clientHeight,u|=16),d){let e=t.docView.measureVisibleLineHeights(this.viewport);if(r.mustRefreshForHeights(e)&&(a=!0),a||r.lineWrapping&&Math.abs(S-this.contentDOMWidth)>r.charWidth){let{lineHeight:i,charWidth:s,textHeight:o}=t.docView.measureTextSize();(a=i>0&&r.refresh(h,i,s,o,Math.max(5,S/s),e))&&(t.docView.minWidth=0,u|=16)}for(let i of(w>0&&b>0?f=Math.max(w,b):w<0&&b<0&&(f=Math.min(w,b)),eY=!1,this.viewports)){let s=i.from==this.viewport.from?e:t.docView.measureVisibleLineHeights(i);this.heightMap=(a?eZ.empty().applyChanges(this.stateDeco,l.xv.empty,this.heightOracle,[new et(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(r,0,a,new e_(i.from,s))}eY&&(u|=2)}let M=!this.viewportIsAppropriate(this.viewport,f)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return M&&(2&u&&(u|=this.updateScaler()),this.viewport=this.getViewport(f,this.scrollTarget),u|=this.updateForViewport()),(2&u||M)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(a?[]:this.lineGaps,t)),u|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),u}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,e){let i=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),s=this.heightMap,o=this.heightOracle,{visibleTop:n,visibleBottom:r}=this,l=new it(s.lineAt(n-1e3*i,eQ.ByHeight,o,0,0).from,s.lineAt(r+(1-i)*1e3,eQ.ByHeight,o,0,0).to);if(e){let{head:t}=e.range;if(t<l.from||t>l.to){let i=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),n=s.lineAt(t,eQ.ByPos,o,0,0),r;r="center"==e.y?(n.top+n.bottom)/2-i/2:"start"==e.y||"nearest"==e.y&&t<l.from?n.top:n.bottom-i,l=new it(s.lineAt(r-500,eQ.ByHeight,o,0,0).from,s.lineAt(r+i+500,eQ.ByHeight,o,0,0).to)}}return l}mapViewport(t,e){let i=e.mapPos(t.from,-1),s=e.mapPos(t.to,1);return new it(this.heightMap.lineAt(i,eQ.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(s,eQ.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:e},i=0){if(!this.inView)return!0;let{top:s}=this.heightMap.lineAt(t,eQ.ByPos,this.heightOracle,0,0),{bottom:o}=this.heightMap.lineAt(e,eQ.ByPos,this.heightOracle,0,0),{visibleTop:n,visibleBottom:r}=this;return(0==t||s<=n-Math.max(10,Math.min(-i,250)))&&(e==this.state.doc.length||o>=r+Math.max(10,Math.min(i,250)))&&s>n-2e3&&o<r+2e3}mapLineGaps(t,e){if(!t.length||e.empty)return t;let i=[];for(let s of t)e.touchesRange(s.from,s.to)||i.push(new e4(e.mapPos(s.from),e.mapPos(s.to),s.size,s.displaySize));return i}ensureLineGaps(t,e){let i=this.heightOracle.lineWrapping,s=i?1e4:2e3,o=s>>1,n=s<<1;if(this.defaultTextDirection!=tb.LTR&&!i)return[];let r=[],h=(s,n,a,c)=>{if(n-s<o)return;let d=this.state.selection.main,u=[d.from];for(let t of(d.empty||u.push(d.to),u))if(t>s&&t<n){h(s,t-10,a,c),h(t+10,n,a,c);return}let f=function(t,e){for(let i of t)if(e(i))return i}(t,t=>t.from>=a.from&&t.to<=a.to&&Math.abs(t.from-s)<o&&Math.abs(t.to-n)<o&&!u.some(e=>t.from<e&&t.to>e));if(!f){if(n<a.to&&e&&i&&e.visibleRanges.some(t=>t.from<=n&&t.to>=n)){let t=e.moveToLineBoundary(l.jT.cursor(n),!1,!0).head;t>s&&(n=t)}let t=this.gapSize(a,s,n,c),o=i||t<2e6?t:2e6;f=new e4(s,n,t,o)}r.push(f)},a=e=>{var o,r,a;let c,d,u,f,p;if(e.length<n||e.type!=tr.Text)return;let g=(o=e.from,r=e.to,a=this.stateDeco,u=[],f=o,p=0,l.Xs.spans(a,o,r,{span(){},point(t,e){t>f&&(u.push({from:f,to:t}),p+=t-f),f=e}},20),f<r&&(u.push({from:f,to:r}),p+=r-f),{total:p,ranges:u});if(g.total<n)return;let m=this.scrollTarget?this.scrollTarget.range.head:null;if(i){let t,i,o=s/this.heightOracle.lineLength*this.heightOracle.lineHeight;if(null!=m){let s=ii(g,m),n=((this.visibleBottom-this.visibleTop)/2+o)/e.height;t=s-n,i=s+n}else t=(this.visibleTop-e.top-o)/e.height,i=(this.visibleBottom-e.top+o)/e.height;c=ie(g,t),d=ie(g,i)}else{let i,o,n=g.total*this.heightOracle.charWidth,r=s*this.heightOracle.charWidth,l=0;if(n>2e6)for(let i of t)i.from>=e.from&&i.from<e.to&&i.size!=i.displaySize&&i.from*this.heightOracle.charWidth+l<this.pixelViewport.left&&(l=i.size-i.displaySize);let h=this.pixelViewport.left+l,a=this.pixelViewport.right+l;if(null!=m){let t=ii(g,m),e=((a-h)/2+r)/n;i=t-e,o=t+e}else i=(h-r)/n,o=(a+r)/n;c=ie(g,i),d=ie(g,o)}c>e.from&&h(e.from,c,e,g),d<e.to&&h(d,e.to,e,g)};for(let t of this.viewportLines)Array.isArray(t.type)?t.type.forEach(a):a(t);return r}gapSize(t,e,i,s){let o=ii(s,i)-ii(s,e);return this.heightOracle.lineWrapping?t.height*o:s.total*this.heightOracle.charWidth*o}updateLineGaps(t){e4.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=tl.set(t.map(t=>t.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let e=this.stateDeco;this.lineGaps.length&&(e=e.concat(this.lineGapDeco));let i=[];l.Xs.spans(e,this.viewport.from,this.viewport.to,{span(t,e){i.push({from:t,to:e})},point(){}},20);let s=0;if(i.length!=this.visibleRanges.length)s=12;else for(let e=0;e<i.length&&!(8&s);e++){let o=this.visibleRanges[e],n=i[e];o.from==n.from&&o.to==n.to||(s|=4,t&&t.mapPos(o.from,-1)==n.from&&t.mapPos(o.to,1)==n.to||(s|=8))}return this.visibleRanges=i,s}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(e=>e.from<=t&&e.to>=t)||ir(this.heightMap.lineAt(t,eQ.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(e=>e.top<=t&&e.bottom>=t)||ir(this.heightMap.lineAt(this.scaler.fromDOM(t),eQ.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let e=this.lineBlockAtHeight(t+8);return e.from>=this.viewport.from||this.viewportLines[0].top-t>200?e:this.viewportLines[0]}elementAtHeight(t){return ir(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class it{constructor(t,e){this.from=t,this.to=e}}function ie({total:t,ranges:e},i){if(i<=0)return e[0].from;if(i>=1)return e[e.length-1].to;let s=Math.floor(t*i);for(let t=0;;t++){let{from:i,to:o}=e[t],n=o-i;if(s<=n)return i+s;s-=n}}function ii(t,e){let i=0;for(let{from:s,to:o}of t.ranges){if(e<=o){i+=e-s;break}i+=o-s}return i/t.total}let is={toDOM:t=>t,fromDOM:t=>t,scale:1,eq(t){return t==this}};class io{constructor(t,e,i){let s=0,o=0,n=0;for(let r of(this.viewports=i.map(({from:i,to:o})=>{let n=e.lineAt(i,eQ.ByPos,t,0,0).top,r=e.lineAt(o,eQ.ByPos,t,0,0).bottom;return s+=r-n,{from:i,to:o,top:n,bottom:r,domTop:0,domBottom:0}}),this.scale=(7e6-s)/(e.height-s),this.viewports))r.domTop=n+(r.top-o)*this.scale,n=r.domBottom=r.domTop+(r.bottom-r.top),o=r.bottom}toDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.top)return s+(t-i)*this.scale;if(t<=o.bottom)return o.domTop+(t-o.top);i=o.bottom,s=o.domBottom}}fromDOM(t){for(let e=0,i=0,s=0;;e++){let o=e<this.viewports.length?this.viewports[e]:null;if(!o||t<o.domTop)return i+(t-s)/this.scale;if(t<=o.domBottom)return o.top+(t-o.domTop);i=o.bottom,s=o.domBottom}}eq(t){return t instanceof io&&this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((e,i)=>e.from==t.viewports[i].from&&e.to==t.viewports[i].to)}}function ir(t,e){if(1==e.scale)return t;let i=e.toDOM(t.top),s=e.toDOM(t.bottom);return new eU(t.from,t.length,i,s-i,Array.isArray(t._content)?t._content.map(t=>ir(t,e)):t._content)}let il=l.r$.define({combine:t=>t.join(" ")}),ih=l.r$.define({combine:t=>t.indexOf(!0)>-1}),ia=h.V.newName(),ic=h.V.newName(),id=h.V.newName(),iu={"&light":"."+ic,"&dark":"."+id};function ip(t,e,i){return new h.V(e,{finish:e=>/&/.test(e)?e.replace(/&\w*/,e=>{if("&"==e)return t;if(!i||!i[e])throw RangeError(`Unsupported selector: ${e}`);return i[e]}):t+" "+e})}let ig=ip("."+ia,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",zIndex:200},".cm-gutters-before":{insetInlineStart:0},".cm-gutters-after":{insetInlineEnd:0},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",border:"0px solid #ddd","&.cm-gutters-before":{borderRightWidth:"1px"},"&.cm-gutters-after":{borderLeftWidth:"1px"}},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-dialog":{padding:"2px 19px 4px 6px",position:"relative","& label":{fontSize:"80%"}},".cm-dialog-close":{position:"absolute",top:"3px",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",fontSize:"14px",padding:"0"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top",userSelect:"none"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:'url(\'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>\')',backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},iu),im={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},iv=G.ie&&G.ie_version<=11;class iw{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new x,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(e=>{for(let t of e)this.queue.push(t);(G.ie&&G.ie_version<=11||G.ios&&t.composing)&&e.some(t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&G.android&&!1!==t.constructor.EDIT_CONTEXT&&!(G.chrome&&G.chrome_version<126)&&(this.editContext=new ix(t),t.state.facet(t_)&&(t.contentDOM.editContext=this.editContext.editContext)),iv&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),"function"==typeof ResizeObserver&&(this.resizeScroll=new ResizeObserver(()=>{var t;(null===(t=this.view.docView)||void 0===t?void 0:t.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),"function"==typeof IntersectionObserver&&(this.intersection=new IntersectionObserver(t=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),t.length>0&&t[t.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(t=>{t.length>0&&t[t.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){("change"!=t.type&&t.type||t.matches)&&(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((e,i)=>e!=t[i]))){for(let e of(this.gapIntersection.disconnect(),t))this.gapIntersection.observe(e);this.gaps=t}}onSelectionChange(t){let e=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:i}=this,s=this.selectionRange;if(i.state.facet(t_)?i.root.activeElement!=this.dom:!u(this.dom,s))return;let o=s.anchorNode&&i.docView.nearest(s.anchorNode);if(o&&o.ignoreEvent(t)){e||(this.selectionChanged=!1);return}(G.ie&&G.ie_version<=11||G.android&&G.chrome)&&!i.state.selection.main.empty&&s.focusNode&&p(s.focusNode,s.focusOffset,s.anchorNode,s.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,e=c(t.root);if(!e)return!1;let i=G.safari&&11==t.root.nodeType&&t.root.activeElement==this.dom&&function(t,e){if(e.getComposedRanges){let i=e.getComposedRanges(t.root)[0];if(i)return iy(t,i)}let i=null;function s(t){t.preventDefault(),t.stopImmediatePropagation(),i=t.getTargetRanges()[0]}return t.contentDOM.addEventListener("beforeinput",s,!0),t.dom.ownerDocument.execCommand("indent"),t.contentDOM.removeEventListener("beforeinput",s,!0),i?iy(t,i):null}(this.view,e)||e;if(!i||this.selectionRange.eq(i))return!1;let s=u(this.dom,i);return s&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&function(t,e){let i=e.focusNode,s=e.focusOffset;if(!i||e.anchorNode!=i||e.anchorOffset!=s)return!1;for(s=Math.min(s,w(i));;)if(s){if(1!=i.nodeType)return!1;let t=i.childNodes[s-1];"false"==t.contentEditable?s--:s=w(i=t)}else{if(i==t)return!0;s=g(i),i=i.parentNode}}(this.dom,i)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(i),s&&(this.selectionChanged=!0),!0)}setSelectionRange(t,e){this.selectionRange.set(t.node,t.offset,e.node,e.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,e=null;for(let i=this.dom;i;)if(1==i.nodeType)!e&&t<this.scrollTargets.length&&this.scrollTargets[t]==i?t++:e||(e=this.scrollTargets.slice(0,t)),e&&e.push(i),i=i.assignedSlot||i.parentNode;else if(11==i.nodeType)i=i.host;else break;if(t<this.scrollTargets.length&&!e&&(e=this.scrollTargets.slice(0,t)),e){for(let t of this.scrollTargets)t.removeEventListener("scroll",this.onScroll);for(let t of this.scrollTargets=e)t.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,im),iv&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),iv&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,e){var i;this.delayedAndroidKey||(this.flushingAndroidKey=this.view.win.requestAnimationFrame(()=>{let t=this.delayedAndroidKey;t&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=t.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&t.force&&k(this.dom,t.key,t.keyCode))})),this.delayedAndroidKey&&"Enter"!=t||(this.delayedAndroidKey={key:t,keyCode:e,force:this.lastChange<Date.now()-50||!!(null===(i=this.delayedAndroidKey)||void 0===i?void 0:i.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let e=-1,i=-1,s=!1;for(let o of t){let t=this.readMutation(o);t&&(t.typeOver&&(s=!0),-1==e?{from:e,to:i}=t:(e=Math.min(t.from,e),i=Math.max(t.to,i)))}return{from:e,to:i,typeOver:s}}readChange(){let{from:t,to:e,typeOver:i}=this.processRecords(),s=this.selectionChanged&&u(this.dom,this.selectionRange);if(t<0&&!s)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let o=new ev(this.view,t,e,i);return this.view.docView.domChanged={newSel:o.newSel?o.newSel.main:null},o}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let e=this.readChange();if(!e)return this.view.requestMeasure(),!1;let i=this.view.state,s=ew(this.view,e);return this.view.state==i&&(e.domChanged||e.newSel&&!e.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),s}readMutation(t){let e=this.view.docView.nearest(t.target);if(!e||e.ignoreMutation(t))return null;if(e.markDirty("attributes"==t.type),"attributes"==t.type&&(e.flags|=4),"childList"==t.type){let i=ib(e,t.previousSibling||t.target.previousSibling,-1),s=ib(e,t.nextSibling||t.target.nextSibling,1);return{from:i?e.posAfter(i):e.posAtStart,to:s?e.posBefore(s):e.posAtEnd,typeOver:!1}}return"characterData"==t.type?{from:e.posAtStart,to:e.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet(t_)!=t.state.facet(t_)&&(t.view.contentDOM.editContext=t.state.facet(t_)?this.editContext.editContext:null))}destroy(){var t,e,i;for(let s of(this.stop(),null===(t=this.intersection)||void 0===t||t.disconnect(),null===(e=this.gapIntersection)||void 0===e||e.disconnect(),null===(i=this.resizeScroll)||void 0===i||i.disconnect(),this.scrollTargets))s.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function ib(t,e,i){for(;e;){let s=B.get(e);if(s&&s.parent==t)return s;let o=e.parentNode;e=o!=t.dom?o:i>0?e.nextSibling:e.previousSibling}return null}function iy(t,e){let i=e.startContainer,s=e.startOffset,o=e.endContainer,n=e.endOffset,r=t.docView.domAtPos(t.state.selection.main.anchor);return p(r.node,r.offset,o,n)&&([i,s,o,n]=[o,n,i,s]),{anchorNode:i,anchorOffset:s,focusNode:o,focusOffset:n}}class ix{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let e=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});for(let i in this.handlers.textupdate=e=>{let i=t.state.selection.main,{anchor:s,head:o}=i,n=this.toEditorPos(e.updateRangeStart),r=this.toEditorPos(e.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:e.updateRangeStart,editorBase:n,drifted:!1});let h={from:n,to:r,insert:l.xv.of(e.text.split("\n"))};if(h.from==this.from&&s<this.from?h.from=s:h.to==this.to&&s>this.to&&(h.to=s),h.from==h.to&&!h.insert.length){let s=l.jT.single(this.toEditorPos(e.selectionStart),this.toEditorPos(e.selectionEnd));s.main.eq(i)||t.dispatch({selection:s,userEvent:"select"});return}if((G.mac||G.android)&&h.from==o-1&&/^\. ?$/.test(e.text)&&"off"==t.contentDOM.getAttribute("autocorrect")&&(h={from:n,to:r,insert:l.xv.of([e.text.replace("."," ")])}),this.pendingContextChange=h,!t.state.readOnly){let i=this.to-this.from+(h.to-h.from+h.insert.length);eb(t,h,l.jT.single(this.toEditorPos(e.selectionStart,i),this.toEditorPos(e.selectionEnd,i)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state))},this.handlers.characterboundsupdate=i=>{let s=[],o=null;for(let e=this.toEditorPos(i.rangeStart),n=this.toEditorPos(i.rangeEnd);e<n;e++){let i=t.coordsForChar(e);o=i&&new DOMRect(i.left,i.top,i.right-i.left,i.bottom-i.top)||o||new DOMRect,s.push(o)}e.updateCharacterBounds(i.rangeStart,s)},this.handlers.textformatupdate=e=>{let i=[];for(let t of e.getTextFormats()){let e=t.underlineStyle,s=t.underlineThickness;if("None"!=e&&"None"!=s){let o=this.toEditorPos(t.rangeStart),n=this.toEditorPos(t.rangeEnd);if(o<n){let t=`text-decoration: underline ${"Dashed"==e?"dashed ":"Squiggle"==e?"wavy ":""}${"Thin"==s?1:2}px`;i.push(tl.mark({attributes:{style:t}}).range(o,n))}}}t.dispatch({effects:tY.of(tl.set(i))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:e}=this.composing;this.composing=null,e&&this.reset(t.state)}},this.handlers)e.addEventListener(i,this.handlers[i]);this.measureReq={read:t=>{this.editContext.updateControlBounds(t.contentDOM.getBoundingClientRect());let e=c(t.root);e&&e.rangeCount&&this.editContext.updateSelectionBounds(e.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let e=0,i=!1,s=this.pendingContextChange;return t.changes.iterChanges((o,n,r,l,h)=>{if(i)return;let a=h.length-(n-o);if(s&&n>=s.to){if(s.from==o&&s.to==n&&s.insert.eq(h)){s=this.pendingContextChange=null,e+=a,this.to+=a;return}s=null,this.revertPending(t.state)}if(o+=e,(n+=e)<=this.from)this.from+=a,this.to+=a;else if(o<this.to){if(o<this.from||n>this.to||this.to-this.from+h.length>3e4){i=!0;return}this.editContext.updateText(this.toContextPos(o),this.toContextPos(n),h.toString()),this.to+=a}e+=a}),s&&!i&&this.revertPending(t.state),!i}update(t){let e=this.pendingContextChange,i=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(i.from,i.to)&&t.transactions.some(t=>!t.isUserEvent("input.type")&&t.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):this.applyEdits(t)&&this.rangeIsValid(t.state)?(t.docChanged||t.selectionSet||e)&&this.setSelection(t.state):(this.pendingContextChange=null,this.reset(t.state)),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:e}=t.selection.main;this.from=Math.max(0,e-1e4),this.to=Math.min(t.doc.length,e+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let e=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(e.from),this.toContextPos(e.from+e.insert.length),t.doc.sliceString(e.from,e.to))}setSelection(t){let{main:e}=t.selection,i=this.toContextPos(Math.max(this.from,Math.min(this.to,e.anchor))),s=this.toContextPos(e.head);(this.editContext.selectionStart!=i||this.editContext.selectionEnd!=s)&&this.editContext.updateSelection(i,s)}rangeIsValid(t){let{head:e}=t.selection.main;return!(this.from>0&&e-this.from<500||this.to<t.doc.length&&this.to-e<500||this.to-this.from>3e4)}toEditorPos(t,e=this.to-this.from){t=Math.min(t,e);let i=this.composing;return i&&i.drifted?i.editorBase+(t-i.contextBase):t+this.from}toContextPos(t){let e=this.composing;return e&&e.drifted?e.contextBase+(t-e.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class iS{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return!!this.inputState&&this.inputState.composing>0}get compositionStarted(){return!!this.inputState&&this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var e;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:i}=t;for(let e of(this.dispatchTransactions=t.dispatchTransactions||i&&(t=>t.forEach(t=>i(t,this)))||(t=>this.update(t)),this.dispatch=this.dispatch.bind(this),this._root=t.root||function(t){for(;t;){if(t&&(9==t.nodeType||11==t.nodeType&&t.host))return t;t=t.assignedSlot||t.parentNode}return null}(t.parent)||document,this.viewState=new e7(t.state||l.yy.create(t)),t.scrollTo&&t.scrollTo.is(tX)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(tQ).map(t=>new tJ(t)),this.plugins))e.update(this);this.observer=new iw(this),this.inputState=new ey(this),this.inputState.ensureHandlers(this.plugins),this.docView=new ei(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),(null===(e=document.fonts)||void 0===e?void 0:e.ready)&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let e=1==t.length&&t[0]instanceof l.YW?t:1==t.length&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(e,this)}update(t){if(0!=this.updateState)throw Error("Calls to EditorView.update are not allowed while an update is in progress");let e=!1,i=!1,s,o=this.state;for(let e of t){if(e.startState!=o)throw RangeError("Trying to update state with a transaction that doesn't start from the previous state.");o=e.state}if(this.destroyed){this.viewState.state=o;return}let n=this.hasFocus,r=0,h=null;t.some(t=>t.annotation(eq))?(this.inputState.notifiedFocused=n,r=1):n==this.inputState.notifiedFocused||(this.inputState.notifiedFocused=n,(h=eK(o,n))||(r=1));let a=this.observer.delayedAndroidKey,c=null;if(a?(this.observer.clearDelayedAndroidKey(),((c=this.observer.readChange())&&!this.state.doc.eq(o.doc)||!this.state.selection.eq(o.selection))&&(c=null)):this.observer.clear(),o.facet(l.yy.phrases)!=this.state.facet(l.yy.phrases))return this.setState(o);s=ee.create(this,o,t),s.flags|=r;let d=this.viewState.scrollTarget;try{for(let e of(this.updateState=2,t)){if(d&&(d=d.map(e.changes)),e.scrollIntoView){let{main:t}=e.state.selection;d=new t$(t.empty?t:l.jT.cursor(t.head,t.head>t.anchor?-1:1))}for(let t of e.effects)t.is(tX)&&(d=t.value.clip(this.state))}this.viewState.update(s,d),this.bidiCache=ik.update(this.bidiCache,s.changes),s.empty||(this.updatePlugins(s),this.inputState.update(s)),e=this.docView.update(s),this.state.facet(t7)!=this.styleModules&&this.mountStyles(),i=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(e,t.some(t=>t.isUserEvent("select.pointer")))}finally{this.updateState=0}if(s.startState.facet(il)!=s.state.facet(il)&&(this.viewState.mustMeasureContent=!0),(e||i||d||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),e&&this.docViewUpdate(),!s.empty)for(let t of this.state.facet(tN))try{t(s)}catch(t){tG(this.state,t,"update listener")}(h||c)&&Promise.resolve().then(()=>{h&&this.state==h.startState&&this.dispatch(h),c&&!ew(this,c)&&a.force&&k(this.contentDOM,a.key,a.keyCode)})}setState(t){if(0!=this.updateState)throw Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let e=this.hasFocus;try{for(let t of this.plugins)t.destroy(this);for(let e of(this.viewState=new e7(t),this.plugins=t.facet(tQ).map(t=>new tJ(t)),this.pluginMap.clear(),this.plugins))e.update(this);this.docView.destroy(),this.docView=new ei(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}e&&this.focus(),this.requestMeasure()}updatePlugins(t){let e=t.startState.facet(tQ),i=t.state.facet(tQ);if(e!=i){let s=[];for(let o of i){let i=e.indexOf(o);if(i<0)s.push(new tJ(o));else{let e=this.plugins[i];e.mustUpdate=t,s.push(e)}}for(let e of this.plugins)e.mustUpdate!=t&&e.destroy(this);this.plugins=s,this.pluginMap.clear()}else for(let e of this.plugins)e.mustUpdate=t;for(let t=0;t<this.plugins.length;t++)this.plugins[t].update(this);e!=i&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let e=t.value;if(e&&e.docViewUpdate)try{e.docViewUpdate(this)}catch(t){tG(this.state,t,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let e=null,i=this.scrollDOM,s=i.scrollTop*this.scaleY,{scrollAnchorPos:o,scrollAnchorHeight:n}=this.viewState;Math.abs(s-this.viewState.scrollTop)>1&&(n=-1),this.viewState.scrollAnchorHeight=-1;try{for(let t=0;;t++){if(n<0){if(D(i))o=-1,n=this.viewState.heightMap.height;else{let t=this.viewState.scrollAnchorAt(s);o=t.from,n=t.top}}this.updateState=1;let r=this.viewState.measure(this);if(!r&&!this.measureRequests.length&&null==this.viewState.scrollTarget)break;if(t>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let l=[];4&r||([this.measureRequests,l]=[l,this.measureRequests]);let h=l.map(t=>{try{return t.read(this)}catch(t){return tG(this.state,t),iC}}),a=ee.create(this,this.state,[]),c=!1;a.flags|=r,e?e.flags|=r:e=a,this.updateState=2,!a.empty&&(this.updatePlugins(a),this.inputState.update(a),this.updateAttrs(),(c=this.docView.update(a))&&this.docViewUpdate());for(let t=0;t<l.length;t++)if(h[t]!=iC)try{let e=l[t];e.write&&e.write(h[t],this)}catch(t){tG(this.state,t)}if(c&&this.docView.updateSelection(!0),!a.viewportChanged&&0==this.measureRequests.length){if(this.viewState.editorHeight){if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,n=-1;continue}{let t=(o<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(o).top)-n;if(t>1||t<-1){s+=t,i.scrollTop=s/this.scaleY,n=-1;continue}}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(e&&!e.empty)for(let t of this.state.facet(tN))t(e)}get themeClasses(){return ia+" "+(this.state.facet(ih)?id:ic)+" "+this.state.facet(il)}updateAttrs(){let t=iA(this,t0,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),e={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet(t_)?"true":"false",class:"cm-content",style:`${G.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(e["aria-readonly"]="true"),iA(this,t1,e);let i=this.observer.ignore(()=>{let i=to(this.contentDOM,this.contentAttrs,e),s=to(this.dom,this.editorAttrs,t);return i||s});return this.editorAttrs=t,this.contentAttrs=e,i}showAnnouncements(t){let e=!0;for(let i of t)for(let t of i.effects)t.is(iS.announce)&&(e&&(this.announceDOM.textContent=""),e=!1,this.announceDOM.appendChild(document.createElement("div")).textContent=t.value)}mountStyles(){this.styleModules=this.state.facet(t7);let t=this.state.facet(iS.cspNonce);h.V.mount(this.root,this.styleModules.concat(ig).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(2==this.updateState)throw Error("Reading the editor layout isn't allowed during an update");0==this.updateState&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t&&!(this.measureRequests.indexOf(t)>-1)){if(null!=t.key){for(let e=0;e<this.measureRequests.length;e++)if(this.measureRequests[e].key===t.key){this.measureRequests[e]=t;return}}this.measureRequests.push(t)}}plugin(t){let e=this.pluginMap.get(t);return(void 0===e||e&&e.plugin!=t)&&this.pluginMap.set(t,e=this.plugins.find(e=>e.plugin==t)||null),e&&e.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,e,i){return ep(this,t,eu(this,t,e,i))}moveByGroup(t,e){return ep(this,t,eu(this,t,e,e=>{var i;let s,o;return i=t.head,o=(s=this.state.charCategorizer(i))(e),t=>{let e=s(t);return o==l.D0.Space&&(o=e),o==e}}))}visualLineSide(t,e){let i=this.bidiSpans(t),s=this.textDirectionAt(t.from),o=i[e?i.length-1:0];return l.jT.cursor(o.side(e,s)+t.from,o.forward(!e,s)?1:-1)}moveToLineBoundary(t,e,i=!0){return function(t,e,i,s){let o=ed(t,e.head,e.assoc||-1),n=s&&o.type==tr.Text&&(t.lineWrapping||o.widgetLineBreaks)?t.coordsAtPos(e.assoc<0&&e.head>o.from?e.head-1:e.head):null;if(n){let e=t.dom.getBoundingClientRect(),s=t.textDirectionAt(o.from),r=t.posAtCoords({x:i==(s==tb.LTR)?e.right-1:e.left+1,y:(n.top+n.bottom)/2});if(null!=r)return l.jT.cursor(r,i?-1:1)}return l.jT.cursor(i?o.to:o.from,i?-1:1)}(this,t,e,i)}moveVertically(t,e,i){return ep(this,t,function(t,e,i,s){let o=e.head,n=i?1:-1;if(o==(i?t.state.doc.length:0))return l.jT.cursor(o,e.assoc);let r=e.goalColumn,h,a=t.contentDOM.getBoundingClientRect(),c=t.coordsAtPos(o,e.assoc||-1),d=t.documentTop;if(c)null==r&&(r=c.left-a.left),h=n<0?c.top:c.bottom;else{let e=t.viewState.lineBlockAt(o);null==r&&(r=Math.min(a.right-a.left,t.defaultCharacterWidth*(o-e.from))),h=(n<0?e.top:e.bottom)+d}let u=a.left+r,f=null!=s?s:t.viewState.heightOracle.textHeight>>1;for(let e=0;;e+=10){let i=h+(f+e)*n,s=ea(t,{x:u,y:i},!1,n);if(i<a.top||i>a.bottom||(n<0?s<o:s>o)){let e=t.docView.coordsForChar(s),o=!e||i<e.top?-1:1;return l.jT.cursor(s,o,void 0,r)}}}(this,t,e,i))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,e=0){return this.docView.posFromDOM(t,e)}posAtCoords(t,e=!0){return this.readMeasured(),ea(this,t,e)}coordsAtPos(t,e=1){this.readMeasured();let i=this.docView.coordsAt(t,e);if(!i||i.left==i.right)return i;let s=this.state.doc.lineAt(t),o=this.bidiSpans(s);return b(i,o[tO.find(o,t-s.from,-1,e)].dir==tb.LTR==e>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(tq)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>iM)return tR(t.length);let e=this.textDirectionAt(t.from),i;for(let s of this.bidiCache)if(s.from==t.from&&s.dir==e&&(s.fresh||function t(e,i){if(e.length!=i.length)return!1;for(let s=0;s<e.length;s++){let o=e[s],n=i[s];if(o.from!=n.from||o.to!=n.to||o.direction!=n.direction||!t(o.inner,n.inner))return!1}return!0}(s.isolates,i=t5(this,t))))return s.order;i||(i=t5(this,t));let s=function(t,e,i){if(!t)return[new tO(0,0,e==tx?1:0)];if(e==ty&&!i.length&&!tT.test(t))return tR(t.length);if(i.length)for(;t.length>tE.length;)tE[tE.length]=256;let s=[],o=e==ty?0:1;return function t(e,i,s,o,n,r,l){let h=i%2?2:1;(function(t,e,i,s,o){for(let n=0;n<=s.length;n++){let r=n?s[n-1].to:e,l=n<s.length?s[n].from:i,h=n?256:o;for(let e=r,i=h,s=h;e<l;e++){let o=tD(t.charCodeAt(e));512==o?o=i:8==o&&4==s&&(o=16),tE[e]=4==o?2:o,7&o&&(s=o),i=o}for(let t=r,e=h,s=h;t<l;t++){let o=tE[t];if(128==o)t<l-1&&e==tE[t+1]&&24&e?o=tE[t]=e:tE[t]=256;else if(64==o){let o=t+1;for(;o<l&&64==tE[o];)o++;let n=t&&8==e||o<i&&8==tE[o]?1==s?1:8:256;for(let e=t;e<o;e++)tE[e]=n;t=o-1}else 8==o&&1==s&&(tE[t]=1);e=o,7&o&&(s=o)}}})(e,n,r,o,h),function(t,e,i,s,o){let n=1==o?2:1;for(let r=0,l=0,h=0;r<=s.length;r++){let a=r?s[r-1].to:e,c=r<s.length?s[r].from:i;for(let e=a,i,s,r;e<c;e++)if(s=tk[i=t.charCodeAt(e)]){if(s<0){for(let t=l-3;t>=0;t-=3)if(tA[t+1]==-s){let i=tA[t+2],s=2&i?o:4&i?1&i?n:o:0;s&&(tE[e]=tE[tA[t]]=s),l=t;break}}else if(189==tA.length)break;else tA[l++]=e,tA[l++]=i,tA[l++]=h}else if(2==(r=tE[e])||1==r){let t=r==o;h=t?0:1;for(let e=l-3;e>=0;e-=3){let i=tA[e+2];if(2&i)break;if(t)tA[e+2]|=2;else{if(4&i)break;tA[e+2]|=4}}}}}(e,n,r,o,h),function(t,e,i,s){for(let o=0,n=s;o<=i.length;o++){let r=o?i[o-1].to:t,l=o<i.length?i[o].from:e;for(let h=r;h<l;){let r=tE[h];if(256==r){let r=h+1;for(;;)if(r==l){if(o==i.length)break;r=i[o++].to,l=o<i.length?i[o].from:e}else if(256==tE[r])r++;else break;let a=1==n,c=a==((r<e?tE[r]:s)==1)?a?1:2:s;for(let e=r,s=o,n=s?i[s-1].to:t;e>h;)e==n&&(e=i[--s].from,n=s?i[s-1].to:t),tE[--e]=c;h=r}else n=r,h++}}}(n,r,o,h),function e(i,s,o,n,r,l,h){let a=n%2?2:1;if(n%2==r%2)for(let c=s,d=0;c<o;){let s=!0,u=!1;if(d==l.length||c<l[d].from){let t=tE[c];t!=a&&(s=!1,u=16==t)}let f=s||1!=a?null:[],p=s?n:n+1,g=c;t:for(;;)if(d<l.length&&g==l[d].from){if(u)break;let e=l[d];if(!s)for(let t=e.to,i=d+1;;){if(t==o)break t;if(i<l.length&&l[i].from==t)t=l[i++].to;else if(tE[t]==a)break t;else break}d++,f?f.push(e):(e.from>c&&h.push(new tO(c,e.from,p)),t(i,e.direction==ty!=!(p%2)?n+1:n,r,e.inner,e.from,e.to,h),c=e.to),g=e.to}else if(g==o||(s?tE[g]!=a:tE[g]==a))break;else g++;f?e(i,c,g,n+1,r,f,h):c<g&&h.push(new tO(c,g,p)),c=g}else for(let c=o,d=l.length;c>s;){let o=!0,u=!1;if(!d||c>l[d-1].to){let t=tE[c-1];t!=a&&(o=!1,u=16==t)}let f=o||1!=a?null:[],p=o?n:n+1,g=c;t:for(;;)if(d&&g==l[d-1].to){if(u)break;let e=l[--d];if(!o)for(let t=e.from,i=d;;){if(t==s)break t;if(i&&l[i-1].to==t)t=l[--i].from;else if(tE[t-1]==a)break t;else break}f?f.push(e):(e.to<c&&h.push(new tO(e.to,c,p)),t(i,e.direction==ty!=!(p%2)?n+1:n,r,e.inner,e.from,e.to,h),c=e.from),g=e.from}else if(g==s||(o?tE[g-1]!=a:tE[g-1]==a))break;else g--;f?e(i,g,c,n+1,r,f,h):g<c&&h.push(new tO(g,c,p)),c=g}}(e,n,r,i,s,o,l)}(t,o,o,i,0,t.length,s),s}(t.text,e,i);return this.bidiCache.push(new ik(t.from,t.to,e,i,!0,s)),s}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||G.safari&&(null===(t=this.inputState)||void 0===t?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{M(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((9==t.nodeType?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){for(let t of(this.root.activeElement==this.contentDOM&&this.contentDOM.blur(),this.plugins))t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,e={}){return tX.of(new t$("number"==typeof t?l.jT.cursor(t):t,e.y,e.x,e.yMargin,e.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:e}=this.scrollDOM,i=this.viewState.scrollAnchorAt(t);return tX.of(new t$(l.jT.cursor(i.from),"start","start",i.top-t,e,!0))}setTabFocusMode(t){null==t?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:"boolean"==typeof t?this.inputState.tabFocusMode=t?0:-1:0!=this.inputState.tabFocusMode&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return tZ.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return tZ.define(()=>({}),{eventObservers:t})}static theme(t,e){let i=h.V.newName(),s=[il.of(i),t7.of(ip(`.${i}`,t))];return e&&e.dark&&s.push(ih.of(!0)),s}static baseTheme(t){return l.Wl.lowest(t7.of(ip("."+ia,t,iu)))}static findFromDOM(t){var e;let i=t.querySelector(".cm-content"),s=i&&B.get(i)||B.get(t);return(null===(e=null==s?void 0:s.rootView)||void 0===e?void 0:e.view)||null}}iS.styleModule=t7,iS.inputHandler=tW,iS.clipboardInputFilter=tz,iS.clipboardOutputFilter=tI,iS.scrollHandler=tj,iS.focusChangeEffect=tF,iS.perLineTextDirection=tq,iS.exceptionSink=tV,iS.updateListener=tN,iS.editable=t_,iS.mouseSelectionStyle=tH,iS.dragMovesSelection=tP,iS.clickAddsSelectionRange=tL,iS.decorations=t2,iS.outerDecorations=t8,iS.atomicRanges=t3,iS.bidiIsolatedRanges=t9,iS.scrollMargins=t4,iS.darkTheme=ih,iS.cspNonce=l.r$.define({combine:t=>t.length?t[0]:""}),iS.contentAttributes=t1,iS.editorAttributes=t0,iS.lineWrapping=iS.contentAttributes.of({class:"cm-lineWrapping"}),iS.announce=l.Py.define();let iM=4096,iC={};class ik{constructor(t,e,i,s,o,n){this.from=t,this.to=e,this.dir=i,this.isolates=s,this.fresh=o,this.order=n}static update(t,e){if(e.empty&&!t.some(t=>t.fresh))return t;let i=[],s=t.length?t[t.length-1].dir:tb.LTR;for(let o=Math.max(0,t.length-10);o<t.length;o++){let n=t[o];n.dir!=s||e.touchesRange(n.from,n.to)||i.push(new ik(e.mapPos(n.from,1),e.mapPos(n.to,-1),n.dir,n.isolates,!1,n.order))}return i}}function iA(t,e,i){for(let s=t.state.facet(e),o=s.length-1;o>=0;o--){let e=s[o],n="function"==typeof e?e(t):e;n&&te(n,i)}return i}let iD=G.mac?"mac":G.windows?"win":G.linux?"linux":"key";function iT(t,e,i){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),!1!==i&&e.shiftKey&&(t="Shift-"+t),t}let iO=l.Wl.default(iS.domEventHandlers({keydown:(t,e)=>iV(iB(e.state),t,e,"editor")})),iE=l.r$.define({enables:iO}),iR=new WeakMap;function iB(t){let e=t.facet(iE),i=iR.get(e);return i||iR.set(e,i=function(t,e=iD){let i=Object.create(null),s=Object.create(null),o=(t,e)=>{let i=s[t];if(null==i)s[t]=e;else if(i!=e)throw Error("Key binding "+t+" is used both as a regular binding and as a multi-stroke prefix")},n=(t,s,n,r,l)=>{var h,a;let c=i[t]||(i[t]=Object.create(null)),d=s.split(/ (?!$)/).map(t=>(function(t,e){let i,s,o,n;let r=t.split(/-(?!$)/),l=r[r.length-1];"Space"==l&&(l=" ");for(let t=0;t<r.length-1;++t){let l=r[t];if(/^(cmd|meta|m)$/i.test(l))n=!0;else if(/^a(lt)?$/i.test(l))i=!0;else if(/^(c|ctrl|control)$/i.test(l))s=!0;else if(/^s(hift)?$/i.test(l))o=!0;else if(/^mod$/i.test(l))"mac"==e?n=!0:s=!0;else throw Error("Unrecognized modifier name: "+l)}return i&&(l="Alt-"+l),s&&(l="Ctrl-"+l),n&&(l="Meta-"+l),o&&(l="Shift-"+l),l})(t,e));for(let e=1;e<d.length;e++){let i=d.slice(0,e).join(" ");o(i,!0),c[i]||(c[i]={preventDefault:!0,stopPropagation:!1,run:[e=>{let s=iP={view:e,prefix:i,scope:t};return setTimeout(()=>{iP==s&&(iP=null)},4e3),!0}]})}let u=d.join(" ");o(u,!1);let f=c[u]||(c[u]={preventDefault:!1,stopPropagation:!1,run:(null===(a=null===(h=c._any)||void 0===h?void 0:h.run)||void 0===a?void 0:a.slice())||[]});n&&f.run.push(n),r&&(f.preventDefault=!0),l&&(f.stopPropagation=!0)};for(let s of t){let t=s.scope?s.scope.split(" "):["editor"];if(s.any)for(let e of t){let t=i[e]||(i[e]=Object.create(null));t._any||(t._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:o}=s;for(let e in t)t[e].run.push(t=>o(t,iH))}let o=s[e]||s.key;if(o)for(let e of t)n(e,o,s.run,s.preventDefault,s.stopPropagation),s.shift&&n(e,"Shift-"+o,s.shift,s.preventDefault,s.stopPropagation)}return i}(e.reduce((t,e)=>t.concat(e),[]))),i}function iL(t,e,i){return iV(iB(t.state),e,t,i)}let iP=null,iH=null;function iV(t,e,i,s){iH=e;let o=(0,a.YG)(e),n=(0,l.gm)(o,0),r=(0,l.nZ)(n)==o.length&&" "!=o,h="",c=!1,d=!1,u=!1;iP&&iP.view==i&&iP.scope==s&&(h=iP.prefix+" ",0>eC.indexOf(e.keyCode)&&(d=!0,iP=null));let f=new Set,p=t=>{if(t){for(let e of t.run)if(!f.has(e)&&(f.add(e),e(i)))return t.stopPropagation&&(u=!0),!0;t.preventDefault&&(t.stopPropagation&&(u=!0),d=!0)}return!1},g=t[s],m,v;return g&&(p(g[h+iT(o,e,!r)])?c=!0:r&&(e.altKey||e.metaKey||e.ctrlKey)&&!(G.windows&&e.ctrlKey&&e.altKey)&&!(G.mac&&e.altKey&&!e.ctrlKey)&&(m=a.ue[e.keyCode])&&m!=o?p(g[h+iT(m,e,!0)])?c=!0:e.shiftKey&&(v=a.uY[e.keyCode])!=o&&v!=m&&p(g[h+iT(v,e,!1)])&&(c=!0):r&&e.shiftKey&&p(g[h+iT(o,e,!0)])&&(c=!0),!c&&p(g._any)&&(c=!0)),d&&(c=!0),c&&u&&e.stopPropagation(),iH=null,c}class iN{constructor(t,e,i,s,o){this.className=t,this.left=e,this.top=i,this.width=s,this.height=o}draw(){let t=document.createElement("div");return t.className=this.className,this.adjust(t),t}update(t,e){return e.className==this.className&&(this.adjust(t),!0)}adjust(t){t.style.left=this.left+"px",t.style.top=this.top+"px",null!=this.width&&(t.style.width=this.width+"px"),t.style.height=this.height+"px"}eq(t){return this.left==t.left&&this.top==t.top&&this.width==t.width&&this.height==t.height&&this.className==t.className}static forRange(t,e,i){if(!i.empty)return function(t,e,i){if(i.to<=t.viewport.from||i.from>=t.viewport.to)return[];let s=Math.max(i.from,t.viewport.from),o=Math.min(i.to,t.viewport.to),n=t.textDirection==tb.LTR,r=t.contentDOM,l=r.getBoundingClientRect(),h=iW(t),a=r.querySelector(".cm-line"),c=a&&window.getComputedStyle(a),d=l.left+(c?parseInt(c.paddingLeft)+Math.min(0,parseInt(c.textIndent)):0),u=l.right-(c?parseInt(c.paddingRight):0),f=ed(t,s,1),p=ed(t,o,-1),g=f.type==tr.Text?f:null,m=p.type==tr.Text?p:null;if(g&&(t.lineWrapping||f.widgetLineBreaks)&&(g=iF(t,s,1,g)),m&&(t.lineWrapping||p.widgetLineBreaks)&&(m=iF(t,o,-1,m)),g&&m&&g.from==m.from&&g.to==m.to)return w(b(i.from,i.to,g));{let e=g?b(i.from,null,g):y(f,!1),s=m?b(null,i.to,m):y(p,!0),o=[];return(g||f).to<(m||p).from-(g&&m?1:0)||f.widgetLineBreaks>1&&e.bottom+t.defaultLineHeight/2<s.top?o.push(v(d,e.bottom,u,s.top)):e.bottom<s.top&&t.elementAtHeight((e.bottom+s.top)/2).type==tr.Text&&(e.bottom=s.top=(e.bottom+s.top)/2),w(e).concat(o).concat(w(s))}function v(t,i,s,o){return new iN(e,t-h.left,i-h.top,s-t,o-i)}function w({top:t,bottom:e,horizontal:i}){let s=[];for(let o=0;o<i.length;o+=2)s.push(v(i[o],t,i[o+1],e));return s}function b(e,i,s){let o=1e9,r=-1e9,l=[];function h(e,i,h,a,c){let f=t.coordsAtPos(e,e==s.to?-2:2),p=t.coordsAtPos(h,h==s.from?2:-2);f&&p&&(o=Math.min(f.top,p.top,o),r=Math.max(f.bottom,p.bottom,r),c==tb.LTR?l.push(n&&i?d:f.left,n&&a?u:p.right):l.push(!n&&a?d:p.left,!n&&i?u:f.right))}let a=null!=e?e:s.from,c=null!=i?i:s.to;for(let s of t.visibleRanges)if(s.to>a&&s.from<c)for(let o=Math.max(s.from,a),n=Math.min(s.to,c);;){let s=t.state.doc.lineAt(o);for(let r of t.bidiSpans(s)){let t=r.from+s.from,l=r.to+s.from;if(t>=n)break;l>o&&h(Math.max(t,o),null==e&&t<=a,Math.min(l,n),null==i&&l>=c,r.dir)}if((o=s.to+1)>=n)break}return 0==l.length&&h(a,null==e,c,null==i,t.textDirection),{top:o,bottom:r,horizontal:l}}function y(t,e){let i=l.top+(e?t.top:t.bottom);return{top:i,bottom:i,horizontal:[]}}}(t,e,i);{let s=t.coordsAtPos(i.head,i.assoc||1);if(!s)return[];let o=iW(t);return[new iN(e,s.left-o.left,s.top-o.top,null,s.bottom-s.top)]}}}function iW(t){let e=t.scrollDOM.getBoundingClientRect();return{left:(t.textDirection==tb.LTR?e.left:e.right-t.scrollDOM.clientWidth*t.scaleX)-t.scrollDOM.scrollLeft*t.scaleX,top:e.top-t.scrollDOM.scrollTop*t.scaleY}}function iF(t,e,i,s){let o=t.coordsAtPos(e,2*i);if(!o)return s;let n=t.dom.getBoundingClientRect(),r=(o.top+o.bottom)/2,l=t.posAtCoords({x:n.left+1,y:r}),h=t.posAtCoords({x:n.right-1,y:r});return null==l||null==h?s:{from:Math.max(s.from,Math.min(l,h)),to:Math.min(s.to,Math.max(l,h))}}class iz{constructor(t,e){this.view=t,this.layer=e,this.drawn=[],this.scaleX=1,this.scaleY=1,this.measureReq={read:this.measure.bind(this),write:this.draw.bind(this)},this.dom=t.scrollDOM.appendChild(document.createElement("div")),this.dom.classList.add("cm-layer"),e.above&&this.dom.classList.add("cm-layer-above"),e.class&&this.dom.classList.add(e.class),this.scale(),this.dom.setAttribute("aria-hidden","true"),this.setOrder(t.state),t.requestMeasure(this.measureReq),e.mount&&e.mount(this.dom,t)}update(t){t.startState.facet(iI)!=t.state.facet(iI)&&this.setOrder(t.state),(this.layer.update(t,this.dom)||t.geometryChanged)&&(this.scale(),t.view.requestMeasure(this.measureReq))}docViewUpdate(t){!1!==this.layer.updateOnDocViewUpdate&&t.requestMeasure(this.measureReq)}setOrder(t){let e=0,i=t.facet(iI);for(;e<i.length&&i[e]!=this.layer;)e++;this.dom.style.zIndex=String((this.layer.above?150:-1)-e)}measure(){return this.layer.markers(this.view)}scale(){let{scaleX:t,scaleY:e}=this.view;(t!=this.scaleX||e!=this.scaleY)&&(this.scaleX=t,this.scaleY=e,this.dom.style.transform=`scale(${1/t}, ${1/e})`)}draw(t){if(t.length!=this.drawn.length||t.some((t,e)=>{var i;return i=this.drawn[e],!(t.constructor==i.constructor&&t.eq(i))})){let e=this.dom.firstChild,i=0;for(let s of t)s.update&&e&&s.constructor&&this.drawn[i].constructor&&s.update(e,this.drawn[i])?(e=e.nextSibling,i++):this.dom.insertBefore(s.draw(),e);for(;e;){let t=e.nextSibling;e.remove(),e=t}this.drawn=t}}destroy(){this.layer.destroy&&this.layer.destroy(this.dom,this.view),this.dom.remove()}}let iI=l.r$.define();function iq(t){return[tZ.define(e=>new iz(e,t)),iI.of(t)]}let iK=l.r$.define({combine:t=>(0,l.BO)(t,{cursorBlinkRate:1200,drawRangeCursor:!0},{cursorBlinkRate:(t,e)=>Math.min(t,e),drawRangeCursor:(t,e)=>t||e})});function ij(t={}){return[iK.of(t),iX,iG,i_,tK.of(!0)]}function i$(t){return t.startState.facet(iK)!=t.state.facet(iK)}let iX=iq({above:!0,markers(t){let{state:e}=t,i=e.facet(iK),s=[];for(let o of e.selection.ranges){let n=o==e.selection.main;if(o.empty||i.drawRangeCursor){let e=n?"cm-cursor cm-cursor-primary":"cm-cursor cm-cursor-secondary",i=o.empty?o:l.jT.cursor(o.head,o.head>o.anchor?-1:1);for(let o of iN.forRange(t,e,i))s.push(o)}}return s},update(t,e){t.transactions.some(t=>t.selection)&&(e.style.animationName="cm-blink"==e.style.animationName?"cm-blink2":"cm-blink");let i=i$(t);return i&&iY(t.state,e),t.docChanged||t.selectionSet||i},mount(t,e){iY(e.state,t)},class:"cm-cursorLayer"});function iY(t,e){e.style.animationDuration=t.facet(iK).cursorBlinkRate+"ms"}let iG=iq({above:!1,markers:t=>t.state.selection.ranges.map(e=>e.empty?[]:iN.forRange(t,"cm-selectionBackground",e)).reduce((t,e)=>t.concat(e)),update:(t,e)=>t.docChanged||t.selectionSet||t.viewportChanged||i$(t),class:"cm-selectionLayer"}),i_=l.Wl.highest(iS.theme({".cm-line":{"& ::selection, &::selection":{backgroundColor:"transparent !important"},caretColor:"transparent !important"},".cm-content":{caretColor:"transparent !important","& :focus":{caretColor:"initial !important","&::selection, & ::selection":{backgroundColor:"Highlight !important"}}}})),iU=l.Py.define({map:(t,e)=>null==t?null:e.mapPos(t)}),iQ=l.QQ.define({create:()=>null,update:(t,e)=>(null!=t&&(t=e.changes.mapPos(t)),e.effects.reduce((t,e)=>e.is(iU)?e.value:t,t))}),iZ=tZ.fromClass(class{constructor(t){this.view=t,this.cursor=null,this.measureReq={read:this.readPos.bind(this),write:this.drawCursor.bind(this)}}update(t){var e;let i=t.state.field(iQ);null==i?null!=this.cursor&&(null===(e=this.cursor)||void 0===e||e.remove(),this.cursor=null):(this.cursor||(this.cursor=this.view.scrollDOM.appendChild(document.createElement("div")),this.cursor.className="cm-dropCursor"),(t.startState.field(iQ)!=i||t.docChanged||t.geometryChanged)&&this.view.requestMeasure(this.measureReq))}readPos(){let{view:t}=this,e=t.state.field(iQ),i=null!=e&&t.coordsAtPos(e);if(!i)return null;let s=t.scrollDOM.getBoundingClientRect();return{left:i.left-s.left+t.scrollDOM.scrollLeft*t.scaleX,top:i.top-s.top+t.scrollDOM.scrollTop*t.scaleY,height:i.bottom-i.top}}drawCursor(t){if(this.cursor){let{scaleX:e,scaleY:i}=this.view;t?(this.cursor.style.left=t.left/e+"px",this.cursor.style.top=t.top/i+"px",this.cursor.style.height=t.height/i+"px"):this.cursor.style.left="-100000px"}}destroy(){this.cursor&&this.cursor.remove()}setDropPos(t){this.view.state.field(iQ)!=t&&this.view.dispatch({effects:iU.of(t)})}},{eventObservers:{dragover(t){this.setDropPos(this.view.posAtCoords({x:t.clientX,y:t.clientY}))},dragleave(t){t.target!=this.view.contentDOM&&this.view.contentDOM.contains(t.relatedTarget)||this.setDropPos(null)},dragend(){this.setDropPos(null)},drop(){this.setDropPos(null)}}});function iJ(){return[iQ,iZ]}function i0(t,e,i,s,o){e.lastIndex=0;for(let n=t.iterRange(i,s),r=i,l;!n.next().done;r+=n.value.length)if(!n.lineBreak)for(;l=e.exec(n.value);)o(r+l.index,l)}class i1{constructor(t){let{regexp:e,decoration:i,decorate:s,boundary:o,maxLength:n=1e3}=t;if(!e.global)throw RangeError("The regular expression given to MatchDecorator should have its 'g' flag set");if(this.regexp=e,s)this.addMatch=(t,e,i,o)=>s(o,i,i+t[0].length,t,e);else if("function"==typeof i)this.addMatch=(t,e,s,o)=>{let n=i(t,e,s);n&&o(s,s+t[0].length,n)};else if(i)this.addMatch=(t,e,s,o)=>o(s,s+t[0].length,i);else throw RangeError("Either 'decorate' or 'decoration' should be provided to MatchDecorator");this.boundary=o,this.maxLength=n}createDeco(t){let e=new l.f_,i=e.add.bind(e);for(let{from:e,to:s}of function(t,e){let i=t.visibleRanges;if(1==i.length&&i[0].from==t.viewport.from&&i[0].to==t.viewport.to)return i;let s=[];for(let{from:o,to:n}of i)o=Math.max(t.state.doc.lineAt(o).from,o-e),n=Math.min(t.state.doc.lineAt(n).to,n+e),s.length&&s[s.length-1].to>=o?s[s.length-1].to=n:s.push({from:o,to:n});return s}(t,this.maxLength))i0(t.state.doc,this.regexp,e,s,(e,s)=>this.addMatch(s,t,e,i));return e.finish()}updateDeco(t,e){let i=1e9,s=-1;return(t.docChanged&&t.changes.iterChanges((e,o,n,r)=>{r>=t.view.viewport.from&&n<=t.view.viewport.to&&(i=Math.min(n,i),s=Math.max(r,s))}),t.viewportMoved||s-i>1e3)?this.createDeco(t.view):s>-1?this.updateRange(t.view,e.map(t.changes),i,s):e}updateRange(t,e,i,s){for(let o of t.visibleRanges){let n=Math.max(o.from,i),r=Math.min(o.to,s);if(r>=n){let i=t.state.doc.lineAt(n),s=i.to<r?t.state.doc.lineAt(r):i,l=Math.max(o.from,i.from),h=Math.min(o.to,s.to);if(this.boundary){for(;n>i.from;n--)if(this.boundary.test(i.text[n-1-i.from])){l=n;break}for(;r<s.to;r++)if(this.boundary.test(s.text[r-s.from])){h=r;break}}let a=[],c,d=(t,e,i)=>a.push(i.range(t,e));if(i==s)for(this.regexp.lastIndex=l-i.from;(c=this.regexp.exec(i.text))&&c.index<h-i.from;)this.addMatch(c,t,c.index+i.from,d);else i0(t.state.doc,this.regexp,l,h,(e,i)=>this.addMatch(i,t,e,d));e=e.update({filterFrom:l,filterTo:h,filter:(t,e)=>t<l||e>h,add:a})}}return e}}let i2=null!=/x/.unicode?"gu":"g",i8=RegExp("[\x00-\b\n-\x1f\x7f-\x9f\xad؜​‎‏\u2028\u2029‭‮⁦⁧⁩\uFEFF￹-￼]",i2),i3={0:"null",7:"bell",8:"backspace",10:"newline",11:"vertical tab",13:"carriage return",27:"escape",8203:"zero width space",8204:"zero width non-joiner",8205:"zero width joiner",8206:"left-to-right mark",8207:"right-to-left mark",8232:"line separator",8237:"left-to-right override",8238:"right-to-left override",8294:"left-to-right isolate",8295:"right-to-left isolate",8297:"pop directional isolate",8233:"paragraph separator",65279:"zero width no-break space",65532:"object replacement"},i9=null,i5=l.r$.define({combine(t){let e=(0,l.BO)(t,{render:null,specialChars:i8,addSpecialChars:null});return(e.replaceTabs=!function(){var t;if(null==i9&&"undefined"!=typeof document&&document.body){let e=document.body.style;i9=(null!==(t=e.tabSize)&&void 0!==t?t:e.MozTabSize)!=null}return i9||!1}())&&(e.specialChars=RegExp("	|"+e.specialChars.source,i2)),e.addSpecialChars&&(e.specialChars=RegExp(e.specialChars.source+"|"+e.addSpecialChars.source,i2)),e}});function i4(t={}){return[i5.of(t),i6||(i6=tZ.fromClass(class{constructor(t){this.view=t,this.decorations=tl.none,this.decorationCache=Object.create(null),this.decorator=this.makeDecorator(t.state.facet(i5)),this.decorations=this.decorator.createDeco(t)}makeDecorator(t){return new i1({regexp:t.specialChars,decoration:(e,i,s)=>{let{doc:o}=i.state,n=(0,l.gm)(e[0],0);if(9==n){let t=o.lineAt(s),e=i.state.tabSize,n=(0,l.IS)(t.text,e,s-t.from);return tl.replace({widget:new st((e-n%e)*this.view.defaultCharacterWidth/this.view.scaleX)})}return this.decorationCache[n]||(this.decorationCache[n]=tl.replace({widget:new i7(t,n)}))},boundary:t.replaceTabs?void 0:/[^]/})}update(t){let e=t.state.facet(i5);t.startState.facet(i5)!=e?(this.decorator=this.makeDecorator(e),this.decorations=this.decorator.createDeco(t.view)):this.decorations=this.decorator.updateDeco(t,this.decorations)}},{decorations:t=>t.decorations}))]}let i6=null;class i7 extends tn{constructor(t,e){super(),this.options=t,this.code=e}eq(t){return t.code==this.code}toDOM(t){var e;let i=(e=this.code)>=32?"•":10==e?"␤":String.fromCharCode(9216+e),s=t.state.phrase("Control character")+" "+(i3[this.code]||"0x"+this.code.toString(16)),o=this.options.render&&this.options.render(this.code,s,i);if(o)return o;let n=document.createElement("span");return n.textContent=i,n.title=s,n.setAttribute("aria-label",s),n.className="cm-specialChar",n}ignoreEvent(){return!1}}class st extends tn{constructor(t){super(),this.width=t}eq(t){return t.width==this.width}toDOM(){let t=document.createElement("span");return t.textContent="	",t.className="cm-tab",t.style.width=this.width+"px",t}ignoreEvent(){return!1}}function se(){return ss}let si=tl.line({class:"cm-activeLine"}),ss=tZ.fromClass(class{constructor(t){this.decorations=this.getDeco(t)}update(t){(t.docChanged||t.selectionSet)&&(this.decorations=this.getDeco(t.view))}getDeco(t){let e=-1,i=[];for(let s of t.state.selection.ranges){let o=t.lineBlockAt(s.head);o.from>e&&(i.push(si.range(o.from)),e=o.from)}return tl.set(i)}},{decorations:t=>t.decorations});class so extends tn{constructor(t){super(),this.content=t}toDOM(t){let e=document.createElement("span");return e.className="cm-placeholder",e.style.pointerEvents="none",e.appendChild("string"==typeof this.content?document.createTextNode(this.content):"function"==typeof this.content?this.content(t):this.content.cloneNode(!0)),e.setAttribute("aria-hidden","true"),e}coordsAt(t){let e=t.firstChild?f(t.firstChild):[];if(!e.length)return null;let i=window.getComputedStyle(t.parentNode),s=b(e[0],"rtl"!=i.direction),o=parseInt(i.lineHeight);return s.bottom-s.top>1.5*o?{left:s.left,right:s.right,top:s.top,bottom:s.top+o}:s}ignoreEvent(){return!1}}function sn(t){let e=tZ.fromClass(class{constructor(e){this.view=e,this.placeholder=t?tl.set([tl.widget({widget:new so(t),side:1}).range(0)]):tl.none}get decorations(){return this.view.state.doc.length?tl.none:this.placeholder}},{decorations:t=>t.decorations});return"string"==typeof t?[e,iS.contentAttributes.of({"aria-placeholder":t})]:e}function sr(t,e){var i;let s,o=t.posAtCoords({x:e.clientX,y:e.clientY},!1),n=t.state.doc.lineAt(o),r=o-n.from,h=r>2e3?-1:r==n.length?(i=e.clientX,(s=t.coordsAtPos(t.viewport.from))?Math.round(Math.abs((s.left-i)/t.defaultCharacterWidth)):-1):(0,l.IS)(n.text,t.state.tabSize,o-n.from);return{line:n.number,col:h,off:r}}function sl(t){let e=(null==t?void 0:t.eventFilter)||(t=>t.altKey&&0==t.button);return iS.mouseSelectionStyle.of((t,i)=>{let s,o;return e(i)?(s=sr(t,i),o=t.state.selection,s?{update(t){if(t.docChanged){let e=t.changes.mapPos(t.startState.doc.line(s.line).from),i=t.state.doc.lineAt(e);s={line:i.number,col:s.col,off:Math.min(s.off,i.length)},o=o.map(t.changes)}},get(e,i,n){let r=sr(t,e);if(!r)return o;let h=function(t,e,i){let s=Math.min(e.line,i.line),o=Math.max(e.line,i.line),n=[];if(e.off>2e3||i.off>2e3||e.col<0||i.col<0){let r=Math.min(e.off,i.off),h=Math.max(e.off,i.off);for(let e=s;e<=o;e++){let i=t.doc.line(e);i.length<=h&&n.push(l.jT.range(i.from+r,i.to+h))}}else{let r=Math.min(e.col,i.col),h=Math.max(e.col,i.col);for(let e=s;e<=o;e++){let i=t.doc.line(e),s=(0,l.Gz)(i.text,r,t.tabSize,!0);if(s<0)n.push(l.jT.cursor(i.to));else{let e=(0,l.Gz)(i.text,h,t.tabSize);n.push(l.jT.range(i.from+s,i.from+e))}}}return n}(t.state,s,r);return h.length?n?l.jT.create(h.concat(o.ranges)):l.jT.create(h):o}}:null):null})}let sh={Alt:[18,t=>!!t.altKey],Control:[17,t=>!!t.ctrlKey],Shift:[16,t=>!!t.shiftKey],Meta:[91,t=>!!t.metaKey]},sa={style:"cursor: crosshair"};function sc(t={}){let[e,i]=sh[t.key||"Alt"],s=tZ.fromClass(class{constructor(t){this.view=t,this.isDown=!1}set(t){this.isDown!=t&&(this.isDown=t,this.view.update([]))}},{eventObservers:{keydown(t){this.set(t.keyCode==e||i(t))},keyup(t){t.keyCode!=e&&i(t)||this.set(!1)},mousemove(t){this.set(i(t))}}});return[s,iS.contentAttributes.of(t=>{var e;return(null===(e=t.plugin(s))||void 0===e?void 0:e.isDown)?sa:null})]}let sd="-10000px";class su{constructor(t,e,i,s){this.facet=e,this.createTooltipView=i,this.removeTooltipView=s,this.input=t.state.facet(e),this.tooltips=this.input.filter(t=>t);let o=null;this.tooltipViews=this.tooltips.map(t=>o=i(t,o))}update(t,e){var i;let s=t.state.facet(this.facet),o=s.filter(t=>t);if(s===this.input){for(let e of this.tooltipViews)e.update&&e.update(t);return!1}let n=[],r=e?[]:null;for(let i=0;i<o.length;i++){let s=o[i],l=-1;if(s){for(let t=0;t<this.tooltips.length;t++){let e=this.tooltips[t];e&&e.create==s.create&&(l=t)}if(l<0)n[i]=this.createTooltipView(s,i?n[i-1]:null),r&&(r[i]=!!s.above);else{let s=n[i]=this.tooltipViews[l];r&&(r[i]=e[l]),s.update&&s.update(t)}}}for(let t of this.tooltipViews)0>n.indexOf(t)&&(this.removeTooltipView(t),null===(i=t.destroy)||void 0===i||i.call(t));return e&&(r.forEach((t,i)=>e[i]=t),e.length=r.length),this.input=s,this.tooltips=o,this.tooltipViews=n,!0}}function sf(t){let e=t.dom.ownerDocument.documentElement;return{top:0,left:0,bottom:e.clientHeight,right:e.clientWidth}}let sp=l.r$.define({combine:t=>{var e,i,s;return{position:G.ios?"absolute":(null===(e=t.find(t=>t.position))||void 0===e?void 0:e.position)||"fixed",parent:(null===(i=t.find(t=>t.parent))||void 0===i?void 0:i.parent)||null,tooltipSpace:(null===(s=t.find(t=>t.tooltipSpace))||void 0===s?void 0:s.tooltipSpace)||sf}}}),sg=new WeakMap,sm=tZ.fromClass(class{constructor(t){this.view=t,this.above=[],this.inView=!0,this.madeAbsolute=!1,this.lastTransaction=0,this.measureTimeout=-1;let e=t.state.facet(sp);this.position=e.position,this.parent=e.parent,this.classes=t.themeClasses,this.createContainer(),this.measureReq={read:this.readMeasure.bind(this),write:this.writeMeasure.bind(this),key:this},this.resizeObserver="function"==typeof ResizeObserver?new ResizeObserver(()=>this.measureSoon()):null,this.manager=new su(t,sy,(t,e)=>this.createTooltip(t,e),t=>{this.resizeObserver&&this.resizeObserver.unobserve(t.dom),t.dom.remove()}),this.above=this.manager.tooltips.map(t=>!!t.above),this.intersectionObserver="function"==typeof IntersectionObserver?new IntersectionObserver(t=>{Date.now()>this.lastTransaction-50&&t.length>0&&t[t.length-1].intersectionRatio<1&&this.measureSoon()},{threshold:[1]}):null,this.observeIntersection(),t.win.addEventListener("resize",this.measureSoon=this.measureSoon.bind(this)),this.maybeMeasure()}createContainer(){this.parent?(this.container=document.createElement("div"),this.container.style.position="relative",this.container.className=this.view.themeClasses,this.parent.appendChild(this.container)):this.container=this.view.dom}observeIntersection(){if(this.intersectionObserver)for(let t of(this.intersectionObserver.disconnect(),this.manager.tooltipViews))this.intersectionObserver.observe(t.dom)}measureSoon(){this.measureTimeout<0&&(this.measureTimeout=setTimeout(()=>{this.measureTimeout=-1,this.maybeMeasure()},50))}update(t){t.transactions.length&&(this.lastTransaction=Date.now());let e=this.manager.update(t,this.above);e&&this.observeIntersection();let i=e||t.geometryChanged,s=t.state.facet(sp);if(s.position!=this.position&&!this.madeAbsolute){for(let t of(this.position=s.position,this.manager.tooltipViews))t.dom.style.position=this.position;i=!0}if(s.parent!=this.parent){for(let t of(this.parent&&this.container.remove(),this.parent=s.parent,this.createContainer(),this.manager.tooltipViews))this.container.appendChild(t.dom);i=!0}else this.parent&&this.view.themeClasses!=this.classes&&(this.classes=this.container.className=this.view.themeClasses);i&&this.maybeMeasure()}createTooltip(t,e){let i=t.create(this.view),s=e?e.dom:null;if(i.dom.classList.add("cm-tooltip"),t.arrow&&!i.dom.querySelector(".cm-tooltip > .cm-tooltip-arrow")){let t=document.createElement("div");t.className="cm-tooltip-arrow",i.dom.appendChild(t)}return i.dom.style.position=this.position,i.dom.style.top=sd,i.dom.style.left="0px",this.container.insertBefore(i.dom,s),i.mount&&i.mount(this.view),this.resizeObserver&&this.resizeObserver.observe(i.dom),i}destroy(){var t,e,i;for(let e of(this.view.win.removeEventListener("resize",this.measureSoon),this.manager.tooltipViews))e.dom.remove(),null===(t=e.destroy)||void 0===t||t.call(e);this.parent&&this.container.remove(),null===(e=this.resizeObserver)||void 0===e||e.disconnect(),null===(i=this.intersectionObserver)||void 0===i||i.disconnect(),clearTimeout(this.measureTimeout)}readMeasure(){let t=1,e=1,i=!1;if("fixed"==this.position&&this.manager.tooltipViews.length){let{dom:t}=this.manager.tooltipViews[0];if(G.gecko)i=t.offsetParent!=this.container.ownerDocument.body;else if(t.style.top==sd&&"0px"==t.style.left){let e=t.getBoundingClientRect();i=Math.abs(e.top+1e4)>1||Math.abs(e.left)>1}}if(i||"absolute"==this.position){if(this.parent){let i=this.parent.getBoundingClientRect();i.width&&i.height&&(t=i.width/this.parent.offsetWidth,e=i.height/this.parent.offsetHeight)}else({scaleX:t,scaleY:e}=this.view.viewState)}let s=this.view.scrollDOM.getBoundingClientRect(),o=t6(this.view);return{visible:{left:s.left+o.left,top:s.top+o.top,right:s.right-o.right,bottom:s.bottom-o.bottom},parent:this.parent?this.container.getBoundingClientRect():this.view.dom.getBoundingClientRect(),pos:this.manager.tooltips.map((t,e)=>{let i=this.manager.tooltipViews[e];return i.getCoords?i.getCoords(t.pos):this.view.coordsAtPos(t.pos)}),size:this.manager.tooltipViews.map(({dom:t})=>t.getBoundingClientRect()),space:this.view.state.facet(sp).tooltipSpace(this.view),scaleX:t,scaleY:e,makeAbsolute:i}}writeMeasure(t){var e;if(t.makeAbsolute)for(let t of(this.madeAbsolute=!0,this.position="absolute",this.manager.tooltipViews))t.dom.style.position="absolute";let{visible:i,space:s,scaleX:o,scaleY:n}=t,r=[];for(let l=0;l<this.manager.tooltips.length;l++){let h=this.manager.tooltips[l],a=this.manager.tooltipViews[l],{dom:c}=a,d=t.pos[l],u=t.size[l];if(!d||!1!==h.clip&&(d.bottom<=Math.max(i.top,s.top)||d.top>=Math.min(i.bottom,s.bottom)||d.right<Math.max(i.left,s.left)-.1||d.left>Math.min(i.right,s.right)+.1)){c.style.top=sd;continue}let f=h.arrow?a.dom.querySelector(".cm-tooltip-arrow"):null,p=f?7:0,g=u.right-u.left,m=null!==(e=sg.get(a))&&void 0!==e?e:u.bottom-u.top,v=a.offset||sb,w=this.view.textDirection==tb.LTR,b=u.width>s.right-s.left?w?s.left:s.right-u.width:w?Math.max(s.left,Math.min(d.left-(f?14:0)+v.x,s.right-g)):Math.min(Math.max(s.left,d.left-g+(f?14:0)-v.x),s.right-g),y=this.above[l];!h.strictSide&&(y?d.top-m-p-v.y<s.top:d.bottom+m+p+v.y>s.bottom)&&y==s.bottom-d.bottom>d.top-s.top&&(y=this.above[l]=!y);let x=(y?d.top-s.top:s.bottom-d.bottom)-p;if(x<m&&!1!==a.resize){if(x<this.view.defaultLineHeight){c.style.top=sd;continue}sg.set(a,m),c.style.height=(m=x)/n+"px"}else c.style.height&&(c.style.height="");let S=y?d.top-m-p-v.y:d.bottom+p+v.y,M=b+g;if(!0!==a.overlap)for(let t of r)t.left<M&&t.right>b&&t.top<S+m&&t.bottom>S&&(S=y?t.top-m-2-p:t.bottom+p+2);if("absolute"==this.position?(c.style.top=(S-t.parent.top)/n+"px",sv(c,(b-t.parent.left)/o)):(c.style.top=S/n+"px",sv(c,b/o)),f){let t=d.left+(w?v.x:-v.x)-(b+14-7);f.style.left=t/o+"px"}!0!==a.overlap&&r.push({left:b,top:S,right:M,bottom:S+m}),c.classList.toggle("cm-tooltip-above",y),c.classList.toggle("cm-tooltip-below",!y),a.positioned&&a.positioned(t.space)}}maybeMeasure(){if(this.manager.tooltips.length&&(this.view.inView&&this.view.requestMeasure(this.measureReq),this.inView!=this.view.inView&&(this.inView=this.view.inView,!this.inView)))for(let t of this.manager.tooltipViews)t.dom.style.top=sd}},{eventObservers:{scroll(){this.maybeMeasure()}}});function sv(t,e){let i=parseInt(t.style.left,10);(isNaN(i)||Math.abs(e-i)>1)&&(t.style.left=e+"px")}let sw=iS.baseTheme({".cm-tooltip":{zIndex:500,boxSizing:"border-box"},"&light .cm-tooltip":{border:"1px solid #bbb",backgroundColor:"#f5f5f5"},"&light .cm-tooltip-section:not(:first-child)":{borderTop:"1px solid #bbb"},"&dark .cm-tooltip":{backgroundColor:"#333338",color:"white"},".cm-tooltip-arrow":{height:"7px",width:"14px",position:"absolute",zIndex:-1,overflow:"hidden","&:before, &:after":{content:"''",position:"absolute",width:0,height:0,borderLeft:"7px solid transparent",borderRight:"7px solid transparent"},".cm-tooltip-above &":{bottom:"-7px","&:before":{borderTop:"7px solid #bbb"},"&:after":{borderTop:"7px solid #f5f5f5",bottom:"1px"}},".cm-tooltip-below &":{top:"-7px","&:before":{borderBottom:"7px solid #bbb"},"&:after":{borderBottom:"7px solid #f5f5f5",top:"1px"}}},"&dark .cm-tooltip .cm-tooltip-arrow":{"&:before":{borderTopColor:"#333338",borderBottomColor:"#333338"},"&:after":{borderTopColor:"transparent",borderBottomColor:"transparent"}}}),sb={x:0,y:0},sy=l.r$.define({enables:[sm,sw]}),sx=l.r$.define({combine:t=>t.reduce((t,e)=>t.concat(e),[])});class sS{static create(t){return new sS(t)}constructor(t){this.view=t,this.mounted=!1,this.dom=document.createElement("div"),this.dom.classList.add("cm-tooltip-hover"),this.manager=new su(t,sx,(t,e)=>this.createHostedView(t,e),t=>t.dom.remove())}createHostedView(t,e){let i=t.create(this.view);return i.dom.classList.add("cm-tooltip-section"),this.dom.insertBefore(i.dom,e?e.dom.nextSibling:this.dom.firstChild),this.mounted&&i.mount&&i.mount(this.view),i}mount(t){for(let e of this.manager.tooltipViews)e.mount&&e.mount(t);this.mounted=!0}positioned(t){for(let e of this.manager.tooltipViews)e.positioned&&e.positioned(t)}update(t){this.manager.update(t)}destroy(){var t;for(let e of this.manager.tooltipViews)null===(t=e.destroy)||void 0===t||t.call(e)}passProp(t){let e;for(let i of this.manager.tooltipViews){let s=i[t];if(void 0!==s){if(void 0===e)e=s;else if(e!==s)return}}return e}get offset(){return this.passProp("offset")}get getCoords(){return this.passProp("getCoords")}get overlap(){return this.passProp("overlap")}get resize(){return this.passProp("resize")}}let sM=sy.compute([sx],t=>{let e=t.facet(sx);return 0===e.length?null:{pos:Math.min(...e.map(t=>t.pos)),end:Math.max(...e.map(t=>{var e;return null!==(e=t.end)&&void 0!==e?e:t.pos})),create:sS.create,above:e[0].above,arrow:e.some(t=>t.arrow)}});class sC{constructor(t,e,i,s,o){this.view=t,this.source=e,this.field=i,this.setHover=s,this.hoverTime=o,this.hoverTimeout=-1,this.restartTimeout=-1,this.pending=null,this.lastMove={x:0,y:0,target:t.dom,time:0},this.checkHover=this.checkHover.bind(this),t.dom.addEventListener("mouseleave",this.mouseleave=this.mouseleave.bind(this)),t.dom.addEventListener("mousemove",this.mousemove=this.mousemove.bind(this))}update(){this.pending&&(this.pending=null,clearTimeout(this.restartTimeout),this.restartTimeout=setTimeout(()=>this.startHover(),20))}get active(){return this.view.state.field(this.field)}checkHover(){if(this.hoverTimeout=-1,this.active.length)return;let t=Date.now()-this.lastMove.time;t<this.hoverTime?this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime-t):this.startHover()}startHover(){clearTimeout(this.restartTimeout);let{view:t,lastMove:e}=this,i=t.docView.nearest(e.target);if(!i)return;let s,o=1;if(i instanceof Q)s=i.posAtStart;else{if(null==(s=t.posAtCoords(e)))return;let i=t.coordsAtPos(s);if(!i||e.y<i.top||e.y>i.bottom||e.x<i.left-t.defaultCharacterWidth||e.x>i.right+t.defaultCharacterWidth)return;let n=t.bidiSpans(t.state.doc.lineAt(s)).find(t=>t.from<=s&&t.to>=s),r=n&&n.dir==tb.RTL?-1:1;o=e.x<i.left?-r:r}let n=this.source(t,s,o);if(null==n?void 0:n.then){let e=this.pending={pos:s};n.then(i=>{this.pending==e&&(this.pending=null,i&&!(Array.isArray(i)&&!i.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(i)?i:[i])}))},e=>tG(t.state,e,"hover tooltip"))}else n&&!(Array.isArray(n)&&!n.length)&&t.dispatch({effects:this.setHover.of(Array.isArray(n)?n:[n])})}get tooltip(){let t=this.view.plugin(sm),e=t?t.manager.tooltips.findIndex(t=>t.create==sS.create):-1;return e>-1?t.manager.tooltipViews[e]:null}mousemove(t){var e,i;this.lastMove={x:t.clientX,y:t.clientY,target:t.target,time:Date.now()},this.hoverTimeout<0&&(this.hoverTimeout=setTimeout(this.checkHover,this.hoverTime));let{active:s,tooltip:o}=this;if(s.length&&o&&!function(t,e){let{left:i,right:s,top:o,bottom:n}=t.getBoundingClientRect(),r;if(r=t.querySelector(".cm-tooltip-arrow")){let t=r.getBoundingClientRect();o=Math.min(t.top,o),n=Math.max(t.bottom,n)}return e.clientX>=i-4&&e.clientX<=s+4&&e.clientY>=o-4&&e.clientY<=n+4}(o.dom,t)||this.pending){let{pos:o}=s[0]||this.pending,n=null!==(i=null===(e=s[0])||void 0===e?void 0:e.end)&&void 0!==i?i:o;(o==n?this.view.posAtCoords(this.lastMove)!=o:!function(t,e,i,s,o,n){let r=t.scrollDOM.getBoundingClientRect(),l=t.documentTop+t.documentPadding.top+t.contentHeight;if(r.left>s||r.right<s||r.top>o||Math.min(r.bottom,l)<o)return!1;let h=t.posAtCoords({x:s,y:o},!1);return h>=e&&h<=i}(this.view,o,n,t.clientX,t.clientY))&&(this.view.dispatch({effects:this.setHover.of([])}),this.pending=null)}}mouseleave(t){clearTimeout(this.hoverTimeout),this.hoverTimeout=-1;let{active:e}=this;if(e.length){let{tooltip:e}=this;e&&e.dom.contains(t.relatedTarget)?this.watchTooltipLeave(e.dom):this.view.dispatch({effects:this.setHover.of([])})}}watchTooltipLeave(t){let e=i=>{t.removeEventListener("mouseleave",e),this.active.length&&!this.view.dom.contains(i.relatedTarget)&&this.view.dispatch({effects:this.setHover.of([])})};t.addEventListener("mouseleave",e)}destroy(){clearTimeout(this.hoverTimeout),this.view.dom.removeEventListener("mouseleave",this.mouseleave),this.view.dom.removeEventListener("mousemove",this.mousemove)}}function sk(t,e={}){let i=l.Py.define(),s=l.QQ.define({create:()=>[],update(t,s){if(t.length&&(e.hideOnChange&&(s.docChanged||s.selection)?t=[]:e.hideOn&&(t=t.filter(t=>!e.hideOn(s,t))),s.docChanged)){let e=[];for(let i of t){let t=s.changes.mapPos(i.pos,-1,l.gc.TrackDel);if(null!=t){let o=Object.assign(Object.create(null),i);o.pos=t,null!=o.end&&(o.end=s.changes.mapPos(o.end)),e.push(o)}}t=e}for(let e of s.effects)e.is(i)&&(t=e.value),e.is(sD)&&(t=[]);return t},provide:t=>sx.from(t)});return{active:s,extension:[s,tZ.define(o=>new sC(o,t,s,i,e.hoverTime||300)),sM]}}function sA(t,e){let i=t.plugin(sm);if(!i)return null;let s=i.manager.tooltips.indexOf(e);return s<0?null:i.manager.tooltipViews[s]}let sD=l.Py.define(),sT=l.r$.define({combine(t){let e,i;for(let s of t)e=e||s.topContainer,i=i||s.bottomContainer;return{topContainer:e,bottomContainer:i}}});function sO(t,e){let i=t.plugin(sE),s=i?i.specs.indexOf(e):-1;return s>-1?i.panels[s]:null}let sE=tZ.fromClass(class{constructor(t){this.input=t.state.facet(sL),this.specs=this.input.filter(t=>t),this.panels=this.specs.map(e=>e(t));let e=t.state.facet(sT);for(let i of(this.top=new sR(t,!0,e.topContainer),this.bottom=new sR(t,!1,e.bottomContainer),this.top.sync(this.panels.filter(t=>t.top)),this.bottom.sync(this.panels.filter(t=>!t.top)),this.panels))i.dom.classList.add("cm-panel"),i.mount&&i.mount()}update(t){let e=t.state.facet(sT);this.top.container!=e.topContainer&&(this.top.sync([]),this.top=new sR(t.view,!0,e.topContainer)),this.bottom.container!=e.bottomContainer&&(this.bottom.sync([]),this.bottom=new sR(t.view,!1,e.bottomContainer)),this.top.syncClasses(),this.bottom.syncClasses();let i=t.state.facet(sL);if(i!=this.input){let e=i.filter(t=>t),s=[],o=[],n=[],r=[];for(let i of e){let e=this.specs.indexOf(i),l;e<0?(l=i(t.view),r.push(l)):(l=this.panels[e]).update&&l.update(t),s.push(l),(l.top?o:n).push(l)}for(let t of(this.specs=e,this.panels=s,this.top.sync(o),this.bottom.sync(n),r))t.dom.classList.add("cm-panel"),t.mount&&t.mount()}else for(let e of this.panels)e.update&&e.update(t)}destroy(){this.top.sync([]),this.bottom.sync([])}},{provide:t=>iS.scrollMargins.of(e=>{let i=e.plugin(t);return i&&{top:i.top.scrollMargin(),bottom:i.bottom.scrollMargin()}})});class sR{constructor(t,e,i){this.view=t,this.top=e,this.container=i,this.dom=void 0,this.classes="",this.panels=[],this.syncClasses()}sync(t){for(let e of this.panels)e.destroy&&0>t.indexOf(e)&&e.destroy();this.panels=t,this.syncDOM()}syncDOM(){if(0==this.panels.length){this.dom&&(this.dom.remove(),this.dom=void 0);return}if(!this.dom){this.dom=document.createElement("div"),this.dom.className=this.top?"cm-panels cm-panels-top":"cm-panels cm-panels-bottom",this.dom.style[this.top?"top":"bottom"]="0";let t=this.container||this.view.dom;t.insertBefore(this.dom,this.top?t.firstChild:null)}let t=this.dom.firstChild;for(let e of this.panels)if(e.dom.parentNode==this.dom){for(;t!=e.dom;)t=sB(t);t=t.nextSibling}else this.dom.insertBefore(e.dom,t);for(;t;)t=sB(t)}scrollMargin(){return!this.dom||this.container?0:Math.max(0,this.top?this.dom.getBoundingClientRect().bottom-Math.max(0,this.view.scrollDOM.getBoundingClientRect().top):Math.min(innerHeight,this.view.scrollDOM.getBoundingClientRect().bottom)-this.dom.getBoundingClientRect().top)}syncClasses(){if(this.container&&this.classes!=this.view.themeClasses){for(let t of this.classes.split(" "))t&&this.container.classList.remove(t);for(let t of(this.classes=this.view.themeClasses).split(" "))t&&this.container.classList.add(t)}}}function sB(t){let e=t.nextSibling;return t.remove(),e}let sL=l.r$.define({enables:sE});class sP extends l.uU{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}sP.prototype.elementClass="",sP.prototype.toDOM=void 0,sP.prototype.mapMode=l.gc.TrackBefore,sP.prototype.startSide=sP.prototype.endSide=-1,sP.prototype.point=!0;let sH=l.r$.define(),sV=l.r$.define(),sN={class:"",renderEmptyElements:!1,elementStyle:"",markers:()=>l.Xs.empty,lineMarker:()=>null,widgetMarker:()=>null,lineMarkerChange:null,initialSpacer:null,updateSpacer:null,domEventHandlers:{},side:"before"},sW=l.r$.define();function sF(t){return[sI(),sW.of({...sN,...t})]}let sz=l.r$.define({combine:t=>t.some(t=>t)});function sI(t){let e=[sq];return t&&!1===t.fixed&&e.push(sz.of(!0)),e}let sq=tZ.fromClass(class{constructor(t){for(let e of(this.view=t,this.domAfter=null,this.prevViewport=t.viewport,this.dom=document.createElement("div"),this.dom.className="cm-gutters cm-gutters-before",this.dom.setAttribute("aria-hidden","true"),this.dom.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.gutters=t.state.facet(sW).map(e=>new sX(t,e)),this.fixed=!t.state.facet(sz),this.gutters))"after"==e.config.side?this.getDOMAfter().appendChild(e.dom):this.dom.appendChild(e.dom);this.fixed&&(this.dom.style.position="sticky"),this.syncGutters(!1),t.scrollDOM.insertBefore(this.dom,t.contentDOM)}getDOMAfter(){return this.domAfter||(this.domAfter=document.createElement("div"),this.domAfter.className="cm-gutters cm-gutters-after",this.domAfter.setAttribute("aria-hidden","true"),this.domAfter.style.minHeight=this.view.contentHeight/this.view.scaleY+"px",this.domAfter.style.position=this.fixed?"sticky":"",this.view.scrollDOM.appendChild(this.domAfter)),this.domAfter}update(t){if(this.updateGutters(t)){let e=this.prevViewport,i=t.view.viewport,s=Math.min(e.to,i.to)-Math.max(e.from,i.from);this.syncGutters(s<(i.to-i.from)*.8)}if(t.geometryChanged){let t=this.view.contentHeight/this.view.scaleY+"px";this.dom.style.minHeight=t,this.domAfter&&(this.domAfter.style.minHeight=t)}this.view.state.facet(sz)!=!this.fixed&&(this.fixed=!this.fixed,this.dom.style.position=this.fixed?"sticky":"",this.domAfter&&(this.domAfter.style.position=this.fixed?"sticky":"")),this.prevViewport=t.view.viewport}syncGutters(t){let e=this.dom.nextSibling;t&&(this.dom.remove(),this.domAfter&&this.domAfter.remove());let i=l.Xs.iter(this.view.state.facet(sH),this.view.viewport.from),s=[],o=this.gutters.map(t=>new s$(t,this.view.viewport,-this.view.documentPadding.top));for(let t of this.view.viewportLineBlocks)if(s.length&&(s=[]),Array.isArray(t.type)){let e=!0;for(let n of t.type)if(n.type==tr.Text&&e){for(let t of(sj(i,s,n.from),o))t.line(this.view,n,s);e=!1}else if(n.widget)for(let t of o)t.widget(this.view,n)}else if(t.type==tr.Text)for(let e of(sj(i,s,t.from),o))e.line(this.view,t,s);else if(t.widget)for(let e of o)e.widget(this.view,t);for(let t of o)t.finish();t&&(this.view.scrollDOM.insertBefore(this.dom,e),this.domAfter&&this.view.scrollDOM.appendChild(this.domAfter))}updateGutters(t){let e=t.startState.facet(sW),i=t.state.facet(sW),s=t.docChanged||t.heightChanged||t.viewportChanged||!l.Xs.eq(t.startState.facet(sH),t.state.facet(sH),t.view.viewport.from,t.view.viewport.to);if(e==i)for(let e of this.gutters)e.update(t)&&(s=!0);else{s=!0;let o=[];for(let s of i){let i=e.indexOf(s);i<0?o.push(new sX(this.view,s)):(this.gutters[i].update(t),o.push(this.gutters[i]))}for(let t of this.gutters)t.dom.remove(),0>o.indexOf(t)&&t.destroy();for(let t of o)"after"==t.config.side?this.getDOMAfter().appendChild(t.dom):this.dom.appendChild(t.dom);this.gutters=o}return s}destroy(){for(let t of this.gutters)t.destroy();this.dom.remove(),this.domAfter&&this.domAfter.remove()}},{provide:t=>iS.scrollMargins.of(e=>{let i=e.plugin(t);if(!i||0==i.gutters.length||!i.fixed)return null;let s=i.dom.offsetWidth*e.scaleX,o=i.domAfter?i.domAfter.offsetWidth*e.scaleX:0;return e.textDirection==tb.LTR?{left:s,right:o}:{right:s,left:o}})});function sK(t){return Array.isArray(t)?t:[t]}function sj(t,e,i){for(;t.value&&t.from<=i;)t.from==i&&e.push(t.value),t.next()}class s${constructor(t,e,i){this.gutter=t,this.height=i,this.i=0,this.cursor=l.Xs.iter(t.markers,e.from)}addElement(t,e,i){let{gutter:s}=this,o=(e.top-this.height)/t.scaleY,n=e.height/t.scaleY;if(this.i==s.elements.length){let e=new sY(t,n,o,i);s.elements.push(e),s.dom.appendChild(e.dom)}else s.elements[this.i].update(t,n,o,i);this.height=e.bottom,this.i++}line(t,e,i){let s=[];sj(this.cursor,s,e.from),i.length&&(s=s.concat(i));let o=this.gutter.config.lineMarker(t,e,s);o&&s.unshift(o);let n=this.gutter;(0!=s.length||n.config.renderEmptyElements)&&this.addElement(t,e,s)}widget(t,e){let i=this.gutter.config.widgetMarker(t,e.widget,e),s=i?[i]:null;for(let i of t.state.facet(sV)){let o=i(t,e.widget,e);o&&(s||(s=[])).push(o)}s&&this.addElement(t,e,s)}finish(){let t=this.gutter;for(;t.elements.length>this.i;){let e=t.elements.pop();t.dom.removeChild(e.dom),e.destroy()}}}class sX{constructor(t,e){for(let i in this.view=t,this.config=e,this.elements=[],this.spacer=null,this.dom=document.createElement("div"),this.dom.className="cm-gutter"+(this.config.class?" "+this.config.class:""),e.domEventHandlers)this.dom.addEventListener(i,s=>{let o=s.target,n;if(o!=this.dom&&this.dom.contains(o)){for(;o.parentNode!=this.dom;)o=o.parentNode;let t=o.getBoundingClientRect();n=(t.top+t.bottom)/2}else n=s.clientY;let r=t.lineBlockAtHeight(n-t.documentTop);e.domEventHandlers[i](t,r,s)&&s.preventDefault()});this.markers=sK(e.markers(t)),e.initialSpacer&&(this.spacer=new sY(t,0,0,[e.initialSpacer(t)]),this.dom.appendChild(this.spacer.dom),this.spacer.dom.style.cssText+="visibility: hidden; pointer-events: none")}update(t){let e=this.markers;if(this.markers=sK(this.config.markers(t.view)),this.spacer&&this.config.updateSpacer){let e=this.config.updateSpacer(this.spacer.markers[0],t);e!=this.spacer.markers[0]&&this.spacer.update(t.view,0,0,[e])}let i=t.view.viewport;return!l.Xs.eq(this.markers,e,i.from,i.to)||!!this.config.lineMarkerChange&&this.config.lineMarkerChange(t)}destroy(){for(let t of this.elements)t.destroy()}}class sY{constructor(t,e,i,s){this.height=-1,this.above=0,this.markers=[],this.dom=document.createElement("div"),this.dom.className="cm-gutterElement",this.update(t,e,i,s)}update(t,e,i,s){this.height!=e&&(this.height=e,this.dom.style.height=e+"px"),this.above!=i&&(this.dom.style.marginTop=(this.above=i)?i+"px":""),!function(t,e){if(t.length!=e.length)return!1;for(let i=0;i<t.length;i++)if(!t[i].compare(e[i]))return!1;return!0}(this.markers,s)&&this.setMarkers(t,s)}setMarkers(t,e){let i="cm-gutterElement",s=this.dom.firstChild;for(let o=0,n=0;;){let r=n,l=o<e.length?e[o++]:null,h=!1;if(l){let t=l.elementClass;t&&(i+=" "+t);for(let t=n;t<this.markers.length;t++)if(this.markers[t].compare(l)){r=t,h=!0;break}}else r=this.markers.length;for(;n<r;){let t=this.markers[n++];if(t.toDOM){t.destroy(s);let e=s.nextSibling;s.remove(),s=e}}if(!l)break;l.toDOM&&(h?s=s.nextSibling:this.dom.insertBefore(l.toDOM(t),s)),h&&n++}this.dom.className=i,this.markers=e}destroy(){this.setMarkers(null,[])}}let sG=l.r$.define(),s_=l.r$.define(),sU=l.r$.define({combine:t=>(0,l.BO)(t,{formatNumber:String,domEventHandlers:{}},{domEventHandlers(t,e){let i=Object.assign({},t);for(let t in e){let s=i[t],o=e[t];i[t]=s?(t,e,i)=>s(t,e,i)||o(t,e,i):o}return i}})});class sQ extends sP{constructor(t){super(),this.number=t}eq(t){return this.number==t.number}toDOM(){return document.createTextNode(this.number)}}function sZ(t,e){return t.state.facet(sU).formatNumber(e,t.state)}let sJ=sW.compute([sU],t=>({class:"cm-lineNumbers",renderEmptyElements:!1,markers:t=>t.state.facet(sG),lineMarker:(t,e,i)=>i.some(t=>t.toDOM)?null:new sQ(sZ(t,t.state.doc.lineAt(e.from).number)),widgetMarker:(t,e,i)=>{for(let s of t.state.facet(s_)){let o=s(t,e,i);if(o)return o}return null},lineMarkerChange:t=>t.startState.facet(sU)!=t.state.facet(sU),initialSpacer:t=>new sQ(sZ(t,s1(t.state.doc.lines))),updateSpacer(t,e){let i=sZ(e.view,s1(e.view.state.doc.lines));return i==t.number?t:new sQ(i)},domEventHandlers:t.facet(sU).domEventHandlers,side:"before"}));function s0(t={}){return[sU.of(t),sI(),sJ]}function s1(t){let e=9;for(;e<t;)e=10*e+9;return e}let s2=new class extends sP{constructor(){super(...arguments),this.elementClass="cm-activeLineGutter"}},s8=sH.compute(["selection"],t=>{let e=[],i=-1;for(let s of t.selection.ranges){let o=t.doc.lineAt(s.head).from;o>i&&(i=o,e.push(s2.range(o)))}return l.Xs.of(e)});function s3(){return s8}}}]);