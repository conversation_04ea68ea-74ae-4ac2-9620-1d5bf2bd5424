{"/api/auth/[...all]/route": "/api/auth/[...all]", "/api/robots.txt/route": "/api/robots.txt", "/api/rules/raw/route": "/api/rules/raw", "/api/rules/download/route": "/api/rules/download", "/api/sitemap.xml/route": "/api/sitemap.xml", "/api/tags/route": "/api/tags", "/api/rules/export/route": "/api/rules/export", "/api/rules/route": "/api/rules", "/api/rules/[id]/route": "/api/rules/[id]", "/_not-found/page": "/_not-found", "/sitemap.xml/route": "/sitemap.xml", "/r/[id]/page": "/r/[id]", "/test-theme/page": "/test-theme", "/auth/signup/page": "/auth/signup", "/auth/signin/page": "/auth/signin", "/rules/[id]/page": "/rules/[id]", "/tutorials/page": "/tutorials", "/dashboard/page": "/dashboard", "/profile/[username]/page": "/profile/[username]", "/templates/page": "/templates", "/ides/augment/page": "/ides/augment", "/ides/github-copilot/page": "/ides/github-copilot", "/(static)/page": "/", "/ides/page": "/ides", "/ides/windsurf/page": "/ides/windsurf", "/ides/cursor/page": "/ides/cursor", "/ides/claude/page": "/ides/claude", "/ides/cline/page": "/ides/cline"}