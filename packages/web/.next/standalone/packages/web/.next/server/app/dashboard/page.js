(()=>{var a={};a.id=105,a.ids=[105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},890:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(93824),C=c.n(B),D=c(97540),E=c(49005),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,27576)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,52868)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,93824,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,50134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/dashboard/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23270:(a,b,c)=>{Promise.resolve().then(c.bind(c,55476))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26716:(a,b,c)=>{"use strict";c.d(b,{a:()=>k});var d=c(60159),e=c(86135),f=c(30101);let g={as:{type:"enum",values:["div","span"],default:"div"},...c(23831).f,display:{type:"enum",className:"rt-r-display",values:["none","inline","inline-block","block","contents"],responsive:!0}};var h=c(87160),i=c(2107),j=c(91683);let k=d.forwardRef((a,b)=>{let{className:c,asChild:k,as:l="div",...m}=(0,h.o)(a,g,i.i,j.y);return d.createElement(k?f.DX:l,{...m,ref:b,className:e("rt-Box",c)})});k.displayName="Box"},27576:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,dynamic:()=>e});var d=c(66352);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/dashboard/page.tsx","dynamic"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/dashboard/page.tsx","default")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},54814:(a,b,c)=>{Promise.resolve().then(c.bind(c,27576))},55476:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>bc,dynamic:()=>bb});var d=c(13486),e=c(60159),f=c(3935),g=c(26716),h=c(16918),i=c(99865),j=c(16692),k=c(5229),l=c(16996),m=c(26857),n=c(7921),o=c(74010),p=c(62559),q=c(74439),r=c(44022),s=c(9431),t=c(84667);let u=(0,t.A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var v=c(42726),w=c(35795),x=c(16209),y=c(57649),z=c(86135),A=c(96171),B=c(88476);let C={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft"],default:"surface"},resize:{type:"enum",className:"rt-r-resize",values:["none","vertical","horizontal","both"],responsive:!0},...A._s,...B.F};var D=c(87160),E=c(91683);let F=e.forwardRef((a,b)=>{let{className:c,color:d,radius:f,style:g,...h}=(0,D.o)(a,C,E.y);return e.createElement("div",{"data-accent-color":d,"data-radius":f,className:z("rt-TextAreaRoot",c),style:g},e.createElement("textarea",{className:"rt-reset rt-TextAreaInput",ref:b,...h}))});F.displayName="TextArea";let G=(0,t.A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var H=c(34606),I=c(21693),J=c(27134),K=c(94108),L=c(64935),M=c(66634),N=c(40594),O="Toggle",P=e.forwardRef((a,b)=>{let{pressed:c,defaultPressed:e,onPressedChange:f,...g}=a,[h,i]=(0,N.i)({prop:c,onChange:f,defaultProp:e??!1,caller:O});return(0,d.jsx)(K.sG.button,{type:"button","aria-pressed":h,"data-state":h?"on":"off","data-disabled":a.disabled?"":void 0,...g,ref:b,onClick:(0,M.m)(a.onClick,()=>{a.disabled||i(!h)})})});P.displayName=O;var Q=c(88200),R="ToggleGroup",[S,T]=(0,J.A)(R,[L.RG]),U=(0,L.RG)(),V=e.forwardRef((a,b)=>{let{type:c,...e}=a;if("single"===c)return(0,d.jsx)(Y,{...e,ref:b});if("multiple"===c)return(0,d.jsx)(Z,{...e,ref:b});throw Error(`Missing prop \`type\` expected on \`${R}\``)});V.displayName=R;var[W,X]=S(R),Y=e.forwardRef((a,b)=>{let{value:c,defaultValue:f,onValueChange:g=()=>{},...h}=a,[i,j]=(0,N.i)({prop:c,defaultProp:f??"",onChange:g,caller:R});return(0,d.jsx)(W,{scope:a.__scopeToggleGroup,type:"single",value:e.useMemo(()=>i?[i]:[],[i]),onItemActivate:j,onItemDeactivate:e.useCallback(()=>j(""),[j]),children:(0,d.jsx)(aa,{...h,ref:b})})}),Z=e.forwardRef((a,b)=>{let{value:c,defaultValue:f,onValueChange:g=()=>{},...h}=a,[i,j]=(0,N.i)({prop:c,defaultProp:f??[],onChange:g,caller:R}),k=e.useCallback(a=>j((b=[])=>[...b,a]),[j]),l=e.useCallback(a=>j((b=[])=>b.filter(b=>b!==a)),[j]);return(0,d.jsx)(W,{scope:a.__scopeToggleGroup,type:"multiple",value:i,onItemActivate:k,onItemDeactivate:l,children:(0,d.jsx)(aa,{...h,ref:b})})});V.displayName=R;var[$,_]=S(R),aa=e.forwardRef((a,b)=>{let{__scopeToggleGroup:c,disabled:e=!1,rovingFocus:f=!0,orientation:g,dir:h,loop:i=!0,...j}=a,k=U(c),l=(0,Q.jH)(h),m={role:"group",dir:l,...j};return(0,d.jsx)($,{scope:c,rovingFocus:f,disabled:e,children:f?(0,d.jsx)(L.bL,{asChild:!0,...k,orientation:g,dir:l,loop:i,children:(0,d.jsx)(K.sG.div,{...m,ref:b})}):(0,d.jsx)(K.sG.div,{...m,ref:b})})}),ab="ToggleGroupItem",ac=e.forwardRef((a,b)=>{let c=X(ab,a.__scopeToggleGroup),f=_(ab,a.__scopeToggleGroup),g=U(a.__scopeToggleGroup),h=c.value.includes(a.value),i=f.disabled||a.disabled,j={...a,pressed:h,disabled:i},k=e.useRef(null);return f.rovingFocus?(0,d.jsx)(L.q7,{asChild:!0,...g,focusable:!i,active:h,ref:k,children:(0,d.jsx)(ad,{...j,ref:b})}):(0,d.jsx)(ad,{...j,ref:b})});ac.displayName=ab;var ad=e.forwardRef((a,b)=>{let{__scopeToggleGroup:c,value:e,...f}=a,g=X(ab,c),h={role:"radio","aria-checked":a.pressed,"aria-pressed":void 0},i="single"===g.type?h:void 0;return(0,d.jsx)(P,{...i,...f,ref:b,onPressedChange:a=>{a?g.onItemActivate(e):g.onItemDeactivate(e)}})});let ae=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?ae(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},af=/^\[(.+)\]$/,ag=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:ah(b,a)).classGroupId=c;return}if("function"==typeof a)return ai(a)?void ag(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{ag(e,ah(b,a),c,d)})})},ah=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},ai=a=>a.isThemeGetter,aj=(a,b)=>b?a.map(([a,c])=>[a,c.map(a=>"string"==typeof a?b+a:"object"==typeof a?Object.fromEntries(Object.entries(a).map(([a,c])=>[b+a,c])):a)]):a,ak=a=>{if(a.length<=1)return a;let b=[],c=[];return a.forEach(a=>{"["===a[0]?(b.push(...c.sort(),a),c=[]):c.push(a)}),b.push(...c.sort()),b},al=/\s+/;function am(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=an(a))&&(d&&(d+=" "),d+=b);return d}let an=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=an(a[d]))&&(c&&(c+=" "),c+=b);return c},ao=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},ap=/^\[(?:([a-z-]+):)?(.+)\]$/i,aq=/^\d+\/\d+$/,ar=new Set(["px","full","screen"]),as=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,at=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,au=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,av=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,aw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ax=a=>az(a)||ar.has(a)||aq.test(a),ay=a=>aM(a,"length",aN),az=a=>!!a&&!Number.isNaN(Number(a)),aA=a=>aM(a,"number",az),aB=a=>!!a&&Number.isInteger(Number(a)),aC=a=>a.endsWith("%")&&az(a.slice(0,-1)),aD=a=>ap.test(a),aE=a=>as.test(a),aF=new Set(["length","size","percentage"]),aG=a=>aM(a,aF,aO),aH=a=>aM(a,"position",aO),aI=new Set(["image","url"]),aJ=a=>aM(a,aI,aQ),aK=a=>aM(a,"",aP),aL=()=>!0,aM=(a,b,c)=>{let d=ap.exec(a);return!!d&&(d[1]?"string"==typeof b?d[1]===b:b.has(d[1]):c(d[2]))},aN=a=>at.test(a)&&!au.test(a),aO=()=>!1,aP=a=>av.test(a),aQ=a=>aw.test(a);Symbol.toStringTag;let aR=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{separator:b,experimentalParseClassName:c}=a,d=1===b.length,e=b[0],f=b.length,g=a=>{let c,g=[],h=0,i=0;for(let j=0;j<a.length;j++){let k=a[j];if(0===h){if(k===e&&(d||a.slice(j,j+f)===b)){g.push(a.slice(i,j)),i=j+f;continue}if("/"===k){c=j;continue}}"["===k?h++:"]"===k&&h--}let j=0===g.length?a:a.substring(i),k=j.startsWith("!"),l=k?j.substring(1):j;return{modifiers:g,hasImportantModifier:k,baseClassName:l,maybePostfixModifierPosition:c&&c>i?c-i:void 0}};return c?a=>c({className:a,parseClassName:g}):g})(i),...(a=>{let b=(a=>{let{theme:b,prefix:c}=a,d={nextPart:new Map,validators:[]};return aj(Object.entries(a.classGroups),c).forEach(([a,c])=>{ag(c,d,a,b)}),d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),ae(c,b)||(a=>{if(af.test(a)){let b=af.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e}=b,f=[],g=a.trim().split(al),h="";for(let a=g.length-1;a>=0;a-=1){let b=g[a],{modifiers:i,hasImportantModifier:j,baseClassName:k,maybePostfixModifierPosition:l}=c(b),m=!!l,n=d(m?k.substring(0,l):k);if(!n){if(!m||!(n=d(k))){h=b+(h.length>0?" "+h:h);continue}m=!1}let o=ak(i).join(":"),p=j?o+"!":o,q=p+n;if(f.includes(q))continue;f.push(q);let r=e(n,m);for(let a=0;a<r.length;++a){let b=r[a];f.push(p+b)}h=b+(h.length>0?" "+h:h)}return h})(a,c);return e(a,f),f}return function(){return f(am.apply(null,arguments))}}(()=>{let a=ao("colors"),b=ao("spacing"),c=ao("blur"),d=ao("brightness"),e=ao("borderColor"),f=ao("borderRadius"),g=ao("borderSpacing"),h=ao("borderWidth"),i=ao("contrast"),j=ao("grayscale"),k=ao("hueRotate"),l=ao("invert"),m=ao("gap"),n=ao("gradientColorStops"),o=ao("gradientColorStopPositions"),p=ao("inset"),q=ao("margin"),r=ao("opacity"),s=ao("padding"),t=ao("saturate"),u=ao("scale"),v=ao("sepia"),w=ao("skew"),x=ao("space"),y=ao("translate"),z=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",aD,b],C=()=>[aD,b],D=()=>["",ax,ay],E=()=>["auto",az,aD],F=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],G=()=>["solid","dashed","dotted","double","none"],H=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],I=()=>["start","end","center","between","around","evenly","stretch"],J=()=>["","0",aD],K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>[az,aD];return{cacheSize:500,separator:":",theme:{colors:[aL],spacing:[ax,ay],blur:["none","",aE,aD],brightness:L(),borderColor:[a],borderRadius:["none","","full",aE,aD],borderSpacing:C(),borderWidth:D(),contrast:L(),grayscale:J(),hueRotate:L(),invert:J(),gap:C(),gradientColorStops:[a],gradientColorStopPositions:[aC,ay],inset:B(),margin:B(),opacity:L(),padding:C(),saturate:L(),scale:L(),sepia:J(),skew:L(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",aD]}],container:["container"],columns:[{columns:[aE]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...F(),aD]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",aB,aD]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",aD]}],grow:[{grow:J()}],shrink:[{shrink:J()}],order:[{order:["first","last","none",aB,aD]}],"grid-cols":[{"grid-cols":[aL]}],"col-start-end":[{col:["auto",{span:["full",aB,aD]},aD]}],"col-start":[{"col-start":E()}],"col-end":[{"col-end":E()}],"grid-rows":[{"grid-rows":[aL]}],"row-start-end":[{row:["auto",{span:[aB,aD]},aD]}],"row-start":[{"row-start":E()}],"row-end":[{"row-end":E()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",aD]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",aD]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...I()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...I(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...I(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[s]}],px:[{px:[s]}],py:[{py:[s]}],ps:[{ps:[s]}],pe:[{pe:[s]}],pt:[{pt:[s]}],pr:[{pr:[s]}],pb:[{pb:[s]}],pl:[{pl:[s]}],m:[{m:[q]}],mx:[{mx:[q]}],my:[{my:[q]}],ms:[{ms:[q]}],me:[{me:[q]}],mt:[{mt:[q]}],mr:[{mr:[q]}],mb:[{mb:[q]}],ml:[{ml:[q]}],"space-x":[{"space-x":[x]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[x]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",aD,b]}],"min-w":[{"min-w":[aD,b,"min","max","fit"]}],"max-w":[{"max-w":[aD,b,"none","full","min","max","fit","prose",{screen:[aE]},aE]}],h:[{h:[aD,b,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[aD,b,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[aD,b,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[aD,b,"auto","min","max","fit"]}],"font-size":[{text:["base",aE,ay]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",aA]}],"font-family":[{font:[aL]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",aD]}],"line-clamp":[{"line-clamp":["none",az,aA]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",ax,aD]}],"list-image":[{"list-image":["none",aD]}],"list-style-type":[{list:["none","disc","decimal",aD]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[a]}],"placeholder-opacity":[{"placeholder-opacity":[r]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[a]}],"text-opacity":[{"text-opacity":[r]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...G(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",ax,ay]}],"underline-offset":[{"underline-offset":["auto",ax,aD]}],"text-decoration-color":[{decoration:[a]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",aD]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",aD]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[r]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...F(),aH]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",aG]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},aJ]}],"bg-color":[{bg:[a]}],"gradient-from-pos":[{from:[o]}],"gradient-via-pos":[{via:[o]}],"gradient-to-pos":[{to:[o]}],"gradient-from":[{from:[n]}],"gradient-via":[{via:[n]}],"gradient-to":[{to:[n]}],rounded:[{rounded:[f]}],"rounded-s":[{"rounded-s":[f]}],"rounded-e":[{"rounded-e":[f]}],"rounded-t":[{"rounded-t":[f]}],"rounded-r":[{"rounded-r":[f]}],"rounded-b":[{"rounded-b":[f]}],"rounded-l":[{"rounded-l":[f]}],"rounded-ss":[{"rounded-ss":[f]}],"rounded-se":[{"rounded-se":[f]}],"rounded-ee":[{"rounded-ee":[f]}],"rounded-es":[{"rounded-es":[f]}],"rounded-tl":[{"rounded-tl":[f]}],"rounded-tr":[{"rounded-tr":[f]}],"rounded-br":[{"rounded-br":[f]}],"rounded-bl":[{"rounded-bl":[f]}],"border-w":[{border:[h]}],"border-w-x":[{"border-x":[h]}],"border-w-y":[{"border-y":[h]}],"border-w-s":[{"border-s":[h]}],"border-w-e":[{"border-e":[h]}],"border-w-t":[{"border-t":[h]}],"border-w-r":[{"border-r":[h]}],"border-w-b":[{"border-b":[h]}],"border-w-l":[{"border-l":[h]}],"border-opacity":[{"border-opacity":[r]}],"border-style":[{border:[...G(),"hidden"]}],"divide-x":[{"divide-x":[h]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[h]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[r]}],"divide-style":[{divide:G()}],"border-color":[{border:[e]}],"border-color-x":[{"border-x":[e]}],"border-color-y":[{"border-y":[e]}],"border-color-s":[{"border-s":[e]}],"border-color-e":[{"border-e":[e]}],"border-color-t":[{"border-t":[e]}],"border-color-r":[{"border-r":[e]}],"border-color-b":[{"border-b":[e]}],"border-color-l":[{"border-l":[e]}],"divide-color":[{divide:[e]}],"outline-style":[{outline:["",...G()]}],"outline-offset":[{"outline-offset":[ax,aD]}],"outline-w":[{outline:[ax,ay]}],"outline-color":[{outline:[a]}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[a]}],"ring-opacity":[{"ring-opacity":[r]}],"ring-offset-w":[{"ring-offset":[ax,ay]}],"ring-offset-color":[{"ring-offset":[a]}],shadow:[{shadow:["","inner","none",aE,aK]}],"shadow-color":[{shadow:[aL]}],opacity:[{opacity:[r]}],"mix-blend":[{"mix-blend":[...H(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":H()}],filter:[{filter:["","none"]}],blur:[{blur:[c]}],brightness:[{brightness:[d]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",aE,aD]}],grayscale:[{grayscale:[j]}],"hue-rotate":[{"hue-rotate":[k]}],invert:[{invert:[l]}],saturate:[{saturate:[t]}],sepia:[{sepia:[v]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[c]}],"backdrop-brightness":[{"backdrop-brightness":[d]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[j]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[k]}],"backdrop-invert":[{"backdrop-invert":[l]}],"backdrop-opacity":[{"backdrop-opacity":[r]}],"backdrop-saturate":[{"backdrop-saturate":[t]}],"backdrop-sepia":[{"backdrop-sepia":[v]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[g]}],"border-spacing-x":[{"border-spacing-x":[g]}],"border-spacing-y":[{"border-spacing-y":[g]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",aD]}],duration:[{duration:L()}],ease:[{ease:["linear","in","out","in-out",aD]}],delay:[{delay:L()}],animate:[{animate:["none","spin","ping","pulse","bounce",aD]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[u]}],"scale-x":[{"scale-x":[u]}],"scale-y":[{"scale-y":[u]}],rotate:[{rotate:[aB,aD]}],"translate-x":[{"translate-x":[y]}],"translate-y":[{"translate-y":[y]}],"skew-x":[{"skew-x":[w]}],"skew-y":[{"skew-y":[w]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",aD]}],accent:[{accent:["auto",a]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",aD]}],"caret-color":[{caret:[a]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",aD]}],fill:[{fill:[a,"none"]}],"stroke-w":[{stroke:[ax,ay,aA]}],stroke:[{stroke:[a,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function aS(...a){return aR(function(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}(a))}let aT=e.createContext({size:"2",variant:"soft"}),aU=e.forwardRef(({className:a,variant:b,size:c,children:e,...f},g)=>(0,d.jsx)(V,{ref:g,className:aS("flex items-center justify-center gap-1",a),...f,children:(0,d.jsx)(aT.Provider,{value:{variant:b,size:c},children:e})}));aU.displayName=V.displayName;let aV=e.forwardRef(({className:a,children:b,variant:c,size:e,...f},g)=>(0,d.jsx)(ac,{ref:g,className:aS("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",a),...f,children:b}));aV.displayName=ac.displayName;var aW=c(72513),aX=c(25413),aY=c(76521);let aZ=(0,t.A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),a$=(0,t.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),a_=(0,t.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),a0=(0,t.A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var a1=c(38782),a2=c(37558);function a3(a){if(!a.trim())return[{id:a5(),title:"Section 1",content:""}];let b=a.split(/^---\s*$/m);if(1===b.length){let b=a.match(/^#\s+(.+)/m),c=b?b[1].trim():"Section 1";return[{id:a5(),title:c,content:a.trim()}]}let c=[],d={};for(let a=0;a<b.length;a++){let e=b[a].trim();if(!e)continue;let f=e.split("\n"),g=!0,h={};for(let a of f){let b=a.trim();if(!b)continue;let c=b.match(/^(description|name|globs):\s*(.+)$/);if(c){let[,a,b]=c;h[a]=b.trim()}else{g=!1;break}}if(g&&Object.keys(h).length>0)d={...d,...h};else{let a=e,b={...d},g=f.slice(),h=0;for(let a=0;a<f.length;a++){let c=f[a].trim();if(!c){h=a+1;continue}let d=c.match(/^(description|name|globs):\s*(.+)$/);if(d){let[,c,e]=d;b[c]=e.trim(),h=a+1}else break}if(a=g.slice(h).join("\n").trim()){let e=a.match(/^#\s+(.+)/m),f=e?e[1].trim():`Section ${c.length+1}`;c.push({id:a5(),title:f,content:a,...b}),d={}}}}return c.length>0?c:[{id:a5(),title:"Section 1",content:a.trim()}]}function a4(a){return 0===a.length?"":1!==a.length||a[0].description||a[0].name||a[0].globs?a.map(b=>{let c=[],d=b.description||b.name||b.globs;return(0!==a.indexOf(b)||d)&&c.push("---"),b.description&&c.push(`description: ${b.description}`),b.name&&c.push(`name: ${b.name}`),b.globs&&c.push(`globs: ${b.globs}`),d&&c.push("---"),c.push(""),c.push(b.content),c.join("\n")}).join("\n\n"):a[0].content}function a5(){return`section_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function a6({section:a,index:b,totalSections:c,isExpanded:i,onUpdate:m,onDelete:n,onDuplicate:o,onMoveUp:p,onMoveDown:q,onToggleExpanded:r}){let[s,t]=(0,e.useState)([]),u=(b,c)=>{let d={...a,[b]:c};m(d),t(function(a){let b=[];return a.title.trim()||b.push("Section title is required"),a.content.trim()||b.push("Section content is required"),b}(d))},v=function(a){let b=a.content.slice(0,100).replace(/\n/g," ");return b.length>100?`${b}...`:b}(a),w=s.length>0;return(0,d.jsxs)(k.Z,{className:w?"error-border":"",style:{transition:"all 0.2s ease"},children:[(0,d.jsxs)("div",{style:{paddingBottom:"var(--space-3)"},children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",children:[(0,d.jsxs)(h.s,{align:"center",gap:"3",style:{flex:1},children:[(0,d.jsxs)(h.s,{align:"center",gap:"1",children:[(0,d.jsx)(aZ,{size:16,style:{color:"var(--gray-9)",cursor:"grab"}}),(0,d.jsx)(j.E,{size:"2",weight:"medium",style:{color:"var(--gray-9)"},children:b+1})]}),(0,d.jsxs)(g.a,{style:{flex:1},children:[(0,d.jsx)("div",{className:"font-semibold",style:{fontSize:"var(--font-size-3)"},children:a.title||`Section ${b+1}`}),!i&&(0,d.jsx)(j.E,{size:"2",style:{color:"var(--gray-9)",marginTop:"var(--space-1)"},truncate:!0,children:v})]})]}),(0,d.jsxs)(h.s,{align:"center",gap:"2",children:[(0,d.jsxs)(h.s,{align:"center",children:[(0,d.jsx)(f.$,{variant:"ghost",size:"1",onClick:p,disabled:0===b,style:{width:"32px",height:"32px",padding:0},children:(0,d.jsx)(a$,{size:16})}),(0,d.jsx)(f.$,{variant:"ghost",size:"1",onClick:q,disabled:b===c-1,style:{width:"32px",height:"32px",padding:0},children:(0,d.jsx)(a_,{size:16})})]}),(0,d.jsxs)(aX.bL,{children:[(0,d.jsx)(aX.l9,{children:(0,d.jsx)(f.$,{variant:"ghost",size:"1",style:{width:"32px",height:"32px",padding:0},children:(0,d.jsx)(a0,{size:16})})}),(0,d.jsxs)(aX.UC,{align:"end",children:[(0,d.jsxs)(aX.q7,{onClick:o,children:[(0,d.jsx)(a1.A,{size:16,style:{marginRight:"var(--space-2)"}}),"Duplicate"]}),(0,d.jsxs)(aX.q7,{onClick:n,disabled:1===c,className:"error-text",children:[(0,d.jsx)(a2.A,{size:16,style:{marginRight:"var(--space-2)"}}),"Delete"]})]})]}),(0,d.jsx)(f.$,{variant:"ghost",size:"1",onClick:r,style:{width:"32px",height:"32px",padding:0},children:i?(0,d.jsx)(a$,{size:16}):(0,d.jsx)(a_,{size:16})})]})]}),w&&(0,d.jsx)(g.a,{style:{marginTop:"var(--space-2)"},children:s.map((a,b)=>(0,d.jsxs)(j.E,{size:"2",className:"error-text",style:{display:"block"},children:["• ",a]},b))})]}),i&&(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"var(--space-4)"},children:[(0,d.jsxs)(g.a,{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:`section-title-${a.id}`,size:"2",weight:"medium",children:"Section Title"}),(0,d.jsx)(l.b,{id:`section-title-${a.id}`,value:a.title,onChange:a=>u("title",a.target.value),placeholder:"Enter section title..."})]}),(0,d.jsxs)(aY.x,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,d.jsxs)(g.a,{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:`section-name-${a.id}`,size:"2",weight:"medium",children:"Name (Optional)"}),(0,d.jsx)(l.b,{id:`section-name-${a.id}`,value:a.name||"",onChange:a=>u("name",a.target.value),placeholder:"e.g., global, stylesheet"})]}),(0,d.jsxs)(g.a,{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:`section-globs-${a.id}`,size:"2",weight:"medium",children:"File Patterns (Optional)"}),(0,d.jsx)(l.b,{id:`section-globs-${a.id}`,value:a.globs||"",onChange:a=>u("globs",a.target.value),placeholder:"e.g., **.css, *.js"})]}),(0,d.jsxs)(g.a,{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:`section-description-${a.id}`,size:"2",weight:"medium",children:"Description (Optional)"}),(0,d.jsx)(l.b,{id:`section-description-${a.id}`,value:a.description||"",onChange:a=>u("description",a.target.value),placeholder:"Brief description"})]})]}),(0,d.jsxs)(g.a,{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:`section-content-${a.id}`,size:"2",weight:"medium",children:"Content"}),(0,d.jsx)(I.B,{value:a.content,onChange:a=>u("content",a),placeholder:"Enter section content (markdown supported)..."})]})]})]})}function a7({sections:a,onSectionsChange:b}){let[c,g]=(0,e.useState)(new Set(1===a.length?[a[0]?.id]:[])),h=()=>{var c;let d=(c=a.length,{id:a5(),title:`Section ${c+1}`,content:""});b([...a,d]),g(a=>new Set([...a,d.id]))},i=(c,d)=>{b(function(a,b,c){let d=[...a],[e]=d.splice(b,1);return d.splice(c,0,e),d}(a,c,d))};return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(j.E,{className:"text-base font-medium",children:"Rule Sections"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",a.length," section",1!==a.length?"s":"",")"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[a.length>1&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(f.$,{variant:"ghost",size:"1",onClick:()=>{g(new Set([...a.map(a=>a.id)]))},className:"text-xs",children:"Expand All"}),(0,d.jsx)(f.$,{variant:"ghost",size:"1",onClick:()=>{g(new Set([]))},className:"text-xs",children:"Collapse All"})]}),(0,d.jsxs)(f.$,{variant:"outline",size:"1",onClick:h,className:"gap-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Add Section"]})]})]}),(0,d.jsx)("div",{className:"space-y-3",children:0===a.length?(0,d.jsxs)("div",{className:"text-center py-12 border-2 border-dashed border-muted rounded-lg",children:[(0,d.jsx)(aW.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No sections yet"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add your first section to get started"}),(0,d.jsxs)(f.$,{onClick:h,className:"gap-2",children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),"Add Section"]})]}):a.map((e,f)=>(0,d.jsx)(a6,{section:e,index:f,totalSections:a.length,isExpanded:c.has(e.id),onUpdate:c=>((c,d)=>{let e=[...a];e[c]=d,b(e)})(f,c),onDelete:()=>(c=>{if(1===a.length)return;let d=a[c];b(a.filter((a,b)=>b!==c)),g(a=>{let b=new Set(a);return b.delete(d.id),b})})(f),onDuplicate:()=>(c=>{var d;let e={...d=a[c],id:a5(),title:`${d.title} (Copy)`},f=[...a];f.splice(c+1,0,e),b(f),g(a=>new Set([...a,e.id]))})(f),onMoveUp:()=>i(f,f-1),onMoveDown:()=>i(f,f+1),onToggleExpanded:()=>{var a;return a=e.id,void g(b=>{let c=new Set(b);return c.has(a)?c.delete(a):c.add(a),c})}},e.id))}),a.length>0&&(0,d.jsxs)("div",{className:"text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg",children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Section Format:"}),(0,d.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,d.jsxs)("li",{children:["• Sections are separated by ",(0,d.jsx)("code",{children:"---"})," delimiters"]}),(0,d.jsxs)("li",{children:["• Optional metadata: ",(0,d.jsx)("code",{children:"description"}),", ",(0,d.jsx)("code",{children:"name"}),", ",(0,d.jsx)("code",{children:"globs"})]}),(0,d.jsx)("li",{children:"• Content supports markdown formatting"}),(0,d.jsx)("li",{children:"• Use file patterns (globs) to target specific file types"})]})]})]})}function a8({rule:a,onSave:b,onCancel:c}){let[g,i]=(0,e.useState)(a?.title||""),[k,o]=(0,e.useState)(a?.description||""),[p,s]=(0,e.useState)([]),[t,u]=(0,e.useState)(a?.content||""),[v,w]=(0,e.useState)("simple"),[x,y]=(0,e.useState)(a?.ideType||"GENERAL"),[z,A]=(0,e.useState)(a?.visibility||"PRIVATE"),[B,C]=(0,e.useState)(a?.tags.map(a=>a.tag.name)||[]),[D,E]=(0,e.useState)(""),J=()=>{D.trim()&&!B.includes(D.trim())&&(C([...B,D.trim()]),E(""))};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:"title",size:"2",weight:"medium",children:"Rule Title"}),(0,d.jsx)(l.b,{id:"title",value:g,onChange:a=>i(a.target.value),placeholder:"Enter rule title..."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:"description",size:"2",weight:"medium",children:"Description"}),(0,d.jsx)(F,{id:"description",value:k,onChange:a=>o(a.target.value),placeholder:"Describe what this rule does...",rows:3})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:"ide-type",size:"2",weight:"medium",children:"IDE Type"}),(0,d.jsxs)(m.bL,{value:x,onValueChange:a=>y(a),children:[(0,d.jsx)(m.l9,{placeholder:"Select IDE type"}),(0,d.jsxs)(m.UC,{children:[(0,d.jsx)(m.q7,{value:"GENERAL",children:"General"}),(0,d.jsx)(m.q7,{value:"CURSOR",children:"Cursor"}),(0,d.jsx)(m.q7,{value:"AUGMENT",children:"Augment Code"}),(0,d.jsx)(m.q7,{value:"WINDSURF",children:"Windsurf"}),(0,d.jsx)(m.q7,{value:"CLAUDE",children:"Claude"}),(0,d.jsx)(m.q7,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,d.jsx)(m.q7,{value:"GEMINI",children:"Gemini"}),(0,d.jsx)(m.q7,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,d.jsx)(m.q7,{value:"CLINE",children:"Cline"}),(0,d.jsx)(m.q7,{value:"JUNIE",children:"Junie"}),(0,d.jsx)(m.q7,{value:"TRAE",children:"Trae"}),(0,d.jsx)(m.q7,{value:"LINGMA",children:"Lingma"}),(0,d.jsx)(m.q7,{value:"KIRO",children:"Kiro"}),(0,d.jsx)(m.q7,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{as:"label",htmlFor:"visibility",size:"2",weight:"medium",children:"Visibility"}),(0,d.jsxs)(m.bL,{value:z,onValueChange:a=>A(a),children:[(0,d.jsx)(m.l9,{placeholder:"Select visibility"}),(0,d.jsxs)(m.UC,{children:[(0,d.jsx)(m.q7,{value:"PRIVATE",children:"Private"}),(0,d.jsx)(m.q7,{value:"PUBLIC",children:"Public"})]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{as:"label",size:"2",weight:"medium",children:"Tags"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:B.map(a=>(0,d.jsxs)(n.E,{variant:"soft",className:"gap-1",children:[a,(0,d.jsx)(f.$,{variant:"ghost",size:"1",className:"h-auto p-0 w-4 h-4",onClick:()=>{C(B.filter(b=>b!==a))},children:(0,d.jsx)(G,{className:"h-3 w-3"})})]},a))}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(l.b,{value:D,onChange:a=>E(a.target.value),placeholder:"Add a tag...",onKeyPress:a=>"Enter"===a.key&&J()}),(0,d.jsx)(f.$,{onClick:J,size:"1",children:(0,d.jsx)(q.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",children:[(0,d.jsx)(j.E,{as:"label",size:"2",weight:"medium",children:"Rule Content"}),(0,d.jsxs)(aU,{type:"single",value:v,onValueChange:a=>a&&void(a!==v&&("simple"===a?u(a4(p)):s(a3(t)),w(a))),className:"bg-gray-50 dark:bg-gray-800 rounded-md p-1",children:[(0,d.jsxs)(aV,{value:"simple",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),"Simple"]}),(0,d.jsxs)(aV,{value:"advanced",className:"flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700",children:[(0,d.jsx)(H.A,{className:"h-4 w-4"}),"Advanced"]})]})]}),"simple"===v?(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content in a simple code editor format"}),(0,d.jsx)(I.B,{value:t,onChange:a=>{u(a),"simple"===v&&s(a3(a))},placeholder:"Enter your rule content here...",className:"min-h-[300px]"})]}):(0,d.jsxs)("div",{children:[(0,d.jsx)(j.E,{size:"1",color:"gray",className:"mb-2 block",children:"Edit your rule content using structured sections with metadata"}),(0,d.jsx)(a7,{sections:p,onSectionsChange:a=>{s(a),"advanced"===v&&u(a4(a))}})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,d.jsx)(f.$,{variant:"outline",onClick:c,children:"Cancel"}),(0,d.jsx)(f.$,{onClick:()=>{b({title:g,description:k,content:"simple"===v?t:a4(p),ideType:x,visibility:z,tags:B})},children:a?"Update Rule":"Create Rule"})]})]})}var a9=c(73916),ba=c(81604);let bb="force-dynamic";function bc(){let{data:a}=(0,a9.wV)(),[b,c]=(0,e.useState)([]),[t,z]=(0,e.useState)([]),[A,B]=(0,e.useState)(!0),[C,D]=(0,e.useState)(""),[E,F]=(0,e.useState)([]),[G,H]=(0,e.useState)("ALL"),[I,J]=(0,e.useState)(!1),[K,L]=(0,e.useState)(null),M=(0,e.useCallback)(async()=>{try{let a=new URLSearchParams;C&&a.set("search",C),E.length>0&&a.set("tags",E.join(",")),"ALL"!==G&&a.set("ideType",G);let b=await fetch(`/api/rules?${a}`),d=await b.json();c(Array.isArray(d)?d:[])}catch(a){console.error("Error fetching rules:",a),ba.oR.error("Failed to fetch rules"),c([])}finally{B(!1)}},[C,E,G]),N=()=>{L(null),J(!0)},O=a=>{L(a),J(!0)},P=async a=>{try{let b=K?"PUT":"POST",c=K?`/api/rules/${K.id}`:"/api/rules";(await fetch(c,{method:b,headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok?(ba.oR.success(K?"Rule updated":"Rule created"),J(!1),L(null),M()):ba.oR.error("Failed to save rule")}catch(a){console.error("Error saving rule:",a),ba.oR.error("Failed to save rule")}},Q=async a=>{try{(await fetch(`/api/rules/${a}`,{method:"DELETE"})).ok?(ba.oR.success("Rule deleted"),M()):ba.oR.error("Failed to delete rule")}catch(a){console.error("Error deleting rule:",a),ba.oR.error("Failed to delete rule")}};if(!a?.user)return(0,d.jsx)("div",{className:"container py-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-4",style:{color:"hsl(0 0% 98%)"},children:"Please sign in to continue"}),(0,d.jsx)(f.$,{asChild:!0,children:(0,d.jsx)("a",{href:"/auth/signin",children:"Sign In"})})]})});let R=a||null,S=Array.isArray(b)?b.filter(a=>a.userId===R?.user?.id):[],T=Array.isArray(b)?b.filter(a=>"PUBLIC"===a.visibility):[],U={totalRules:S.length,publicRules:S.filter(a=>"PUBLIC"===a.visibility).length,privateRules:S.filter(a=>"PRIVATE"===a.visibility).length,totalViews:0};return(0,d.jsxs)(g.a,{className:"container py-8 space-y-8",children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",children:[(0,d.jsxs)(g.a,{children:[(0,d.jsx)(i.D,{size:"8",weight:"bold",color:"gray",highContrast:!0,children:"Dashboard"}),(0,d.jsx)(j.E,{size:"3",color:"gray",children:"Manage your AI prompt rules and explore the community"})]}),(0,d.jsxs)(f.$,{onClick:N,children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"New Rule"]})]}),(0,d.jsxs)(g.a,{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsxs)(k.Z,{children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",pb:"2",children:[(0,d.jsx)(j.E,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Total Rules"}),(0,d.jsx)(r.A,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,d.jsx)(j.E,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:U.totalRules})]}),(0,d.jsxs)(k.Z,{children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",pb:"2",children:[(0,d.jsx)(j.E,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Public Rules"}),(0,d.jsx)(s.A,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,d.jsx)(j.E,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:U.publicRules})]}),(0,d.jsxs)(k.Z,{children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",pb:"2",children:[(0,d.jsx)(j.E,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Private Rules"}),(0,d.jsx)(u,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,d.jsx)(j.E,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:U.privateRules})]}),(0,d.jsxs)(k.Z,{children:[(0,d.jsxs)(h.s,{justify:"between",align:"center",pb:"2",children:[(0,d.jsx)(j.E,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Total Views"}),(0,d.jsx)(v.A,{className:"h-4 w-4",style:{color:"var(--gray-11)"}})]}),(0,d.jsx)(j.E,{size:"7",weight:"bold",color:"gray",highContrast:!0,children:U.totalViews})]})]}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(l.b,{placeholder:"Search rules...",value:C,onChange:a=>D(a.target.value),className:"pl-10"})]}),(0,d.jsxs)(m.bL,{value:G,onValueChange:H,children:[(0,d.jsx)(m.l9,{className:"w-full md:w-48",placeholder:"IDE Type"}),(0,d.jsxs)(m.UC,{children:[(0,d.jsx)(m.q7,{value:"ALL",children:"All IDEs"}),(0,d.jsx)(m.q7,{value:"GENERAL",children:"General"}),(0,d.jsx)(m.q7,{value:"CURSOR",children:"Cursor"}),(0,d.jsx)(m.q7,{value:"AUGMENT",children:"Augment Code"}),(0,d.jsx)(m.q7,{value:"WINDSURF",children:"Windsurf"}),(0,d.jsx)(m.q7,{value:"CLAUDE",children:"Claude"}),(0,d.jsx)(m.q7,{value:"GITHUB_COPILOT",children:"GitHub Copilot"}),(0,d.jsx)(m.q7,{value:"GEMINI",children:"Gemini"}),(0,d.jsx)(m.q7,{value:"OPENAI_CODEX",children:"OpenAI Codex"}),(0,d.jsx)(m.q7,{value:"CLINE",children:"Cline"}),(0,d.jsx)(m.q7,{value:"JUNIE",children:"Junie"}),(0,d.jsx)(m.q7,{value:"TRAE",children:"Trae"}),(0,d.jsx)(m.q7,{value:"LINGMA",children:"Lingma"}),(0,d.jsx)(m.q7,{value:"KIRO",children:"Kiro"}),(0,d.jsx)(m.q7,{value:"TENCENT_CODEBUDDY",children:"Tencent Cloud CodeBuddy"})]})]})]}),t.length>0&&(0,d.jsxs)(g.a,{className:"space-y-2",children:[(0,d.jsxs)(h.s,{align:"center",gap:"2",children:[(0,d.jsx)(x.A,{className:"h-4 w-4",style:{color:"var(--gray-12)"}}),(0,d.jsx)(j.E,{size:"2",weight:"medium",color:"gray",highContrast:!0,children:"Filter by tags:"})]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(a=>(0,d.jsx)(n.E,{variant:E.includes(a.name)?"solid":"outline",className:"cursor-pointer",onClick:()=>{var b;return b=a.name,void F(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])},style:{borderColor:a.color,backgroundColor:E.includes(a.name)?a.color:"transparent"},children:a.name},a.id))})]}),(0,d.jsxs)(o.bL,{defaultValue:"my-rules",className:"space-y-6",children:[(0,d.jsxs)(o.B8,{children:[(0,d.jsxs)(o.l9,{value:"my-rules",children:["My Rules (",S.length,")"]}),(0,d.jsxs)(o.l9,{value:"community",children:["Community (",T.length,")"]})]}),(0,d.jsx)(o.UC,{value:"my-rules",className:"space-y-6",children:0===S.length?(0,d.jsxs)(k.Z,{className:"py-12 text-center",children:[(0,d.jsx)(r.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No rules yet"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Create your first AI prompt rule to get started"}),(0,d.jsxs)(f.$,{onClick:N,children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Create Rule"]})]}):(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:S.map(a=>(0,d.jsx)(y.z,{rule:a,onEdit:O,onDelete:Q,isOwner:!0},a.id))})}),(0,d.jsx)(o.UC,{value:"community",className:"space-y-6",children:0===T.length?(0,d.jsxs)(k.Z,{className:"py-12 text-center",children:[(0,d.jsx)(s.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No community rules found"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"adjusting your filters or check back later"})]}):(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:T.map(a=>(0,d.jsx)(y.z,{rule:a,isOwner:a.userId===R?.user?.id},a.id))})})]}),(0,d.jsx)(p.bL,{open:I,onOpenChange:J,children:(0,d.jsxs)(p.UC,{className:"max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,d.jsx)(p.hE,{children:K?"Edit Rule":"Create New Rule"}),(0,d.jsx)("div",{className:"overflow-y-auto max-h-[70vh]",children:(0,d.jsx)(a8,{rule:K||void 0,onSave:P,onCancel:()=>{J(!1),L(null)}})})]})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76521:(a,b,c)=>{"use strict";c.d(b,{x:()=>n});var d=c(60159),e=c(86135),f=c(30101),g=c(23831),h=c(3293);let i={as:{type:"enum",values:["div","span"],default:"div"},...g.f,display:{type:"enum",className:"rt-r-display",values:["none","inline-grid","grid"],responsive:!0},areas:{type:"string",className:"rt-r-gta",customProperties:["--grid-template-areas"],responsive:!0},columns:{type:"enum | string",className:"rt-r-gtc",customProperties:["--grid-template-columns"],values:["1","2","3","4","5","6","7","8","9"],parseValue:j,responsive:!0},rows:{type:"enum | string",className:"rt-r-gtr",customProperties:["--grid-template-rows"],values:["1","2","3","4","5","6","7","8","9"],parseValue:j,responsive:!0},flow:{type:"enum",className:"rt-r-gaf",values:["row","column","dense","row-dense","column-dense"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(a){return"between"===a?"space-between":a},responsive:!0},...h.o};function j(a){return i.columns.values.includes(a)?a:a?.match(/^\d+$/)?`repeat(${a}, minmax(0, 1fr))`:a}var k=c(87160),l=c(2107),m=c(91683);let n=d.forwardRef((a,b)=>{let{className:c,asChild:g,as:h="div",...j}=(0,k.o)(a,i,l.i,m.y);return d.createElement(g?f.DX:h,{...j,ref:b,className:e("rt-Grid",c)})});n.displayName="Grid"},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,208,528,557,204,649],()=>b(b.s=890));module.exports=c})();