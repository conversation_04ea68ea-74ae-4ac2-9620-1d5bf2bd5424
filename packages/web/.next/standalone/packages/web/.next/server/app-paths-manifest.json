{"/api/auth/[...all]/route": "app/api/auth/[...all]/route.js", "/api/robots.txt/route": "app/api/robots.txt/route.js", "/api/rules/raw/route": "app/api/rules/raw/route.js", "/api/rules/download/route": "app/api/rules/download/route.js", "/api/sitemap.xml/route": "app/api/sitemap.xml/route.js", "/api/tags/route": "app/api/tags/route.js", "/api/rules/export/route": "app/api/rules/export/route.js", "/api/rules/route": "app/api/rules/route.js", "/api/rules/[id]/route": "app/api/rules/[id]/route.js", "/_not-found/page": "app/_not-found/page.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/r/[id]/page": "app/r/[id]/page.js", "/test-theme/page": "app/test-theme/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/rules/[id]/page": "app/rules/[id]/page.js", "/tutorials/page": "app/tutorials/page.js", "/dashboard/page": "app/dashboard/page.js", "/profile/[username]/page": "app/profile/[username]/page.js", "/templates/page": "app/templates/page.js", "/ides/augment/page": "app/ides/augment/page.js", "/ides/github-copilot/page": "app/ides/github-copilot/page.js", "/(static)/page": "app/(static)/page.js", "/ides/page": "app/ides/page.js", "/ides/windsurf/page": "app/ides/windsurf/page.js", "/ides/cursor/page": "app/ides/cursor/page.js", "/ides/claude/page": "app/ides/claude/page.js", "/ides/cline/page": "app/ides/cline/page.js"}