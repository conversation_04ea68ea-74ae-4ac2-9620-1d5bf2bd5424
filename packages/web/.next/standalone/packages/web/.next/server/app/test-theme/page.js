(()=>{var a={};a.id=67,a.ids=[67],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5229:(a,b,c)=>{"use strict";c.d(b,{Z:()=>j});var d=c(60159),e=c(86135),f=c(90691);let g={...c(23831).f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var h=c(87160),i=c(91683);let j=d.forwardRef((a,b)=>{let{asChild:c,className:j,...k}=(0,h.o)(a,g,i.y),l=c?f.bL:"div";return d.createElement(l,{ref:b,...k,className:e("rt-reset","rt-BaseCard","rt-Card",j)})});j.displayName="Card"},7906:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(24332),e=c(48819),f=c(93949),g=c(98730),h=c(88996),i=c(16318),j=c(3093),k=c(36748),l=c(98190),m=c(53904),n=c(47735),o=c(20611),p=c(22512),q=c(261),r=c(13863),s=c(8748),t=c(26713),u=c(65262),v=c(97779),w=c(5303),x=c(66704),y=c(67656),z=c(3072),A=c(86439),B=c(93824),C=c.n(B),D=c(97540),E=c(49005),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["test-theme",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,99982)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/test-theme/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,52868)),"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,93824,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,50134,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,35983,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,74482,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/test-theme/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/test-theme/page",pathname:"/test-theme",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/test-theme/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16996:(a,b,c)=>{"use strict";c.d(b,{b:()=>o});var d=c(60159),e=c(86135),f=c(11246),g=c(96171),h=c(716),i=c(88476),j=c(23726);let k={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft"],default:"surface"},...g._s,...i.F},l={side:{type:"enum",values:["left","right"]},...g._s,gap:j.F.gap,px:h.T.px,pl:h.T.pl,pr:h.T.pr};var m=c(87160),n=c(91683);let o=d.forwardRef((a,b)=>{let c=d.useRef(null),{children:g,className:h,color:i,radius:j,style:l,...o}=(0,m.o)(a,k,n.y);return d.createElement("div",{"data-accent-color":i,"data-radius":j,style:l,className:e("rt-TextFieldRoot",h),onPointerDown:a=>{let b=a.target;if(b.closest("input, button, a"))return;let d=c.current;if(!d)return;let e=b.closest(`
            .rt-TextFieldSlot[data-side='right'],
            .rt-TextFieldSlot:not([data-side='right']) ~ .rt-TextFieldSlot:not([data-side='left'])
          `)?d.value.length:0;requestAnimationFrame(()=>{try{d.setSelectionRange(e,e)}catch{}d.focus()})}},d.createElement("input",{spellCheck:"false",...o,ref:(0,f.t)(c,b),className:"rt-reset rt-TextFieldInput"}),g)});o.displayName="TextField.Root",d.forwardRef((a,b)=>{let{className:c,color:f,side:g,...h}=(0,m.o)(a,l);return d.createElement("div",{"data-accent-color":f,"data-side":g,...h,ref:b,className:e("rt-TextFieldSlot",c)})}).displayName="TextField.Slot"},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20638:(a,b,c)=>{Promise.resolve().then(c.bind(c,52830))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26716:(a,b,c)=>{"use strict";c.d(b,{a:()=>k});var d=c(60159),e=c(86135),f=c(30101);let g={as:{type:"enum",values:["div","span"],default:"div"},...c(23831).f,display:{type:"enum",className:"rt-r-display",values:["none","inline","inline-block","block","contents"],responsive:!0}};var h=c(87160),i=c(2107),j=c(91683);let k=d.forwardRef((a,b)=>{let{className:c,asChild:k,as:l="div",...m}=(0,h.o)(a,g,i.i,j.y);return d.createElement(k?f.DX:l,{...m,ref:b,className:e("rt-Box",c)})});k.displayName="Box"},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},52830:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>K});var d=c(13486),e=c(60159),f=c(86135),g=c(90691);let h={...c(23831).f,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"4",responsive:!0},display:{type:"enum",className:"rt-r-display",values:["none","initial"],parseValue:function(a){return"initial"===a?"flex":a},responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["left","center","right"],parseValue:function(a){return"left"===a?"start":"right"===a?"end":a},responsive:!0}};var i=c(87160),j=c(90833),k=c(60274),l=c(2107),m=c(91683),n=c(34589);let o=e.forwardRef(({width:a,minWidth:b,maxWidth:c,height:d,minHeight:o,maxHeight:p,...q},r)=>{let{asChild:s,children:t,className:u,...v}=(0,i.o)(q,h,l.i,m.y),{className:w,style:x}=(0,i.o)({width:a,minWidth:b,maxWidth:c,height:d,minHeight:o,maxHeight:p},n.w,k.B),y=s?g.bL:"div";return e.createElement(y,{...v,ref:r,className:f("rt-Container",u)},(0,j.T)({asChild:s,children:t},a=>e.createElement("div",{className:f("rt-ContainerInner",w),style:x},a)))});o.displayName="Container";var p=c(16918),q=c(26716),r=c(99865),s=c(16692),t=c(76521),u=c(5229),v=c(3935),w=c(16996),x=c(26857),y=c(74010),z=c(96171),A=c(81969);let B={orientation:{type:"enum",className:"rt-r-orientation",values:["horizontal","vertical"],default:"horizontal",responsive:!0},size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},trim:{...c(3117).$.trim,className:"rt-r-trim"}},C={align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0}},D={...n.w,...z._s,...A.Z},E=e.forwardRef((a,b)=>{let{className:c,...d}=(0,i.o)(a,B,m.y);return e.createElement(s.E,{asChild:!0},e.createElement("dl",{...d,ref:b,className:f("rt-DataListRoot",c)}))});E.displayName="DataList.Root";let F=e.forwardRef((a,b)=>{let{className:c,...d}=(0,i.o)(a,C);return e.createElement("div",{...d,ref:b,className:f("rt-DataListItem",c)})});F.displayName="DataList.Item";let G=e.forwardRef((a,b)=>{let{className:c,color:d,...g}=(0,i.o)(a,D);return e.createElement("dt",{...g,"data-accent-color":d,ref:b,className:f("rt-DataListLabel",c)})});G.displayName="DataList.Label";let H=e.forwardRef(({children:a,className:b,...c},d)=>e.createElement("dd",{...c,ref:d,className:f(b,"rt-DataListValue")},a));H.displayName="DataList.Value";var I=c(7921),J=c(62559);function K(){let[a,b]=(0,e.useState)(!1);return(0,d.jsx)(o,{size:"3",py:"8",children:(0,d.jsxs)(p.s,{direction:"column",gap:"8",children:[(0,d.jsxs)(q.a,{children:[(0,d.jsx)(r.D,{size:"8",mb:"4",children:"Radix UI Theme v3 Test Page"}),(0,d.jsx)(s.E,{size:"4",color:"gray",children:"This page demonstrates the proper implementation of Radix UI Theme v3 components with consistent styling and design patterns."})]}),(0,d.jsxs)(t.x,{columns:{initial:"1",md:"3"},gap:"4",children:[(0,d.jsx)(u.Z,{children:(0,d.jsxs)(p.s,{direction:"column",gap:"3",children:[(0,d.jsx)(r.D,{size:"4",children:"Classic Card"}),(0,d.jsx)(s.E,{color:"gray",children:"This is a classic card with default styling from Radix UI Themes."}),(0,d.jsx)(v.$,{variant:"soft",children:"Learn More"})]})}),(0,d.jsx)(u.Z,{variant:"surface",children:(0,d.jsxs)(p.s,{direction:"column",gap:"3",children:[(0,d.jsx)(r.D,{size:"4",children:"Surface Card"}),(0,d.jsx)(s.E,{color:"gray",children:"Surface variant provides a subtle background color."}),(0,d.jsx)(v.$,{variant:"surface",children:"Explore"})]})}),(0,d.jsx)(u.Z,{variant:"ghost",children:(0,d.jsxs)(p.s,{direction:"column",gap:"3",children:[(0,d.jsx)(r.D,{size:"4",children:"Ghost Card"}),(0,d.jsx)(s.E,{color:"gray",children:"Ghost variant has minimal visual styling."}),(0,d.jsx)(v.$,{variant:"outline",children:"Discover"})]})})]}),(0,d.jsxs)(u.Z,{size:"3",children:[(0,d.jsx)(r.D,{size:"5",mb:"4",children:"Form Elements"}),(0,d.jsxs)(p.s,{direction:"column",gap:"4",children:[(0,d.jsxs)(q.a,{children:[(0,d.jsx)(s.E,{as:"label",size:"2",weight:"medium",htmlFor:"name",children:"Name"}),(0,d.jsx)(w.b,{id:"name",placeholder:"Enter your name",mt:"1"})]}),(0,d.jsxs)(q.a,{children:[(0,d.jsx)(s.E,{as:"label",size:"2",weight:"medium",htmlFor:"role",children:"Role"}),(0,d.jsxs)(x.bL,{defaultValue:"developer",children:[(0,d.jsx)(x.l9,{id:"role",placeholder:"Select a role"}),(0,d.jsxs)(x.UC,{children:[(0,d.jsx)(x.q7,{value:"developer",children:"Developer"}),(0,d.jsx)(x.q7,{value:"designer",children:"Designer"}),(0,d.jsx)(x.q7,{value:"manager",children:"Manager"})]})]})]}),(0,d.jsxs)(p.s,{gap:"3",mt:"4",children:[(0,d.jsx)(v.$,{variant:"solid",children:"Submit"}),(0,d.jsx)(v.$,{variant:"soft",color:"gray",children:"Cancel"})]})]})]}),(0,d.jsx)(u.Z,{children:(0,d.jsxs)(y.bL,{defaultValue:"overview",children:[(0,d.jsxs)(y.B8,{children:[(0,d.jsx)(y.l9,{value:"overview",children:"Overview"}),(0,d.jsx)(y.l9,{value:"details",children:"Details"}),(0,d.jsx)(y.l9,{value:"settings",children:"Settings"})]}),(0,d.jsxs)(q.a,{pt:"3",children:[(0,d.jsx)(y.UC,{value:"overview",children:(0,d.jsx)(s.E,{size:"2",children:"This is the overview tab content. It demonstrates how tabs work in Radix UI Themes with proper spacing and typography."})}),(0,d.jsx)(y.UC,{value:"details",children:(0,d.jsxs)(E,{children:[(0,d.jsxs)(F,{children:[(0,d.jsx)(G,{children:"Status"}),(0,d.jsx)(H,{children:(0,d.jsx)(I.E,{color:"green",children:"Active"})})]}),(0,d.jsxs)(F,{children:[(0,d.jsx)(G,{children:"Version"}),(0,d.jsx)(H,{children:"3.0.0"})]}),(0,d.jsxs)(F,{children:[(0,d.jsx)(G,{children:"Theme"}),(0,d.jsx)(H,{children:"Radix UI"})]})]})}),(0,d.jsx)(y.UC,{value:"settings",children:(0,d.jsx)(s.E,{size:"2",children:"Settings configuration would go here."})})]})]})}),(0,d.jsx)(p.s,{gap:"3",children:(0,d.jsxs)(J.bL,{open:a,onOpenChange:b,children:[(0,d.jsx)(J.l9,{children:(0,d.jsx)(v.$,{children:"Open Dialog"})}),(0,d.jsxs)(J.UC,{maxWidth:"450px",children:[(0,d.jsx)(J.hE,{children:"Example Dialog"}),(0,d.jsx)(J.VY,{size:"2",mb:"4",children:"This dialog demonstrates proper theming with Radix UI v3."}),(0,d.jsxs)(p.s,{direction:"column",gap:"3",children:[(0,d.jsx)(w.b,{placeholder:"Enter some text..."}),(0,d.jsx)(s.E,{size:"2",color:"gray",children:"Dialog content is properly styled and accessible."})]}),(0,d.jsxs)(p.s,{gap:"3",mt:"4",justify:"end",children:[(0,d.jsx)(J.bm,{children:(0,d.jsx)(v.$,{variant:"soft",color:"gray",children:"Cancel"})}),(0,d.jsx)(v.$,{children:"Save Changes"})]})]})]})}),(0,d.jsxs)(q.a,{children:[(0,d.jsx)(r.D,{size:"5",mb:"4",children:"Color System"}),(0,d.jsxs)(p.s,{gap:"2",wrap:"wrap",children:[(0,d.jsx)(I.E,{children:"Default"}),(0,d.jsx)(I.E,{color:"blue",children:"Blue"}),(0,d.jsx)(I.E,{color:"green",children:"Green"}),(0,d.jsx)(I.E,{color:"red",children:"Red"}),(0,d.jsx)(I.E,{color:"orange",children:"Orange"}),(0,d.jsx)(I.E,{color:"purple",children:"Purple"}),(0,d.jsx)(I.E,{color:"pink",children:"Pink"}),(0,d.jsx)(I.E,{color:"yellow",children:"Yellow"})]})]})]})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74010:(a,b,c)=>{"use strict";c.d(b,{UC:()=>L,B8:()=>J,bL:()=>I,l9:()=>K});var d=c(60159),e=c(86135),f=c(66634),g=c(27134),h=c(64935),i=c(78998),j=c(94108),k=c(88200),l=c(40594),m=c(32194),n=c(13486),o="Tabs",[p,q]=(0,g.A)(o,[h.RG]),r=(0,h.RG)(),[s,t]=p(o),u=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:e,defaultValue:f,orientation:g="horizontal",dir:h,activationMode:i="automatic",...p}=a,q=(0,k.jH)(h),[r,t]=(0,l.i)({prop:d,onChange:e,defaultProp:f??"",caller:o});return(0,n.jsx)(s,{scope:c,baseId:(0,m.B)(),value:r,onValueChange:t,orientation:g,dir:q,activationMode:i,children:(0,n.jsx)(j.sG.div,{dir:q,"data-orientation":g,...p,ref:b})})});u.displayName=o;var v="TabsList",w=d.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...e}=a,f=t(v,c),g=r(c);return(0,n.jsx)(h.bL,{asChild:!0,...g,orientation:f.orientation,dir:f.dir,loop:d,children:(0,n.jsx)(j.sG.div,{role:"tablist","aria-orientation":f.orientation,...e,ref:b})})});w.displayName=v;var x="TabsTrigger",y=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:e=!1,...g}=a,i=t(x,c),k=r(c),l=B(i.baseId,d),m=C(i.baseId,d),o=d===i.value;return(0,n.jsx)(h.q7,{asChild:!0,...k,focusable:!e,active:o,children:(0,n.jsx)(j.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":m,"data-state":o?"active":"inactive","data-disabled":e?"":void 0,disabled:e,id:l,...g,ref:b,onMouseDown:(0,f.m)(a.onMouseDown,a=>{e||0!==a.button||!1!==a.ctrlKey?a.preventDefault():i.onValueChange(d)}),onKeyDown:(0,f.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&i.onValueChange(d)}),onFocus:(0,f.m)(a.onFocus,()=>{let a="manual"!==i.activationMode;o||e||!a||i.onValueChange(d)})})})});y.displayName=x;var z="TabsContent",A=d.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,forceMount:f,children:g,...h}=a,k=t(z,c),l=B(k.baseId,e),m=C(k.baseId,e),o=e===k.value,p=d.useRef(o);return d.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,n.jsx)(i.C,{present:f||o,children:({present:c})=>(0,n.jsx)(j.sG.div,{"data-state":o?"active":"inactive","data-orientation":k.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:m,tabIndex:0,...h,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&g})})});function B(a,b){return`${a}-trigger-${b}`}function C(a,b){return`${a}-content-${b}`}A.displayName=z;var D=c(96171),E=c(81969);let F={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end"],responsive:!0},...D._s,...E.Z};var G=c(87160),H=c(91683);let I=d.forwardRef((a,b)=>{let{className:c,...f}=(0,G.o)(a,H.y);return d.createElement(u,{...f,ref:b,className:e("rt-TabsRoot",c)})});I.displayName="Tabs.Root";let J=d.forwardRef((a,b)=>{let{className:c,color:f,...g}=(0,G.o)(a,F,H.y);return d.createElement(w,{"data-accent-color":f,...g,asChild:!1,ref:b,className:e("rt-BaseTabList","rt-TabsList",c)})});J.displayName="Tabs.List";let K=d.forwardRef((a,b)=>{let{className:c,children:f,...g}=a;return d.createElement(y,{...g,asChild:!1,ref:b,className:e("rt-reset","rt-BaseTabListTrigger","rt-TabsTrigger",c)},d.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabsTriggerInner"},f),d.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden"},f))});K.displayName="Tabs.Trigger";let L=d.forwardRef((a,b)=>{let{className:c,...f}=(0,G.o)(a,H.y);return d.createElement(A,{...f,ref:b,className:e("rt-TabsContent",c)})});L.displayName="Tabs.Content"},76521:(a,b,c)=>{"use strict";c.d(b,{x:()=>n});var d=c(60159),e=c(86135),f=c(30101),g=c(23831),h=c(3293);let i={as:{type:"enum",values:["div","span"],default:"div"},...g.f,display:{type:"enum",className:"rt-r-display",values:["none","inline-grid","grid"],responsive:!0},areas:{type:"string",className:"rt-r-gta",customProperties:["--grid-template-areas"],responsive:!0},columns:{type:"enum | string",className:"rt-r-gtc",customProperties:["--grid-template-columns"],values:["1","2","3","4","5","6","7","8","9"],parseValue:j,responsive:!0},rows:{type:"enum | string",className:"rt-r-gtr",customProperties:["--grid-template-rows"],values:["1","2","3","4","5","6","7","8","9"],parseValue:j,responsive:!0},flow:{type:"enum",className:"rt-r-gaf",values:["row","column","dense","row-dense","column-dense"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(a){return"between"===a?"space-between":a},responsive:!0},...h.o};function j(a){return i.columns.values.includes(a)?a:a?.match(/^\d+$/)?`repeat(${a}, minmax(0, 1fr))`:a}var k=c(87160),l=c(2107),m=c(91683);let n=d.forwardRef((a,b)=>{let{className:c,asChild:g,as:h="div",...j}=(0,k.o)(a,i,l.i,m.y);return d.createElement(g?f.DX:h,{...j,ref:b,className:e("rt-Grid",c)})});n.displayName="Grid"},80726:(a,b,c)=>{Promise.resolve().then(c.bind(c,99982))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},99982:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(66352).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/test-theme/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/test-theme/page.tsx","default")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[657,208,528,204],()=>b(b.s=7906));module.exports=c})();