(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,s={};function i(e){var t;const r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function c(e){const t=new Map;for(const r of e.split(/; */)){if(!r)continue;const e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}const[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function u(e){if(!e)return;const[[t,r],...n]=c(e),{domain:o,expires:a,httponly:s,maxage:i,path:u,samesite:d,secure:h}=Object.fromEntries(n.map((([e,t])=>[e.toLowerCase(),t])));return function(e){const t={};for(const r in e)e[r]&&(t[r]=e[r]);return t}({name:t,value:decodeURIComponent(r),domain:o,...a&&{expires:new Date(a)},...s&&{httpOnly:!0},..."string"==typeof i&&{maxAge:Number(i)},path:u,...d&&{sameSite:(f=d,f=f.toLowerCase(),l.includes(f)?f:void 0)},...h&&{secure:!0}});var f}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(s,{RequestCookies:()=>h,ResponseCookies:()=>f,parseCookie:()=>c,parseSetCookie:()=>u,splitCookiesString:()=>d,stringifyCookie:()=>i}),e.exports=(t=s,((e,t,s,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of o(t))a.call(e,s)||undefined===s||r(e,s,{get:()=>t[s],enumerable:!(i=n(t,s))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t));var l=["strict","lax","none"];function d(e){if(!e)return[];var t,r,n,o,a,s=[],i=0;function c(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,a=!1;c();)if(","===(r=e.charAt(i))){for(n=i,i+=1,c(),o=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(a=!0,i=o,s.push(e.substring(t,n)),t=i):i=n+1}else i+=1;(!a||i>=e.length)&&s.push(e.substring(t,e.length))}return s}var h=class{constructor(e){this._parsed=new Map,this._headers=e;const t=e.get("cookie");if(t){const e=c(t);for(const[t,r]of e)this._parsed.set(t,{name:t,value:r})}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){const t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;const r=Array.from(this._parsed);if(!e.length)return r.map((([e,t])=>t));const n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter((([e])=>e===n)).map((([e,t])=>t))}has(e){return this._parsed.has(e)}set(...e){const[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map((([e,t])=>i(t))).join("; ")),this}delete(e){const t=this._parsed,r=Array.isArray(e)?e.map((e=>t.delete(e))):t.delete(e);return this._headers.set("cookie",Array.from(t).map((([e,t])=>i(t))).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map((e=>`${e.name}=${encodeURIComponent(e.value)}`)).join("; ")}},f=class{constructor(e){var t;this._parsed=new Map,this._headers=e;const r=null==(t=e.getSetCookie)?void 0:t.call(e);e.get("set-cookie");const n=Array.isArray(r)?r:d(r);for(const e of n){const t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){const t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;const r=Array.from(this._parsed.values());if(!e.length)return r;const n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter((e=>e.name===n))}has(e){return this._parsed.has(e)}set(...e){const[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),null!==e.path&&void 0!==e.path||(e.path="/"),e}({name:t,value:r,...n})),function(e,t){t.delete("set-cookie");for(const[,r]of e){const e=i(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){const[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{var e=t;e.parse=function(e,t){if("string"!=typeof e)throw new TypeError("argument str must be a string");for(var n={},a=t||{},i=e.split(o),c=a.decode||r,u=0;u<i.length;u++){var l=i[u],d=l.indexOf("=");if(!(d<0)){var h=l.substr(0,d).trim(),f=l.substr(++d,l.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),null==n[h]&&(n[h]=s(f,c))}}return n},e.serialize=function(e,t,r){var o=r||{},s=o.encode||n;if("function"!=typeof s)throw new TypeError("option encode is invalid");if(!a.test(e))throw new TypeError("argument name is invalid");var i=s(t);if(i&&!a.test(i))throw new TypeError("argument val is invalid");var c=e+"="+i;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw new TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(u)}if(o.domain){if(!a.test(o.domain))throw new TypeError("option domain is invalid");c+="; Domain="+o.domain}if(o.path){if(!a.test(o.path))throw new TypeError("option path is invalid");c+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw new TypeError("option expires is invalid");c+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(c+="; HttpOnly"),o.secure&&(c+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"strict":c+="; SameSite=Strict";break;case"none":c+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return c};var r=decodeURIComponent,n=encodeURIComponent,o=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function s(e,t){try{return t(e)}catch(t){return e}}})(),e.exports=t})()},"./dist/compiled/react/cjs/react.production.min.js":(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.for("react.default_value"),m=Symbol.iterator,g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},v=Object.assign,y={};function S(e,t,r){this.props=e,this.context=t,this.refs=y,this.updater=r||g}function w(){}function b(e,t,r){this.props=e,this.context=t,this.refs=y,this.updater=r||g}S.prototype.isReactComponent={},S.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},S.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},w.prototype=S.prototype;var x=b.prototype=new w;x.constructor=b,v(x,S.prototype),x.isPureReactComponent=!0;var _=Array.isArray,R=Object.prototype.hasOwnProperty,C={current:null},P={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,n){var o,a={},s=null,i=null;if(null!=t)for(o in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(s=""+t.key),t)R.call(t,o)&&!P.hasOwnProperty(o)&&(a[o]=t[o]);var c=arguments.length-2;if(1===c)a.children=n;else if(1<c){for(var u=Array(c),l=0;l<c;l++)u[l]=arguments[l+2];a.children=u}if(e&&e.defaultProps)for(o in c=e.defaultProps)void 0===a[o]&&(a[o]=c[o]);return{$$typeof:r,type:e,key:s,ref:i,props:a,_owner:C.current}}function k(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var A=/\/+/g;function T(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function O(e,t,o,a,s){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var c=!1;if(null===e)c=!0;else switch(i){case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case r:case n:c=!0}}if(c)return s=s(c=e),e=""===a?"."+T(c,0):a,_(s)?(o="",null!=e&&(o=e.replace(A,"$&/")+"/"),O(s,t,o,"",(function(e){return e}))):null!=s&&(k(s)&&(s=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,o+(!s.key||c&&c.key===s.key?"":(""+s.key).replace(A,"$&/")+"/")+e)),t.push(s)),1;if(c=0,a=""===a?".":a+":",_(e))for(var u=0;u<e.length;u++){var l=a+T(i=e[u],u);c+=O(i,t,o,l,s)}else if(l=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=m&&e[m]||e["@@iterator"])?e:null}(e),"function"==typeof l)for(e=l.call(e),u=0;!(i=e.next()).done;)c+=O(i=i.value,t,o,l=a+T(i,u++),s);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return c}function N(e,t,r){if(null==e)return e;var n=[],o=0;return O(e,n,"","",(function(e){return t.call(r,e,o++)})),n}function L(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var j={current:null};function $(){return new WeakMap}var I={current:null},M={transition:null},H={ReactCurrentDispatcher:I,ReactCurrentCache:j,ReactCurrentBatchConfig:M,ReactCurrentOwner:C,ContextRegistry:{}},D=H.ContextRegistry;t.Children={map:N,forEach:function(e,t,r){N(e,(function(){t.apply(this,arguments)}),r)},count:function(e){var t=0;return N(e,(function(){t++})),t},toArray:function(e){return N(e,(function(e){return e}))||[]},only:function(e){if(!k(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=S,t.Fragment=o,t.Profiler=s,t.PureComponent=b,t.StrictMode=a,t.Suspense=d,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=H,t.cache=function(e){return function(){var t=j.current;if(!t)return e.apply(null,arguments);var r=t.getCacheForType($);void 0===(t=r.get(e))&&(t={s:0,v:void 0,o:null,p:null},r.set(e,t)),r=0;for(var n=arguments.length;r<n;r++){var o=arguments[r];if("function"==typeof o||"object"==typeof o&&null!==o){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(o))&&(t={s:0,v:void 0,o:null,p:null},a.set(o,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(o))&&(t={s:0,v:void 0,o:null,p:null},a.set(o,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(r=t).s=1,r.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=v({},e.props),a=e.key,s=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,i=C.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(u in t)R.call(t,u)&&!P.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==c?c[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){c=Array(u);for(var l=0;l<u;l++)c[l]=arguments[l+2];o.children=c}return{$$typeof:r,type:e.type,key:a,ref:s,props:o,_owner:i}},t.createContext=function(e){return(e={$$typeof:c,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.createServerContext=function(e,t){var r=!0;if(!D[e]){r=!1;var n={$$typeof:u,_currentValue:t,_currentValue2:t,_defaultValue:t,_threadCount:0,Provider:null,Consumer:null,_globalName:e};n.Provider={$$typeof:i,_context:n},D[e]=n}if((n=D[e])._defaultValue===p)n._defaultValue=t,n._currentValue===p&&(n._currentValue=t),n._currentValue2===p&&(n._currentValue2=t);else if(r)throw Error("ServerContext: "+e+" already defined");return n},t.forwardRef=function(e){return{$$typeof:l,render:e}},t.isValidElement=k,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.unstable_useCacheRefresh=function(){return I.current.useCacheRefresh()},t.use=function(e){return I.current.use(e)},t.useCallback=function(e,t){return I.current.useCallback(e,t)},t.useContext=function(e){return I.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return I.current.useDeferredValue(e)},t.useEffect=function(e,t){return I.current.useEffect(e,t)},t.useId=function(){return I.current.useId()},t.useImperativeHandle=function(e,t,r){return I.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return I.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return I.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return I.current.useMemo(e,t)},t.useReducer=function(e,t,r){return I.current.useReducer(e,t,r)},t.useRef=function(e){return I.current.useRef(e)},t.useState=function(e){return I.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return I.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return I.current.useTransition()},t.version="18.3.0-canary-d6dcad6a8-20230914"},"./dist/compiled/react/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react/cjs/react.production.min.js")},"./dist/esm/lib/web/chalk.js":(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});const n=new Proxy((e=>e),{get:(e,t)=>["hex","rgb","ansi256","bgHex","bgRgb","bgAnsi256"].includes(t)?()=>n:n}),o=n}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n),r.d(n,{AppRouteRouteModule:()=>ke,default:()=>Ae});var e={};r.r(e),r.d(e,{DYNAMIC_ERROR_CODE:()=>de,DynamicServerError:()=>he});var t={};r.r(t),r.d(t,{cookies:()=>we,draftMode:()=>be,headers:()=>Se});var o={};r.r(o),r.d(o,{AppRouterContext:()=>Re,CacheStates:()=>xe,GlobalLayoutRouterContext:()=>Pe,LayoutRouterContext:()=>Ce,TemplateContext:()=>Ee});var a={};r.r(a),r.d(a,{appRouterContext:()=>o});class s{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}const i=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]];class c{static get(e,t,r){const n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}class u extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new u}}class l extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return c.get(t,r,n);const o=r.toLowerCase(),a=Object.keys(e).find((e=>e.toLowerCase()===o));return void 0!==a?c.get(t,a,n):void 0},set(t,r,n,o){if("symbol"==typeof r)return c.set(t,r,n,o);const a=r.toLowerCase(),s=Object.keys(e).find((e=>e.toLowerCase()===a));return c.set(t,s??r,n,o)},has(t,r){if("symbol"==typeof r)return c.has(t,r);const n=r.toLowerCase(),o=Object.keys(e).find((e=>e.toLowerCase()===n));return void 0!==o&&c.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return c.deleteProperty(t,r);const n=r.toLowerCase(),o=Object.keys(e).find((e=>e.toLowerCase()===n));return void 0===o||c.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return u.callable;default:return c.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new l(e)}append(e,t){const r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){const t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(const[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(const e of Object.keys(this.headers)){const t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(const e of Object.keys(this.headers)){const t=e.toLowerCase();yield t}}*values(){for(const e of Object.keys(this.headers)){const t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var d=r("./dist/compiled/@edge-runtime/cookies/index.js");class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new h}}class f{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return h.callable;default:return c.get(e,t,r)}}})}}const p=Symbol.for("next.mutated.cookies");function m(e,t){const r=function(e){const t=e[p];return t&&Array.isArray(t)&&0!==t.length?t:[]}(t);if(0===r.length)return!1;const n=new d.ResponseCookies(e),o=n.getAll();for(const e of r)n.set(e);for(const e of o)n.set(e);return!0}class g{static wrap(e,t){const r=new d.ResponseCookies(new Headers);for(const t of e.getAll())r.set(t);let n=[];const o=new Set,a=()=>{var e;const a=null==fetch.__nextGetStaticStore||null==(e=fetch.__nextGetStaticStore.call(fetch))?void 0:e.getStore();a&&(a.pathWasRevalidated=!0);const s=r.getAll();if(n=s.filter((e=>o.has(e.name))),t){const e=[];for(const t of n){const r=new d.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case p:return n;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{a()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{a()}};default:return c.get(e,t,r)}}})}}const v="_N_T_",y="__prerender_bypass";Symbol("__next_preview_data"),Symbol(y);class S{constructor(e,t,r,n){var o;const a=e&&function(e,t){const r=l.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,s=null==(o=r.get(y))?void 0:o.value;this.isEnabled=Boolean(!a&&s&&e&&s===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}enable(){if(!this._previewModeId)throw new Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:y,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:y,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function w(e,t){const r=new d.RequestCookies(l.from(e));return g.wrap(r,t)}const b={wrap(e,{req:t,res:r,renderOpts:n},o){let a;function s(e){r&&r.setHeader("Set-Cookie",e)}n&&"previewProps"in n&&(a=n.previewProps);const c={},u={get headers(){return c.headers||(c.headers=function(e){const t=l.from(e);for(const e of i)t.delete(e.toString().toLowerCase());return l.seal(t)}(t.headers)),c.headers},get cookies(){return c.cookies||(c.cookies=function(e){const t=new d.RequestCookies(l.from(e));return f.seal(t)}(t.headers)),c.cookies},get mutableCookies(){return c.mutableCookies||(c.mutableCookies=w(t.headers,(null==n?void 0:n.onUpdateCookies)||(r?s:void 0))),c.mutableCookies},get draftMode(){return c.draftMode||(c.draftMode=new S(a,t,this.cookies,this.mutableCookies)),c.draftMode}};return e.run(u,o,u)}},x={wrap(e,{urlPathname:t,renderOpts:r},n){const o={isStaticGeneration:!r.supportsDynamicHTML&&!r.isDraftMode&&!r.isServerAction,urlPathname:t,pagePath:r.originalPathname,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode};return r.store=o,e.run(o,n,o)}};function _(){return new Response(null,{status:400})}function R(){return new Response(null,{status:405})}const C=["GET","HEAD","OPTIONS","POST","PUT","DELETE","PATCH"];var P,E,k,A,T,O,N,L,j,$,I;!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(P||(P={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(E||(E={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(k||(k={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(A||(A={})),function(e){e.startServer="startServer.startServer"}(T||(T={})),function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(O||(O={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(N||(N={})),function(e){e.executeRoute="Router.executeRoute"}(L||(L={})),function(e){e.runHandler="Node.runHandler"}(j||(j={})),function(e){e.runHandler="AppRouteRouteHandlers.runHandler"}($||($={})),function(e){e.generateMetadata="ResolveMetadata.generateMetadata"}(I||(I={}));const M=require("next/dist/server/lib/trace/tracer");let H;H=r("./dist/esm/lib/web/chalk.js").Z;const D=H,q={wait:D.white(D.bold("○")),error:D.red(D.bold("X")),warn:D.yellow(D.bold("⚠")),ready:D.bold("▲"),info:D.white(D.bold(" ")),event:D.green(D.bold("✓")),trace:D.magenta(D.bold("»"))},U={log:"log",warn:"warn",error:"error"};function G(...e){!function(e,...t){""!==t[0]&&void 0!==t[0]||1!==t.length||t.shift();const r=e in U?U[e]:"log",n=q[e];0===t.length?console[r](""):console[r](" "+n,...t)}("warn",...e)}new Set;const B=e=>{const t=["/layout"];if(e.startsWith("/")){const r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${n.endsWith("/")?"":"/"}layout`),t.push(n))}}return t};function F(e){const t=[];if(!e)return t;const{pagePath:r,urlPathname:n}=e;if(Array.isArray(e.tags)||(e.tags=[]),r){const n=B(r);for(let r of n){var o;r=`${v}${r}`,(null==(o=e.tags)?void 0:o.includes(r))||e.tags.push(r),t.push(r)}}if(n){var a;const r=`${v}${n}`;(null==(a=e.tags)?void 0:a.includes(r))||e.tags.push(r),t.push(r)}return t}function W(e,t){if(!e)return;e.fetchMetrics||(e.fetchMetrics=[]);const r=["url","status","method"];e.fetchMetrics.some((e=>r.every((r=>e[r]===t[r]))))||e.fetchMetrics.push({url:t.url,cacheStatus:t.cacheStatus,cacheReason:t.cacheReason,status:t.status,method:t.method,start:t.start,end:Date.now(),idx:e.nextFetchId||0})}function V(e){return e.replace(/\/$/,"")||"/"}function z(e){const t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function Y(e,t){if(!e.startsWith("/")||!t)return e;const{pathname:r,query:n,hash:o}=z(e);return""+t+r+n+o}function Z(e,t){if(!e.startsWith("/")||!t)return e;const{pathname:r,query:n,hash:o}=z(e);return""+r+t+n+o}function X(e,t){if("string"!=typeof e)return!1;const{pathname:r}=z(e);return r===t||r.startsWith(t+"/")}function J(e,t){let r;const n=e.split("/");return(t||[]).some((t=>!(!n[1]||n[1].toLowerCase()!==t.toLowerCase()||(r=t,n.splice(1,1),e=n.join("/")||"/",0)))),{pathname:e,detectedLocale:r}}const K=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function Q(e,t){return new URL(String(e).replace(K,"localhost"),t&&String(t).replace(K,"localhost"))}const ee=Symbol("NextURLInternal");class te{constructor(e,t,r){let n,o;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,o=r||{}):o=r||t||{},this[ee]={url:Q(e,n??o.base),options:o,basePath:""},this.analyze()}analyze(){var e,t,r,n,o;const a=function(e,t){var r;const{basePath:n,i18n:o,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};n&&X(s.pathname,n)&&(s.pathname=function(e,t){if(!X(e,t))return e;const r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,n),s.basePath=n);let i=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){const e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];s.buildId=r,i="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=i)}if(o){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):J(s.pathname,o.locales);var c;s.locale=e.detectedLocale,s.pathname=null!=(c=e.pathname)?c:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(i):J(i,o.locales),e.detectedLocale&&(s.locale=e.detectedLocale))}return s}(this[ee].url.pathname,{nextConfig:this[ee].options.nextConfig,parseData:!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,i18nProvider:this[ee].options.i18nProvider}),s=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":")[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[ee].url,this[ee].options.headers);this[ee].domainLocale=this[ee].options.i18nProvider?this[ee].options.i18nProvider.detectDomainLocale(s):function(e,t,r){if(e){r&&(r=r.toLowerCase());for(const a of e){var n,o;if(t===(null==(n=a.domain)?void 0:n.split(":")[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(o=a.locales)?void 0:o.some((e=>e.toLowerCase()===r))))return a}}}(null==(t=this[ee].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,s);const i=(null==(r=this[ee].domainLocale)?void 0:r.defaultLocale)||(null==(o=this[ee].options.nextConfig)||null==(n=o.i18n)?void 0:n.defaultLocale);this[ee].url.pathname=a.pathname,this[ee].defaultLocale=i,this[ee].basePath=a.basePath??"",this[ee].buildId=a.buildId,this[ee].locale=a.locale??i,this[ee].trailingSlash=a.trailingSlash}formatPathname(){return function(e){let t=function(e,t,r,n){if(!t||t===r)return e;const o=e.toLowerCase();if(!n){if(X(o,"/api"))return e;if(X(o,"/"+t.toLowerCase()))return e}return Y(e,"/"+t)}(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return!e.buildId&&e.trailingSlash||(t=V(t)),e.buildId&&(t=Z(Y(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=Y(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:Z(t,"/"):V(t)}({basePath:this[ee].basePath,buildId:this[ee].buildId,defaultLocale:this[ee].options.forceLocale?void 0:this[ee].defaultLocale,locale:this[ee].locale,pathname:this[ee].url.pathname,trailingSlash:this[ee].trailingSlash})}formatSearch(){return this[ee].url.search}get buildId(){return this[ee].buildId}set buildId(e){this[ee].buildId=e}get locale(){return this[ee].locale??""}set locale(e){var t,r;if(!this[ee].locale||!(null==(r=this[ee].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw new TypeError(`The NextURL configuration includes no locale "${e}"`);this[ee].locale=e}get defaultLocale(){return this[ee].defaultLocale}get domainLocale(){return this[ee].domainLocale}get searchParams(){return this[ee].url.searchParams}get host(){return this[ee].url.host}set host(e){this[ee].url.host=e}get hostname(){return this[ee].url.hostname}set hostname(e){this[ee].url.hostname=e}get port(){return this[ee].url.port}set port(e){this[ee].url.port=e}get protocol(){return this[ee].url.protocol}set protocol(e){this[ee].url.protocol=e}get href(){const e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[ee].url=Q(e),this.analyze()}get origin(){return this[ee].url.origin}get pathname(){return this[ee].url.pathname}set pathname(e){this[ee].url.pathname=e}get hash(){return this[ee].url.hash}set hash(e){this[ee].url.hash=e}get search(){return this[ee].url.search}set search(e){this[ee].url.search=e}get password(){return this[ee].url.password}set password(e){this[ee].url.password=e}get username(){return this[ee].url.username}set username(e){this[ee].url.username=e}get basePath(){return this[ee].basePath}set basePath(e){this[ee].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new te(String(this),this[ee].options)}}function re(e){const t=new URL(e);return t.host="localhost:3000",t.search="",t.protocol="http",t.toString()}const ne=require("next/dist/client/components/request-async-storage.external.js"),oe="NEXT_REDIRECT";var ae;function se(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;const[t,r,n,o]=e.digest.split(";",4);return!(t!==oe||"replace"!==r&&"push"!==r||"string"!=typeof n||"true"!==o&&"false"!==o)}!function(e){e.push="push",e.replace="replace"}(ae||(ae={}));const ie=["HEAD","OPTIONS"],ce=["OPTIONS","POST","PUT","DELETE","PATCH"];var ue;function le(e){const t={};for(const[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}!function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(ue||(ue={}));const de="DYNAMIC_SERVER_USAGE";class he extends Error{constructor(e){super("Dynamic server usage: "+e),this.digest=de}}const fe=require("next/dist/client/components/action-async-storage.external.js"),pe=require("next/dist/client/components/static-generation-async-storage.external.js");class me extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}function ge(e,t){const{dynamic:r,link:n}=t||{};return"Page"+(r?' with `dynamic = "'+r+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(n?" See more info here: "+n:"")}const ve=(e,t)=>{const r=pe.staticGenerationAsyncStorage.getStore();if(null==r?void 0:r.forceStatic)return!0;var n;if(null==r?void 0:r.dynamicShouldError)throw new me(ge(e,{...t,dynamic:null!=(n=null==t?void 0:t.dynamic)?n:"error"}));if(r&&(r.revalidate=0),null==r?void 0:r.isStaticGeneration){const n=new he(ge(e,{...t,link:"https://nextjs.org/docs/messages/dynamic-server-error"}));throw r.dynamicUsageDescription=e,r.dynamicUsageStack=n.stack,n}return!1};class ye{get isEnabled(){return this._provider.isEnabled}enable(){if(!ve("draftMode().enable()"))return this._provider.enable()}disable(){if(!ve("draftMode().disable()"))return this._provider.disable()}constructor(e){this._provider=e}}function Se(){if(ve("headers",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return l.seal(new Headers({}));const e=ne.requestAsyncStorage.getStore();if(!e)throw new Error("Invariant: headers() expects to have requestAsyncStorage, none available.");return e.headers}function we(){if(ve("cookies",{link:"https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering"}))return f.seal(new d.RequestCookies(new Headers({})));const e=ne.requestAsyncStorage.getStore();if(!e)throw new Error("Invariant: cookies() expects to have requestAsyncStorage, none available.");const t=fe.actionAsyncStorage.getStore();return t&&(t.isAction||t.isAppRoute)?e.mutableCookies:e.cookies}function be(){const e=ne.requestAsyncStorage.getStore();if(!e)throw new Error("Invariant: draftMode() expects to have requestAsyncStorage, none available.");return new ye(e.draftMode)}var xe,_e=r("./dist/compiled/react/index.js");!function(e){e.LAZY_INITIALIZED="LAZYINITIALIZED",e.DATA_FETCH="DATAFETCH",e.READY="READY"}(xe||(xe={}));const Re=_e.createContext(null),Ce=_e.createContext(null),Pe=_e.createContext(null),Ee=_e.createContext(null);class ke extends s{static#e=this.sharedModules=a;static is(e){return e.definition.kind===ue.APP_ROUTE}constructor({userland:r,definition:n,resolvedPagePath:o,nextConfigOutput:a}){if(super({userland:r,definition:n}),this.requestAsyncStorage=ne.requestAsyncStorage,this.staticGenerationAsyncStorage=pe.staticGenerationAsyncStorage,this.serverHooks=e,this.headerHooks=t,this.staticGenerationBailout=ve,this.actionAsyncStorage=fe.actionAsyncStorage,this.resolvedPagePath=o,this.nextConfigOutput=a,this.methods=function(e){const t=C.reduce(((t,r)=>({...t,[r]:e[r]??R})),{}),r=new Set(C.filter((t=>e[t]))),n=ie.filter((e=>!r.has(e)));for(const o of n)if("HEAD"!==o){if("OPTIONS"!==o)throw new Error(`Invariant: should handle all automatic implementable methods, got method: ${o}`);{const e=["OPTIONS",...r];!r.has("HEAD")&&r.has("GET")&&e.push("HEAD");const n={Allow:e.sort().join(", ")};t.OPTIONS=()=>new Response(null,{status:204,headers:n}),r.add("OPTIONS")}}else{if(!e.GET)break;t.HEAD=e.GET,r.add("HEAD")}return t}(r),this.nonStaticMethods=function(e){const t=ce.filter((t=>e[t]));return 0!==t.length&&t}(r),this.dynamic=this.userland.dynamic,"export"===this.nextConfigOutput)if(this.dynamic&&"auto"!==this.dynamic){if("force-dynamic"===this.dynamic)throw new Error(`export const dynamic = "force-dynamic" on page "${n.pathname}" cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`)}else this.dynamic="error"}resolve(e){return t=e,C.includes(t)?this.methods[e]:_;var t}async execute(e,t){const r=this.resolve(e.method),n={req:e};n.renderOpts={previewProps:t.prerenderManifest.preview};const o={urlPathname:e.nextUrl.pathname,renderOpts:t.staticGenerationContext??{supportsDynamicHTML:!1,originalPathname:this.definition.pathname}};o.renderOpts.fetchCache=this.userland.fetchCache;const a=await this.actionAsyncStorage.run({isAppRoute:!0},(()=>b.wrap(this.requestAsyncStorage,n,(()=>x.wrap(this.staticGenerationAsyncStorage,o,(n=>{var o;switch(this.nonStaticMethods&&this.staticGenerationBailout(`non-static methods used ${this.nonStaticMethods.join(", ")}`),this.dynamic){case"force-dynamic":n.forceDynamic=!0,this.staticGenerationBailout("force-dynamic",{dynamic:this.dynamic});break;case"force-static":n.forceStatic=!0;break;case"error":n.dynamicShouldError=!0}n.revalidate??=this.userland.revalidate??!1;const a=function(e,{dynamic:t},r){function n(e){switch(e){case"search":case"searchParams":case"toString":case"href":case"origin":return void r.staticGenerationBailout(`nextUrl.${e}`);default:return}}const o={},a=(e,t)=>{switch(t){case"search":return"";case"searchParams":return o.searchParams||(o.searchParams=new URLSearchParams),o.searchParams;case"url":case"href":return o.url||(o.url=re(e)),o.url;case"toJSON":case"toString":return o.url||(o.url=re(e)),o.toString||(o.toString=()=>o.url),o.toString;case"headers":return o.headers||(o.headers=new Headers),o.headers;case"cookies":return o.headers||(o.headers=new Headers),o.cookies||(o.cookies=new d.RequestCookies(o.headers)),o.cookies;case"clone":return o.url||(o.url=re(e)),()=>new te(o.url)}},s=new Proxy(e.nextUrl,{get(e,r){if(n(r),"force-static"===t&&"string"==typeof r){const t=a(e.href,r);if(void 0!==t)return t}const o=e[r];return"function"==typeof o?o.bind(e):o},set:(e,t,r)=>(n(t),e[t]=r,!0)}),i=e=>{switch(e){case"headers":return void r.headerHooks.headers();case"url":case"body":case"blob":case"json":case"text":case"arrayBuffer":case"formData":return void r.staticGenerationBailout(`request.${e}`);default:return}};return new Proxy(e,{get(e,r){if(i(r),"nextUrl"===r)return s;if("force-static"===t&&"string"==typeof r){const t=a(e.url,r);if(void 0!==t)return t}const n=e[r];return"function"==typeof n?n.bind(e):n},set:(e,t,r)=>(i(t),e[t]=r,!0)})}(e,{dynamic:this.dynamic},{headerHooks:this.headerHooks,serverHooks:this.serverHooks,staticGenerationBailout:this.staticGenerationBailout}),s=function(e){let t="/app/";e.includes(t)||(t="\\app\\");const[,...r]=e.split(t);return(t[0]+r.join(t)).split(".").slice(0,-1).join(".")}(this.resolvedPagePath);return null==(o=(0,M.getTracer)().getRootSpanAttributes())||o.set("next.route",s),(0,M.getTracer)().trace($.runHandler,{spanName:`executing api route (app) ${s}`,attributes:{"next.route":s}},(async()=>{var e;!function({serverHooks:e,staticGenerationAsyncStorage:t}){if(globalThis._nextOriginalFetch||(globalThis._nextOriginalFetch=globalThis.fetch),globalThis.fetch.__nextPatched)return;const{DynamicServerError:r}=e,n=globalThis._nextOriginalFetch;globalThis.fetch=async(e,o)=>{var a,s;let i;try{i=new URL(e instanceof Request?e.url:e),i.username="",i.password=""}catch{i=void 0}const c=(null==i?void 0:i.href)??"",u=Date.now(),l=(null==o||null==(a=o.method)?void 0:a.toUpperCase())||"GET",d=!0===(null==(s=null==o?void 0:o.next)?void 0:s.internal);return await(0,M.getTracer)().trace(d?A.internalFetch:N.fetch,{kind:M.SpanKind.CLIENT,spanName:["fetch",l,c].filter(Boolean).join(" "),attributes:{"http.url":c,"http.method":l,"net.peer.name":null==i?void 0:i.hostname,"net.peer.port":(null==i?void 0:i.port)||void 0}},(async()=>{var a;const s=t.getStore()||(null==fetch.__nextGetStaticStore?void 0:fetch.__nextGetStaticStore.call(fetch)),i=e&&"object"==typeof e&&"string"==typeof e.method,l=t=>(i?e[t]:null)||(null==o?void 0:o[t]);if(!s||d||s.isDraftMode)return n(e,o);let h;const f=t=>{var r,n,a;return void 0!==(null==o||null==(r=o.next)?void 0:r[t])?null==o||null==(n=o.next)?void 0:n[t]:i?null==(a=e.next)?void 0:a[t]:void 0};let p=f("revalidate");const m=function(e,t){const r=[],n=[];for(const t of e)"string"!=typeof t?n.push({tag:t,reason:"invalid type, must be a string"}):t.length>256?n.push({tag:t,reason:"exceeded max length of 256"}):r.push(t);if(n.length>0){console.warn(`Warning: invalid tags passed to ${t}: `);for(const{tag:e,reason:t}of n)console.log(`tag: "${e}" ${t}`)}return r}(f("tags")||[],`fetch ${e.toString()}`);if(Array.isArray(m)){s.tags||(s.tags=[]);for(const e of m)s.tags.includes(e)||s.tags.push(e)}const g=F(s),v="only-cache"===s.fetchCache,y="force-cache"===s.fetchCache,S="default-cache"===s.fetchCache,w="default-no-store"===s.fetchCache,b="only-no-store"===s.fetchCache,x="force-no-store"===s.fetchCache;let _=l("cache"),R="";"string"==typeof _&&void 0!==p&&(G(`fetch for ${c} on ${s.urlPathname} specified "cache: ${_}" and "revalidate: ${p}", only one should be specified.`),_=void 0),"force-cache"===_&&(p=!1),["no-cache","no-store"].includes(_||"")&&(p=0,R=`cache: ${_}`),"number"!=typeof p&&!1!==p||(h=p);const C=l("headers"),P="function"==typeof(null==C?void 0:C.get)?C:new Headers(C||{}),E=P.get("authorization")||P.get("cookie"),k=!["get","head"].includes((null==(a=l("method"))?void 0:a.toLowerCase())||"get"),A=(E||k)&&0===s.revalidate;if(x&&(h=0,R="fetchCache = force-no-store"),b){if("force-cache"===_||0===h)throw new Error(`cache: 'force-cache' used on fetch for ${c} with 'export const fetchCache = 'only-no-store'`);h=0,R="fetchCache = only-no-store"}if(v&&"no-store"===_)throw new Error(`cache: 'no-store' used on fetch for ${c} with 'export const fetchCache = 'only-cache'`);!y||void 0!==p&&0!==p||(R="fetchCache = force-cache",h=!1),void 0===h?S?(h=!1,R="fetchCache = default-cache"):A?(h=0,R="auto no cache"):w?(h=0,R="fetchCache = default-no-store"):(R="auto cache",h="boolean"!=typeof s.revalidate&&void 0!==s.revalidate&&s.revalidate):R||(R=`revalidate: ${h}`),!A&&(void 0===s.revalidate||"number"==typeof h&&(!1===s.revalidate||"number"==typeof s.revalidate&&h<s.revalidate))&&(s.revalidate=h);const T="number"==typeof h&&h>0||!1===h;let O;if(s.incrementalCache&&T)try{O=await s.incrementalCache.fetchCacheKey(c,i?e:o)}catch(t){console.error("Failed to generate cache key for",e)}const N=s.nextFetchId??1;s.nextFetchId=N+1;const L="number"!=typeof h?31536e3:h,j=async(t,r)=>{const a=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...t?[]:["signal"]];if(i){const t=e,r={body:t._ogBody||t.body};for(const e of a)r[e]=t[e];e=new Request(t.url,r)}else if(o){const e=o;o={body:o._ogBody||o.body};for(const t of a)o[t]=e[t]}const l={...o,next:{...null==o?void 0:o.next,fetchType:"origin",fetchIdx:N}};return n(e,l).then((async n=>{if(t||W(s,{start:u,url:c,cacheReason:r||R,cacheStatus:0===h||r?"skip":"miss",status:n.status,method:l.method||"GET"}),200===n.status&&s.incrementalCache&&O&&T){const t=Buffer.from(await n.arrayBuffer());try{await s.incrementalCache.set(O,{kind:"FETCH",data:{headers:Object.fromEntries(n.headers.entries()),body:t.toString("base64"),status:n.status,url:n.url},revalidate:L},{fetchCache:!0,revalidate:h,fetchUrl:c,fetchIdx:N,tags:m})}catch(t){console.warn("Failed to set fetch cache",e,t)}const r=new Response(t,{headers:new Headers(n.headers),status:n.status});return Object.defineProperty(r,"url",{value:n.url}),r}return n}))};let $,I=()=>Promise.resolve();if(O&&s.incrementalCache){I=await s.incrementalCache.lock(O);const e=s.isOnDemandRevalidate?null:await s.incrementalCache.get(O,{fetchCache:!0,revalidate:h,fetchUrl:c,fetchIdx:N,tags:m,softTags:g});if(e?await I():$="cache-control: no-cache (hard refresh)",(null==e?void 0:e.value)&&"FETCH"===e.value.kind&&(!s.isRevalidate||!e.isStale)){e.isStale&&(s.pendingRevalidates||(s.pendingRevalidates=[]),s.pendingRevalidates.push(j(!0).catch(console.error)));const t=e.value.data;let r;r=Buffer.from(t.body,"base64").subarray(),W(s,{start:u,url:c,cacheReason:R,cacheStatus:"hit",status:t.status||200,method:(null==o?void 0:o.method)||"GET"});const n=new Response(r,{headers:t.headers,status:t.status});return Object.defineProperty(n,"url",{value:e.value.data.url}),n}}if(s.isStaticGeneration&&o&&"object"==typeof o){if("no-store"===o.cache){s.revalidate=0;const t=`no-store fetch ${e}${s.urlPathname?` ${s.urlPathname}`:""}`,n=new r(t);s.dynamicUsageErr=n,s.dynamicUsageStack=n.stack,s.dynamicUsageDescription=t}const t="next"in o,n=o.next||{};if("number"==typeof n.revalidate&&(void 0===s.revalidate||"number"==typeof s.revalidate&&n.revalidate<s.revalidate)){const t=s.forceDynamic;if(t&&0===n.revalidate||(s.revalidate=n.revalidate),!t&&0===n.revalidate){const t=`revalidate: ${n.revalidate} fetch ${e}${s.urlPathname?` ${s.urlPathname}`:""}`,o=new r(t);s.dynamicUsageErr=o,s.dynamicUsageStack=o.stack,s.dynamicUsageDescription=t}}t&&delete o.next}return j(!1,$).finally(I)}))},globalThis.fetch.__nextGetStaticStore=()=>t,globalThis.fetch.__nextPatched=!0}({serverHooks:this.serverHooks,staticGenerationAsyncStorage:this.staticGenerationAsyncStorage});const o=await r(a,{params:t.params?le(t.params):void 0});if(!(o instanceof Response))throw new Error(`No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \`Response\` or a \`NextResponse\` in all branches of your handler.`);t.staticGenerationContext.fetchMetrics=n.fetchMetrics,await Promise.all(n.pendingRevalidates||[]),F(n),t.staticGenerationContext.fetchTags=null==(e=n.tags)?void 0:e.join(",");const s=this.requestAsyncStorage.getStore();if(s&&s.mutableCookies){const e=new Headers(o.headers);if(m(e,s.mutableCookies))return new Response(o.body,{status:o.status,statusText:o.statusText,headers:e})}return o}))}))))));if(!(a instanceof Response))return new Response(null,{status:500});if(a.headers.has("x-middleware-rewrite"))throw new Error("NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue.");if("1"===a.headers.get("x-middleware-next"))throw new Error("NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler");return a}async handle(e,t){try{return await this.execute(e,t)}catch(e){const t=function(e){if(se(e)){const r=se(t=e)?t.digest.split(";",3)[2]:null;if(!r)throw new Error("Invariant: Unexpected redirect url format");return function(e,t){const r=new Headers({location:e});return m(r,t),new Response(null,{status:307,headers:r})}(r,e.mutableCookies)}var t;return!!function(e){return"NEXT_NOT_FOUND"===(null==e?void 0:e.digest)}(e)&&new Response(null,{status:404})}(e);if(!t)throw e;return t}}}const Ae=ke})(),module.exports=n})();