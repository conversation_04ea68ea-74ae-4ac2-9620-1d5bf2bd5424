"use strict";exports.id=3287,exports.ids=[3287],exports.modules={3287:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{Head:function(){return E},NextScript:function(){return g},Html:function(){return y},Main:function(){return S},default:function(){return I}});let r=u(n(6689)),i=n(659),o=n(5383),s=n(2809),l=u(n(274)),a=n(3577);function u(e){return e&&e.__esModule?e:{default:e}}let c=new Set;function f(e,t,n){let r=(0,o.getPageFiles)(e,"/_app"),i=n?[]:(0,o.getPageFiles)(e,t);return{sharedFiles:r,pageFiles:i,allFiles:[...new Set([...r,...i])]}}function d(e,t){let{assetPrefix:n,buildManifest:i,assetQueryString:o,disableOptimizedLoading:s,crossOrigin:l}=e;return i.polyfillFiles.filter(e=>e.endsWith(".js")&&!e.endsWith(".module.js")).map(e=>r.default.createElement("script",{key:e,defer:!s,nonce:t.nonce,crossOrigin:t.crossOrigin||l,noModule:!0,src:`${n}/_next/${e}${o}`}))}function p({styles:e}){if(!e)return null;let t=Array.isArray(e)?e:[];if(e.props&&Array.isArray(e.props.children)){let n=e=>{var t,n;return null==e?void 0:null==(n=e.props)?void 0:null==(t=n.dangerouslySetInnerHTML)?void 0:t.__html};e.props.children.forEach(e=>{Array.isArray(e)?e.forEach(e=>n(e)&&t.push(e)):n(e)&&t.push(e)})}return r.default.createElement("style",{"amp-custom":"",dangerouslySetInnerHTML:{__html:t.map(e=>e.props.dangerouslySetInnerHTML.__html).join("").replace(/\/\*# sourceMappingURL=.*\*\//g,"").replace(/\/\*@ sourceURL=.*?\*\//g,"")}})}function h(e,t,n){let{dynamicImports:i,assetPrefix:o,isDevelopment:s,assetQueryString:l,disableOptimizedLoading:a,crossOrigin:u}=e;return i.map(e=>!e.endsWith(".js")||n.allFiles.includes(e)?null:r.default.createElement("script",{async:!s&&a,defer:!a,key:e,src:`${o}/_next/${encodeURI(e)}${l}`,nonce:t.nonce,crossOrigin:t.crossOrigin||u}))}function m(e,t,n){var i;let{assetPrefix:o,buildManifest:s,isDevelopment:l,assetQueryString:a,disableOptimizedLoading:u,crossOrigin:c}=e,f=n.allFiles.filter(e=>e.endsWith(".js")),d=null==(i=s.lowPriorityFiles)?void 0:i.filter(e=>e.endsWith(".js"));return[...f,...d].map(e=>r.default.createElement("script",{key:e,src:`${o}/_next/${encodeURI(e)}${a}`,nonce:t.nonce,async:!l&&u,defer:!u,crossOrigin:t.crossOrigin||c}))}function _(e,t){let{scriptLoader:n,disableOptimizedLoading:i,crossOrigin:o}=e,s=function(e,t){let{assetPrefix:n,scriptLoader:i,crossOrigin:o,nextScriptWorkers:s}=e;if(!s)return null;try{let{partytownSnippet:e}=require("@builder.io/partytown/integration"),s=Array.isArray(t.children)?t.children:[t.children],l=s.find(e=>{var t,n;return!!e&&!!e.props&&(null==e?void 0:null==(n=e.props)?void 0:null==(t=n.dangerouslySetInnerHTML)?void 0:t.__html.length)&&"data-partytown-config"in e.props});return r.default.createElement(r.default.Fragment,null,!l&&r.default.createElement("script",{"data-partytown-config":"",dangerouslySetInnerHTML:{__html:`
            partytown = {
              lib: "${n}/_next/static/~partytown/"
            };
          `}}),r.default.createElement("script",{"data-partytown":"",dangerouslySetInnerHTML:{__html:e()}}),(i.worker||[]).map((e,n)=>{let{strategy:i,src:s,children:l,dangerouslySetInnerHTML:a,...u}=e,c={};if(s)c.src=s;else if(a&&a.__html)c.dangerouslySetInnerHTML={__html:a.__html};else if(l)c.dangerouslySetInnerHTML={__html:"string"==typeof l?l:Array.isArray(l)?l.join(""):""};else throw Error("Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script");return r.default.createElement("script",{...c,...u,type:"text/partytown",key:s||n,nonce:t.nonce,"data-nscript":"worker",crossOrigin:t.crossOrigin||o})}))}catch(e){return(0,l.default)(e)&&"MODULE_NOT_FOUND"!==e.code&&console.warn(`Warning: ${e.message}`),null}}(e,t),a=(n.beforeInteractive||[]).filter(e=>e.src).map((e,n)=>{let{strategy:s,...l}=e;return r.default.createElement("script",{...l,key:l.src||n,defer:l.defer??!i,nonce:t.nonce,"data-nscript":"beforeInteractive",crossOrigin:t.crossOrigin||o})});return r.default.createElement(r.default.Fragment,null,s,a)}class E extends r.default.Component{static #e=this.contextType=a.HtmlContext;getCssLinks(e){let{assetPrefix:t,assetQueryString:n,dynamicImports:i,crossOrigin:o,optimizeCss:s,optimizeFonts:l}=this.context,a=e.allFiles.filter(e=>e.endsWith(".css")),u=new Set(e.sharedFiles),c=new Set([]),f=Array.from(new Set(i.filter(e=>e.endsWith(".css"))));if(f.length){let e=new Set(a);f=f.filter(t=>!(e.has(t)||u.has(t))),c=new Set(f),a.push(...f)}let d=[];return a.forEach(e=>{let i=u.has(e);s||d.push(r.default.createElement("link",{key:`${e}-preload`,nonce:this.props.nonce,rel:"preload",href:`${t}/_next/${encodeURI(e)}${n}`,as:"style",crossOrigin:this.props.crossOrigin||o}));let l=c.has(e);d.push(r.default.createElement("link",{key:e,nonce:this.props.nonce,rel:"stylesheet",href:`${t}/_next/${encodeURI(e)}${n}`,crossOrigin:this.props.crossOrigin||o,"data-n-g":l?void 0:i?"":void 0,"data-n-p":l?void 0:i?void 0:""}))}),l&&(d=this.makeStylesheetInert(d)),0===d.length?null:d}getPreloadDynamicChunks(){let{dynamicImports:e,assetPrefix:t,assetQueryString:n,crossOrigin:i}=this.context;return e.map(e=>e.endsWith(".js")?r.default.createElement("link",{rel:"preload",key:e,href:`${t}/_next/${encodeURI(e)}${n}`,as:"script",nonce:this.props.nonce,crossOrigin:this.props.crossOrigin||i}):null).filter(Boolean)}getPreloadMainLinks(e){let{assetPrefix:t,assetQueryString:n,scriptLoader:i,crossOrigin:o}=this.context,s=e.allFiles.filter(e=>e.endsWith(".js"));return[...(i.beforeInteractive||[]).map(e=>r.default.createElement("link",{key:e.src,nonce:this.props.nonce,rel:"preload",href:e.src,as:"script",crossOrigin:this.props.crossOrigin||o})),...s.map(e=>r.default.createElement("link",{key:e,nonce:this.props.nonce,rel:"preload",href:`${t}/_next/${encodeURI(e)}${n}`,as:"script",crossOrigin:this.props.crossOrigin||o}))]}getBeforeInteractiveInlineScripts(){let{scriptLoader:e}=this.context,{nonce:t,crossOrigin:n}=this.props;return(e.beforeInteractive||[]).filter(e=>!e.src&&(e.dangerouslySetInnerHTML||e.children)).map((e,i)=>{let{strategy:o,children:s,dangerouslySetInnerHTML:l,src:a,...u}=e,c="";return l&&l.__html?c=l.__html:s&&(c="string"==typeof s?s:Array.isArray(s)?s.join(""):""),r.default.createElement("script",{...u,dangerouslySetInnerHTML:{__html:c},key:u.id||i,nonce:t,"data-nscript":"beforeInteractive",crossOrigin:n||void 0})})}getDynamicChunks(e){return h(this.context,this.props,e)}getPreNextScripts(){return _(this.context,this.props)}getScripts(e){return m(this.context,this.props,e)}getPolyfillScripts(){return d(this.context,this.props)}makeStylesheetInert(e){return r.default.Children.map(e,e=>{var t,n;if((null==e?void 0:e.type)==="link"&&(null==e?void 0:null==(t=e.props)?void 0:t.href)&&i.OPTIMIZED_FONT_PROVIDERS.some(({url:t})=>{var n,r;return null==e?void 0:null==(r=e.props)?void 0:null==(n=r.href)?void 0:n.startsWith(t)})){let t={...e.props||{},"data-href":e.props.href,href:void 0};return r.default.cloneElement(e,t)}if(null==e?void 0:null==(n=e.props)?void 0:n.children){let t={...e.props||{},children:this.makeStylesheetInert(e.props.children)};return r.default.cloneElement(e,t)}return e}).filter(Boolean)}render(){let{styles:e,ampPath:t,inAmpMode:i,hybridAmp:o,canonicalBase:s,__NEXT_DATA__:l,dangerousAsPath:a,headTags:u,unstable_runtimeJS:c,unstable_JsPreload:d,disableOptimizedLoading:h,optimizeCss:m,optimizeFonts:_,assetPrefix:E,nextFontManifest:g}=this.context,y=!1===c,S=!1===d||!h;this.context.docComponentsRendered.Head=!0;let{head:I}=this.context,T=[],O=[];I&&(I.forEach(e=>{let t;this.context.strictNextHead&&(t=r.default.createElement("meta",{name:"next-head",content:"1"})),e&&"link"===e.type&&"preload"===e.props.rel&&"style"===e.props.as?(t&&T.push(t),T.push(e)):e&&(t&&("meta"!==e.type||!e.props.charSet)&&O.push(t),O.push(e))}),I=T.concat(O));let P=r.default.Children.toArray(this.props.children).filter(Boolean);_&&!i&&(P=this.makeStylesheetInert(P));let v=!1,N=!1;I=r.default.Children.map(I||[],e=>{if(!e)return e;let{type:t,props:n}=e;if(i){let r="";if("meta"===t&&"viewport"===n.name?r='name="viewport"':"link"===t&&"canonical"===n.rel?N=!0:"script"===t&&(n.src&&-1>n.src.indexOf("ampproject")||n.dangerouslySetInnerHTML&&(!n.type||"text/javascript"===n.type))&&(r="<script",Object.keys(n).forEach(e=>{r+=` ${e}="${n[e]}"`}),r+="/>"),r)return console.warn(`Found conflicting amp tag "${e.type}" with conflicting prop ${r} in ${l.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`),null}else"link"===t&&"amphtml"===n.rel&&(v=!0);return e});let b=f(this.context.buildManifest,this.context.__NEXT_DATA__.page,i),A=function(e,t,n=""){if(!e)return{preconnect:null,preload:null};let i=e.pages["/_app"],o=e.pages[t],s=[...i??[],...o??[]],l=!!(0===s.length&&(i||o));return{preconnect:l?r.default.createElement("link",{"data-next-font":e.pagesUsingSizeAdjust?"size-adjust":"",rel:"preconnect",href:"/",crossOrigin:"anonymous"}):null,preload:s?s.map(e=>{let t=/\.(woff|woff2|eot|ttf|otf)$/.exec(e)[1];return r.default.createElement("link",{key:e,rel:"preload",href:`${n}/_next/${encodeURI(e)}`,as:"font",type:`font/${t}`,crossOrigin:"anonymous","data-next-font":e.includes("-s")?"size-adjust":""})}):null}}(g,a,E);return r.default.createElement("head",function(e){let{crossOrigin:t,nonce:n,...r}=e;return r}(this.props),this.context.isDevelopment&&r.default.createElement(r.default.Fragment,null,r.default.createElement("style",{"data-next-hide-fouc":!0,"data-ampdevmode":i?"true":void 0,dangerouslySetInnerHTML:{__html:"body{display:none}"}}),r.default.createElement("noscript",{"data-next-hide-fouc":!0,"data-ampdevmode":i?"true":void 0},r.default.createElement("style",{dangerouslySetInnerHTML:{__html:"body{display:block}"}}))),I,this.context.strictNextHead?null:r.default.createElement("meta",{name:"next-head-count",content:r.default.Children.count(I||[]).toString()}),P,_&&r.default.createElement("meta",{name:"next-font-preconnect"}),A.preconnect,A.preload,i&&r.default.createElement(r.default.Fragment,null,r.default.createElement("meta",{name:"viewport",content:"width=device-width,minimum-scale=1,initial-scale=1"}),!N&&r.default.createElement("link",{rel:"canonical",href:s+n(9505).cleanAmpPath(a)}),r.default.createElement("link",{rel:"preload",as:"script",href:"https://cdn.ampproject.org/v0.js"}),r.default.createElement(p,{styles:e}),r.default.createElement("style",{"amp-boilerplate":"",dangerouslySetInnerHTML:{__html:"body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}"}}),r.default.createElement("noscript",null,r.default.createElement("style",{"amp-boilerplate":"",dangerouslySetInnerHTML:{__html:"body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}"}})),r.default.createElement("script",{async:!0,src:"https://cdn.ampproject.org/v0.js"})),!i&&r.default.createElement(r.default.Fragment,null,!v&&o&&r.default.createElement("link",{rel:"amphtml",href:s+(t||`${a}${a.includes("?")?"&":"?"}amp=1`)}),this.getBeforeInteractiveInlineScripts(),!m&&this.getCssLinks(b),!m&&r.default.createElement("noscript",{"data-n-css":this.props.nonce??""}),!y&&!S&&this.getPreloadDynamicChunks(),!y&&!S&&this.getPreloadMainLinks(b),!h&&!y&&this.getPolyfillScripts(),!h&&!y&&this.getPreNextScripts(),!h&&!y&&this.getDynamicChunks(b),!h&&!y&&this.getScripts(b),m&&this.getCssLinks(b),m&&r.default.createElement("noscript",{"data-n-css":this.props.nonce??""}),this.context.isDevelopment&&r.default.createElement("noscript",{id:"__next_css__DO_NOT_USE__"}),e||null),r.default.createElement(r.default.Fragment,{},...u||[]))}}class g extends r.default.Component{static #e=this.contextType=a.HtmlContext;getDynamicChunks(e){return h(this.context,this.props,e)}getPreNextScripts(){return _(this.context,this.props)}getScripts(e){return m(this.context,this.props,e)}getPolyfillScripts(){return d(this.context,this.props)}static getInlineScriptSource(e){let{__NEXT_DATA__:t,largePageDataBytes:r}=e;try{let i=JSON.stringify(t);if(c.has(t.page))return(0,s.htmlEscapeJsonString)(i);let o=Buffer.from(i).byteLength,l=n(6549).Z;return r&&o>r&&(c.add(t.page),console.warn(`Warning: data for page "${t.page}"${t.page===e.dangerousAsPath?"":` (path "${e.dangerousAsPath}")`} is ${l(o)} which exceeds the threshold of ${l(r)}, this amount of data can reduce performance.
See more info here: https://nextjs.org/docs/messages/large-page-data`)),(0,s.htmlEscapeJsonString)(i)}catch(e){if((0,l.default)(e)&&-1!==e.message.indexOf("circular structure"))throw Error(`Circular structure in "getInitialProps" result of page "${t.page}". https://nextjs.org/docs/messages/circular-structure`);throw e}}render(){let{assetPrefix:e,inAmpMode:t,buildManifest:n,unstable_runtimeJS:i,docComponentsRendered:o,assetQueryString:s,disableOptimizedLoading:l,crossOrigin:a}=this.context,u=!1===i;if(o.NextScript=!0,t)return null;let c=f(this.context.buildManifest,this.context.__NEXT_DATA__.page,t);return r.default.createElement(r.default.Fragment,null,!u&&n.devFiles?n.devFiles.map(t=>r.default.createElement("script",{key:t,src:`${e}/_next/${encodeURI(t)}${s}`,nonce:this.props.nonce,crossOrigin:this.props.crossOrigin||a})):null,u?null:r.default.createElement("script",{id:"__NEXT_DATA__",type:"application/json",nonce:this.props.nonce,crossOrigin:this.props.crossOrigin||a,dangerouslySetInnerHTML:{__html:g.getInlineScriptSource(this.context)}}),l&&!u&&this.getPolyfillScripts(),l&&!u&&this.getPreNextScripts(),l&&!u&&this.getDynamicChunks(c),l&&!u&&this.getScripts(c))}}function y(e){let{inAmpMode:t,docComponentsRendered:n,locale:i,scriptLoader:o,__NEXT_DATA__:s}=(0,a.useHtmlContext)();return n.Html=!0,function(e,t,n){var i,o,s,l;if(!n.children)return;let a=[],u=Array.isArray(n.children)?n.children:[n.children],c=null==(o=u.find(e=>e.type===E))?void 0:null==(i=o.props)?void 0:i.children,f=null==(l=u.find(e=>"body"===e.type))?void 0:null==(s=l.props)?void 0:s.children,d=[...Array.isArray(c)?c:[c],...Array.isArray(f)?f:[f]];r.default.Children.forEach(d,t=>{var n;if(t&&(null==(n=t.type)?void 0:n.__nextScript)){if("beforeInteractive"===t.props.strategy){e.beforeInteractive=(e.beforeInteractive||[]).concat([{...t.props}]);return}if(["lazyOnload","afterInteractive","worker"].includes(t.props.strategy)){a.push(t.props);return}}}),t.scriptLoader=a}(o,s,e),r.default.createElement("html",{...e,lang:e.lang||i||void 0,amp:t?"":void 0,"data-ampdevmode":void 0})}function S(){let{docComponentsRendered:e}=(0,a.useHtmlContext)();return e.Main=!0,r.default.createElement("next-js-internal-body-render-target",null)}class I extends r.default.Component{static getInitialProps(e){return e.defaultGetInitialProps(e)}render(){return r.default.createElement(y,null,r.default.createElement(E,null),r.default.createElement("body",null,r.default.createElement(S,null),r.default.createElement(g,null)))}}I[i.NEXT_BUILTIN_DOCUMENT]=function(){return r.default.createElement(y,null,r.default.createElement(E,null),r.default.createElement("body",null,r.default.createElement(S,null),r.default.createElement(g,null)))}},659:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{MODERN_BROWSERSLIST_TARGET:function(){return i.default},COMPILER_NAMES:function(){return o},INTERNAL_HEADERS:function(){return s},COMPILER_INDEXES:function(){return l},PHASE_EXPORT:function(){return a},PHASE_PRODUCTION_BUILD:function(){return u},PHASE_PRODUCTION_SERVER:function(){return c},PHASE_DEVELOPMENT_SERVER:function(){return f},PHASE_TEST:function(){return d},PHASE_INFO:function(){return p},PAGES_MANIFEST:function(){return h},APP_PATHS_MANIFEST:function(){return m},APP_PATH_ROUTES_MANIFEST:function(){return _},BUILD_MANIFEST:function(){return E},APP_BUILD_MANIFEST:function(){return g},FUNCTIONS_CONFIG_MANIFEST:function(){return y},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return S},NEXT_FONT_MANIFEST:function(){return I},EXPORT_MARKER:function(){return T},EXPORT_DETAIL:function(){return O},PRERENDER_MANIFEST:function(){return P},ROUTES_MANIFEST:function(){return v},IMAGES_MANIFEST:function(){return N},SERVER_FILES_MANIFEST:function(){return b},DEV_CLIENT_PAGES_MANIFEST:function(){return A},MIDDLEWARE_MANIFEST:function(){return R},DEV_MIDDLEWARE_MANIFEST:function(){return M},REACT_LOADABLE_MANIFEST:function(){return x},FONT_MANIFEST:function(){return L},SERVER_DIRECTORY:function(){return C},CONFIG_FILES:function(){return j},BUILD_ID_FILE:function(){return D},BLOCKED_PAGES:function(){return F},CLIENT_PUBLIC_FILES_PATH:function(){return w},CLIENT_STATIC_FILES_PATH:function(){return k},STRING_LITERAL_DROP_BUNDLE:function(){return U},NEXT_BUILTIN_DOCUMENT:function(){return $},CLIENT_REFERENCE_MANIFEST:function(){return B},SERVER_REFERENCE_MANIFEST:function(){return H},MIDDLEWARE_BUILD_MANIFEST:function(){return W},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return G},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return Y},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return z},APP_CLIENT_INTERNALS:function(){return V},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return X},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return q},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return K},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return J},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return Z},EDGE_RUNTIME_WEBPACK:function(){return Q},TEMPORARY_REDIRECT_STATUS:function(){return ee},PERMANENT_REDIRECT_STATUS:function(){return et},STATIC_PROPS_ID:function(){return en},SERVER_PROPS_ID:function(){return er},PAGE_SEGMENT_KEY:function(){return ei},GOOGLE_FONT_PROVIDER:function(){return eo},OPTIMIZED_FONT_PROVIDERS:function(){return es},DEFAULT_SERIF_FONT:function(){return el},DEFAULT_SANS_SERIF_FONT:function(){return ea},STATIC_STATUS_PAGES:function(){return eu},TRACE_OUTPUT_VERSION:function(){return ec},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return ef},RSC_MODULE_TYPES:function(){return ed},EDGE_UNSUPPORTED_NODE_APIS:function(){return ep},SYSTEM_ENTRYPOINTS:function(){return eh}});let r=n(7083),i=r._(n(475)),o={client:"client",server:"server",edgeServer:"edge-server"},s=["x-invoke-path","x-invoke-status","x-invoke-error","x-invoke-query","x-middleware-invoke"],l={[o.client]:0,[o.server]:1,[o.edgeServer]:2},a="phase-export",u="phase-production-build",c="phase-production-server",f="phase-development-server",d="phase-test",p="phase-info",h="pages-manifest.json",m="app-paths-manifest.json",_="app-path-routes-manifest.json",E="build-manifest.json",g="app-build-manifest.json",y="functions-config-manifest.json",S="subresource-integrity-manifest",I="next-font-manifest",T="export-marker.json",O="export-detail.json",P="prerender-manifest.json",v="routes-manifest.json",N="images-manifest.json",b="required-server-files.json",A="_devPagesManifest.json",R="middleware-manifest.json",M="_devMiddlewareManifest.json",x="react-loadable-manifest.json",L="font-manifest.json",C="server",j=["next.config.js","next.config.mjs"],D="BUILD_ID",F=["/_document","/_app","/_error"],w="public",k="static",U="__NEXT_DROP_CLIENT_FILE__",$="__NEXT_BUILTIN_DOCUMENT__",B="client-reference-manifest",H="server-reference-manifest",W="middleware-build-manifest",G="middleware-react-loadable-manifest",Y="main",z=""+Y+"-app",V="app-pages-internals",X="react-refresh",q="amp",K="webpack",J="polyfills",Z=Symbol(J),Q="edge-runtime-webpack",ee=307,et=308,en="__N_SSG",er="__N_SSP",ei="__PAGE__",eo="https://fonts.googleapis.com/",es=[{url:eo,preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],el={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},ea={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},eu=["/500"],ec=1,ef=6e3,ed={client:"client",server:"server"},ep=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],eh=new Set([Y,X,q,z]);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8804:(e,t)=>{function n(e){return Object.prototype.toString.call(e)}function r(e){if("[object Object]"!==n(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getObjectClassLabel:function(){return n},isPlainObject:function(){return r}})},475:e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},1512:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"denormalizePagePath",{enumerable:!0,get:function(){return o}});let r=n(4610),i=n(7022);function o(e){let t=(0,i.normalizePathSep)(e);return t.startsWith("/index/")&&!(0,r.isDynamicRoute)(t)?t.slice(6):"/index"!==t?t:"/"}},2764:(e,t)=>{function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},9185:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePagePath",{enumerable:!0,get:function(){return s}});let r=n(2764),i=n(4610),o=n(6738);function s(e){let t=/^\/index(\/|$)/.test(e)&&!(0,i.isDynamicRoute)(e)?"/index"+e:"/"===e?"/index":(0,r.ensureLeadingSlash)(e);{let{posix:e}=n(1017),r=e.normalize(t);if(r!==t)throw new o.NormalizeError("Requested and resolved page mismatch: "+t+" "+r)}return t}},7022:(e,t)=>{function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},4610:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getSortedRoutes:function(){return r.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let r=n(5853),i=n(7411)},7411:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return r}});let n=/\/\[[^/]+?\](?=\/|$)/;function r(e){return n.test(e)}},5853:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return r}});class n{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let n=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&n.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');n.unshift(t)}return null!==this.restSlugName&&n.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&n.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),n}_insert(e,t,r){if(0===e.length){this.placeholder=!1;return}if(r)throw Error("Catch-all must be the last part of the URL.");let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let n=i.slice(1,-1),s=!1;if(n.startsWith("[")&&n.endsWith("]")&&(n=n.slice(1,-1),s=!0),n.startsWith("...")&&(n=n.substring(3),r=!0),n.startsWith("[")||n.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+n+"').");if(n.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+n+"').");function o(e,n){if(null!==e&&e!==n)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+n+"').");t.forEach(e=>{if(e===n)throw Error('You cannot have the same slug name "'+n+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+n+'" differ only by non-word symbols within a single dynamic path')}),t.push(n)}if(r){if(s){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');o(this.optionalRestSlugName,n),this.optionalRestSlugName=n,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');o(this.restSlugName,n),this.restSlugName=n,i="[...]"}}else{if(s)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');o(this.slugName,n),this.slugName=n,i="[]"}}this.children.has(i)||this.children.set(i,new n),this.children.get(i)._insert(e.slice(1),t,r)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function r(e){let t=new n;return e.forEach(e=>t.insert(e)),t.smoosh()}},274:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return i},getProperError:function(){return o}});let r=n(8804);function i(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function o(e){return i(e)?e:Error((0,r.isPlainObject)(e)?JSON.stringify(e):e+"")}},6549:(e,t)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return i}});let n=["B","kB","MB","GB","TB","PB","EB","ZB","YB"],r=(e,t)=>{let n=e;return"string"==typeof t?n=e.toLocaleString(t):!0===t&&(n=e.toLocaleString()),n};function i(e,t){if(!Number.isFinite(e))throw TypeError(`Expected a finite number, got ${typeof e}: ${e}`);if((t=Object.assign({},t)).signed&&0===e)return" 0 B";let i=e<0,o=i?"-":t.signed?"+":"";if(i&&(e=-e),e<1){let n=r(e,t.locale);return o+n+" B"}let s=Math.min(Math.floor(Math.log10(e)/3),n.length-1);e=Number((e/Math.pow(1e3,s)).toPrecision(3));let l=r(e,t.locale),a=n[s];return o+l+" "+a}},8877:(e,t,n)=>{e.exports=n(2785)},3577:(e,t,n)=>{e.exports=n(8877).vendored.contexts.HtmlContext},5383:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPageFiles",{enumerable:!0,get:function(){return o}});let r=n(1512),i=n(9185);function o(e,t){let n=(0,r.denormalizePagePath)((0,i.normalizePagePath)(t));return e.pages[n]||(console.warn(`Could not find files for ${n} in .next/build-manifest.json`),[])}},2809:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ESCAPE_REGEX:function(){return r},htmlEscapeJsonString:function(){return i}});let n={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},r=/[&><\u2028\u2029]/g;function i(e){return e.replace(r,e=>n[e])}},9505:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{isBlockedPage:function(){return i},cleanAmpPath:function(){return o},debounce:function(){return s}});let r=n(659);function i(e){return r.BLOCKED_PAGES.includes(e)}function o(e){return e.match(/\?amp=(y|yes|true|1)/)&&(e=e.replace(/\?amp=(y|yes|true|1)&?/,"?")),e.match(/&amp=(y|yes|true|1)/)&&(e=e.replace(/&amp=(y|yes|true|1)/,"")),e=e.replace(/\?$/,"")}function s(e,t,n=1/0){let r,i,o;let s=0,l=0;function a(){let u=Date.now(),c=l+t-u;c<=0||s+n>=u?(r=void 0,e.apply(o,i)):r=setTimeout(a,c)}return function(...e){i=e,o=this,l=Date.now(),void 0===r&&(s=l,r=setTimeout(a,t))}}}};