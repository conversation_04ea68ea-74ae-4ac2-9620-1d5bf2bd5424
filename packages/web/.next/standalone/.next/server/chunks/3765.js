"use strict";exports.id=3765,exports.ids=[3765],exports.modules={2751:(e,a,r)=>{r.d(a,{Z:()=>c});var t=r(8726),s=r(7253),n=r(120),i=r(3564);let l={...i.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["surface","classic","ghost"],default:"surface"}};var o=r(38),d=r(1260);let c=t.forwardRef((e,a)=>{let{asChild:r,className:i,...c}=(0,o.y)(e,l,d.E),u=r?n.fC:"div";return t.createElement(u,{ref:a,...c,className:s("rt-reset","rt-BaseCard","rt-Card",i)})});c.displayName="Card"},4716:(e,a,r)=>{r.d(a,{VY:()=>W,aV:()=>F,fC:()=>B,xz:()=>K});var t=r(8726),s=r(7253),n=r(1193),i=r(752),l=r(9746),o=r(7602),d=r(4049),c=r(7796),u=r(9520),m=r(7098),f=r(2395),v="Tabs",[b,p]=(0,i.b)(v,[l.Pc]),y=(0,l.Pc)(),[T,N]=b(v),g=t.forwardRef((e,a)=>{let{__scopeTabs:r,value:t,onValueChange:s,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:o="automatic",...b}=e,p=(0,c.gm)(l),[y,N]=(0,u.T)({prop:t,onChange:s,defaultProp:n??"",caller:v});return(0,f.jsx)(T,{scope:r,baseId:(0,m.M)(),value:y,onValueChange:N,orientation:i,dir:p,activationMode:o,children:(0,f.jsx)(d.WV.div,{dir:p,"data-orientation":i,...b,ref:a})})});g.displayName=v;var C="TabsList",h=t.forwardRef((e,a)=>{let{__scopeTabs:r,loop:t=!0,...s}=e,n=N(C,r),i=y(r);return(0,f.jsx)(l.fC,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:t,children:(0,f.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...s,ref:a})})});h.displayName=C;var w="TabsTrigger",E=t.forwardRef((e,a)=>{let{__scopeTabs:r,value:t,disabled:s=!1,...i}=e,o=N(w,r),c=y(r),u=j(o.baseId,t),m=I(o.baseId,t),v=t===o.value;return(0,f.jsx)(l.ck,{asChild:!0,...c,focusable:!s,active:v,children:(0,f.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":m,"data-state":v?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:u,...i,ref:a,onMouseDown:(0,n.M)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(t)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(t)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;v||s||!e||o.onValueChange(t)})})})});E.displayName=w;var x="TabsContent",R=t.forwardRef((e,a)=>{let{__scopeTabs:r,value:s,forceMount:n,children:i,...l}=e,c=N(x,r),u=j(c.baseId,s),m=I(c.baseId,s),v=s===c.value,b=t.useRef(v);return t.useEffect(()=>{let e=requestAnimationFrame(()=>b.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(o.z,{present:n||v,children:({present:r})=>(0,f.jsx)(d.WV.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:m,tabIndex:0,...l,ref:a,style:{...e.style,animationDuration:b.current?"0s":void 0},children:r&&i})})});function j(e,a){return`${e}-trigger-${a}`}function I(e,a){return`${e}-content-${a}`}R.displayName=x;var V=r(3929),z=r(7946);let L={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end"],responsive:!0},...V.EG,...z.K};var M=r(38),D=r(1260);let B=t.forwardRef((e,a)=>{let{className:r,...n}=(0,M.y)(e,D.E);return t.createElement(g,{...n,ref:a,className:s("rt-TabsRoot",r)})});B.displayName="Tabs.Root";let F=t.forwardRef((e,a)=>{let{className:r,color:n,...i}=(0,M.y)(e,L,D.E);return t.createElement(h,{"data-accent-color":n,...i,asChild:!1,ref:a,className:s("rt-BaseTabList","rt-TabsList",r)})});F.displayName="Tabs.List";let K=t.forwardRef((e,a)=>{let{className:r,children:n,...i}=e;return t.createElement(E,{...i,asChild:!1,ref:a,className:s("rt-reset","rt-BaseTabListTrigger","rt-TabsTrigger",r)},t.createElement("span",{className:"rt-BaseTabListTriggerInner rt-TabsTriggerInner"},n),t.createElement("span",{className:"rt-BaseTabListTriggerInnerHidden rt-TabsTriggerInnerHidden"},n))});K.displayName="Tabs.Trigger";let W=t.forwardRef((e,a)=>{let{className:r,...n}=(0,M.y)(e,D.E);return t.createElement(R,{...n,ref:a,className:s("rt-TabsContent",r)})});W.displayName="Tabs.Content"}};