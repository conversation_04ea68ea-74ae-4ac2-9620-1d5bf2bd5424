"use strict";exports.id=8962,exports.ids=[8962],exports.modules={8962:(e,t,a)=>{a.d(t,{f:()=>m});var l=a(8726),r=a(7253),s=a(7754),i=a(3929),d=a(690),o=a(6660),c=a(2808);let n={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft"],default:"surface"},...i.EG,...o.I},u={side:{type:"enum",values:["left","right"]},...i.EG,gap:c.l.gap,px:d.i.px,pl:d.i.pl,pr:d.i.pr};var p=a(38),f=a(1260);let m=l.forwardRef((e,t)=>{let a=l.useRef(null),{children:i,className:d,color:o,radius:c,style:u,...m}=(0,p.y)(e,n,f.E);return l.createElement("div",{"data-accent-color":o,"data-radius":c,style:u,className:r("rt-TextFieldRoot",d),onPointerDown:e=>{let t=e.target;if(t.closest("input, button, a"))return;let l=a.current;if(!l)return;let r=t.closest(`
            .rt-TextFieldSlot[data-side='right'],
            .rt-TextFieldSlot:not([data-side='right']) ~ .rt-TextFieldSlot:not([data-side='left'])
          `)?l.value.length:0;requestAnimationFrame(()=>{try{l.setSelectionRange(r,r)}catch{}l.focus()})}},l.createElement("input",{spellCheck:"false",...m,ref:(0,s.F)(a,t),className:"rt-reset rt-TextFieldInput"}),i)});m.displayName="TextField.Root";let x=l.forwardRef((e,t)=>{let{className:a,color:s,side:i,...d}=(0,p.y)(e,u);return l.createElement("div",{"data-accent-color":s,"data-side":i,...d,ref:t,className:r("rt-TextFieldSlot",a)})});x.displayName="TextField.Slot"}};