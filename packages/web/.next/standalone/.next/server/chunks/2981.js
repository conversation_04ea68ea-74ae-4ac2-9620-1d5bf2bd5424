"use strict";exports.id=2981,exports.ids=[2981],exports.modules={2981:(e,s,t)=>{t.d(s,{E:()=>E,I:()=>d});var r=t(7158),l=t(4492),o=t(3524);let i=null,a=!process.env.VERCEL&&!process.env.RAILWAY_ENVIRONMENT,n=process.env.DATABASE_URL?.includes("build-mock-host")||process.env.DATABASE_URL?.includes("localhost"),c=process.env.DATABASE_URL&&process.env.DATABASE_URL.startsWith("postgresql://")&&!n;if(!a&&c)try{i=new o.PrismaClient}catch(e){console.warn("Failed to create Prisma client:",e)}let p=null;function u(){if(!p){let e={secret:process.env.BETTER_AUTH_SECRET||"default-secret-change-in-production",baseURL:"http://localhost:3000",trustedOrigins:["http://localhost:3000","https://onlyrules.codes"],emailAndPassword:{enabled:!1},session:{expiresIn:604800,updateAge:86400},socialProviders:{...process.env.GITHUB_CLIENT_ID&&process.env.GITHUB_CLIENT_SECRET?{github:{clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET,scope:["read:user","user:email"]}}:{}},callbacks:{async signIn({user:e,account:s,profile:t}){if(!e.email&&s?.provider==="github"){if(console.log("GitHub user without email:",{user:e,profile:t}),t&&t.email)e.email=t.email;else{let s=e.name?.toLowerCase().replace(/[^a-z0-9]/g,".")||"user";e.email=`${s}.${Date.now()}@github.user`,console.log("Created placeholder email:",e.email)}}return!0}}};i&&(e.database=(0,l.prismaAdapter)(i,{provider:"postgresql"})),p=(0,r.betterAuth)(e)}return p}let d=new Proxy({},{get:(e,s)=>u()[s]}),E=()=>u()}};