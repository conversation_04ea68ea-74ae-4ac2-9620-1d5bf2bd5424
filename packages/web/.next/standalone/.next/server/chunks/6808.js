exports.id=6808,exports.ids=[6808],exports.modules={2690:(e,t)=>{"use strict";t.J_=t.zJ=t.Ry=void 0;var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},n=new WeakMap,o=new WeakMap,a={},l=0,i=function(e){return e&&(e.host||i(e.parentNode))},s=function(e,t,r,s){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=i(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var c=a[r],d=[],f=new Set,p=new Set(u),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(s),a=null!==t&&"false"!==t,l=(n.get(e)||0)+1,i=(c.get(e)||0)+1;n.set(e,l),c.set(e,i),d.push(e),1===l&&a&&o.set(e,!0),1===i&&e.setAttribute(r,"true"),a||e.setAttribute(s,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=n.get(e)-1,a=c.get(e)-1;n.set(e,t),c.set(e,a),t||(o.has(e)||e.removeAttribute(s),o.delete(e)),a||e.removeAttribute(r)}),--l||(n=new WeakMap,n=new WeakMap,o=new WeakMap,a={})}};t.Ry=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),s(o,a,n,"aria-hidden")):function(){return null}},t.zJ=function(e,t,n){void 0===n&&(n="data-inert-ed");var o=t||r(e);return o?s(e,o,n,"inert"):function(){return null}},t.J_=function(){return"undefined"!=typeof HTMLElement&&HTMLElement.prototype.hasOwnProperty("inert")}},1610:e=>{e.exports.isNode="[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)},2702:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.setNonce=function(e){n=e},t.getNonce=function(){return n||r.nc}},2023:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(8726);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:i="",children:s,iconNode:u,...c},d)=>(0,n.createElement)("svg",{ref:d,...l,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:a("lucide",i),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(s)?s:[s]])),s=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...l},s)=>(0,n.createElement)(i,{ref:s,iconNode:t,className:a(`lucide-${o(e)}`,r),...l}));return r.displayName=`${e}`,r}},767:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},7132:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},8568:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},9529:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},8467:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},1155:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8373:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("StarOff",[["path",{d:"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43",key:"16m0ql"}],["path",{d:"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91",key:"1vt8nq"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},949:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},7638:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3390:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(2023);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,n.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},8511:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(2952),o=r(1567);function a(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9521:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(1567);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return o}});let n=r(580);async function o(e,t){let r=(0,n.getServerActionDispatcher)();if(!r)throw Error("Invariant: missing action dispatcher.");return new Promise((n,o)=>{r({actionId:e,actionArgs:t,resolve:n,reject:o})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7947:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return l}});let n=r(8726),o=r(1298),a="next-route-announcer";function l(e){let{tree:t}=e,[r,l]=(0,n.useState)(null);(0,n.useEffect)(()=>{let e=function(){var e;let t=document.getElementsByName(a)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal";let r=e.attachShadow({mode:"open"});return r.appendChild(t),document.body.appendChild(e),t}}();return l(e),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}},[]);let[i,s]=(0,n.useState)(""),u=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&s(e),u.current=e},[t]),r?(0,o.createPortal)(i,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},647:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RSC:function(){return r},ACTION:function(){return n},NEXT_ROUTER_STATE_TREE:function(){return o},NEXT_ROUTER_PREFETCH:function(){return a},NEXT_URL:function(){return l},RSC_CONTENT_TYPE_HEADER:function(){return i},RSC_VARY_HEADER:function(){return s},FLIGHT_PARAMETERS:function(){return u},NEXT_RSC_UNION_QUERY:function(){return c}});let r="RSC",n="Next-Action",o="Next-Router-State-Tree",a="Next-Router-Prefetch",l="Next-Url",i="text/x-component",s=r+", "+o+", "+a+", "+l,u=[[r],[o],[a]],c="_rsc";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},580:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getServerActionDispatcher:function(){return R},urlToUrlWithoutFlightMarker:function(){return P},default:function(){return T}});let n=r(2434),o=n._(r(8726)),a=r(4949),l=r(5467),i=r(3825),s=r(8959),u=r(1006),c=r(5167),d=r(1328),f=r(1907),p=r(8176),h=r(8511),m=r(7947),v=r(1825),g=r(6985),y=r(6244),b=r(647),w=r(4645),x=r(2372),E=null,_=null;function R(){return _}let S={refresh:()=>{}};function P(e){let t=new URL(e,location.origin);return t.searchParams.delete(b.NEXT_RSC_UNION_QUERY),t}function C(e){return e.origin!==window.location.origin}function j(e){let{tree:t,pushRef:r,canonicalUrl:n,sync:a}=e;return(0,o.useInsertionEffect)(()=>{let e={__NA:!0,tree:t};r.pendingPush&&(0,s.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(e,"",n)):window.history.replaceState(e,"",n),a()},[t,r,n,a]),null}let O=()=>({status:a.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map});function M(e){let{buildId:t,initialHead:r,initialTree:n,initialCanonicalUrl:s,children:d,assetPrefix:b}=e,R=(0,o.useMemo)(()=>(0,f.createInitialRouterState)({buildId:t,children:d,initialCanonicalUrl:s,initialTree:n,initialParallelRoutes:E,isServer:!0,location:null,initialHead:r}),[t,d,s,n,r]),[{tree:P,cache:M,prefetchCache:T,pushRef:N,focusAndScrollRef:k,canonicalUrl:A,nextUrl:I},D,L]=(0,c.useReducerWithReduxDevtools)(l.reducer,R);(0,o.useEffect)(()=>{E=null},[]);let{searchParams:F,pathname:W}=(0,o.useMemo)(()=>{let e=new URL(A,"http://n");return{searchParams:e.searchParams,pathname:(0,x.hasBasePath)(e.pathname)?(0,w.removeBasePath)(e.pathname):e.pathname}},[A]),B=(0,o.useCallback)((e,t,r)=>{(0,o.startTransition)(()=>{D({type:i.ACTION_SERVER_PATCH,flightData:t,previousTree:e,overrideCanonicalUrl:r,cache:O(),mutable:{globalMutable:S}})})},[D]),U=(0,o.useCallback)((e,t,r,n)=>{let o=new URL((0,h.addBasePath)(e),location.href);return S.pendingNavigatePath=e,D({type:i.ACTION_NAVIGATE,url:o,isExternalUrl:C(o),locationSearch:location.search,forceOptimisticNavigation:r,shouldScroll:null==n||n,navigateType:t,cache:O(),mutable:{globalMutable:S}})},[D]);!function(e){let t=(0,o.useCallback)(t=>{(0,o.startTransition)(()=>{e({...t,type:i.ACTION_SERVER_ACTION,mutable:{globalMutable:S},cache:O()})})},[e]);_=t}(D);let z=(0,o.useMemo)(()=>{let e={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{if((0,p.isBot)(window.navigator.userAgent))return;let r=new URL((0,h.addBasePath)(e),location.href);C(r)||(0,o.startTransition)(()=>{var e;D({type:i.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:i.PrefetchKind.FULL})})},replace:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;U(e,"replace",!!t.forceOptimisticNavigation,null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;U(e,"push",!!t.forceOptimisticNavigation,null==(r=t.scroll)||r)})},refresh:()=>{(0,o.startTransition)(()=>{D({type:i.ACTION_REFRESH,cache:O(),mutable:{globalMutable:S},origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}};return e},[D,U]);if((0,o.useEffect)(()=>{window.next&&(window.next.router=z)},[z]),(0,o.useEffect)(()=>{S.refresh=z.refresh},[z.refresh]),(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.tree)&&D({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.tree})}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[D]),N.mpaNavigation){if(S.pendingMpaPath!==A){let e=window.location;N.pendingPush?e.assign(A):e.replace(A),S.pendingMpaPath=A}(0,o.use)((0,y.createInfinitePromise)())}let H=(0,o.useCallback)(e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,o.startTransition)(()=>{D({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:t.tree})})}},[D]);(0,o.useEffect)(()=>(window.addEventListener("popstate",H),()=>{window.removeEventListener("popstate",H)}),[H]);let V=(0,o.useMemo)(()=>(0,g.findHeadInCache)(M,P[1]),[M,P]),$=o.default.createElement(v.RedirectBoundary,null,V,M.subTreeData,o.default.createElement(m.AppRouterAnnouncer,{tree:P}));return o.default.createElement(o.default.Fragment,null,o.default.createElement(j,{tree:P,pushRef:N,canonicalUrl:A,sync:L}),o.default.createElement(u.PathnameContext.Provider,{value:W},o.default.createElement(u.SearchParamsContext.Provider,{value:F},o.default.createElement(a.GlobalLayoutRouterContext.Provider,{value:{buildId:t,changeByServerResponse:B,tree:P,focusAndScrollRef:k,nextUrl:I}},o.default.createElement(a.AppRouterContext.Provider,{value:z},o.default.createElement(a.LayoutRouterContext.Provider,{value:{childNodes:M.parallelRoutes,tree:P,url:A}},$))))))}function T(e){let{globalErrorComponent:t,...r}=e;return o.default.createElement(d.ErrorBoundary,{errorComponent:t},o.default.createElement(M,r))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},676:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return a}});let n=r(4573),o=r(4749);function a(){let e=o.staticGenerationAsyncStorage.getStore();return null!=e&&!!e.forceStatic||((null==e?void 0:e.isStaticGeneration)&&(0,n.suspense)(),!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},519:(e,t,r)=>{"use strict";function n(e){}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clientHookInServerComponentError",{enumerable:!0,get:function(){return n}}),r(8446),r(8726),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1328:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundaryHandler:function(){return i},GlobalError:function(){return s},default:function(){return u},ErrorBoundary:function(){return c}});let n=r(8446),o=n._(r(8726)),a=r(5086),l={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};class i extends o.default.Component{static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?o.default.createElement(o.default.Fragment,null,this.props.errorStyles,o.default.createElement(this.props.errorComponent,{error:this.state.error,reset:this.reset})):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function s(e){let{error:t}=e,r=null==t?void 0:t.digest;return o.default.createElement("html",{id:"__next_error__"},o.default.createElement("head",null),o.default.createElement("body",null,o.default.createElement("div",{style:l.error},o.default.createElement("div",null,o.default.createElement("h2",{style:l.text},"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."),r?o.default.createElement("p",{style:l.text},"Digest: "+r):null))))}let u=s;function c(e){let{errorComponent:t,errorStyles:r,children:n}=e,l=(0,a.usePathname)();return t?o.default.createElement(i,{pathname:l,errorComponent:t,errorStyles:r},n):o.default.createElement(o.default.Fragment,null,n)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},878:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_ERROR_CODE:function(){return r},DynamicServerError:function(){return n}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.digest=r}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6244:(e,t)=>{"use strict";let r;function n(){return r||(r=new Promise(()=>{})),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInfinitePromise",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},988:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return x}}),r(8446);let n=r(2434),o=n._(r(8726));r(1298);let a=r(4949),l=r(1290),i=r(6244),s=r(1328),u=r(9603),c=r(8253),d=r(1825),f=r(1554),p=r(4876),h=r(8158),m=["bottom","height","left","right","top","width","x","y"];function v(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class g extends o.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,u.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r&&(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return m.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,c.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!v(r,t)&&(e.scrollTop=0,v(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function y(e){let{segmentPath:t,children:r}=e,n=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!n)throw Error("invariant global layout router not mounted");return o.default.createElement(g,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef},r)}function b(e){let{parallelRouterKey:t,url:r,childNodes:n,childProp:s,segmentPath:c,tree:d,cacheKey:f}=e,p=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!p)throw Error("invariant global layout router not mounted");let{buildId:h,changeByServerResponse:m,tree:v}=p,g=n.get(f);if(s&&null!==s.current&&(g?g.status===a.CacheStates.LAZY_INITIALIZED&&(g.status=a.CacheStates.READY,g.subTreeData=s.current):(g={status:a.CacheStates.READY,data:null,subTreeData:s.current,parallelRoutes:new Map},n.set(f,g))),!g||g.status===a.CacheStates.LAZY_INITIALIZED){let e=function e(t,r){if(t){let[n,o]=t,a=2===t.length;if((0,u.matchSegment)(r[0],n)&&r[1].hasOwnProperty(o)){if(a){let t=e(void 0,r[1][o]);return[r[0],{...r[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[o]:e(t.slice(2),r[1][o])}]}}return r}(["",...c],v);g={status:a.CacheStates.DATA_FETCH,data:(0,l.fetchServerResponse)(new URL(r,location.origin),e,p.nextUrl,h),subTreeData:null,head:g&&g.status===a.CacheStates.LAZY_INITIALIZED?g.head:void 0,parallelRoutes:g&&g.status===a.CacheStates.LAZY_INITIALIZED?g.parallelRoutes:new Map},n.set(f,g)}if(!g)throw Error("Child node should always exist");if(g.subTreeData&&g.data)throw Error("Child node should not have both subTreeData and data");if(g.data){let[e,t]=(0,o.use)(g.data);g.data=null,setTimeout(()=>{(0,o.startTransition)(()=>{m(v,e,t)})}),(0,o.use)((0,i.createInfinitePromise)())}g.subTreeData||(0,o.use)((0,i.createInfinitePromise)());let y=o.default.createElement(a.LayoutRouterContext.Provider,{value:{tree:d[1][t],childNodes:g.parallelRoutes,url:r}},g.subTreeData);return y}function w(e){let{children:t,loading:r,loadingStyles:n,hasLoading:a}=e;return a?o.default.createElement(o.Suspense,{fallback:o.default.createElement(o.default.Fragment,null,n,r)},t):o.default.createElement(o.default.Fragment,null,t)}function x(e){let{parallelRouterKey:t,segmentPath:r,childProp:n,error:l,errorStyles:i,templateStyles:c,loading:m,loadingStyles:v,hasLoading:g,template:x,notFound:E,notFoundStyles:_,styles:R}=e,S=(0,o.useContext)(a.LayoutRouterContext);if(!S)throw Error("invariant expected layout router to be mounted");let{childNodes:P,tree:C,url:j}=S,O=P.get(t);O||(O=new Map,P.set(t,O));let M=C[1][t][0],T=n.segment,N=(0,p.getSegmentValue)(M),k=[M];return o.default.createElement(o.default.Fragment,null,R,k.map(e=>{let R=(0,u.matchSegment)(e,T),S=(0,p.getSegmentValue)(e),P=(0,h.createRouterCacheKey)(e);return o.default.createElement(a.TemplateContext.Provider,{key:(0,h.createRouterCacheKey)(e,!0),value:o.default.createElement(y,{segmentPath:r},o.default.createElement(s.ErrorBoundary,{errorComponent:l,errorStyles:i},o.default.createElement(w,{hasLoading:g,loading:m,loadingStyles:v},o.default.createElement(f.NotFoundBoundary,{notFound:E,notFoundStyles:_},o.default.createElement(d.RedirectBoundary,null,o.default.createElement(b,{parallelRouterKey:t,url:j,tree:C,childNodes:O,childProp:R?n:null,segmentPath:r,cacheKey:P,isActive:N===S}))))))},c,x)}))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9603:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{matchSegment:function(){return o},canSegmentBeOverridden:function(){return a}});let n=r(5074),o=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1],a=(e,t)=>{var r;return!Array.isArray(e)&&!!Array.isArray(t)&&(null==(r=(0,n.getSegmentParam)(e))?void 0:r.param)===t[0]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5086:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return p},useSearchParams:function(){return h},usePathname:function(){return m},ServerInsertedHTMLContext:function(){return s.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return s.useServerInsertedHTML},useRouter:function(){return v},useParams:function(){return g},useSelectedLayoutSegments:function(){return y},useSelectedLayoutSegment:function(){return b},redirect:function(){return u.redirect},permanentRedirect:function(){return u.permanentRedirect},RedirectType:function(){return u.RedirectType},notFound:function(){return c.notFound}});let n=r(8726),o=r(4949),a=r(1006),l=r(519),i=r(4876),s=r(1247),u=r(630),c=r(4942),d=Symbol("internal for urlsearchparams readonly");function f(){return Error("ReadonlyURLSearchParams cannot be modified")}class p{[Symbol.iterator](){return this[d][Symbol.iterator]()}append(){throw f()}delete(){throw f()}set(){throw f()}sort(){throw f()}constructor(e){this[d]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}}function h(){(0,l.clientHookInServerComponentError)("useSearchParams");let e=(0,n.useContext)(a.SearchParamsContext),t=(0,n.useMemo)(()=>e?new p(e):null,[e]);{let{bailoutToClientRendering:e}=r(676);e()}return t}function m(){return(0,l.clientHookInServerComponentError)("usePathname"),(0,n.useContext)(a.PathnameContext)}function v(){(0,l.clientHookInServerComponentError)("useRouter");let e=(0,n.useContext)(o.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function g(){(0,l.clientHookInServerComponentError)("useParams");let e=(0,n.useContext)(o.GlobalLayoutRouterContext),t=(0,n.useContext)(a.PathParamsContext);return e?function e(t,r){void 0===r&&(r={});let n=t[1];for(let t of Object.values(n)){let n=t[0],o=Array.isArray(n),a=o?n[1]:n;if(!a||a.startsWith("__PAGE__"))continue;let l=o&&("c"===n[2]||"oc"===n[2]);l?r[n[0]]=n[1].split("/"):o&&(r[n[0]]=n[1]),r=e(t,r)}return r}(e.tree):t}function y(e){void 0===e&&(e="children"),(0,l.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:t}=(0,n.useContext)(o.LayoutRouterContext);return function e(t,r,n,o){let a;if(void 0===n&&(n=!0),void 0===o&&(o=[]),n)a=t[1][r];else{var l;let e=t[1];a=null!=(l=e.children)?l:Object.values(e)[0]}if(!a)return o;let s=a[0],u=(0,i.getSegmentValue)(s);return!u||u.startsWith("__PAGE__")?o:(o.push(u),e(a,r,!1,o))}(t,e)}function b(e){void 0===e&&(e="children"),(0,l.clientHookInServerComponentError)("useSelectedLayoutSegment");let t=y(e);return 0===t.length?null:t[0]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1554:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotFoundBoundary",{enumerable:!0,get:function(){return i}});let n=r(8446),o=n._(r(8726)),a=r(5086);class l extends o.default.Component{static getDerivedStateFromError(e){if((null==e?void 0:e.digest)==="NEXT_NOT_FOUND")return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:t.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?o.default.createElement(o.default.Fragment,null,o.default.createElement("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}}function i(e){let{notFound:t,notFoundStyles:r,asNotFound:n,children:i}=e,s=(0,a.usePathname)();return t?o.default.createElement(l,{pathname:s,notFound:t,notFoundStyles:r,asNotFound:n},i):o.default.createElement(o.default.Fragment,null,i)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4942:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{notFound:function(){return n},isNotFoundError:function(){return o}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return(null==e?void 0:e.digest)===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8836:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(6270),o=r(1471);var a=o._("_maxConcurrency"),l=o._("_runningCount"),i=o._("_queue"),s=o._("_processNext");class u{enqueue(e){let t,r;let o=new Promise((e,n)=>{t=e,r=n}),a=async()=>{try{n._(this,l)[l]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,l)[l]--,n._(this,s)[s]()}};return n._(this,i)[i].push({promiseFn:o,task:a}),n._(this,s)[s](),o}bump(e){let t=n._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,i)[i].splice(t,1)[0];n._(this,i)[i].unshift(e),n._(this,s)[s](!0)}}constructor(e=5){Object.defineProperty(this,s,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,l,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),n._(this,a)[a]=e,n._(this,l)[l]=0,n._(this,i)[i]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,l)[l]<n._(this,a)[a]||e)&&n._(this,i)[i].length>0){var t;null==(t=n._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1825:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectErrorBoundary:function(){return s},RedirectBoundary:function(){return u}});let n=r(2434),o=n._(r(8726)),a=r(5086),l=r(630);function i(e){let{redirect:t,reset:r,redirectType:n}=e,i=(0,a.useRouter)();return(0,o.useEffect)(()=>{o.default.startTransition(()=>{n===l.RedirectType.push?i.push(t,{}):i.replace(t,{}),r()})},[t,n,r,i]),null}class s extends o.default.Component{static getDerivedStateFromError(e){if((0,l.isRedirectError)(e)){let t=(0,l.getURLFromRedirectError)(e),r=(0,l.getRedirectTypeFromError)(e);return{redirect:t,redirectType:r}}throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?o.default.createElement(i,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function u(e){let{children:t}=e,r=(0,a.useRouter)();return o.default.createElement(s,{router:r},t)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},630:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},redirect:function(){return i},permanentRedirect:function(){return s},isRedirectError:function(){return u},getURLFromRedirectError:function(){return c},getRedirectTypeFromError:function(){return d}});let o=r(5403),a="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=!1);let n=Error(a);n.digest=a+";"+t+";"+e+";"+r;let l=o.requestAsyncStorage.getStore();return l&&(n.mutableCookies=l.mutableCookies),n}function i(e,t){throw void 0===t&&(t="replace"),l(e,t,!1)}function s(e,t){throw void 0===t&&(t="replace"),l(e,t,!0)}function u(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;let[t,r,n,o]=e.digest.split(";",4);return t===a&&("replace"===r||"push"===r)&&"string"==typeof n&&("true"===o||"false"===o)}function c(e){return u(e)?e.digest.split(";",3)[2]:null}function d(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",3)[1]}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3288:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(2434),o=n._(r(8726)),a=r(4949);function l(){let e=(0,o.useContext)(a.TemplateContext);return o.default.createElement(o.default.Fragment,null,e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5811:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return l}});let n=r(4949),o=r(15),a=r(6148);function l(e,t,r,l){void 0===l&&(l=!1);let[i,s,u]=r.slice(-3);return null!==s&&(3===r.length?(t.status=n.CacheStates.READY,t.subTreeData=s,(0,o.fillLazyItemsTillLeafWithHead)(t,e,i,u,l)):(t.status=n.CacheStates.READY,t.subTreeData=e.subTreeData,t.parallelRoutes=new Map(e.parallelRoutes),(0,a.fillCacheWithNewSubTreeData)(t,e,r,l)),!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2948:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,a){let l;let[i,s,,,u]=r;if(1===t.length){let e=o(r,a);return e}let[c,d]=t;if(!(0,n.matchSegment)(c,i))return null;let f=2===t.length;if(f)l=o(s[d],a);else if(null===(l=e(t.slice(2),s[d],a)))return null;let p=[t[0],{...s,[d]:l}];return u&&(p[4]=!0),p}}});let n=r(9603);function o(e,t){let[r,a]=e,[l,i]=t;if("__DEFAULT__"===l&&"__DEFAULT__"!==r)return e;if((0,n.matchSegment)(r,l)){let t={};for(let e in a){let r=void 0!==i[e];r?t[e]=o(a[e],i[e]):t[e]=a[e]}for(let e in i)t[e]||(t[e]=i[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6924:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractPathFromFlightRouterState:function(){return u},computeChangedPath:function(){return c}});let n=r(469),o=r(5451),a=r(9603),l=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?e:e[1];function s(e){return e.reduce((e,t)=>""===(t=l(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if("__DEFAULT__"===r||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith("__PAGE__"))return"";let o=[r],a=null!=(t=e[1])?t:{},l=a.children?u(a.children):void 0;if(void 0!==l)o.push(l);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return s(o)}function c(e,t){let r=function e(t,r){let[o,l]=t,[s,c]=r,d=i(o),f=i(s);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,a.matchSegment)(o,s)){var p;return null!=(p=u(r))?p:""}for(let t in l)if(c[t]){let r=e(l[t],c[t]);if(null!==r)return i(s)+"/"+r}return null}(e,t);return null==r||"/"===r?r:s(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8959:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1907:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return i}});let n=r(4949),o=r(8959),a=r(15),l=r(6924);function i(e){var t;let{buildId:r,initialTree:i,children:s,initialCanonicalUrl:u,initialParallelRoutes:c,isServer:d,location:f,initialHead:p}=e,h={status:n.CacheStates.READY,data:null,subTreeData:s,parallelRoutes:d?new Map:c};return(null===c||0===c.size)&&(0,a.fillLazyItemsTillLeafWithHead)(h,void 0,i,p),{buildId:r,tree:i,cache:h,prefetchCache:new Map,pushRef:{pendingPush:!1,mpaNavigation:!1},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:f?(0,o.createHrefFromUrl)(f):u,nextUrl:null!=(t=(0,l.extractPathFromFlightRouterState)(i)||(null==f?void 0:f.pathname))?t:null}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9126:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createOptimisticTree",{enumerable:!0,get:function(){return function e(t,r,o){let a;let[l,i,s,u,c]=r||[null,{}],d=t[0],f=1===t.length,p=null!==l&&(0,n.matchSegment)(l,d),h=Object.keys(i).length>1,m=!r||!p||h,v={};if(null!==l&&p&&(v=i),!f&&!h){let r=e(t.slice(1),v?v.children:null,o||m);a=r}let g=[d,{...v,...a?{children:a}:{}}];return s&&(g[2]=s),!o&&m?g[3]="refetch":p&&u&&(g[3]=u),p&&c&&(g[4]=c),g}}});let n=r(9603);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8876:(e,t)=>{"use strict";function r(e){return e.status="pending",e.then(t=>{"pending"===e.status&&(e.status="fulfilled",e.value=t)},t=>{"pending"===e.status&&(e.status="rejected",e.value=t)}),e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRecordFromThenable",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8158:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!1),Array.isArray(e)?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith("__PAGE__")?"__PAGE__":e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1290:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return c}});let n=r(647),o=r(580),a=r(47),l=r(3825),i=r(7460),{createFromFetch:s}=r(4554);function u(e){return[(0,o.urlToUrlWithoutFlightMarker)(e).toString(),void 0]}async function c(e,t,r,c,d){let f={[n.RSC]:"1",[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};d===l.PrefetchKind.AUTO&&(f[n.NEXT_ROUTER_PREFETCH]="1"),r&&(f[n.NEXT_URL]=r);let p=(0,i.hexHash)([f[n.NEXT_ROUTER_PREFETCH]||"0",f[n.NEXT_ROUTER_STATE_TREE],f[n.NEXT_URL]].join(","));try{let t=new URL(e);t.searchParams.set(n.NEXT_RSC_UNION_QUERY,p);let r=await fetch(t,{credentials:"same-origin",headers:f}),l=(0,o.urlToUrlWithoutFlightMarker)(r.url),i=r.redirected?l:void 0,d=r.headers.get("content-type")||"";if(d!==n.RSC_CONTENT_TYPE_HEADER||!r.ok)return u(l.toString());let[h,m]=await s(Promise.resolve(r),{callServer:a.callServer});if(c!==h)return u(r.url);return[m,i]}catch(t){return console.error("Failed to fetch RSC payload. Falling back to browser navigation.",t),[e.toString(),void 0]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7570:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithDataProperty",{enumerable:!0,get:function(){return function e(t,r,a,l,i){void 0===i&&(i=!1);let s=a.length<=2,[u,c]=a,d=(0,o.createRouterCacheKey)(c),f=r.parallelRoutes.get(u);if(!f||i&&r.parallelRoutes.size>1)return{bailOptimistic:!0};let p=t.parallelRoutes.get(u);p&&p!==f||(p=new Map(f),t.parallelRoutes.set(u,p));let h=f.get(d),m=p.get(d);if(s){m&&m.data&&m!==h||p.set(d,{status:n.CacheStates.DATA_FETCH,data:l(),subTreeData:null,parallelRoutes:new Map});return}if(!m||!h){m||p.set(d,{status:n.CacheStates.DATA_FETCH,data:l(),subTreeData:null,parallelRoutes:new Map});return}return m===h&&(m={status:m.status,data:m.data,subTreeData:m.subTreeData,parallelRoutes:new Map(m.parallelRoutes)},p.set(d,m)),e(m,h,a.slice(2),l)}}});let n=r(4949),o=r(8158);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6148:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,r,i,s){let u=i.length<=5,[c,d]=i,f=(0,l.createRouterCacheKey)(d),p=r.parallelRoutes.get(c);if(!p)return;let h=t.parallelRoutes.get(c);h&&h!==p||(h=new Map(p),t.parallelRoutes.set(c,h));let m=p.get(f),v=h.get(f);if(u){v&&v.data&&v!==m||(v={status:n.CacheStates.READY,data:null,subTreeData:i[3],parallelRoutes:m?new Map(m.parallelRoutes):new Map},m&&(0,o.invalidateCacheByRouterState)(v,m,i[2]),(0,a.fillLazyItemsTillLeafWithHead)(v,m,i[2],i[4],s),h.set(f,v));return}v&&m&&(v===m&&(v={status:v.status,data:v.data,subTreeData:v.subTreeData,parallelRoutes:new Map(v.parallelRoutes)},h.set(f,v)),e(v,m,i.slice(2),s))}}});let n=r(4949),o=r(4566),a=r(15),l=r(8158);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,a,l,i){let s=0===Object.keys(a[1]).length;if(s){t.head=l;return}for(let s in a[1]){let u=a[1][s],c=u[0],d=(0,o.createRouterCacheKey)(c);if(r){let o=r.parallelRoutes.get(s);if(o){let r=new Map(o),a=r.get(d),c=i&&a?{status:a.status,data:a.data,subTreeData:a.subTreeData,parallelRoutes:new Map(a.parallelRoutes)}:{status:n.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map(null==a?void 0:a.parallelRoutes)};r.set(d,c),e(c,a,u,l,i),t.parallelRoutes.set(s,r);continue}}let f={status:n.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map},p=t.parallelRoutes.get(s);p?p.set(d,f):t.parallelRoutes.set(s,new Map([[d,f]])),e(f,void 0,u,l,i)}}}});let n=r(4949),o=r(8158);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6839:(e,t)=>{"use strict";var r;function n(e){let{kind:t,prefetchTime:r,lastUsedTime:n}=e;return Date.now()<(null!=n?n:r)+3e4?n?"reusable":"fresh":"auto"===t&&Date.now()<r+3e5?"stale":"full"===t&&Date.now()<r+3e5?"reusable":"expired"}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PrefetchCacheEntryStatus:function(){return r},getPrefetchEntryCacheStatus:function(){return n}}),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7544:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(6924);function o(e,t){var r,o,a,l;let i=null==(o=t.shouldScroll)||o;return{buildId:e.buildId,canonicalUrl:null!=t.canonicalUrl?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:null!=t.pendingPush?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:null!=t.mpaNavigation?t.mpaNavigation:e.pushRef.mpaNavigation},focusAndScrollRef:{apply:!!i&&((null==t?void 0:t.scrollableSegments)!==void 0||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#")[0]===(null==(r=t.canonicalUrl)?void 0:r.split("#")[0]),hashFragment:i?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:i?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:void 0!==t.patchedTree?t.patchedTree:e.tree,nextUrl:void 0!==t.patchedTree?null!=(l=(0,n.computeChangedPath)(e.tree,t.patchedTree))?l:e.canonicalUrl:e.nextUrl}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4058:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,o){let a=o.length<=2,[l,i]=o,s=(0,n.createRouterCacheKey)(i),u=r.parallelRoutes.get(l);if(!u)return;let c=t.parallelRoutes.get(l);if(c&&c!==u||(c=new Map(u),t.parallelRoutes.set(l,c)),a){c.delete(s);return}let d=u.get(s),f=c.get(s);f&&d&&(f===d&&(f={status:f.status,data:f.data,subTreeData:f.subTreeData,parallelRoutes:new Map(f.parallelRoutes)},c.set(s,f)),e(f,d,o.slice(2)))}}});let n=r(8158);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4566:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let n=r(8158);function o(e,t,r){for(let o in r[1]){let a=r[1][o][0],l=(0,n.createRouterCacheKey)(a),i=t.parallelRoutes.get(o);if(i){let t=new Map(i);t.delete(l),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2847:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],o=r[0];if(Array.isArray(n)&&Array.isArray(o)){if(n[0]!==o[0]||n[2]!==o[2])return!0}else if(n!==o)return!0;if(t[4])return!r[4];if(r[4])return!0;let a=Object.values(t[1])[0],l=Object.values(r[1])[0];return!a||!l||e(a,l)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},830:(e,t)=>{"use strict";function r(e){if("fulfilled"===e.status)return e.value;throw e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"readRecordValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3209:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fastRefreshReducer",{enumerable:!0,get:function(){return n}}),r(1290),r(8876),r(830),r(8959),r(2948),r(2847),r(9274),r(7544),r(5811);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6985:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return function e(t,r){let o=0===Object.keys(r).length;if(o)return t.head;for(let o in r){let[a,l]=r[o],i=t.parallelRoutes.get(o);if(!i)continue;let s=(0,n.createRouterCacheKey)(a),u=i.get(s);if(!u)continue;let c=e(u,l);if(c)return c}}}});let n=r(8158);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4876:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9274:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return w},navigateReducer:function(){return E}});let n=r(4949),o=r(1290),a=r(8876),l=r(830),i=r(8959),s=r(4058),u=r(7570),c=r(9126),d=r(2948),f=r(2490),p=r(2847),h=r(3825),m=r(7544),v=r(5811),g=r(6839),y=r(3589),b=r(2561);function w(e,t,r,n){return t.previousTree=e.tree,t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,m.handleMutable)(e,t)}function x(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,o]of Object.entries(n))for(let n of x(o))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}function E(e,t){let{url:r,isExternalUrl:E,navigateType:_,cache:R,mutable:S,forceOptimisticNavigation:P,shouldScroll:C}=t,{pathname:j,hash:O}=r,M=(0,i.createHrefFromUrl)(r),T="push"===_;(0,y.prunePrefetchCache)(e.prefetchCache);let N=JSON.stringify(S.previousTree)===JSON.stringify(e.tree);if(N)return(0,m.handleMutable)(e,S);if(E)return w(e,S,r.toString(),T);let k=e.prefetchCache.get((0,i.createHrefFromUrl)(r,!1));if(P&&(null==k?void 0:k.kind)!==h.PrefetchKind.TEMPORARY){let t;let l=j.split("/");l.push("__PAGE__");let s=(0,c.createOptimisticTree)(l,e.tree,!1),d={...R};d.status=n.CacheStates.READY,d.subTreeData=e.cache.subTreeData,d.parallelRoutes=new Map(e.cache.parallelRoutes);let f=l.slice(1).map(e=>["children",e]).flat(),p=(0,u.fillCacheWithDataProperty)(d,e.cache,f,()=>(t||(t=(0,a.createRecordFromThenable)((0,o.fetchServerResponse)(r,s,e.nextUrl,e.buildId))),t),!0);if(!(null==p?void 0:p.bailOptimistic))return S.previousTree=e.tree,S.patchedTree=s,S.pendingPush=T,S.hashFragment=O,S.shouldScroll=C,S.scrollableSegments=[],S.cache=d,S.canonicalUrl=M,e.prefetchCache.set((0,i.createHrefFromUrl)(r,!1),{data:(0,a.createRecordFromThenable)(Promise.resolve(t)),kind:h.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:e.tree,lastUsedTime:Date.now()}),(0,m.handleMutable)(e,S)}if(!k){let t=(0,a.createRecordFromThenable)((0,o.fetchServerResponse)(r,e.tree,e.nextUrl,e.buildId,void 0)),n={data:(0,a.createRecordFromThenable)(Promise.resolve(t)),kind:h.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:e.tree,lastUsedTime:null};e.prefetchCache.set((0,i.createHrefFromUrl)(r,!1),n),k=n}let A=(0,g.getPrefetchEntryCacheStatus)(k),{treeAtTimeOfPrefetch:I,data:D}=k;b.prefetchQueue.bump(D);let[L,F]=(0,l.readRecordValue)(D);if(k.lastUsedTime||(k.lastUsedTime=Date.now()),"string"==typeof L)return w(e,S,L,T);let W=e.tree,B=e.cache,U=[];for(let t of L){let a=t.slice(0,-4),l=t.slice(-3)[0],i=["",...a],c=(0,d.applyRouterStatePatchToTree)(i,W,l);if(null===c&&(c=(0,d.applyRouterStatePatchToTree)(i,I,l)),null!==c){if((0,p.isNavigatingToNewRootLayout)(W,c))return w(e,S,M,T);let d=(0,v.applyFlightData)(B,R,t,"auto"===k.kind&&A===g.PrefetchCacheEntryStatus.reusable);d||A!==g.PrefetchCacheEntryStatus.stale||(d=function(e,t,r,o,a){let l=!1;e.status=n.CacheStates.READY,e.subTreeData=t.subTreeData,e.parallelRoutes=new Map(t.parallelRoutes);let i=x(o).map(e=>[...r,...e]);for(let r of i){let n=(0,u.fillCacheWithDataProperty)(e,t,r,a);(null==n?void 0:n.bailOptimistic)||(l=!0)}return l}(R,B,a,l,()=>(0,o.fetchServerResponse)(r,W,e.nextUrl,e.buildId)));let h=(0,f.shouldHardNavigate)(i,W);for(let e of(h?(R.status=n.CacheStates.READY,R.subTreeData=B.subTreeData,(0,s.invalidateCacheBelowFlightSegmentPath)(R,B,a),S.cache=R):d&&(S.cache=R),B=R,W=c,x(l))){let t=[...a,...e];"__DEFAULT__"!==t[t.length-1]&&U.push(t)}}}return S.previousTree=e.tree,S.patchedTree=W,S.canonicalUrl=F?(0,i.createHrefFromUrl)(F):M,S.pendingPush=T,S.scrollableSegments=U,S.hashFragment=O,S.shouldScroll=C,(0,m.handleMutable)(e,S)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2561:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return c},prefetchReducer:function(){return d}});let n=r(8959),o=r(1290),a=r(3825),l=r(8876),i=r(3589),s=r(647),u=r(8836),c=new u.PromiseQueue(5);function d(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;r.searchParams.delete(s.NEXT_RSC_UNION_QUERY);let u=(0,n.createHrefFromUrl)(r,!1),d=e.prefetchCache.get(u);if(d&&(d.kind===a.PrefetchKind.TEMPORARY&&e.prefetchCache.set(u,{...d,kind:t.kind}),!(d.kind===a.PrefetchKind.AUTO&&t.kind===a.PrefetchKind.FULL)))return e;let f=(0,l.createRecordFromThenable)(c.enqueue(()=>(0,o.fetchServerResponse)(r,e.tree,e.nextUrl,e.buildId,t.kind)));return e.prefetchCache.set(u,{treeAtTimeOfPrefetch:e.tree,data:f,kind:t.kind,prefetchTime:Date.now(),lastUsedTime:null}),e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3589:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"prunePrefetchCache",{enumerable:!0,get:function(){return o}});let n=r(6839);function o(e){for(let[t,r]of e)(0,n.getPrefetchEntryCacheStatus)(r)===n.PrefetchCacheEntryStatus.expired&&e.delete(t)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6904:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(1290),o=r(8876),a=r(830),l=r(8959),i=r(2948),s=r(2847),u=r(9274),c=r(7544),d=r(4949),f=r(15);function p(e,t){let{cache:r,mutable:p,origin:h}=t,m=e.canonicalUrl,v=e.tree,g=JSON.stringify(p.previousTree)===JSON.stringify(v);if(g)return(0,c.handleMutable)(e,p);r.data||(r.data=(0,o.createRecordFromThenable)((0,n.fetchServerResponse)(new URL(m,h),[v[0],v[1],v[2],"refetch"],e.nextUrl,e.buildId)));let[y,b]=(0,a.readRecordValue)(r.data);if("string"==typeof y)return(0,u.handleExternalUrl)(e,p,y,e.pushRef.pendingPush);for(let t of(r.data=null,y)){if(3!==t.length)return console.log("REFRESH FAILED"),e;let[n]=t,o=(0,i.applyRouterStatePatchToTree)([""],v,n);if(null===o)throw Error("SEGMENT MISMATCH");if((0,s.isNavigatingToNewRootLayout)(v,o))return(0,u.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let a=b?(0,l.createHrefFromUrl)(b):void 0;b&&(p.canonicalUrl=a);let[c,h]=t.slice(-2);null!==c&&(r.status=d.CacheStates.READY,r.subTreeData=c,(0,f.fillLazyItemsTillLeafWithHead)(r,void 0,n,h),p.cache=r,p.prefetchCache=new Map),p.previousTree=v,p.patchedTree=o,p.canonicalUrl=m,v=o}return(0,c.handleMutable)(e,p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5616:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(8959);function o(e,t){let{url:r,tree:o}=t,a=(0,n.createHrefFromUrl)(r);return{buildId:e.buildId,canonicalUrl:a,pushRef:e.pushRef,focusAndScrollRef:e.focusAndScrollRef,cache:e.cache,prefetchCache:e.prefetchCache,tree:o,nextUrl:r.pathname}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2351:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return y}});let n=r(47),o=r(647),a=r(8876),l=r(830),i=r(8511),s=r(8959),u=r(9274),c=r(2948),d=r(2847),f=r(4949),p=r(7544),h=r(15),{createFromFetch:m,encodeReply:v}=r(4554);async function g(e,t){let r,{actionId:a,actionArgs:l}=t,s=await v(l),u=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION]:a,[o.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...e.nextUrl?{[o.NEXT_URL]:e.nextUrl}:{}},body:s}),c=u.headers.get("x-action-redirect");try{let e=JSON.parse(u.headers.get("x-action-revalidated")||"[[],0,0]");r={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){r={paths:[],tag:!1,cookie:!1}}let d=c?new URL((0,i.addBasePath)(c),new URL(e.canonicalUrl,window.location.href)):void 0;if(u.headers.get("content-type")===o.RSC_CONTENT_TYPE_HEADER){let e=await m(Promise.resolve(u),{callServer:n.callServer});if(c){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:d,revalidatedParts:r}}let[t,[,o]]=null!=e?e:[];return{actionResult:t,actionFlightData:o,redirectLocation:d,revalidatedParts:r}}return{redirectLocation:d,revalidatedParts:r}}function y(e,t){let{mutable:r,cache:n,resolve:o,reject:i}=t,m=e.canonicalUrl,v=e.tree,y=JSON.stringify(r.previousTree)===JSON.stringify(v);if(y)return(0,p.handleMutable)(e,r);if(r.inFlightServerAction){if(r.globalMutable.pendingNavigatePath&&r.globalMutable.pendingNavigatePath!==m)return r.inFlightServerAction.then(()=>{r.actionResultResolved||(r.inFlightServerAction=null,r.globalMutable.pendingNavigatePath=void 0,r.globalMutable.refresh(),r.actionResultResolved=!0)}),e}else r.inFlightServerAction=(0,a.createRecordFromThenable)(g(e,t));try{let{actionResult:t,actionFlightData:a,redirectLocation:i}=(0,l.readRecordValue)(r.inFlightServerAction);if(i&&(e.pushRef.pendingPush=!0,r.pendingPush=!0),r.previousTree=e.tree,!a){if(r.actionResultResolved||(o(t),r.actionResultResolved=!0),i)return(0,u.handleExternalUrl)(e,r,i.href,e.pushRef.pendingPush);return e}if("string"==typeof a)return(0,u.handleExternalUrl)(e,r,a,e.pushRef.pendingPush);for(let t of(r.inFlightServerAction=null,a)){if(3!==t.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[o]=t,a=(0,c.applyRouterStatePatchToTree)([""],v,o);if(null===a)throw Error("SEGMENT MISMATCH");if((0,d.isNavigatingToNewRootLayout)(v,a))return(0,u.handleExternalUrl)(e,r,m,e.pushRef.pendingPush);let[l,i]=t.slice(-2);null!==l&&(n.status=f.CacheStates.READY,n.subTreeData=l,(0,h.fillLazyItemsTillLeafWithHead)(n,void 0,o,i),r.cache=n,r.prefetchCache=new Map),r.previousTree=v,r.patchedTree=a,r.canonicalUrl=m,v=a}if(i){let e=(0,s.createHrefFromUrl)(i,!1);r.canonicalUrl=e}return r.actionResultResolved||(o(t),r.actionResultResolved=!0),(0,p.handleMutable)(e,r)}catch(t){if("rejected"===t.status)return r.actionResultResolved||(i(t.value),r.actionResultResolved=!0),e;throw t}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2725:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return u}});let n=r(8959),o=r(2948),a=r(2847),l=r(9274),i=r(5811),s=r(7544);function u(e,t){let{flightData:r,previousTree:u,overrideCanonicalUrl:c,cache:d,mutable:f}=t,p=JSON.stringify(u)===JSON.stringify(e.tree);if(!p)return console.log("TREE MISMATCH"),e;if(f.previousTree)return(0,s.handleMutable)(e,f);if("string"==typeof r)return(0,l.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let h=e.tree,m=e.cache;for(let t of r){let r=t.slice(0,-4),[s]=t.slice(-3,-2),u=(0,o.applyRouterStatePatchToTree)(["",...r],h,s);if(null===u)throw Error("SEGMENT MISMATCH");if((0,a.isNavigatingToNewRootLayout)(h,u))return(0,l.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let p=c?(0,n.createHrefFromUrl)(c):void 0;p&&(f.canonicalUrl=p),(0,i.applyFlightData)(m,d,t),f.previousTree=h,f.patchedTree=u,f.cache=d,m=d,h=u}return(0,s.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3825:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PrefetchKind:function(){return r},ACTION_REFRESH:function(){return n},ACTION_NAVIGATE:function(){return o},ACTION_RESTORE:function(){return a},ACTION_SERVER_PATCH:function(){return l},ACTION_PREFETCH:function(){return i},ACTION_FAST_REFRESH:function(){return s},ACTION_SERVER_ACTION:function(){return u}});let n="refresh",o="navigate",a="restore",l="server-patch",i="prefetch",s="fast-refresh",u="server-action";(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5467:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(3825),r(9274),r(2725),r(5616),r(6904),r(2561),r(3209),r(2351);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2490:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[o,a]=r,[l,i]=t;if(!(0,n.matchSegment)(l,o))return!!Array.isArray(l);let s=t.length<=2;return!s&&e(t.slice(2),a[i])}}});let n=r(9603);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6497:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return o}});let n=r(5516);function o(){return new Proxy({},{get(e,t){"string"==typeof t&&(0,n.staticGenerationBailout)("searchParams."+t)}})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5516:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"staticGenerationBailout",{enumerable:!0,get:function(){return i}});let n=r(878),o=r(4749);class a extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}function l(e,t){let{dynamic:r,link:n}=t||{};return"Page"+(r?' with `dynamic = "'+r+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(n?" See more info here: "+n:"")}let i=(e,t)=>{let r=o.staticGenerationAsyncStorage.getStore();if(null==r?void 0:r.forceStatic)return!0;if(null==r?void 0:r.dynamicShouldError){var i;throw new a(l(e,{...t,dynamic:null!=(i=null==t?void 0:t.dynamic)?i:"error"}))}if(r&&(r.revalidate=0),null==r?void 0:r.isStaticGeneration){let o=new n.DynamicServerError(l(e,{...t,link:"https://nextjs.org/docs/messages/dynamic-server-error"}));throw r.dynamicUsageDescription=e,r.dynamicUsageStack=o.stack,o}return!1};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(8446),o=n._(r(8726)),a=r(6497);function l(e){let{Component:t,propsForComponent:r}=e,n=(0,a.createSearchParamsBailoutProxy)();return o.default.createElement(t,{searchParams:n,...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5167:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useReducerWithReduxDevtools",{enumerable:!0,get:function(){return o}});let n=r(8726),o=function(e,t){let[r,o]=(0,n.useReducer)(e,t);return[r,o,()=>{}]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8845:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(1567),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2372:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(5202);function o(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6508:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return y}});let n=r(8446),o=n._(r(8726)),a=r(8443),l=r(5672),i=r(6698),s=r(7925),u=r(9521),c=r(6245),d=r(4949),f=r(3682),p=r(8845),h=r(8511),m=r(3825);function v(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}let g=o.default.forwardRef(function(e,t){let r,n;let{href:i,as:g,children:y,prefetch:b=null,passHref:w,replace:x,shallow:E,scroll:_,locale:R,onClick:S,onMouseEnter:P,onTouchStart:C,legacyBehavior:j=!1,...O}=e;r=y,j&&("string"==typeof r||"number"==typeof r)&&(r=o.default.createElement("a",null,r));let M=o.default.useContext(c.RouterContext),T=o.default.useContext(d.AppRouterContext),N=null!=M?M:T,k=!M,A=!1!==b,I=null===b?m.PrefetchKind.AUTO:m.PrefetchKind.FULL,{href:D,as:L}=o.default.useMemo(()=>{if(!M){let e=v(i);return{href:e,as:g?v(g):e}}let[e,t]=(0,a.resolveHref)(M,i,!0);return{href:e,as:g?(0,a.resolveHref)(M,g):t||e}},[M,i,g]),F=o.default.useRef(D),W=o.default.useRef(L);j&&(n=o.default.Children.only(r));let B=j?n&&"object"==typeof n&&n.ref:t,[U,z,H]=(0,f.useIntersection)({rootMargin:"200px"}),V=o.default.useCallback(e=>{(W.current!==L||F.current!==D)&&(H(),W.current=L,F.current=D),U(e),B&&("function"==typeof B?B(e):"object"==typeof B&&(B.current=e))},[L,B,D,H,U]);o.default.useEffect(()=>{},[L,D,z,R,A,null==M?void 0:M.locale,N,k,I]);let $={ref:V,onClick(e){j||"function"!=typeof S||S(e),j&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&!e.defaultPrevented&&function(e,t,r,n,a,i,s,u,c,d){let{nodeName:f}=e.currentTarget,p="A"===f.toUpperCase();if(p&&(function(e){let t=e.currentTarget,r=t.getAttribute("target");return r&&"_self"!==r||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,l.isLocalURL)(r)))return;e.preventDefault();let h=()=>{let e=null==s||s;"beforePopState"in t?t[a?"replace":"push"](r,n,{shallow:i,locale:u,scroll:e}):t[a?"replace":"push"](n||r,{forceOptimisticNavigation:!d,scroll:e})};c?o.default.startTransition(h):h()}(e,N,D,L,x,E,_,R,k,A)},onMouseEnter(e){j||"function"!=typeof P||P(e),j&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart(e){j||"function"!=typeof C||C(e),j&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,s.isAbsoluteUrl)(L))$.href=L;else if(!j||w||"a"===n.type&&!("href"in n.props)){let e=void 0!==R?R:null==M?void 0:M.locale,t=(null==M?void 0:M.isLocaleDomain)&&(0,p.getDomainLocale)(L,e,null==M?void 0:M.locales,null==M?void 0:M.domainLocales);$.href=t||(0,h.addBasePath)((0,u.addLocale)(L,e,null==M?void 0:M.defaultLocale))}return j?o.default.cloneElement(n,$):o.default.createElement("a",{...O,...$},r)}),y=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1567:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(9270),o=r(5185),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,o.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4645:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(2372),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1626:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{requestIdleCallback:function(){return r},cancelIdleCallback:function(){return n}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8443:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(1504),o=r(6698),a=r(8406),l=r(7925),i=r(1567),s=r(5672),u=r(8894),c=r(5979);function d(e,t,r){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f,m=h.split("?");if((m[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,l.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,s.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,i.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:l,params:i}=(0,c.interpolateAs)(e.pathname,e.pathname,r);l&&(t=(0,o.formatWithValidation)({pathname:l,hash:e.hash,query:(0,a.omit)(r,i)}))}let l=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[l,t||l]:l}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3682:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return s}});let n=r(8726),o=r(1626),a="function"==typeof IntersectionObserver,l=new Map,i=[];function s(e){let{rootRef:t,rootMargin:r,disabled:s}=e,u=s||!a,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);(0,n.useEffect)(()=>{if(a){if(u||c)return;let e=f.current;if(e&&e.tagName){let n=function(e,t,r){let{id:n,observer:o,elements:a}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=i.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=l.get(n)))return t;let o=new Map,a=new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e);return t={id:r,observer:a,elements:o},i.push(r),l.set(r,t),t}(r);return a.set(e,t),o.observe(e),function(){if(a.delete(e),o.unobserve(e),0===a.size){o.disconnect(),l.delete(n);let e=i.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&i.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r});return n}}else if(!c){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[u,r,t,c,f.current]);let h=(0,n.useCallback)(()=>{d(!1)},[]);return[p,c,h]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9787:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},7460:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);t=(t<<5)+t+n}return Math.abs(t)}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},4573:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{suspense:function(){return o},NoSSR:function(){return a}}),r(8446),r(8726);let n=r(9706);function o(){let e=Error(n.NEXT_DYNAMIC_NO_SSR_CODE);throw e.digest=n.NEXT_DYNAMIC_NO_SSR_CODE,e}function a(e){let{children:t}=e;return o(),t}},9706:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NEXT_DYNAMIC_NO_SSR_CODE",{enumerable:!0,get:function(){return r}});let r="NEXT_DYNAMIC_NO_SSR_CODE"},9150:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},2952:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(5185);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:a}=(0,n.parsePath)(e);return""+t+r+o+a}},3913:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscPath:function(){return l}});let n=r(9150),o=r(5451);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function l(e,t){return t?e.replace(/\.rsc($|\?)/,"$1"):e}},6698:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return l},urlObjectKeys:function(){return i},formatWithValidation:function(){return s}});let n=r(2434),o=n._(r(1504)),a=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:r}=e,n=e.protocol||"",l=e.pathname||"",i=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(o.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return n&&!n.endsWith(":")&&(n+=":"),e.slashes||(!n||a.test(n))&&!1!==u?(u="//"+(u||""),l&&"/"!==l[0]&&(l="/"+l)):u||(u=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+n+u+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return l(e)}},8253:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange){e();return}let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},8894:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(1841),o=r(572)},5979:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(1630),o=r(3339);function a(e,t,r){let a="",l=(0,o.getRouteRegex)(e),i=l.groups,s=(t!==e?(0,n.getRouteMatcher)(l)(t):"")||r;a=e;let u=Object.keys(i);return u.every(e=>{let t=s[e]||"",{repeat:r,optional:n}=i[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in s)&&(a=a.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:u,result:a}}},8176:(e,t)=>{"use strict";function r(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isBot",{enumerable:!0,get:function(){return r}})},572:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return n}});let r=/\/\[[^/]+?\](?=\/|$)/;function n(e){return r.test(e)}},5672:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(7925),o=r(2372);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},8406:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},5185:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},5202:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(5185);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},1504:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o},assign:function(){return a}})},9270:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},1630:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(7925);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},l={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(l[e]=~n.indexOf("/")?n.split("/").map(e=>a(e)):t.repeat?[a(n)]:a(n))}),l}}},3339:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRouteRegex:function(){return s},getNamedRouteRegex:function(){return d},getNamedMiddlewareRegex:function(){return f}});let n=r(469),o=r(9787),a=r(9270);function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function i(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},i=1;return{parameterizedRoute:t.map(e=>{let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:n,repeat:s}=l(a[1]);return r[e]={pos:i++,repeat:s,optional:n},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:n}=l(a[1]);return r[e]={pos:i++,repeat:t,optional:n},t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function s(e){let{parameterizedRoute:t,groups:r}=i(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function u(e){let{getSafeRouteKey:t,segment:r,routeKeys:n,keyPrefix:o}=e,{key:a,optional:i,repeat:s}=l(r),u=a.replace(/\W/g,"");o&&(u=""+o+u);let c=!1;return(0===u.length||u.length>30)&&(c=!0),isNaN(parseInt(u.slice(0,1)))||(c=!0),c&&(u=t()),o?n[u]=""+o+a:n[u]=""+a,s?i?"(?:/(?<"+u+">.+?))?":"/(?<"+u+">.+?)":"/(?<"+u+">[^/]+?)"}function c(e,t){let r;let l=(0,a.removeTrailingSlash)(e).slice(1).split("/"),i=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),s={};return{namedParameterizedRoute:l.map(e=>{let r=n.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);return r&&a?u({getSafeRouteKey:i,segment:a[1],routeKeys:s,keyPrefix:t?"nxtI":void 0}):a?u({getSafeRouteKey:i,segment:a[1],routeKeys:s,keyPrefix:t?"nxtP":void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:s}}function d(e,t){let r=c(e,t);return{...s(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function f(e,t){let{parameterizedRoute:r}=i(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=c(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},1841:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),l=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),l=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function a(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(l){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');a(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(l)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');a(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},5451:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isGroupSegment",{enumerable:!0,get:function(){return r}})},7925:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{WEB_VITALS:function(){return r},execOnce:function(){return n},isAbsoluteUrl:function(){return a},getLocationOrigin:function(){return l},getURL:function(){return i},getDisplayName:function(){return s},isResSent:function(){return u},normalizeRepeatedSlashes:function(){return c},loadGetInitialProps:function(){return d},SP:function(){return f},ST:function(){return p},DecodeError:function(){return h},NormalizeError:function(){return m},PageNotFoundError:function(){return v},MissingStaticPage:function(){return g},MiddlewareNotFoundError:function(){return y},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?"),r=t[0];return r.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n){let t='"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.';throw Error(t)}return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},6962:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return o}});let n=r(3865),o=n.createClientModuleProxy},8590:(e,t,r)=>{"use strict";let{createProxy:n}=r(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/components/app-router.js")},7270:(e,t,r)=>{"use strict";let{createProxy:n}=r(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/components/layout-router.js")},4121:(e,t,r)=>{"use strict";let{createProxy:n}=r(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/components/not-found-boundary.js")},9114:(e,t,r)=>{"use strict";let{createProxy:n}=r(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/components/render-from-template-context.js")},4153:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return o}});let n=r(6153);function o(){return new Proxy({},{get(e,t){"string"==typeof t&&(0,n.staticGenerationBailout)("searchParams."+t)}})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6757:(e,t,r)=>{"use strict";let{createProxy:n}=r(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js")},3333:(e,t,r)=>{"use strict";let{createProxy:n}=r(6962);e.exports=n("/mnt/persist/workspace/node_modules/next/dist/client/link.js")},5288:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouter:function(){return n.default},LayoutRouter:function(){return o.default},RenderFromTemplateContext:function(){return a.default},staticGenerationAsyncStorage:function(){return l.staticGenerationAsyncStorage},requestAsyncStorage:function(){return i.requestAsyncStorage},actionAsyncStorage:function(){return s.actionAsyncStorage},staticGenerationBailout:function(){return u.staticGenerationBailout},createSearchParamsBailoutProxy:function(){return d.createSearchParamsBailoutProxy},serverHooks:function(){return f},renderToReadableStream:function(){return v},decodeReply:function(){return g},decodeAction:function(){return y},preloadStyle:function(){return p.preloadStyle},preloadFont:function(){return p.preloadFont},preconnect:function(){return p.preconnect},StaticGenerationSearchParamsBailoutProvider:function(){return c.default},NotFoundBoundary:function(){return b}});let n=h(r(8590)),o=h(r(7270)),a=h(r(9114)),l=r(5869),i=r(4580),s=r(2934),u=r(6153),c=h(r(6757)),d=r(4153),f=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=m(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=o?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(n,a,l):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(7934)),p=r(7898);function h(e){return e&&e.__esModule?e:{default:e}}function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(m=function(e){return e?r:t})(e)}let{renderToReadableStream:v,decodeReply:g,decodeAction:y}=r(3865),{NotFoundBoundary:b}=r(4121)},7898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preloadStyle:function(){return o},preloadFont:function(){return a},preconnect:function(){return l}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(4881));function o(e){n.default.preload(e,{as:"style"})}function a(e,t){n.default.preload(e,{as:"font",type:t})}function l(e,t){"string"==typeof t?n.default.preconnect(e,{crossOrigin:t}):n.default.preconnect(e)}},6427:(e,t,r)=>{"use strict";e.exports=r(399)},4881:(e,t,r)=>{"use strict";e.exports=r(6427).vendored["react-rsc"].ReactDOM},3865:(e,t,r)=>{"use strict";e.exports=r(6427).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},8304:(e,t,r)=>{"use strict";e.exports=r(6427).vendored["react-rsc"].React},3326:(e,t,r)=>{"use strict";e.exports=r(6427).vendored["react-shared"].ReactJsxRuntime},2467:(e,t,r)=>{"use strict";e.exports=r(3333)},5074:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentParam",{enumerable:!0,get:function(){return o}});let n=r(469);function o(e){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));return(t&&(e=e.slice(t.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:"dynamic",param:e.slice(1,-1)}:null}},469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},isInterceptionRouteAppPath:function(){return a},extractInterceptionRouteInformation:function(){return l}});let n=r(3913),o=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function l(e){let t,r,a;for(let n of e.split("/"))if(r=o.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?`/${a}`:t+"/"+a;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let l=t.split("/");if(l.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);a=l.slice(0,-2).concat(a).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:a}}},5832:(e,t,r)=>{"use strict";e.exports=r(399)},4949:(e,t,r)=>{"use strict";e.exports=r(5832).vendored.contexts.AppRouterContext},1006:(e,t,r)=>{"use strict";e.exports=r(5832).vendored.contexts.HooksClientContext},6245:(e,t,r)=>{"use strict";e.exports=r(5832).vendored.contexts.RouterContext},1247:(e,t,r)=>{"use strict";e.exports=r(5832).vendored.contexts.ServerInsertedHtml},2395:(e,t,r)=>{"use strict";e.exports=r(5832).vendored["react-shared"].ReactJsxRuntime},1298:(e,t,r)=>{"use strict";e.exports=r(5832).vendored["react-ssr"].ReactDOM},4554:(e,t,r)=>{"use strict";e.exports=r(5832).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},8726:(e,t,r)=>{"use strict";e.exports=r(5832).vendored["react-ssr"].React},4288:(e,t,r)=>{e.exports=r(6508)},4160:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveScrollBar=t.useLockAttribute=t.lockAttribute=void 0;var n=r(1980).__importStar(r(8726)),o=r(9825),a=r(7813),l=r(1934),i=(0,o.styleSingleton)();t.lockAttribute="data-scroll-locked";var s=function(e,r,n,o){var l=e.left,i=e.top,s=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat(a.noScrollbarsClassName," {\n   overflow: hidden ").concat(o,";\n   padding-right: ").concat(u,"px ").concat(o,";\n  }\n  body[").concat(t.lockAttribute,"] {\n    overflow: hidden ").concat(o,";\n    overscroll-behavior: contain;\n    ").concat([r&&"position: relative ".concat(o,";"),"margin"===n&&"\n    padding-left: ".concat(l,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(o,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(o,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a.zeroRightClassName," {\n    right: ").concat(u,"px ").concat(o,";\n  }\n  \n  .").concat(a.fullWidthClassName," {\n    margin-right: ").concat(u,"px ").concat(o,";\n  }\n  \n  .").concat(a.zeroRightClassName," .").concat(a.zeroRightClassName," {\n    right: 0 ").concat(o,";\n  }\n  \n  .").concat(a.fullWidthClassName," .").concat(a.fullWidthClassName," {\n    margin-right: 0 ").concat(o,";\n  }\n  \n  body[").concat(t.lockAttribute,"] {\n    ").concat(a.removedBarSizeVariable,": ").concat(u,"px;\n  }\n")},u=function(){var e=parseInt(document.body.getAttribute(t.lockAttribute)||"0",10);return isFinite(e)?e:0};t.useLockAttribute=function(){n.useEffect(function(){return document.body.setAttribute(t.lockAttribute,(u()+1).toString()),function(){var e=u()-1;e<=0?document.body.removeAttribute(t.lockAttribute):document.body.setAttribute(t.lockAttribute,e.toString())}},[])},t.RemoveScrollBar=function(e){var r=e.noRelative,o=e.noImportant,a=e.gapMode,u=void 0===a?"margin":a;(0,t.useLockAttribute)();var c=n.useMemo(function(){return(0,l.getGapWidth)(u)},[u]);return n.createElement(i,{styles:s(c,!r,u,o?"":"!important")})}},7813:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removedBarSizeVariable=t.noScrollbarsClassName=t.fullWidthClassName=t.zeroRightClassName=void 0,t.zeroRightClassName="right-scroll-bar-position",t.fullWidthClassName="width-before-scroll-bar",t.noScrollbarsClassName="with-scroll-bars-hidden",t.removedBarSizeVariable="--removed-body-scroll-bar-size"},7729:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getGapWidth=t.removedBarSizeVariable=t.noScrollbarsClassName=t.fullWidthClassName=t.zeroRightClassName=t.RemoveScrollBar=void 0;var n=r(4160);Object.defineProperty(t,"RemoveScrollBar",{enumerable:!0,get:function(){return n.RemoveScrollBar}});var o=r(7813);Object.defineProperty(t,"zeroRightClassName",{enumerable:!0,get:function(){return o.zeroRightClassName}}),Object.defineProperty(t,"fullWidthClassName",{enumerable:!0,get:function(){return o.fullWidthClassName}}),Object.defineProperty(t,"noScrollbarsClassName",{enumerable:!0,get:function(){return o.noScrollbarsClassName}}),Object.defineProperty(t,"removedBarSizeVariable",{enumerable:!0,get:function(){return o.removedBarSizeVariable}});var a=r(1934);Object.defineProperty(t,"getGapWidth",{enumerable:!0,get:function(){return a.getGapWidth}})},1934:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getGapWidth=t.zeroGap=void 0,t.zeroGap={left:0,top:0,right:0,gap:0};var r=function(e){return parseInt(e||"",10)||0},n=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],o=t["padding"===e?"paddingTop":"marginTop"],a=t["padding"===e?"paddingRight":"marginRight"];return[r(n),r(o),r(a)]};t.getGapWidth=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t.zeroGap;var r=n(e),o=document.documentElement.clientWidth,a=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,a-o+r[2]-r[0])}}},5904:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(1980),o=n.__importStar(r(8726)),a=r(7487),l=n.__importDefault(r(7138)),i=o.forwardRef(function(e,t){return o.createElement(a.RemoveScroll,n.__assign({},e,{ref:t,sideCar:l.default}))});i.classNames=a.RemoveScroll.classNames,t.default=i},4514:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveScrollSideCar=t.getDeltaXY=t.getTouchXY=void 0;var n=r(1980),o=n.__importStar(r(8726)),a=r(7729),l=r(9825),i=r(3315),s=r(2377);t.getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t.getDeltaXY=function(e){return[e.deltaX,e.deltaY]};var u=function(e){return e&&"current"in e?e.current:e},c=0,d=[];t.RemoveScrollSideCar=function(e){var r=o.useRef([]),f=o.useRef([0,0]),p=o.useRef(),h=o.useState(c++)[0],m=o.useState(l.styleSingleton)[0],v=o.useRef(e);o.useEffect(function(){v.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(h));var t=n.__spreadArray([e.lockRef.current],(e.shards||[]).map(u),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(h))}),function(){document.body.classList.remove("block-interactivity-".concat(h)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(h))})}}},[e.inert,e.lockRef.current,e.shards]);var g=o.useCallback(function(e,r){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!v.current.allowPinchZoom;var n,o=(0,t.getTouchXY)(e),a=f.current,l="deltaX"in e?e.deltaX:a[0]-o[0],i="deltaY"in e?e.deltaY:a[1]-o[1],u=e.target,c=Math.abs(l)>Math.abs(i)?"h":"v";if("touches"in e&&"h"===c&&"range"===u.type)return!1;var d=(0,s.locationCouldBeScrolled)(c,u);if(!d)return!0;if(d?n=c:(n="v"===c?"h":"v",d=(0,s.locationCouldBeScrolled)(c,u)),!d)return!1;if(!p.current&&"changedTouches"in e&&(l||i)&&(p.current=n),!n)return!0;var h=p.current||n;return(0,s.handleScroll)(h,r,e,"h"===h?l:i,!0)},[]),y=o.useCallback(function(e){if(d.length&&d[d.length-1]===m){var n="deltaY"in e?(0,t.getDeltaXY)(e):(0,t.getTouchXY)(e),o=r.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(o&&o.should){e.cancelable&&e.preventDefault();return}if(!o){var a=(v.current.shards||[]).map(u).filter(Boolean).filter(function(t){return t.contains(e.target)});(a.length>0?g(e,a[0]):!v.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),b=o.useCallback(function(e,t,n,o){var a={name:e,delta:t,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};r.current.push(a),setTimeout(function(){r.current=r.current.filter(function(e){return e!==a})},1)},[]),w=o.useCallback(function(e){f.current=(0,t.getTouchXY)(e),p.current=void 0},[]),x=o.useCallback(function(r){b(r.type,(0,t.getDeltaXY)(r),r.target,g(r,e.lockRef.current))},[]),E=o.useCallback(function(r){b(r.type,(0,t.getTouchXY)(r),r.target,g(r,e.lockRef.current))},[]);o.useEffect(function(){return d.push(m),e.setCallbacks({onScrollCapture:x,onWheelCapture:x,onTouchMoveCapture:E}),document.addEventListener("wheel",y,i.nonPassive),document.addEventListener("touchmove",y,i.nonPassive),document.addEventListener("touchstart",w,i.nonPassive),function(){d=d.filter(function(e){return e!==m}),document.removeEventListener("wheel",y,i.nonPassive),document.removeEventListener("touchmove",y,i.nonPassive),document.removeEventListener("touchstart",w,i.nonPassive)}},[]);var _=e.removeScrollBar,R=e.inert;return o.createElement(o.Fragment,null,R?o.createElement(m,{styles:"\n  .block-interactivity-".concat(h," {pointer-events: none;}\n  .allow-interactivity-").concat(h," {pointer-events: all;}\n")}):null,_?o.createElement(a.RemoveScrollBar,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}},7487:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RemoveScroll=void 0;var n=r(1980),o=n.__importStar(r(8726)),a=r(7813),l=r(167),i=r(5193),s=function(){},u=o.forwardRef(function(e,t){var r=o.useRef(null),a=o.useState({onScrollCapture:s,onWheelCapture:s,onTouchMoveCapture:s}),u=a[0],c=a[1],d=e.forwardProps,f=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,b=e.noIsolation,w=e.inert,x=e.allowPinchZoom,E=e.as,_=void 0===E?"div":E,R=e.gapMode,S=n.__rest(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(0,l.useMergeRefs)([r,t]),C=n.__assign(n.__assign({},S),u);return o.createElement(o.Fragment,null,m&&o.createElement(g,{sideCar:i.effectCar,removeScrollBar:h,shards:v,noRelative:y,noIsolation:b,inert:w,setCallbacks:c,allowPinchZoom:!!x,lockRef:r,gapMode:R}),d?o.cloneElement(o.Children.only(f),n.__assign(n.__assign({},C),{ref:P})):o.createElement(_,n.__assign({},C,{className:p,ref:P}),f))});t.RemoveScroll=u,u.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},u.classNames={fullWidth:a.fullWidthClassName,zeroRight:a.zeroRightClassName}},3315:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.nonPassive=void 0;var r=!1;if("undefined"!=typeof window)try{var n=Object.defineProperty({},"passive",{get:function(){return r=!0,!0}});window.addEventListener("test",n,n),window.removeEventListener("test",n,n)}catch(e){r=!1}t.nonPassive=!!r&&{passive:!1}},2377:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleScroll=t.locationCouldBeScrolled=void 0;var r=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])};t.locationCouldBeScrolled=function(e,t){var r=t.ownerDocument,a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),n(e,a)){var l=o(e,a);if(l[1]>l[2])return!0}a=a.parentNode}while(a&&a!==r.body);return!1};var n=function(e,t){return"v"===e?r(t,"overflowY"):r(t,"overflowX")},o=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]};t.handleScroll=function(e,t,r,a,l){var i,s=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=s*a,c=r.target,d=t.contains(c),f=!1,p=u>0,h=0,m=0;do{if(!c)break;var v=o(e,c),g=v[0],y=v[1]-v[2]-s*g;(g||y)&&n(e,c)&&(h+=y,m+=g);var b=c.parentNode;c=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!d&&c!==document.body||d&&(t.contains(c)||t===c));return p&&(l&&1>Math.abs(h)||!l&&u>h)?f=!0:!p&&(l&&1>Math.abs(m)||!l&&-u>m)&&(f=!0),f}},4350:(e,t,r)=>{"use strict";t.f=void 0;var n=r(1980).__importDefault(r(5904));t.f=n.default},5193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.effectCar=void 0;var n=r(1824);t.effectCar=(0,n.createSidecarMedium)()},7138:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(1824),o=r(4514),a=r(5193);t.default=(0,n.exportSidecar)(a.effectCar,o.RemoveScrollSideCar)},4444:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.styleSingleton=void 0;var n=r(1468);t.styleSingleton=function(){var e=(0,n.styleHookSingleton)();return function(t){return e(t.styles,t.dynamic),null}}},1468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.styleHookSingleton=void 0;var n=r(1980).__importStar(r(8726)),o=r(3233);t.styleHookSingleton=function(){var e=(0,o.stylesheetSingleton)();return function(t,r){n.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}}},9825:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.styleHookSingleton=t.stylesheetSingleton=t.styleSingleton=void 0;var n=r(4444);Object.defineProperty(t,"styleSingleton",{enumerable:!0,get:function(){return n.styleSingleton}});var o=r(3233);Object.defineProperty(t,"stylesheetSingleton",{enumerable:!0,get:function(){return o.stylesheetSingleton}});var a=r(1468);Object.defineProperty(t,"styleHookSingleton",{enumerable:!0,get:function(){return a.styleHookSingleton}})},3233:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.stylesheetSingleton=void 0;var n=r(2702);t.stylesheetSingleton=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=(0,n.getNonce)();return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}}},334:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assignRef=void 0,t.assignRef=function(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}},1660:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createCallbackRef=void 0,t.createCallbackRef=function(e){var t=null;return{get current(){return t},set current(value){var r=t;r!==value&&(t=value,e(value,r))}}}},167:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useRefToCallback=t.refToCallback=t.transformRef=t.useTransformRef=t.useMergeRefs=t.mergeRefs=t.createCallbackRef=t.useCallbackRef=t.assignRef=void 0;var n=r(334);Object.defineProperty(t,"assignRef",{enumerable:!0,get:function(){return n.assignRef}});var o=r(317);Object.defineProperty(t,"useCallbackRef",{enumerable:!0,get:function(){return o.useCallbackRef}});var a=r(1660);Object.defineProperty(t,"createCallbackRef",{enumerable:!0,get:function(){return a.createCallbackRef}});var l=r(4541);Object.defineProperty(t,"mergeRefs",{enumerable:!0,get:function(){return l.mergeRefs}});var i=r(1969);Object.defineProperty(t,"useMergeRefs",{enumerable:!0,get:function(){return i.useMergeRefs}});var s=r(4506);Object.defineProperty(t,"useTransformRef",{enumerable:!0,get:function(){return s.useTransformRef}});var u=r(9389);Object.defineProperty(t,"transformRef",{enumerable:!0,get:function(){return u.transformRef}});var c=r(8677);Object.defineProperty(t,"refToCallback",{enumerable:!0,get:function(){return c.refToCallback}}),Object.defineProperty(t,"useRefToCallback",{enumerable:!0,get:function(){return c.useRefToCallback}})},4541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mergeRefs=void 0;var n=r(334),o=r(1660);t.mergeRefs=function(e){return(0,o.createCallbackRef)(function(t){return e.forEach(function(e){return(0,n.assignRef)(e,t)})})}},8677:(e,t)=>{"use strict";function r(e){return function(t){"function"==typeof e?e(t):e&&(e.current=t)}}Object.defineProperty(t,"__esModule",{value:!0}),t.useRefToCallback=t.refToCallback=void 0,t.refToCallback=r;var n=function(){return null},o=new WeakMap,a=function(e){var t=e||n,a=o.get(t);if(a)return a;var l=r(t);return o.set(t,l),l};t.useRefToCallback=function(e){return a(e)}},9389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.transformRef=void 0;var n=r(334),o=r(1660);t.transformRef=function(e,t){return(0,o.createCallbackRef)(function(r){return(0,n.assignRef)(e,t(r))})}},1969:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useMergeRefs=void 0;var n=r(1980).__importStar(r(8726)),o=r(334),a=r(317),l="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,i=new WeakMap;t.useMergeRefs=function(e,t){var r=(0,a.useCallbackRef)(t||null,function(t){return e.forEach(function(e){return(0,o.assignRef)(e,t)})});return l(function(){var t=i.get(r);if(t){var n=new Set(t),a=new Set(e),l=r.current;n.forEach(function(e){a.has(e)||(0,o.assignRef)(e,null)}),a.forEach(function(e){n.has(e)||(0,o.assignRef)(e,l)})}i.set(r,e)},[e]),r}},317:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useCallbackRef=void 0;var n=r(8726);t.useCallbackRef=function(e,t){var r=(0,n.useState)(function(){return{value:e,callback:t,facade:{get current(){return r.value},set current(value){var n=r.value;n!==value&&(r.value=value,r.callback(value,n))}}}})[0];return r.callback=t,r.facade}},4506:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useTransformRef=void 0;var n=r(334),o=r(317);t.useTransformRef=function(e,t){return(0,o.useCallbackRef)(null,function(r){return(0,n.assignRef)(e,t(r))})}},3165:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setConfig=t.config=void 0,t.config={onError:function(e){return console.error(e)}},t.setConfig=function(e){Object.assign(t.config,e)}},9253:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.env=void 0;var n=r(1610);t.env={isNode:n.isNode,forceCache:!1}},3645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exportSidecar=void 0;var n=r(1980),o=n.__importStar(r(8726)),a=function(e){var t=e.sideCar,r=n.__rest(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return o.createElement(a,n.__assign({},r))};a.isSideCarExport=!0,t.exportSidecar=function(e,t){return e.useMedium(t),a}},8989:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sidecar=void 0;var n=r(1980),o=n.__importStar(r(8726)),a=r(5882);t.sidecar=function(e,t){var r=function(){return t};return function(l){var i=(0,a.useSidecar)(e,l.sideCar),s=i[0];return i[1]&&t?r:s?o.createElement(s,n.__assign({},l)):null}}},5882:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.useSidecar=void 0;var n=r(8726),o=r(9253),a=new WeakMap,l={};t.useSidecar=function(e,t){var r,i,s,u,c,d,f,p,h=t&&t.options||l;return o.env.isNode&&!h.ssr?[null,null]:(r=t&&t.options||l,i=o.env.forceCache||o.env.isNode&&!!r.ssr||!r.async,u=(s=(0,n.useState)(i?function(){return a.get(e)}:void 0))[0],c=s[1],f=(d=(0,n.useState)(null))[0],p=d[1],(0,n.useEffect)(function(){u||e().then(function(r){var n,o=t?t.read():r.default||r;if(!o)throw console.error("Sidecar error: with importer",e),t?(console.error("Sidecar error: with medium",t),n=Error("Sidecar medium was not found")):n=Error("Sidecar was not found in exports"),p(function(){return n}),n;a.set(e,o),c(function(){return o})},function(e){return p(function(){return e})})},[]),[u,f])}},1824:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.exportSidecar=t.renderCar=t.createSidecarMedium=t.createMedium=t.setConfig=t.useSidecar=t.sidecar=void 0;var n=r(8989);Object.defineProperty(t,"sidecar",{enumerable:!0,get:function(){return n.sidecar}});var o=r(5882);Object.defineProperty(t,"useSidecar",{enumerable:!0,get:function(){return o.useSidecar}});var a=r(3165);Object.defineProperty(t,"setConfig",{enumerable:!0,get:function(){return a.setConfig}});var l=r(9973);Object.defineProperty(t,"createMedium",{enumerable:!0,get:function(){return l.createMedium}}),Object.defineProperty(t,"createSidecarMedium",{enumerable:!0,get:function(){return l.createSidecarMedium}});var i=r(2293);Object.defineProperty(t,"renderCar",{enumerable:!0,get:function(){return i.renderCar}});var s=r(3645);Object.defineProperty(t,"exportSidecar",{enumerable:!0,get:function(){return s.exportSidecar}})},9973:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createSidecarMedium=t.createMedium=void 0;var n=r(1980);function o(e){return e}function a(e,t){void 0===t&&(t=o);var r=[],n=!1;return{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(a)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}}}t.createMedium=function(e,t){return void 0===t&&(t=o),a(e,t)},t.createSidecarMedium=function(e){void 0===e&&(e={});var t=a(null);return t.options=n.__assign({async:!0,ssr:!1},e),t}},2293:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderCar=void 0;var n=r(1980),o=n.__importStar(r(8726)),a=r(8726);t.renderCar=function(e,t){function r(t){var r=t.stateRef,l=t.props,i=(0,a.useCallback)(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,a.useLayoutEffect)(function(){r.current(e)}),null},[]);return o.createElement(e,n.__assign({},l,{children:i}))}var l=o.memo(function(e){var t=e.stateRef,r=e.defaultState,n=e.children,o=(0,a.useState)(r.current),l=o[0],i=o[1];return(0,a.useEffect)(function(){t.current=i},[]),n.apply(void 0,l)},function(){return!0});return function(e){var n=o.useRef(t(e)),a=o.useRef(function(e){return n.current=e});return o.createElement(o.Fragment,null,o.createElement(r,{stateRef:a,props:e}),o.createElement(l,{stateRef:a,defaultState:n,children:e.children}))}}},4064:(e,t,r)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(8726),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,l=n.useEffect,i=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),o=n[0].inst,c=n[1];return i(function(){o.value=r,o.getSnapshot=t,u(o)&&c({inst:o})},[e,r,t]),l(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},5004:(e,t,r)=>{"use strict";e.exports=r(4064)},6270:(e,t)=>{"use strict";t._=t._class_private_field_loose_base=function(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}},1471:(e,t)=>{"use strict";var r=0;t._=t._class_private_field_loose_key=function(e){return"__private_"+r+++"_"+e}},8446:(e,t)=>{"use strict";t._=t._interop_require_default=function(e){return e&&e.__esModule?e:{default:e}}},2434:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=t._interop_require_wildcard=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&Object.prototype.hasOwnProperty.call(e,l)){var i=a?Object.getOwnPropertyDescriptor(e,l):null;i&&(i.get||i.set)?Object.defineProperty(o,l,i):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}},7253:(e,t)=>{var r;/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=a(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=a(t,r));return t}(r)))}return e}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0!==(r=(function(){return o}).apply(t,[]))&&(e.exports=r)}()},6306:(e,t,r)=>{"use strict";function n(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{u:()=>n})},1193:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:()=>n})},7681:(e,t,r)=>{"use strict";r.d(t,{B:()=>s});var n=r(8726),o=r(752),a=r(7754),l=r(120),i=r(2395);function s(e){let t=e+"CollectionProvider",[r,s]=(0,o.b)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),a=n.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:a,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.Z8)(f),h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(f,r),l=(0,a.e)(t,o.collectionRef);return(0,i.jsx)(p,{ref:l,children:n})});h.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,l.Z8)(m),y=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,s=n.useRef(null),u=(0,a.e)(t,s),d=c(m,r);return n.useEffect(()=>(d.itemMap.set(s,{ref:s,...l}),()=>void d.itemMap.delete(s))),(0,i.jsx)(g,{[v]:"",ref:u,children:o})});return y.displayName=m,[{Provider:d,Slot:h,ItemSlot:y},function(t){let r=c(e+"CollectionConsumer",t),o=n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`)),n=Array.from(r.itemMap.values()),o=n.sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current));return o},[r.collectionRef,r.itemMap]);return o},s]}},7754:(e,t,r)=>{"use strict";r.d(t,{F:()=>a,e:()=>l});var n=r(8726);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(a(...e),e)}},752:(e,t,r)=>{"use strict";r.d(t,{b:()=>l,k:()=>a});var n=r(8726),o=r(2395);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,l=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(r.Provider,{value:l,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let l=n.createContext(a),i=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,u=r?.[e]?.[i]||l,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:a})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[i]||l,u=n.useContext(s);if(u)return u;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e),a=o[`__scope${n}`];return{...t,...a}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},7796:(e,t,r)=>{"use strict";r.d(t,{gm:()=>l,zt:()=>i});var n=r(8726),o=r(2395),a=n.createContext(void 0);function l(e){let t=n.useContext(a);return e||t||"ltr"}var i=e=>{let{dir:t,children:r}=e;return(0,o.jsx)(a.Provider,{value:t,children:r})}},6387:(e,t,r)=>{"use strict";r.d(t,{XB:()=>f});var n,o=r(8726),a=r(1193),l=r(4049),i=r(7754),s=r(8414),u=r(2395),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...b}=e,w=o.useContext(d),[x,E]=o.useState(null),_=x?.ownerDocument??globalThis?.document,[,R]=o.useState({}),S=(0,i.e)(t,e=>E(e)),P=Array.from(w.layers),[C]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),j=P.indexOf(C),O=x?P.indexOf(x):-1,M=w.layersWithOutsidePointerEventsDisabled.size>0,T=O>=j,N=function(e,t=globalThis?.document){let r=(0,s.W)(e),n=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=n,t.addEventListener("click",a.current,{once:!0})):n()}else t.removeEventListener("click",a.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));!T||r||(m?.(e),g?.(e),e.defaultPrevented||y?.())},_),k=function(e,t=globalThis?.document){let r=(0,s.W)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));r||(v?.(e),g?.(e),e.defaultPrevented||y?.())},_);return function(e,t=globalThis?.document){let r=(0,s.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{let t=O===w.layers.size-1;t&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},_),o.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=_.body.style.pointerEvents,_.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(_.body.style.pointerEvents=n)}},[x,_,r,w]),o.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(l.WV.div,{...b,ref:S,style:{pointerEvents:M?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.M)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,a.M)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,a.M)(e.onPointerDownCapture,N.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,r,{discrete:n}){let o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,l.jH)(o,a):o.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),a=(0,i.e)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(l.WV.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},9232:(e,t,r)=>{"use strict";r.d(t,{EW:()=>a});var n=r(8726),o=0;function a(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},510:(e,t,r)=>{"use strict";r.d(t,{M:()=>d});var n=r(8726),o=r(7754),a=r(4049),l=r(8414),i=r(2395),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[b,w]=n.useState(null),x=(0,l.W)(v),E=(0,l.W)(g),_=n.useRef(null),R=(0,o.e)(t,e=>w(e)),S=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(S.paused||!b)return;let t=e.target;b.contains(t)?_.current=t:h(_.current,{select:!0})},t=function(e){if(S.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||h(_.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){let t=document.activeElement;if(t===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,S.paused]),n.useEffect(()=>{if(b){m.add(S);let e=document.activeElement,t=b.contains(e);if(!t){let t=new CustomEvent(s,c);b.addEventListener(s,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,E),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(u,E),m.remove(S)},0)}}},[b,x,E,S]);let P=n.useCallback(e=>{if(!r&&!d||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,a]=function(e){let t=f(e),r=p(t,e),n=p(t.reverse(),e);return[r,n]}(t),l=o&&a;l?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(a,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,S.paused]);return(0,i.jsx)(a.WV.div,{tabIndex:-1,...y,ref:R,onKeyDown:P})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},7098:(e,t,r)=>{"use strict";r.d(t,{M:()=>s});var n,o=r(8726),a=r(6007),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function s(e){let[t,r]=o.useState(l());return(0,a.b)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},4959:(e,t,r)=>{"use strict";r.d(t,{ee:()=>e9,Eh:()=>tt,VY:()=>te,fC:()=>e3,D7:()=>eY});var n=r(8726);let o=["top","right","bottom","left"],a=Math.min,l=Math.max,i=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>d[e])}let w=["left","right"],x=["right","left"],E=["top","bottom"],_=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function P(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function C(e,t,r){let n,{reference:o,floating:a}=e,l=y(t),i=m(y(t)),s=v(i),u=p(t),c="y"===l,d=o.x+o.width/2-a.width/2,f=o.y+o.height/2-a.height/2,g=o[s]/2-a[s]/2;switch(u){case"top":n={x:d,y:o.y-a.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-a.width,y:f};break;default:n={x:o.x,y:o.y}}switch(h(t)){case"start":n[i]-=g*(r&&c?-1:1);break;case"end":n[i]+=g*(r&&c?-1:1)}return n}let j=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:l}=r,i=a.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=C(u,n,s),f=n,p={},h=0;for(let r=0;r<i.length;r++){let{name:a,fn:m}=i[r],{x:v,y:g,data:y,reset:b}=await m({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[a]:{...p[a],...y}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=C(u,f,s)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function O(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:a,rects:l,elements:i,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=S(h),v=i[p?"floating"===d?"reference":"floating":d],g=P(await a.getClippingRect({element:null==(r=await (null==a.isElement?void 0:a.isElement(v)))||r?v:v.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(i.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(i.floating)),w=await (null==a.isElement?void 0:a.isElement(b))&&await (null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},x=P(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:y,offsetParent:b,strategy:s}):y);return{top:(g.top-x.top+m.top)/w.y,bottom:(x.bottom-g.bottom+m.bottom)/w.y,left:(g.left-x.left+m.left)/w.x,right:(x.right-g.right+m.right)/w.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}let N=new Set(["left","top"]);async function k(e,t){let{placement:r,platform:n,elements:o}=e,a=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=p(r),i=h(r),s="y"===y(r),u=N.has(l)?-1:1,c=a&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return i&&"number"==typeof g&&(v="end"===i?-1*g:g),s?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function A(){return"undefined"!=typeof window}function I(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function D(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!A()&&(e instanceof Node||e instanceof D(e).Node)}function W(e){return!!A()&&(e instanceof Element||e instanceof D(e).Element)}function B(e){return!!A()&&(e instanceof HTMLElement||e instanceof D(e).HTMLElement)}function U(e){return!!A()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof D(e).ShadowRoot)}let z=new Set(["inline","contents"]);function H(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!z.has(o)}let V=new Set(["table","td","th"]),$=[":popover-open",":modal"];function Y(e){return $.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let K=["transform","translate","scale","rotate","perspective"],G=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function q(e){let t=Z(),r=W(e)?ee(e):e;return K.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||G.some(e=>(r.willChange||"").includes(e))||X.some(e=>(r.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(I(e))}function ee(e){return D(e).getComputedStyle(e)}function et(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===I(e))return e;let t=e.assignedSlot||e.parentNode||U(e)&&e.host||L(e);return U(t)?t.host:t}function en(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=er(t);return Q(r)?t.ownerDocument?t.ownerDocument.body:t.body:B(r)&&H(r)?r:e(r)}(e),a=o===(null==(n=e.ownerDocument)?void 0:n.body),l=D(o);if(a){let e=eo(l);return t.concat(l,l.visualViewport||[],H(o)?o:[],e&&r?en(e):[])}return t.concat(o,en(o,[],r))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=ee(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=B(e),a=o?e.offsetWidth:r,l=o?e.offsetHeight:n,s=i(r)!==a||i(n)!==l;return s&&(r=a,n=l),{width:r,height:n,$:s}}function el(e){return W(e)?e:e.contextElement}function ei(e){let t=el(e);if(!B(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:a}=ea(t),l=(a?i(r.width):r.width)/n,s=(a?i(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let es=u(0);function eu(e){let t=D(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function ec(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let a=e.getBoundingClientRect(),l=el(e),i=u(1);t&&(n?W(n)&&(i=ei(n)):i=ei(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===D(l))&&o)?eu(l):u(0),c=(a.left+s.x)/i.x,d=(a.top+s.y)/i.y,f=a.width/i.x,p=a.height/i.y;if(l){let e=D(l),t=n&&W(n)?D(n):n,r=e,o=eo(r);for(;o&&n&&t!==r;){let e=ei(o),t=o.getBoundingClientRect(),n=ee(o),a=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=a,d+=l,o=eo(r=D(o))}}return P({width:f,height:p,x:c,y:d})}function ed(e,t){let r=et(e).scrollLeft;return t?t.left+r:ec(L(e)).left+r}function ef(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect(),o=n.left+t.scrollLeft-(r?0:ed(e,n)),a=n.top+t.scrollTop;return{x:o,y:a}}let ep=new Set(["absolute","fixed"]);function eh(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=D(e),n=L(e),o=r.visualViewport,a=n.clientWidth,l=n.clientHeight,i=0,s=0;if(o){a=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(i=o.offsetLeft,s=o.offsetTop)}return{width:a,height:l,x:i,y:s}}(e,r);else if("document"===t)n=function(e){let t=L(e),r=et(e),n=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=l(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),i=-r.scrollLeft+ed(e),s=-r.scrollTop;return"rtl"===ee(n).direction&&(i+=l(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:i,y:s}}(L(e));else if(W(t))n=function(e,t){let r=ec(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=B(e)?ei(e):u(1),l=e.clientWidth*a.x,i=e.clientHeight*a.y,s=o*a.x,c=n*a.y;return{width:l,height:i,x:s,y:c}}(t,r);else{let r=eu(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return P(n)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let r=e.offsetParent;return L(e)===r&&(r=r.ownerDocument.body),r}function eg(e,t){var r;let n=D(e);if(Y(e))return n;if(!B(e)){let t=er(e);for(;t&&!Q(t);){if(W(t)&&!em(t))return t;t=er(t)}return n}let o=ev(e,t);for(;o&&(r=o,V.has(I(r)))&&em(o);)o=ev(o,t);return o&&Q(o)&&em(o)&&!q(o)?n:o||function(e){let t=er(e);for(;B(t)&&!Q(t);){if(q(t))return t;if(Y(t))break;t=er(t)}return null}(e)||n}let ey=async function(e){let t=this.getOffsetParent||eg,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=B(t),o=L(t),a="fixed"===r,l=ec(e,!0,a,t),i={scrollLeft:0,scrollTop:0},s=u(0);if(n||!n&&!a){if(("body"!==I(t)||H(o))&&(i=et(t)),n){let e=ec(t,!0,a,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=ed(o))}a&&!n&&o&&(s.x=ed(o));let c=!o||n||a?u(0):ef(o,i),d=l.left+i.scrollLeft-s.x-c.x,f=l.top+i.scrollTop-s.y-c.y;return{x:d,y:f,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,a="fixed"===o,l=L(n),i=!!t&&Y(t.floating);if(n===l||i&&a)return r;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=B(n);if((f||!f&&!a)&&(("body"!==I(n)||H(l))&&(s=et(n)),B(n))){let e=ec(n);c=ei(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!l||f||a?u(0):ef(l,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:r.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i="clippingAncestors"===r?Y(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=en(e,[],!1).filter(e=>W(e)&&"body"!==I(e)),o=null,a="fixed"===ee(e).position,l=a?er(e):e;for(;W(l)&&!Q(l);){let t=ee(l),r=q(l);r||"fixed"!==t.position||(o=null);let i=a?!r&&!o:!r&&"static"===t.position&&!!o&&ep.has(o.position)||H(l)&&!r&&function e(t,r){let n=er(t);return!(n===r||!W(n)||Q(n))&&("fixed"===ee(n).position||e(n,r))}(e,l);i?n=n.filter(e=>e!==l):o=t,l=er(l)}return t.set(e,n),n}(t,this._c):[].concat(r),s=[...i,n],u=s[0],c=s.reduce((e,r)=>{let n=eh(t,r,o);return e.top=l(n.top,e.top),e.right=a(n.right,e.right),e.bottom=a(n.bottom,e.bottom),e.left=l(n.left,e.left),e},eh(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ea(e);return{width:t,height:r}},getScale:ei,isElement:W,isRTL:function(e){return"rtl"===ee(e).direction}};function ew(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=S(p),b={x:r,y:n},w=m(y(o)),x=v(w),E=await s.getDimensions(d),_="y"===w,R=_?"clientHeight":"clientWidth",P=i.reference[x]+i.reference[w]-b[w]-i.floating[x],C=b[w]-i.reference[w],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),O=j?j[R]:0;O&&await (null==s.isElement?void 0:s.isElement(j))||(O=u.floating[R]||i.floating[x]);let M=O/2-E[x]/2-1,T=a(g[_?"top":"left"],M),N=a(g[_?"bottom":"right"],M),k=O-E[x]-N,A=O/2-E[x]/2+(P/2-C/2),I=l(T,a(A,k)),D=!c.arrow&&null!=h(o)&&A!==I&&i.reference[x]/2-(A<T?T:N)-E[x]/2<0,L=D?A<T?A-T:A-k:0;return{[w]:b[w]+L,data:{[w]:I,centerOffset:A-I-L,...D&&{alignmentOffset:L}},reset:D}}}),eE=(e,t,r)=>{let n=new Map,o={platform:eb,...r},a={...o.platform,_c:n};return j(e,t,{...o,platform:a})};var e_=r(1298),eR="undefined"!=typeof document?n.useLayoutEffect:function(){};function eS(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eS(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eS(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eP(e){if("undefined"==typeof window)return 1;let t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function eC(e,t){let r=eP(e);return Math.round(t*r)/r}function ej(e){let t=n.useRef(e);return eR(()=>{t.current=e}),t}let eO=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ex({element:r.current,padding:n}).fn(t):{}:r?ex({element:r,padding:n}).fn(t):{}}}),eM=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:a,placement:l,middlewareData:i}=t,s=await k(t,e);return l===(null==(r=i.offset)?void 0:r.placement)&&null!=(n=i.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:a+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},h=await O(t,c),v=y(p(o)),g=m(v),b=d[g],w=d[v];if(i){let e=b+h["y"===g?"top":"left"],t=b-h["y"===g?"bottom":"right"];b=l(e,a(b,t))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=w+h[e],n=w-h[t];w=l(r,a(w,n))}let x=u.fn({...t,[g]:b,[v]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[g]:i,[v]:s}}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:a,middlewareData:l}=t,{offset:i=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:r,y:n},d=y(o),h=m(d),v=c[h],g=c[d],b=f(i,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===h?"height":"width",t=a.reference[h]-a.floating[e]+w.mainAxis,r=a.reference[h]+a.reference[e]-w.mainAxis;v<t?v=t:v>r&&(v=r)}if(u){var x,E;let e="y"===h?"width":"height",t=N.has(p(o)),r=a.reference[d]-a.floating[e]+(t&&(null==(x=l.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),n=a.reference[d]+a.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[d])||0)-(t?w.crossAxis:0);g<r?g=r:g>n&&(g=n)}return{[h]:v,[d]:g}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,a,l;let{placement:i,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:g}=t,{mainAxis:S=!0,crossAxis:P=!0,fallbackPlacements:C,fallbackStrategy:j="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:T=!0,...N}=f(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let k=p(i),A=y(c),I=p(c)===c,D=await (null==d.isRTL?void 0:d.isRTL(g.floating)),L=C||(I||!T?[R(c)]:function(e){let t=R(e);return[b(e),t,b(t)]}(c)),F="none"!==M;!C&&F&&L.push(...function(e,t,r,n){let o=h(e),a=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?x:w;return t?w:x;case"left":case"right":return t?E:_;default:return[]}}(p(e),"start"===r,n);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(b)))),a}(c,T,M,D));let W=[c,...L],B=await O(t,N),U=[],z=(null==(n=s.flip)?void 0:n.overflows)||[];if(S&&U.push(B[k]),P){let e=function(e,t,r){void 0===r&&(r=!1);let n=h(e),o=m(y(e)),a=v(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[a]>t.floating[a]&&(l=R(l)),[l,R(l)]}(i,u,D);U.push(B[e[0]],B[e[1]])}if(z=[...z,{placement:i,overflows:U}],!U.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=W[e];if(t){let r="alignment"===P&&A!==y(t);if(!r||z.every(e=>y(e.placement)!==A||e.overflows[0]>0))return{data:{index:e,overflows:z},reset:{placement:t}}}let r=null==(a=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!r)switch(j){case"bestFit":{let e=null==(l=z.filter(e=>{if(F){let t=y(e.placement);return t===A||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=c}if(i!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i;let{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),g=await O(t,v),b=p(s),w=h(s),x="y"===y(s),{width:E,height:_}=u.floating;"top"===b||"bottom"===b?(o=b,i=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(i=b,o="end"===w?"top":"bottom");let R=_-g.top-g.bottom,S=E-g.left-g.right,P=a(_-g[o],R),C=a(E-g[i],S),j=!t.middlewareData.shift,M=P,T=C;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(T=S),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(M=R),j&&!w){let e=l(g.left,0),t=l(g.right,0),r=l(g.top,0),n=l(g.bottom,0);x?T=E-2*(0!==e||0!==t?e+t:l(g.left,g.right)):M=_-2*(0!==r||0!==n?r+n:l(g.top,g.bottom))}await m({...t,availableWidth:T,availableHeight:M});let N=await c.getDimensions(d.floating);return E!==N.width||_!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eI=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=f(e,t);switch(n){case"referenceHidden":{let e=await O(t,{...o,elementContext:"reference"}),n=M(e,r.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:T(n)}}}case"escaped":{let e=await O(t,{...o,altBoundary:!0}),n=M(e,r.floating);return{data:{escapedOffsets:n,escaped:T(n)}}}default:return{}}}}}(e),options:[e,t]}),eD=(e,t)=>({...eO(e),options:[e,t]});var eL=r(4049),eF=r(2395),eW=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...a}=e;return(0,eF.jsx)(eL.WV.svg,{...a,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eW.displayName="Arrow";var eB=r(7754),eU=r(752),ez=r(8414),eH=r(6007),eV="Popper",[e$,eY]=(0,eU.b)(eV),[eK,eG]=e$(eV),eX=e=>{let{__scopePopper:t,children:r}=e,[o,a]=n.useState(null);return(0,eF.jsx)(eK,{scope:t,anchor:o,onAnchorChange:a,children:r})};eX.displayName=eV;var eq="PopperAnchor",eZ=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...a}=e,l=eG(eq,r),i=n.useRef(null),s=(0,eB.e)(t,i);return n.useEffect(()=>{l.onAnchorChange(o?.current||i.current)}),o?null:(0,eF.jsx)(eL.WV.div,{...a,ref:s})});eZ.displayName=eq;var eJ="PopperContent",[eQ,e0]=e$(eJ),e1=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:i=0,align:u="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...b}=e,w=eG(eJ,r),[x,E]=n.useState(null),_=(0,eB.e)(t,e=>E(e)),[R,S]=n.useState(null),P=function(e){let[t,r]=n.useState(void 0);return(0,eH.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(R),C=P?.width??0,j=P?.height??0,O="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],T=M.length>0,N={padding:O,boundary:M.filter(e8),altBoundary:T},{refs:k,floatingStyles:A,placement:I,isPositioned:D,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:a,elements:{reference:l,floating:i}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(o);eS(p,o)||h(o);let[m,v]=n.useState(null),[g,y]=n.useState(null),b=n.useCallback(e=>{e!==_.current&&(_.current=e,v(e))},[]),w=n.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=l||m,E=i||g,_=n.useRef(null),R=n.useRef(null),S=n.useRef(d),P=null!=u,C=ej(u),j=ej(a),O=ej(c),M=n.useCallback(()=>{if(!_.current||!R.current)return;let e={placement:t,strategy:r,middleware:p};j.current&&(e.platform=j.current),eE(_.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};T.current&&!eS(S.current,t)&&(S.current=t,e_.flushSync(()=>{f(t)}))})},[p,t,r,j,O]);eR(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let T=n.useRef(!1);eR(()=>(T.current=!0,()=>{T.current=!1}),[]),eR(()=>{if(x&&(_.current=x),E&&(R.current=E),x&&E){if(C.current)return C.current(x,E,M);M()}},[x,E,M,C,P]);let N=n.useMemo(()=>({reference:_,floating:R,setReference:b,setFloating:w}),[b,w]),k=n.useMemo(()=>({reference:x,floating:E}),[x,E]),A=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!k.floating)return e;let t=eC(k.floating,d.x),n=eC(k.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+n+"px)",...eP(k.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,s,k.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:M,refs:N,elements:k,floatingStyles:A}),[d,M,N,k,A])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>{let t=function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=el(e),h=i||u?[...p?en(p):[],...en(t)]:[];h.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let m=p&&d?function(e,t){let r,n=null,o=L(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),i();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let g=s(h),y=s(o.clientWidth-(p+m)),b=s(o.clientHeight-(h+v)),w=s(p),x={rootMargin:-g+"px "+-y+"px "+-b+"px "+-w+"px",threshold:l(0,a(1,d))||1},E=!0;function _(t){let n=t[0].intersectionRatio;if(n!==d){if(!E)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||ew(f,e.getBoundingClientRect())||u(),E=!1}try{n=new IntersectionObserver(_,{...x,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(_,x)}n.observe(e)}(!0),i}(p,r):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!f&&g.observe(p),g.observe(t));let y=f?ec(e):null;return f&&function t(){let n=ec(e);y&&!ew(y,n)&&r(),y=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{i&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...e,{animationFrame:"always"===g});return t},elements:{reference:w.anchor},middleware:[eM({mainAxis:i+j,alignmentAxis:c}),f&&eT({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eN():void 0,...N}),f&&ek({...N}),eA({...N,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:a}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${a}px`)}}),R&&eD({element:R,padding:d}),e7({arrowWidth:C,arrowHeight:j}),v&&eI({strategy:"referenceHidden",...N})]}),[W,B]=e6(I),U=(0,ez.W)(y);(0,eH.b)(()=>{D&&U?.()},[D,U]);let z=F.arrow?.x,H=F.arrow?.y,V=F.arrow?.centerOffset!==0,[$,Y]=n.useState();return(0,eH.b)(()=>{x&&Y(window.getComputedStyle(x).zIndex)},[x]),(0,eF.jsx)("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...A,transform:D?A.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(eQ,{scope:r,placedSide:W,onArrowChange:S,arrowX:z,arrowY:H,shouldHideArrow:V,children:(0,eF.jsx)(eL.WV.div,{"data-side":W,"data-align":B,...b,ref:_,style:{...b.style,animation:D?void 0:"none"}})})})});e1.displayName=eJ;var e2="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e4=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e0(e2,r),a=e5[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eW,{...n,ref:t,style:{...n.style,display:"block"}})})});function e8(e){return null!==e}e4.displayName=e2;var e7=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,a=o.arrow?.centerOffset!==0,l=a?0:e.arrowWidth,i=a?0:e.arrowHeight,[s,u]=e6(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+i/2,p="",h="";return"bottom"===s?(p=a?c:`${d}px`,h=`${-i}px`):"top"===s?(p=a?c:`${d}px`,h=`${n.floating.height+i}px`):"right"===s?(p=`${-i}px`,h=a?c:`${f}px`):"left"===s&&(p=`${n.floating.width+i}px`,h=a?c:`${f}px`),{data:{x:p,y:h}}}});function e6(e){let[t,r="center"]=e.split("-");return[t,r]}var e3=eX,e9=eZ,te=e1,tt=e4},6147:(e,t,r)=>{"use strict";r.d(t,{h:()=>s});var n=r(8726),o=r(1298),a=r(4049),l=r(6007),i=r(2395),s=n.forwardRef((e,t)=>{let{container:r,...s}=e,[u,c]=n.useState(!1);(0,l.b)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?o.createPortal((0,i.jsx)(a.WV.div,{...s,ref:t}),d):null});s.displayName="Portal"},7602:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var n=r(8726),o=r(7754),a=r(6007),l=e=>{let{present:t,children:r}=e,l=function(e){var t;let[r,o]=n.useState(),l=n.useRef(null),s=n.useRef(e),u=n.useRef("none"),c=e?"mounted":"unmounted",[d,f]=(t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,r)=>{let n=t[e][r];return n??e},c));return n.useEffect(()=>{let e=i(l.current);u.current="mounted"===d?e:"none"},[d]),(0,a.b)(()=>{let t=l.current,r=s.current,n=r!==e;if(n){let n=u.current,o=i(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.b)(()=>{if(r){let e;let t=r.ownerDocument.defaultView??window,n=n=>{let o=i(l.current),a=o.includes(n.animationName);if(n.target===r&&a&&(f("ANIMATION_END"),!s.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},o=e=>{e.target===r&&(u.current=i(l.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}f("ANIMATION_END")},[r,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),s="function"==typeof r?r({present:l.isPresent}):n.Children.only(r),u=(0,o.e)(l.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s)),c="function"==typeof r;return c||l.isPresent?n.cloneElement(s,{ref:u}):null};function i(e){return e?.animationName||"none"}l.displayName="Presence"},4049:(e,t,r)=>{"use strict";r.d(t,{WV:()=>i,jH:()=>s});var n=r(8726),o=r(1298),a=r(120),l=r(2395),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e,i=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},9746:(e,t,r)=>{"use strict";r.d(t,{Pc:()=>x,ck:()=>T,fC:()=>M});var n=r(8726),o=r(1193),a=r(7681),l=r(7754),i=r(752),s=r(7098),u=r(4049),c=r(8414),d=r(9520),f=r(7796),p=r(2395),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,b]=(0,a.B)(v),[w,x]=(0,i.b)(v,[b]),[E,_]=w(v),R=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(S,{...e,ref:t})})}));R.displayName=v;var S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:s,currentTabStopId:g,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:_=!1,...R}=e,S=n.useRef(null),P=(0,l.e)(t,S),C=(0,f.gm)(s),[j,M]=(0,d.T)({prop:g,defaultProp:b??null,onChange:w,caller:v}),[T,N]=n.useState(!1),k=(0,c.W)(x),A=y(r),I=n.useRef(!1),[D,L]=n.useState(0);return n.useEffect(()=>{let e=S.current;if(e)return e.addEventListener(h,k),()=>e.removeEventListener(h,k)},[k]),(0,p.jsx)(E,{scope:r,orientation:a,dir:C,loop:i,currentTabStopId:j,onItemFocus:n.useCallback(e=>M(e),[M]),onItemShiftTab:n.useCallback(()=>N(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(u.WV.div,{tabIndex:T||0===D?-1:0,"data-orientation":a,...R,ref:P,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=A().filter(e=>e.focusable),t=e.find(e=>e.active),r=e.find(e=>e.id===j),n=[t,r,...e].filter(Boolean),o=n.map(e=>e.ref.current);O(o,_)}}I.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>N(!1))})})}),P="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:i,children:c,...d}=e,f=(0,s.M)(),h=i||f,m=_(P,r),v=m.currentTabStopId===h,b=y(r),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:E}=m;return n.useEffect(()=>{if(a)return w(),()=>x()},[a,w,x]),(0,p.jsx)(g.ItemSlot,{scope:r,id:h,focusable:a,active:l,children:(0,p.jsx)(u.WV.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return j[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable),n=r.map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>O(n))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=E}):c})})});C.displayName=P;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function O(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var M=R,T=C},7605:(e,t,r)=>{"use strict";r.d(t,{LW:()=>X,Ns:()=>Z,bU:()=>q,fC:()=>K,l_:()=>G});var n=r(8726),o=r(4049),a=r(7602),l=r(752),i=r(7754),s=r(8414),u=r(7796),c=r(6007),d=r(6306),f=r(1193),p=r(2395),h="ScrollArea",[m,v]=(0,l.b)(h),[g,y]=m(h),b=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:a="hover",dir:l,scrollHideDelay:s=600,...c}=e,[d,f]=n.useState(null),[h,m]=n.useState(null),[v,y]=n.useState(null),[b,w]=n.useState(null),[x,E]=n.useState(null),[_,R]=n.useState(0),[S,P]=n.useState(0),[C,j]=n.useState(!1),[O,M]=n.useState(!1),T=(0,i.e)(t,e=>f(e)),N=(0,u.gm)(l);return(0,p.jsx)(g,{scope:r,type:a,dir:N,scrollHideDelay:s,scrollArea:d,viewport:h,onViewportChange:m,content:v,onContentChange:y,scrollbarX:b,onScrollbarXChange:w,scrollbarXEnabled:C,onScrollbarXEnabledChange:j,scrollbarY:x,onScrollbarYChange:E,scrollbarYEnabled:O,onScrollbarYEnabledChange:M,onCornerWidthChange:R,onCornerHeightChange:P,children:(0,p.jsx)(o.WV.div,{dir:N,...c,ref:T,style:{position:"relative","--radix-scroll-area-corner-width":_+"px","--radix-scroll-area-corner-height":S+"px",...e.style}})})});b.displayName=h;var w="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:a,nonce:l,...s}=e,u=y(w,r),c=n.useRef(null),d=(0,i.e)(t,c,u.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,p.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});x.displayName=w;var E="ScrollAreaScrollbar",_=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=y(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:i}=a,s="horizontal"===e.orientation;return n.useEffect(()=>(s?l(!0):i(!0),()=>{s?l(!1):i(!1)}),[s,l,i]),"hover"===a.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"scroll"===a.type?(0,p.jsx)(S,{...o,ref:t,forceMount:r}):"auto"===a.type?(0,p.jsx)(P,{...o,ref:t,forceMount:r}):"always"===a.type?(0,p.jsx)(C,{...o,ref:t}):null});_.displayName=E;var R=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(E,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,p.jsx)(a.z,{present:r||i,children:(0,p.jsx)(P,{"data-state":i?"visible":"hidden",...o,ref:t})})}),S=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,i=y(E,e.__scopeScrollArea),s="horizontal"===e.orientation,u=$(()=>d("SCROLL_END"),100),[c,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return n??e},"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>d("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,i.scrollHideDelay,d]),n.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t],o=r!==n;o&&(d("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,d,u]),(0,p.jsx)(a.z,{present:o||"hidden"!==c,children:(0,p.jsx)(C,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.M)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.M)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),P=n.forwardRef((e,t)=>{let r=y(E,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,c=$(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return Y(r.viewport,c),Y(r.content,c),(0,p.jsx)(a.z,{present:o||i,children:(0,p.jsx)(C,{"data-state":i?"visible":"hidden",...l,ref:t})})}),C=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,a=y(E,e.__scopeScrollArea),l=n.useRef(null),i=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=B(s.viewport,s.content),d={...o,sizes:s,onSizesChange:u,hasThumb:!!(c>0&&c<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=U(r),a=t||o/2,l=r.scrollbar.paddingStart+a,i=r.scrollbar.size-r.scrollbar.paddingEnd-(o-a),s=r.content-r.viewport,u=H([l,i],"ltr"===n?[0,s]:[-1*s,0]);return u(e)}(e,i.current,s,t)}return"horizontal"===r?(0,p.jsx)(j,{...d,ref:t,onThumbPositionChange:()=>{if(a.viewport&&l.current){let e=a.viewport.scrollLeft,t=z(e,s,a.dir);l.current.style.transform=`translate3d(${t}px, 0, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=f(e,a.dir))}}):"vertical"===r?(0,p.jsx)(O,{...d,ref:t,onThumbPositionChange:()=>{if(a.viewport&&l.current){let e=a.viewport.scrollTop,t=z(e,s);l.current.style.transform=`translate3d(0, ${t}px, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=f(e))}}):null}),j=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,l=y(E,e.__scopeScrollArea),[s,u]=n.useState(),c=n.useRef(null),d=(0,i.e)(t,c,l.onScrollbarXChange);return n.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,p.jsx)(N,{"data-orientation":"horizontal",...a,ref:d,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":U(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&l.viewport&&s&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:W(s.paddingLeft),paddingEnd:W(s.paddingRight)}})}})}),O=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,l=y(E,e.__scopeScrollArea),[s,u]=n.useState(),c=n.useRef(null),d=(0,i.e)(t,c,l.onScrollbarYChange);return n.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,p.jsx)(N,{"data-orientation":"vertical",...a,ref:d,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":U(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&l.viewport&&s&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:W(s.paddingTop),paddingEnd:W(s.paddingBottom)}})}})}),[M,T]=m(E),N=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:a,hasThumb:l,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:m,onWheelScroll:v,onResize:g,...b}=e,w=y(E,r),[x,_]=n.useState(null),R=(0,i.e)(t,e=>_(e)),S=n.useRef(null),P=n.useRef(""),C=w.viewport,j=a.content-a.viewport,O=(0,s.W)(v),T=(0,s.W)(h),N=$(g,10);function k(e){if(S.current){let t=e.clientX-S.current.left,r=e.clientY-S.current.top;m({x:t,y:r})}}return n.useEffect(()=>{let e=e=>{let t=e.target,r=x?.contains(t);r&&O(e,j)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[C,x,j,O]),n.useEffect(T,[a,T]),Y(x,N),Y(w.content,N),(0,p.jsx)(M,{scope:r,scrollbar:x,hasThumb:l,onThumbChange:(0,s.W)(u),onThumbPointerUp:(0,s.W)(c),onThumbPositionChange:T,onThumbPointerDown:(0,s.W)(d),children:(0,p.jsx)(o.WV.div,{...b,ref:R,style:{position:"absolute",...b.style},onPointerDown:(0,f.M)(e.onPointerDown,e=>{if(0===e.button){let t=e.target;t.setPointerCapture(e.pointerId),S.current=x.getBoundingClientRect(),P.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),k(e)}}),onPointerMove:(0,f.M)(e.onPointerMove,k),onPointerUp:(0,f.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=P.current,w.viewport&&(w.viewport.style.scrollBehavior=""),S.current=null})})})}),k="ScrollAreaThumb",A=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=T(k,e.__scopeScrollArea);return(0,p.jsx)(a.z,{present:r||o.hasThumb,children:(0,p.jsx)(I,{ref:t,...n})})}),I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:a,...l}=e,s=y(k,r),u=T(k,r),{onThumbPositionChange:c}=u,d=(0,i.e)(t,e=>u.onThumbChange(e)),h=n.useRef(void 0),m=$(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(m(),!h.current){let t=V(e,c);h.current=t,c()}};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,c]),(0,p.jsx)(o.WV.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,f.M)(e.onPointerDownCapture,e=>{let t=e.target,r=t.getBoundingClientRect(),n=e.clientX-r.left,o=e.clientY-r.top;u.onThumbPointerDown({x:n,y:o})}),onPointerUp:(0,f.M)(e.onPointerUp,u.onThumbPointerUp)})});A.displayName=k;var D="ScrollAreaCorner",L=n.forwardRef((e,t)=>{let r=y(D,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY),o="scroll"!==r.type&&n;return o?(0,p.jsx)(F,{...e,ref:t}):null});L.displayName=D;var F=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...a}=e,l=y(D,r),[i,s]=n.useState(0),[u,c]=n.useState(0);return Y(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),c(e)}),Y(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),s(e)}),i&&u?(0,p.jsx)(o.WV.div,{...a,ref:t,style:{width:i,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function B(e,t){let r=e/t;return isNaN(r)?0:r}function U(e){let t=B(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-r)*t;return Math.max(n,18)}function z(e,t,r="ltr"){let n=U(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-o,l=t.content-t.viewport,i="ltr"===r?[0,l]:[-1*l,0],s=(0,d.u)(e,i),u=H([0,l],[0,a-n]);return u(s)}function H(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var V=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let a={left:e.scrollLeft,top:e.scrollTop},l=r.left!==a.left,i=r.top!==a.top;(l||i)&&t(),r=a,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function $(e,t){let r=(0,s.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function Y(e,t){let r=(0,s.W)(t);(0,c.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var K=b,G=x,X=_,q=A,Z=L},120:(e,t,r)=>{"use strict";r.d(t,{A4:()=>c,Z8:()=>l,fC:()=>i,sA:()=>u});var n=r(8726),o=r(7754),a=r(2395);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,s=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n],l=/^on[A-Z]/.test(n);l?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,o.F)(t,i):i),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,i=n.Children.toArray(o),s=i.find(d);if(s){let e=s.props.children,o=i.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),s=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}var c=u("Slottable");function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},8414:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(8726);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},9520:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n,o=r(8726),a=r(6007),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.b;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,i,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),i=o.useRef(t);return l(()=>{i.current=t},[t]),o.useEffect(()=>{a.current!==r&&(i.current?.(r),a.current=r)},[r,a]),[r,n,i]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}let d=o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else i(t)},[u,e,i,s]);return[c,d]}Symbol("RADIX:SYNC_STATE")},6007:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var n=r(8726),o=globalThis?.document?n.useLayoutEffect:()=>{}},2014:(e,t,r)=>{"use strict";r.d(t,{C2:()=>l,fC:()=>s});var n=r(8726),o=r(4049),a=r(2395),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=n.forwardRef((e,t)=>(0,a.jsx)(o.WV.span,{...e,ref:t,style:{...l,...e.style}}));i.displayName="VisuallyHidden";var s=i},8838:(e,t,r)=>{"use strict";r.d(t,{q:()=>T});var n=r(8726),o=r(7253),a=r(752),l=r(8414),i=r(6007),s=r(4049),u=r(5004);function c(){return()=>{}}var d=r(2395),f="Avatar",[p,h]=(0,a.b)(f),[m,v]=p(f),g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,d.jsx)(m,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,d.jsx)(s.WV.span,{...o,ref:t})})});g.displayName=f;var y="AvatarImage",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:a=()=>{},...f}=e,p=v(y,r),h=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),a=n.useRef(null),l=o?(a.current||(a.current=new window.Image),a.current):null,[s,d]=n.useState(()=>E(l,e));return(0,i.b)(()=>{d(E(l,e))},[l,e]),(0,i.b)(()=>{let e=e=>()=>{d(e)};if(!l)return;let n=e("loaded"),o=e("error");return l.addEventListener("load",n),l.addEventListener("error",o),t&&(l.referrerPolicy=t),"string"==typeof r&&(l.crossOrigin=r),()=>{l.removeEventListener("load",n),l.removeEventListener("error",o)}},[l,r,t]),s}(o,f),m=(0,l.W)(e=>{a(e),p.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==h&&m(h)},[h,m]),"loaded"===h?(0,d.jsx)(s.WV.img,{...f,ref:t,src:o}):null});b.displayName=y;var w="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=v(w,r),[i,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),i&&"loaded"!==l.imageLoadingStatus?(0,d.jsx)(s.WV.span,{...a,ref:t}):null});function E(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var _=r(3564),R=r(3929),S=r(7946),P=r(6660);let C={..._.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],default:"3",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"soft"},...R.o3,...S.K,...P.I,fallback:{type:"ReactNode",required:!0}};var j=r(38),O=r(6959),M=r(1260);let T=n.forwardRef((e,t)=>{let{asChild:r,children:a,className:l,style:i,color:s,radius:u,...c}=(0,j.y)(e,C,M.E);return n.createElement(g,{"data-accent-color":s,"data-radius":u,className:o("rt-reset","rt-AvatarRoot",l),style:i,asChild:r},(0,O.x)({asChild:r,children:a},n.createElement(N,{ref:t,...c})))});T.displayName="Avatar";let N=n.forwardRef(({fallback:e,...t},r)=>{let[a,l]=n.useState("idle");return n.createElement(n.Fragment,null,"idle"===a||"loading"===a?n.createElement("span",{className:"rt-AvatarFallback"}):null,"error"===a?n.createElement(x,{className:o("rt-AvatarFallback",{"rt-one-letter":"string"==typeof e&&1===e.length,"rt-two-letters":"string"==typeof e&&2===e.length}),delayMs:0},e):null,n.createElement(b,{ref:r,className:"rt-AvatarImage",...t,onLoadingStatusChange:e=>{t.onLoadingStatusChange?.(e),l(e)}}))});N.displayName="AvatarImpl"},4202:(e,t,r)=>{"use strict";r.d(t,{C:()=>p});var n=r(8726),o=r(7253),a=r(120),l=r(3564),i=r(3929),s=r(7946),u=r(6660);let c={...l.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"1",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft","surface","outline"],default:"soft"},...i.o3,...s.K,...u.I};var d=r(38),f=r(1260);let p=n.forwardRef((e,t)=>{let{asChild:r,className:l,color:i,radius:s,...u}=(0,d.y)(e,c,f.E),p=r?a.fC:"span";return n.createElement(p,{"data-accent-color":i,"data-radius":s,...u,ref:t,className:o("rt-reset","rt-Badge",l)})});p.displayName="Badge"},3282:(e,t,r)=>{"use strict";r.d(t,{z:()=>w});var n=r(8726),o=r(7253),a=r(120),l=r(3564),i=r(3929),s=r(7946),u=r(6660);let c={...l.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["classic","solid","soft","surface","outline","ghost"],default:"solid"},...i.o3,...s.K,...u.I,loading:{type:"boolean",className:"rt-loading",default:!1}};var d=r(6812);let f={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0},loading:{type:"boolean",default:!0}};var p=r(38),h=r(1260);let m=n.forwardRef((e,t)=>{let{className:r,children:a,loading:l,...i}=(0,p.y)(e,f,h.E);if(!l)return a;let s=n.createElement("span",{...i,ref:t,className:o("rt-Spinner",r)},n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}),n.createElement("span",{className:"rt-SpinnerLeaf"}));return void 0===a?s:n.createElement(d.k,{asChild:!0,position:"relative",align:"center",justify:"center"},n.createElement("span",null,n.createElement("span",{"aria-hidden":!0,style:{display:"contents",visibility:"hidden"},inert:void 0},a),n.createElement(d.k,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},n.createElement("span",null,s))))});m.displayName="Spinner";var v=r(2014);let g=v.fC;v.fC;var y=r(7507);let b=n.forwardRef((e,t)=>{let{size:r=c.size.default}=e,{className:l,children:i,asChild:s,color:u,radius:f,disabled:v=e.loading,...b}=(0,p.y)(e,c,h.E),w=s?a.fC:"button";return n.createElement(w,{"data-disabled":v||void 0,"data-accent-color":u,"data-radius":f,...b,ref:t,className:o("rt-reset","rt-BaseButton",l),disabled:v},e.loading?n.createElement(n.Fragment,null,n.createElement("span",{style:{display:"contents",visibility:"hidden"},"aria-hidden":!0},i),n.createElement(g,null,i),n.createElement(d.k,{asChild:!0,align:"center",justify:"center",position:"absolute",inset:"0"},n.createElement("span",null,n.createElement(m,{size:(0,y.qz)(r,y.AG)})))):i)});b.displayName="BaseButton";let w=n.forwardRef(({className:e,...t},r)=>n.createElement(b,{...t,ref:r,className:o("rt-Button",e)}));w.displayName="Button"},5539:(e,t,r)=>{"use strict";r.d(t,{x8:()=>ep,VY:()=>ec,dk:()=>ef,fC:()=>es,Dx:()=>ed,xz:()=>eu});var n=r(8726),o=r(7253),a=r(1193),l=r(7754),i=r(752),s=r(7098),u=r(9520),c=r(6387),d=r(510),f=r(6147),p=r(7602),h=r(4049),m=r(9232),v=r(4350),g=r(2690),y=r(120),b=r(2395),w="Dialog",[x,E]=(0,i.b)(w),[_,R]=x(w),S=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:i=!0}=e,c=n.useRef(null),d=n.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:a??!1,onChange:l,caller:w});return(0,b.jsx)(_,{scope:t,triggerRef:c,contentRef:d,contentId:(0,s.M)(),titleId:(0,s.M)(),descriptionId:(0,s.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:i,children:r})};S.displayName=w;var P="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(P,r),i=(0,l.e)(t,o.triggerRef);return(0,b.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":K(o.open),...n,ref:i,onClick:(0,a.M)(e.onClick,o.onOpenToggle)})});C.displayName=P;var j="DialogPortal",[O,M]=x(j,{forceMount:void 0}),T=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=R(j,t);return(0,b.jsx)(O,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,b.jsx)(p.z,{present:r||l.open,children:(0,b.jsx)(f.h,{asChild:!0,container:a,children:e})}))})};T.displayName=j;var N="DialogOverlay",k=n.forwardRef((e,t)=>{let r=M(N,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(N,e.__scopeDialog);return a.modal?(0,b.jsx)(p.z,{present:n||a.open,children:(0,b.jsx)(I,{...o,ref:t})}):null});k.displayName=N;var A=(0,y.Z8)("DialogOverlay.RemoveScroll"),I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(N,r);return(0,b.jsx)(v.f,{as:A,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(h.WV.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),D="DialogContent",L=n.forwardRef((e,t)=>{let r=M(D,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(D,e.__scopeDialog);return(0,b.jsx)(p.z,{present:n||a.open,children:a.modal?(0,b.jsx)(F,{...o,ref:t}):(0,b.jsx)(W,{...o,ref:t})})});L.displayName=D;var F=n.forwardRef((e,t)=>{let r=R(D,e.__scopeDialog),o=n.useRef(null),i=(0,l.e)(t,r.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,g.Ry)(e)},[]),(0,b.jsx)(B,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;n&&e.preventDefault()}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault())})}),W=n.forwardRef((e,t)=>{let r=R(D,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,b.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target,l=r.triggerRef.current?.contains(n);l&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,u=R(D,r),f=n.useRef(null),p=(0,l.e)(t,f);return(0,m.EW)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(d.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,b.jsx)(c.XB,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":K(u.open),...s,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(Z,{titleId:u.titleId}),(0,b.jsx)(J,{contentRef:f,descriptionId:u.descriptionId})]})]})}),U="DialogTitle",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(U,r);return(0,b.jsx)(h.WV.h2,{id:o.titleId,...n,ref:t})});z.displayName=U;var H="DialogDescription",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(H,r);return(0,b.jsx)(h.WV.p,{id:o.descriptionId,...n,ref:t})});V.displayName=H;var $="DialogClose",Y=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R($,r);return(0,b.jsx)(h.WV.button,{type:"button",...n,ref:t,onClick:(0,a.M)(e.onClick,()=>o.onOpenChange(!1))})});function K(e){return e?"open":"closed"}Y.displayName=$;var G="DialogTitleWarning",[X,q]=(0,i.k)(G,{contentName:D,titleName:U,docsSlug:"dialog"}),Z=({titleId:e})=>{let t=q(G),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{if(e){let t=document.getElementById(e);t||console.error(r)}},[r,e]),null},J=({contentRef:e,descriptionId:t})=>{let r=q("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");if(t&&r){let e=document.getElementById(t);e||console.warn(o)}},[o,e,t]),null},Q=r(3564),ee=r(5693),et=r(6350);let er={...Q.C,align:{type:"enum",className:"rt-r-align",values:["start","center"],default:"center"},size:{type:"enum",className:"rt-r-size",values:["1","2","3","4"],default:"3",responsive:!0},width:ee.n.width,minWidth:ee.n.minWidth,maxWidth:{...ee.n.maxWidth,default:"600px"},...et.F};var en=r(3558),eo=r(2602),ea=r(2426),el=r(38),ei=r(8197);let es=e=>n.createElement(S,{...e,modal:!0});es.displayName="Dialog.Root";let eu=n.forwardRef(({children:e,...t},r)=>n.createElement(C,{...t,ref:r,asChild:!0},(0,ei.O)(e)));eu.displayName="Dialog.Trigger";let ec=n.forwardRef(({align:e,...t},r)=>{let{align:a,...l}=er,{className:i}=(0,el.y)({align:e},{align:a}),{className:s,forceMount:u,container:c,...d}=(0,el.y)(t,l);return n.createElement(T,{container:c,forceMount:u},n.createElement(ea.Q2,{asChild:!0},n.createElement(k,{className:"rt-BaseDialogOverlay rt-DialogOverlay"},n.createElement("div",{className:"rt-BaseDialogScroll rt-DialogScroll"},n.createElement("div",{className:`rt-BaseDialogScrollPadding rt-DialogScrollPadding ${i}`},n.createElement(L,{...d,ref:r,className:o("rt-BaseDialogContent","rt-DialogContent",s)}))))))});ec.displayName="Dialog.Content";let ed=n.forwardRef((e,t)=>n.createElement(z,{asChild:!0},n.createElement(en.X,{size:"5",mb:"3",trim:"start",...e,asChild:!1,ref:t})));ed.displayName="Dialog.Title";let ef=n.forwardRef((e,t)=>n.createElement(V,{asChild:!0},n.createElement(eo.x,{as:"p",size:"3",...e,asChild:!1,ref:t})));ef.displayName="Dialog.Description";let ep=n.forwardRef(({children:e,...t},r)=>n.createElement(Y,{...t,ref:r,asChild:!0},(0,ei.O)(e)));ep.displayName="Dialog.Close"},3840:(e,t,r)=>{"use strict";r.d(t,{VY:()=>tm,ck:()=>tg,fC:()=>tf,Z0:()=>tS,Tr:()=>tE,tu:()=>tR,fF:()=>t_,xz:()=>tp});var n=r(8726),o=r(7253),a=r(1193),l=r(7754),i=r(752),s=r(9520),u=r(4049),c=r(7681),d=r(7796),f=r(6387),p=r(9232),h=r(510),m=r(7098),v=r(4959),g=r(6147),y=r(7602),b=r(9746),w=r(120),x=r(8414),E=r(2690),_=r(4350),R=r(2395),S=["Enter"," "],P=["ArrowUp","PageDown","End"],C=["ArrowDown","PageUp","Home",...P],j={ltr:[...S,"ArrowRight"],rtl:[...S,"ArrowLeft"]},O={ltr:["ArrowLeft"],rtl:["ArrowRight"]},M="Menu",[T,N,k]=(0,c.B)(M),[A,I]=(0,i.b)(M,[k,v.D7,b.Pc]),D=(0,v.D7)(),L=(0,b.Pc)(),[F,W]=A(M),[B,U]=A(M),z=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:l,modal:i=!0}=e,s=D(t),[u,c]=n.useState(null),f=n.useRef(!1),p=(0,x.W)(l),h=(0,d.gm)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,R.jsx)(v.fC,{...s,children:(0,R.jsx)(F,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:c,children:(0,R.jsx)(B,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:i,children:o})})})};z.displayName=M;var H=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=D(r);return(0,R.jsx)(v.ee,{...o,...n,ref:t})});H.displayName="MenuAnchor";var V="MenuPortal",[$,Y]=A(V,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=W(V,t);return(0,R.jsx)($,{scope:t,forceMount:r,children:(0,R.jsx)(y.z,{present:r||a.open,children:(0,R.jsx)(g.h,{asChild:!0,container:o,children:n})})})};K.displayName=V;var G="MenuContent",[X,q]=A(G),Z=n.forwardRef((e,t)=>{let r=Y(G,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=W(G,e.__scopeMenu),l=U(G,e.__scopeMenu);return(0,R.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(y.z,{present:n||a.open,children:(0,R.jsx)(T.Slot,{scope:e.__scopeMenu,children:l.modal?(0,R.jsx)(J,{...o,ref:t}):(0,R.jsx)(Q,{...o,ref:t})})})})}),J=n.forwardRef((e,t)=>{let r=W(G,e.__scopeMenu),o=n.useRef(null),i=(0,l.e)(t,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,E.Ry)(e)},[]),(0,R.jsx)(et,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let r=W(G,e.__scopeMenu);return(0,R.jsx)(et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ee=(0,w.Z8)("MenuContent.ScrollLock"),et=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:i,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,disableOutsideScroll:E,...S}=e,j=W(G,r),O=U(G,r),M=D(r),T=L(r),k=N(r),[A,I]=n.useState(null),F=n.useRef(null),B=(0,l.e)(t,F,j.onContentChange),z=n.useRef(0),H=n.useRef(""),V=n.useRef(0),$=n.useRef(null),Y=n.useRef("right"),K=n.useRef(0),q=E?_.f:n.Fragment,Z=E?{as:ee,allowPinchZoom:!0}:void 0,J=e=>{let t=H.current+e,r=k().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=r.map(e=>e.textValue),l=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0]),a=o?t[0]:t,l=r?e.indexOf(r):-1,i=(n=Math.max(l,0),e.map((t,r)=>e[(n+r)%e.length])),s=1===a.length;s&&(i=i.filter(e=>e!==r));let u=i.find(e=>e.toLowerCase().startsWith(a.toLowerCase()));return u!==r?u:void 0}(a,t,o),i=r.find(e=>e.textValue===l)?.ref.current;(function e(t){H.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))})(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,p.EW)();let Q=n.useCallback(e=>{let t=Y.current===$.current?.side;return t&&function(e,t){if(!t)return!1;let r={x:e.clientX,y:e.clientY};return function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],s=l.x,u=l.y,c=i.x,d=i.y,f=u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s;f&&(o=!o)}return o}(r,t)}(e,$.current?.area)},[]);return(0,R.jsx)(X,{scope:r,searchRef:H,onItemEnter:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:n.useCallback(e=>{Q(e)||(F.current?.focus(),I(null))},[Q]),onTriggerLeave:n.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:V,onPointerGraceIntentChange:n.useCallback(e=>{$.current=e},[]),children:(0,R.jsx)(q,{...Z,children:(0,R.jsx)(h.M,{asChild:!0,trapped:i,onMountAutoFocus:(0,a.M)(s,e=>{e.preventDefault(),F.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,R.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:x,children:(0,R.jsx)(b.fC,{asChild:!0,...T,dir:O.dir,orientation:"vertical",loop:o,currentTabStopId:A,onCurrentTabStopIdChange:I,onEntryFocus:(0,a.M)(d,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(v.VY,{role:"menu","aria-orientation":"vertical","data-state":eO(j.open),"data-radix-menu-content":"",dir:O.dir,...M,...S,ref:B,style:{outline:"none",...S.style},onKeyDown:(0,a.M)(S.onKeyDown,e=>{let t=e.target,r=t.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,o=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&o&&J(e.key));let a=F.current;if(e.target!==a||!C.includes(e.key))return;e.preventDefault();let l=k().filter(e=>!e.disabled),i=l.map(e=>e.ref.current);P.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,a.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),H.current="")}),onPointerMove:(0,a.M)(e.onPointerMove,eN(e=>{let t=e.target,r=K.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>K.current?"right":"left";Y.current=t,K.current=e.clientX}}))})})})})})})});Z.displayName=G;var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,R.jsx)(u.WV.div,{role:"group",...n,ref:t})});er.displayName="MenuGroup";var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,R.jsx)(u.WV.div,{...n,ref:t})});en.displayName="MenuLabel";var eo="MenuItem",ea="menu.itemSelect",el=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:o,...i}=e,s=n.useRef(null),c=U(eo,e.__scopeMenu),d=q(eo,e.__scopeMenu),f=(0,l.e)(t,s),p=n.useRef(!1);return(0,R.jsx)(ei,{...i,ref:f,disabled:r,onClick:(0,a.M)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(ea,{bubbles:!0,cancelable:!0});e.addEventListener(ea,e=>o?.(e),{once:!0}),(0,u.jH)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,a.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!r&&(!t||" "!==e.key)&&S.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});el.displayName=eo;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:o=!1,textValue:i,...s}=e,c=q(eo,r),d=L(r),f=n.useRef(null),p=(0,l.e)(t,f),[h,m]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[s.children]),(0,R.jsx)(T.ItemSlot,{scope:r,disabled:o,textValue:i??v,children:(0,R.jsx)(b.ck,{asChild:!0,...d,focusable:!o,children:(0,R.jsx)(u.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:p,onPointerMove:(0,a.M)(e.onPointerMove,eN(e=>{if(o)c.onItemLeave(e);else if(c.onItemEnter(e),!e.defaultPrevented){let t=e.currentTarget;t.focus({preventScroll:!0})}})),onPointerLeave:(0,a.M)(e.onPointerLeave,eN(e=>c.onItemLeave(e))),onFocus:(0,a.M)(e.onFocus,()=>m(!0)),onBlur:(0,a.M)(e.onBlur,()=>m(!1))})})})}),es=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...o}=e;return(0,R.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,R.jsx)(el,{role:"menuitemcheckbox","aria-checked":eM(r)?"mixed":r,...o,ref:t,"data-state":eT(r),onSelect:(0,a.M)(o.onSelect,()=>n?.(!!eM(r)||!r),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,ed]=A(eu,{value:void 0,onValueChange:()=>{}}),ef=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,x.W)(n);return(0,R.jsx)(ec,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,R.jsx)(er,{...o,ref:t})})});ef.displayName=eu;var ep="MenuRadioItem",eh=n.forwardRef((e,t)=>{let{value:r,...n}=e,o=ed(ep,e.__scopeMenu),l=r===o.value;return(0,R.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,R.jsx)(el,{role:"menuitemradio","aria-checked":l,...n,ref:t,"data-state":eT(l),onSelect:(0,a.M)(n.onSelect,()=>o.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eh.displayName=ep;var em="MenuItemIndicator",[ev,eg]=A(em,{checked:!1}),ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eg(em,r);return(0,R.jsx)(y.z,{present:n||eM(a.checked)||!0===a.checked,children:(0,R.jsx)(u.WV.span,{...o,ref:t,"data-state":eT(a.checked)})})});ey.displayName=em;var eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,R.jsx)(u.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eb.displayName="MenuSeparator";var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=D(r);return(0,R.jsx)(v.Eh,{...o,...n,ref:t})});ew.displayName="MenuArrow";var ex="MenuSub",[eE,e_]=A(ex),eR=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,l=W(ex,t),i=D(t),[s,u]=n.useState(null),[c,d]=n.useState(null),f=(0,x.W)(a);return n.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,R.jsx)(v.fC,{...i,children:(0,R.jsx)(F,{scope:t,open:o,onOpenChange:f,content:c,onContentChange:d,children:(0,R.jsx)(eE,{scope:t,contentId:(0,m.M)(),triggerId:(0,m.M)(),trigger:s,onTriggerChange:u,children:r})})})};eR.displayName=ex;var eS="MenuSubTrigger",eP=n.forwardRef((e,t)=>{let r=W(eS,e.__scopeMenu),o=U(eS,e.__scopeMenu),i=e_(eS,e.__scopeMenu),s=q(eS,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,f={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,R.jsx)(H,{asChild:!0,...f,children:(0,R.jsx)(ei,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":i.contentId,"data-state":eO(r.open),...e,ref:(0,l.F)(t,i.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,a.M)(e.onPointerMove,eN(t=>{s.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,a.M)(e.onPointerLeave,eN(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],l=t[o?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:l,y:t.top},{x:l,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,a.M)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&j[o.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eP.displayName=eS;var eC="MenuSubContent",ej=n.forwardRef((e,t)=>{let r=Y(G,e.__scopeMenu),{forceMount:o=r.forceMount,...i}=e,s=W(G,e.__scopeMenu),u=U(G,e.__scopeMenu),c=e_(eC,e.__scopeMenu),d=n.useRef(null),f=(0,l.e)(t,d);return(0,R.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(y.z,{present:o||s.open,children:(0,R.jsx)(T.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(et,{id:c.contentId,"aria-labelledby":c.triggerId,...i,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,a.M)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=O[u.dir].includes(e.key);t&&r&&(s.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eO(e){return e?"open":"closed"}function eM(e){return"indeterminate"===e}function eT(e){return eM(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return t=>"mouse"===t.pointerType?e(t):void 0}ej.displayName=eC;var ek="DropdownMenu",[eA,eI]=(0,i.b)(ek,[I]),eD=I(),[eL,eF]=eA(ek),eW=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,c=eD(t),d=n.useRef(null),[f,p]=(0,s.T)({prop:a,defaultProp:l??!1,onChange:i,caller:ek});return(0,R.jsx)(eL,{scope:t,triggerId:(0,m.M)(),triggerRef:d,contentId:(0,m.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,R.jsx)(z,{...c,open:f,onOpenChange:p,dir:o,modal:u,children:r})})};eW.displayName=ek;var eB="DropdownMenuTrigger",eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,i=eF(eB,r),s=eD(r);return(0,R.jsx)(H,{asChild:!0,...s,children:(0,R.jsx)(u.WV.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,l.F)(t,i.triggerRef),onPointerDown:(0,a.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=eB;var ez=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eD(t);return(0,R.jsx)(K,{...n,...r})};ez.displayName="DropdownMenuPortal";var eH="DropdownMenuContent",eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,l=eF(eH,r),i=eD(r),s=n.useRef(!1);return(0,R.jsx)(Z,{id:l.contentId,"aria-labelledby":l.triggerId,...i,...o,ref:t,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{s.current||l.triggerRef.current?.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,a.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!l.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eH;var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(er,{...o,...n,ref:t})});e$.displayName="DropdownMenuGroup";var eY=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(en,{...o,...n,ref:t})});eY.displayName="DropdownMenuLabel";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(el,{...o,...n,ref:t})});eK.displayName="DropdownMenuItem";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(es,{...o,...n,ref:t})});eG.displayName="DropdownMenuCheckboxItem";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ef,{...o,...n,ref:t})});eX.displayName="DropdownMenuRadioGroup";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(eh,{...o,...n,ref:t})});eq.displayName="DropdownMenuRadioItem";var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ey,{...o,...n,ref:t})});eZ.displayName="DropdownMenuItemIndicator";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(eb,{...o,...n,ref:t})});eJ.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ew,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(eP,{...o,...n,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var e0=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eD(r);return(0,R.jsx)(ej,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e0.displayName="DropdownMenuSubContent";var e1=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,l=eD(t),[i,u]=(0,s.T)({prop:n,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,R.jsx)(eR,{...l,open:i,onOpenChange:u,children:r})},e2=r(7605),e5=r(3564),e4=r(6660);let e8={...e5.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"1",responsive:!0},...e4.I,scrollbars:{type:"enum",values:["vertical","horizontal","both"],default:"both"}};var e7=r(9445),e6=r(5392),e3=r(1260);let e9=e3.E.m.values;var te=r(6959);let tt=n.forwardRef((e,t)=>{let{rest:r,...a}=function(e){let{m:t,mx:r,my:n,mt:o,mr:a,mb:l,ml:i,...s}=e;return{m:t,mx:r,my:n,mt:o,mr:a,mb:l,ml:i,rest:s}}(e),[l,i]=function(e){let[t,r]=(0,e7.uq)({className:"rt-r-m",customProperties:["--margin"],propValues:e9,value:e.m}),[n,a]=(0,e7.uq)({className:"rt-r-mx",customProperties:["--margin-left","--margin-right"],propValues:e9,value:e.mx}),[l,i]=(0,e7.uq)({className:"rt-r-my",customProperties:["--margin-top","--margin-bottom"],propValues:e9,value:e.my}),[s,u]=(0,e7.uq)({className:"rt-r-mt",customProperties:["--margin-top"],propValues:e9,value:e.mt}),[c,d]=(0,e7.uq)({className:"rt-r-mr",customProperties:["--margin-right"],propValues:e9,value:e.mr}),[f,p]=(0,e7.uq)({className:"rt-r-mb",customProperties:["--margin-bottom"],propValues:e9,value:e.mb}),[h,m]=(0,e7.uq)({className:"rt-r-ml",customProperties:["--margin-left"],propValues:e9,value:e.ml});return[o(t,n,l,s,c,f,h),(0,e6.y)(r,a,i,u,d,p,m)]}(a),{asChild:s,children:u,className:c,style:d,type:f,scrollHideDelay:p="scroll"!==f?0:void 0,dir:h,size:m=e8.size.default,radius:v=e8.radius.default,scrollbars:g=e8.scrollbars.default,...y}=r;return n.createElement(e2.fC,{type:f,scrollHideDelay:p,className:o("rt-ScrollAreaRoot",l,c),style:(0,e6.y)(i,d),asChild:s},(0,te.x)({asChild:s,children:u},e=>n.createElement(n.Fragment,null,n.createElement(e2.l_,{...y,ref:t,className:"rt-ScrollAreaViewport"},e),n.createElement("div",{className:"rt-ScrollAreaViewportFocusRing"}),"vertical"!==g?n.createElement(e2.LW,{"data-radius":v,orientation:"horizontal",className:o("rt-ScrollAreaScrollbar",(0,e7.RE)({className:"rt-r-size",value:m,propValues:e8.size.values}))},n.createElement(e2.bU,{className:"rt-ScrollAreaThumb"})):null,"horizontal"!==g?n.createElement(e2.LW,{"data-radius":v,orientation:"vertical",className:o("rt-ScrollAreaScrollbar",(0,e7.RE)({className:"rt-r-size",value:m,propValues:e8.size.values}))},n.createElement(e2.bU,{className:"rt-ScrollAreaThumb"})):null,"both"===g?n.createElement(e2.Ns,{className:"rt-ScrollAreaCorner"}):null)))});tt.displayName="ScrollArea";var tr=r(3929),tn=r(7946);let to={size:{type:"enum",className:"rt-r-size",values:["1","2"],default:"2",responsive:!0},variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"solid"},...tr.EG,...tn.K},ta={...e5.C,...tr.EG,shortcut:{type:"string"}},tl={...tr.EG,shortcut:{type:"string"}},ti={...tr.EG};var ts=r(2426),tu=r(4634),tc=r(38),td=r(8197);let tf=e=>n.createElement(eW,{...e});tf.displayName="DropdownMenu.Root";let tp=n.forwardRef(({children:e,...t},r)=>n.createElement(eU,{...t,ref:r,asChild:!0},(0,td.O)(e)));tp.displayName="DropdownMenu.Trigger";let th=n.createContext({}),tm=n.forwardRef((e,t)=>{let r=(0,ts.TC)(),{size:a=to.size.default,variant:l=to.variant.default,highContrast:i=to.highContrast.default}=e,{className:s,children:u,color:c,container:d,forceMount:f,...p}=(0,tc.y)(e,to),h=c||r.accentColor;return n.createElement(ez,{container:d,forceMount:f},n.createElement(ts.Q2,{asChild:!0},n.createElement(eV,{"data-accent-color":h,align:"start",sideOffset:4,collisionPadding:10,...p,asChild:!1,ref:t,className:o("rt-PopperContent","rt-BaseMenuContent","rt-DropdownMenuContent",s)},n.createElement(tt,{type:"auto"},n.createElement("div",{className:o("rt-BaseMenuViewport","rt-DropdownMenuViewport")},n.createElement(th.Provider,{value:n.useMemo(()=>({size:a,variant:l,color:h,highContrast:i}),[a,l,h,i])},u))))))});tm.displayName="DropdownMenu.Content";let tv=n.forwardRef(({className:e,...t},r)=>n.createElement(eY,{...t,asChild:!1,ref:r,className:o("rt-BaseMenuLabel","rt-DropdownMenuLabel",e)}));tv.displayName="DropdownMenu.Label";let tg=n.forwardRef((e,t)=>{let{className:r,children:a,color:l=ta.color.default,shortcut:i,...s}=e;return n.createElement(eK,{"data-accent-color":l,...s,ref:t,className:o("rt-reset","rt-BaseMenuItem","rt-DropdownMenuItem",r)},n.createElement(w.A4,null,a),i&&n.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},i))});tg.displayName="DropdownMenu.Item";let ty=n.forwardRef(({className:e,...t},r)=>n.createElement(e$,{...t,asChild:!1,ref:r,className:o("rt-BaseMenuGroup","rt-DropdownMenuGroup",e)}));ty.displayName="DropdownMenu.Group";let tb=n.forwardRef(({className:e,...t},r)=>n.createElement(eX,{...t,asChild:!1,ref:r,className:o("rt-BaseMenuRadioGroup","rt-DropdownMenuRadioGroup",e)}));tb.displayName="DropdownMenu.RadioGroup";let tw=n.forwardRef((e,t)=>{let{children:r,className:a,color:l=ti.color.default,...i}=e;return n.createElement(eq,{...i,asChild:!1,ref:t,"data-accent-color":l,className:o("rt-BaseMenuItem","rt-BaseMenuRadioItem","rt-DropdownMenuItem","rt-DropdownMenuRadioItem",a)},r,n.createElement(eZ,{className:"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator"},n.createElement(tu.dc,{className:"rt-BaseMenuItemIndicatorIcon rt-DropdownMenuItemIndicatorIcon"})))});tw.displayName="DropdownMenu.RadioItem";let tx=n.forwardRef((e,t)=>{let{children:r,className:a,shortcut:l,color:i=tl.color.default,...s}=e;return n.createElement(eG,{...s,asChild:!1,ref:t,"data-accent-color":i,className:o("rt-BaseMenuItem","rt-BaseMenuCheckboxItem","rt-DropdownMenuItem","rt-DropdownMenuCheckboxItem",a)},r,n.createElement(eZ,{className:"rt-BaseMenuItemIndicator rt-DropdownMenuItemIndicator"},n.createElement(tu.dc,{className:"rt-BaseMenuItemIndicatorIcon rt-ContextMenuItemIndicatorIcon"})),l&&n.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},l))});tx.displayName="DropdownMenu.CheckboxItem";let tE=e=>n.createElement(e1,{...e});tE.displayName="DropdownMenu.Sub";let t_=n.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return n.createElement(eQ,{...l,asChild:!1,ref:t,className:o("rt-BaseMenuItem","rt-BaseMenuSubTrigger","rt-DropdownMenuItem","rt-DropdownMenuSubTrigger",r)},a,n.createElement("div",{className:"rt-BaseMenuShortcut rt-DropdownMenuShortcut"},n.createElement(tu.OW,{className:"rt-BaseMenuSubTriggerIcon rt-DropdownMenuSubtriggerIcon"})))});t_.displayName="DropdownMenu.SubTrigger";let tR=n.forwardRef((e,t)=>{let{size:r,variant:a,color:l,highContrast:i}=n.useContext(th),{className:s,children:u,container:c,forceMount:d,...f}=(0,tc.y)({size:r,variant:a,color:l,highContrast:i,...e},to);return n.createElement(ez,{container:c,forceMount:d},n.createElement(ts.Q2,{asChild:!0},n.createElement(e0,{"data-accent-color":l,alignOffset:-(4*Number(r)),sideOffset:1,collisionPadding:10,...f,asChild:!1,ref:t,className:o("rt-PopperContent","rt-BaseMenuContent","rt-BaseMenuSubContent","rt-DropdownMenuContent","rt-DropdownMenuSubContent",s)},n.createElement(tt,{type:"auto"},n.createElement("div",{className:o("rt-BaseMenuViewport","rt-DropdownMenuViewport")},u)))))});tR.displayName="DropdownMenu.SubContent";let tS=n.forwardRef(({className:e,...t},r)=>n.createElement(eJ,{...t,asChild:!1,ref:r,className:o("rt-BaseMenuSeparator","rt-DropdownMenuSeparator",e)}));tS.displayName="DropdownMenu.Separator"},6812:(e,t,r)=>{"use strict";r.d(t,{k:()=>c});var n=r(8726),o=r(7253),a=r(38),l=r(8683),i=r(1260),s=r(5351),u=r(2808);let c=n.forwardRef((e,t)=>{let{className:r,asChild:c,as:d="div",...f}=(0,a.y)(e,u.l,l.P,i.E);return n.createElement(c?s.g7:d,{...f,ref:t,className:o("rt-Flex",r)})});c.displayName="Flex"},2808:(e,t,r)=>{"use strict";r.d(t,{l:()=>a});var n=r(3564),o=r(666);let a={as:{type:"enum",values:["div","span"],default:"div"},...n.C,display:{type:"enum",className:"rt-r-display",values:["none","inline-flex","flex"],responsive:!0},direction:{type:"enum",className:"rt-r-fd",values:["row","column","row-reverse","column-reverse"],responsive:!0},align:{type:"enum",className:"rt-r-ai",values:["start","center","end","baseline","stretch"],responsive:!0},justify:{type:"enum",className:"rt-r-jc",values:["start","center","end","between"],parseValue:function(e){return"between"===e?"space-between":e},responsive:!0},wrap:{type:"enum",className:"rt-r-fw",values:["nowrap","wrap","wrap-reverse"],responsive:!0},...o.c}},3558:(e,t,r)=>{"use strict";r.d(t,{X:()=>g});var n=r(8726),o=r(7253),a=r(120),l=r(3564),i=r(3929),s=r(7946),u=r(8413),c=r(7756),d=r(5138),f=r(4108),p=r(6198);let h={as:{type:"enum",values:["h1","h2","h3","h4","h5","h6"],default:"h1"},...l.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],default:"6",responsive:!0},...p.x,...c.O,...u.E,...f.w,...d.u,...i.EG,...s.K};var m=r(38),v=r(1260);let g=n.forwardRef((e,t)=>{let{children:r,className:l,asChild:i,as:s="h1",color:u,...c}=(0,m.y)(e,h,v.E);return n.createElement(a.fC,{"data-accent-color":u,...c,ref:t,className:o("rt-Heading",l)},i?r:n.createElement(s,null,r))});g.displayName="Heading"},4634:(e,t,r)=>{"use strict";r.d(t,{OW:()=>i,dc:()=>a,v4:()=>l});var n=r(8726);let o=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.75 4.5C0.75 4.08579 1.08579 3.75 1.5 3.75H7.5C7.91421 3.75 8.25 4.08579 8.25 4.5C8.25 4.91421 7.91421 5.25 7.5 5.25H1.5C1.08579 5.25 0.75 4.91421 0.75 4.5Z"})));o.displayName="ThickDividerHorizontalIcon";let a=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.53547 0.62293C8.88226 0.849446 8.97976 1.3142 8.75325 1.66099L4.5083 8.1599C4.38833 8.34356 4.19397 8.4655 3.9764 8.49358C3.75883 8.52167 3.53987 8.45309 3.3772 8.30591L0.616113 5.80777C0.308959 5.52987 0.285246 5.05559 0.563148 4.74844C0.84105 4.44128 1.31533 4.41757 1.62249 4.69547L3.73256 6.60459L7.49741 0.840706C7.72393 0.493916 8.18868 0.396414 8.53547 0.62293Z"})));a.displayName="ThickCheckIcon";let l=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{d:"M0.135232 3.15803C0.324102 2.95657 0.640521 2.94637 0.841971 3.13523L4.5 6.56464L8.158 3.13523C8.3595 2.94637 8.6759 2.95657 8.8648 3.15803C9.0536 3.35949 9.0434 3.67591 8.842 3.86477L4.84197 7.6148C4.64964 7.7951 4.35036 7.7951 4.15803 7.6148L0.158031 3.86477C-0.0434285 3.67591 -0.0536285 3.35949 0.135232 3.15803Z"})));l.displayName="ChevronDownIcon";let i=n.forwardRef((e,t)=>n.createElement("svg",{width:"9",height:"9",viewBox:"0 0 9 9",fill:"currentcolor",xmlns:"http://www.w3.org/2000/svg",...e,ref:t},n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.23826 0.201711C3.54108 -0.0809141 4.01567 -0.0645489 4.29829 0.238264L7.79829 3.98826C8.06724 4.27642 8.06724 4.72359 7.79829 5.01174L4.29829 8.76174C4.01567 9.06455 3.54108 9.08092 3.23826 8.79829C2.93545 8.51567 2.91909 8.04108 3.20171 7.73826L6.22409 4.5L3.20171 1.26174C2.91909 0.958928 2.93545 0.484337 3.23826 0.201711Z"})));i.displayName="ThickChevronRightIcon"},5353:(e,t,r)=>{"use strict";r.d(t,{VY:()=>ez,ck:()=>eH,fC:()=>eB,xz:()=>eU});var n=r(8726),o=r(7253),a=r(1298),l=r(6306),i=r(1193),s=r(7681),u=r(7754),c=r(752),d=r(7796),f=r(6387),p=r(9232),h=r(510),m=r(7098),v=r(4959),g=r(6147),y=r(4049),b=r(120),w=r(8414),x=r(9520),E=r(6007),_=r(2014),R=r(2690),S=r(4350),P=r(2395),C=[" ","Enter","ArrowUp","ArrowDown"],j=[" ","Enter"],O="Select",[M,T,N]=(0,s.B)(O),[k,A]=(0,c.b)(O,[N,v.D7]),I=(0,v.D7)(),[D,L]=k(O),[F,W]=k(O),B=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:a,onOpenChange:l,value:i,defaultValue:s,onValueChange:u,dir:c,name:f,autoComplete:p,disabled:h,required:g,form:y}=e,b=I(t),[w,E]=n.useState(null),[_,R]=n.useState(null),[S,C]=n.useState(!1),j=(0,d.gm)(c),[T,N]=(0,x.T)({prop:o,defaultProp:a??!1,onChange:l,caller:O}),[k,A]=(0,x.T)({prop:i,defaultProp:s,onChange:u,caller:O}),L=n.useRef(null),W=!w||y||!!w.closest("form"),[B,U]=n.useState(new Set),z=Array.from(B).map(e=>e.props.value).join(";");return(0,P.jsx)(v.fC,{...b,children:(0,P.jsxs)(D,{required:g,scope:t,trigger:w,onTriggerChange:E,valueNode:_,onValueNodeChange:R,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,m.M)(),value:k,onValueChange:A,open:T,onOpenChange:N,dir:j,triggerPointerDownPosRef:L,disabled:h,children:[(0,P.jsx)(M.Provider,{scope:t,children:(0,P.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{U(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{U(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),W?(0,P.jsxs)(eR,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:k,onChange:e=>A(e.target.value),disabled:h,form:y,children:[void 0===k?(0,P.jsx)("option",{value:""}):null,Array.from(B)]},z):null]})})};B.displayName=O;var U="SelectTrigger",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...a}=e,l=I(r),s=L(U,r),c=s.disabled||o,d=(0,u.e)(t,s.onTriggerChange),f=T(r),p=n.useRef("touch"),[h,m,g]=eP(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=eC(t,e,r);void 0!==n&&s.onValueChange(n.value)}),b=e=>{c||(s.onOpenChange(!0),g()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,P.jsx)(v.ee,{asChild:!0,...l,children:(0,P.jsx)(y.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eS(s.value)?"":void 0,...a,ref:d,onClick:(0,i.M)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,i.M)(a.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,i.M)(a.onKeyDown,e=>{let t=""!==h.current,r=e.ctrlKey||e.altKey||e.metaKey;r||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&C.includes(e.key)&&(b(),e.preventDefault())})})})});z.displayName=U;var H="SelectValue",V=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:a,placeholder:l="",...i}=e,s=L(H,r),{onValueNodeHasChildrenChange:c}=s,d=void 0!==a,f=(0,u.e)(t,s.onValueNodeChange);return(0,E.b)(()=>{c(d)},[c,d]),(0,P.jsx)(y.WV.span,{...i,ref:f,style:{pointerEvents:"none"},children:eS(s.value)?(0,P.jsx)(P.Fragment,{children:l}):a})});V.displayName=H;var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,P.jsx)(y.WV.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});$.displayName="SelectIcon";var Y=e=>(0,P.jsx)(g.h,{asChild:!0,...e});Y.displayName="SelectPortal";var K="SelectContent",G=n.forwardRef((e,t)=>{let r=L(K,e.__scopeSelect),[o,l]=n.useState();return((0,E.b)(()=>{l(new DocumentFragment)},[]),r.open)?(0,P.jsx)(J,{...e,ref:t}):o?a.createPortal((0,P.jsx)(X,{scope:e.__scopeSelect,children:(0,P.jsx)(M.Slot,{scope:e.__scopeSelect,children:(0,P.jsx)("div",{children:e.children})})}),o):null});G.displayName=K;var[X,q]=k(K),Z=(0,b.Z8)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:s,side:c,sideOffset:d,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E,..._}=e,C=L(K,r),[j,O]=n.useState(null),[M,N]=n.useState(null),k=(0,u.e)(t,e=>O(e)),[A,I]=n.useState(null),[D,F]=n.useState(null),W=T(r),[B,U]=n.useState(!1),z=n.useRef(!1);n.useEffect(()=>{if(j)return(0,R.Ry)(j)},[j]),(0,p.EW)();let H=n.useCallback(e=>{let[t,...r]=W().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&M&&(M.scrollTop=0),r===n&&M&&(M.scrollTop=M.scrollHeight),r?.focus(),document.activeElement!==o))return},[W,M]),V=n.useCallback(()=>H([A,j]),[H,A,j]);n.useEffect(()=>{B&&V()},[B,V]);let{onOpenChange:$,triggerPointerDownPosRef:Y}=C;n.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Y.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Y.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():j.contains(r.target)||$(!1),document.removeEventListener("pointermove",t),Y.current=null};return null!==Y.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[j,$,Y]),n.useEffect(()=>{let e=()=>$(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[$]);let[G,q]=eP(e=>{let t=W().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eC(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!z.current&&!r,o=void 0!==C.value&&C.value===t;(o||n)&&(I(e),n&&(z.current=!0))},[C.value]),et=n.useCallback(()=>j?.focus(),[j]),er=n.useCallback((e,t,r)=>{let n=!z.current&&!r,o=void 0!==C.value&&C.value===t;(o||n)&&F(e)},[C.value]),en="popper"===o?ee:Q,eo=en===ee?{side:c,sideOffset:d,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:E}:{};return(0,P.jsx)(X,{scope:r,content:j,viewport:M,onViewportChange:N,itemRefCallback:J,selectedItem:A,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:V,selectedItemText:D,position:o,isPositioned:B,searchRef:G,children:(0,P.jsx)(S.f,{as:Z,allowPinchZoom:!0,children:(0,P.jsx)(h.M,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.M)(a,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,P.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,P.jsx)(en,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),..._,...eo,onPlaced:()=>U(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",..._.style},onKeyDown:(0,i.M)(_.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled),r=t.map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(r=r.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let t=e.target,n=r.indexOf(t);r=r.slice(n+1)}setTimeout(()=>H(r)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...a}=e,i=L(K,r),s=q(K,r),[c,d]=n.useState(null),[f,p]=n.useState(null),h=(0,u.e)(t,e=>p(e)),m=T(r),v=n.useRef(!1),g=n.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:x,focusSelectedItem:_}=s,R=n.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&f&&b&&w&&x){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=x.getBoundingClientRect();if("rtl"!==i.dir){let o=n.left-t.left,a=r.left-o,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,l.u)(a,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-n.right,a=window.innerWidth-r.right-o,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,l.u)(a,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let a=m(),s=window.innerHeight-20,u=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=parseInt(d.paddingBottom,10),E=p+h+u+y+g,_=Math.min(5*w.offsetHeight,E),R=window.getComputedStyle(b),S=parseInt(R.paddingTop,10),P=parseInt(R.paddingBottom,10),C=e.top+e.height/2-10,j=w.offsetHeight/2,O=w.offsetTop+j,M=p+h+O;if(M<=C){let e=a.length>0&&w===a[a.length-1].ref.current;c.style.bottom="0px";let t=f.clientHeight-b.offsetTop-b.offsetHeight;c.style.height=M+Math.max(s-C,j+(e?P:0)+t+g)+"px"}else{let e=a.length>0&&w===a[0].ref.current;c.style.top="0px";let t=Math.max(C,p+b.offsetTop+(e?S:0)+j);c.style.height=t+(E-M)+"px",b.scrollTop=M-C+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=_+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,i.trigger,i.valueNode,c,f,b,w,x,i.dir,o]);(0,E.b)(()=>R(),[R]);let[S,C]=n.useState();(0,E.b)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let j=n.useCallback(e=>{e&&!0===g.current&&(R(),_?.(),g.current=!1)},[R,_]);return(0,P.jsx)(et,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:j,children:(0,P.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,P.jsx)(y.WV.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...a}=e,l=I(r);return(0,P.jsx)(v.VY,{...l,...a,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=k(K,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...a}=e,l=q(en,r),s=er(en,r),c=(0,u.e)(t,l.onViewportChange),d=n.useRef(0);return(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,P.jsx)(M.Slot,{scope:r,children:(0,P.jsx)(y.WV.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,i.M)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=parseFloat(r.style.minHeight),a=parseFloat(r.style.height),l=Math.max(o,a);if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=en;var ea="SelectGroup",[el,ei]=k(ea),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,m.M)();return(0,P.jsx)(el,{scope:r,id:o,children:(0,P.jsx)(y.WV.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=ea;var eu="SelectLabel",ec=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ei(eu,r);return(0,P.jsx)(y.WV.div,{id:o.id,...n,ref:t})});ec.displayName=eu;var ed="SelectItem",[ef,ep]=k(ed),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:a=!1,textValue:l,...s}=e,c=L(ed,r),d=q(ed,r),f=c.value===o,[p,h]=n.useState(l??""),[v,g]=n.useState(!1),b=(0,u.e)(t,e=>d.itemRefCallback?.(e,o,a)),w=(0,m.M)(),x=n.useRef("touch"),E=()=>{a||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,P.jsx)(ef,{scope:r,value:o,disabled:a,textId:w,isSelected:f,onItemTextChange:n.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,P.jsx)(M.ItemSlot,{scope:r,value:o,disabled:a,textValue:p,children:(0,P.jsx)(y.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...s,ref:b,onFocus:(0,i.M)(s.onFocus,()=>g(!0)),onBlur:(0,i.M)(s.onBlur,()=>g(!1)),onClick:(0,i.M)(s.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,i.M)(s.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,i.M)(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,i.M)(s.onPointerMove,e=>{x.current=e.pointerType,a?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.M)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,i.M)(s.onKeyDown,e=>{let t=d.searchRef?.current!=="";t&&" "===e.key||(j.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ed;var em="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:l,...i}=e,s=L(em,r),c=q(em,r),d=ep(em,r),f=W(em,r),[p,h]=n.useState(null),m=(0,u.e)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,g=n.useMemo(()=>(0,P.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,E.b)(()=>(b(g),()=>w(g)),[b,w,g]),(0,P.jsxs)(P.Fragment,{children:[(0,P.jsx)(y.WV.span,{id:d.textId,...i,ref:m}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?a.createPortal(i.children,s.valueNode):null]})});ev.displayName=em;var eg="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=ep(eg,r);return o.isSelected?(0,P.jsx)(y.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=eg;var eb="SelectScrollUpButton";n.forwardRef((e,t)=>{let r=q(eb,e.__scopeSelect),o=er(eb,e.__scopeSelect),[a,l]=n.useState(!1),i=(0,u.e)(t,o.onScrollButtonChange);return(0,E.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollTop>0;l(e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,P.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=eb;var ew="SelectScrollDownButton";n.forwardRef((e,t)=>{let r=q(ew,e.__scopeSelect),o=er(ew,e.__scopeSelect),[a,l]=n.useState(!1),i=(0,u.e)(t,o.onScrollButtonChange);return(0,E.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight,r=Math.ceil(t.scrollTop)<e;l(r)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,P.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=ew;var ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...a}=e,l=q("SelectScrollButton",r),s=n.useRef(null),u=T(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,E.b)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,P.jsx)(y.WV.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,i.M)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.M)(a.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.M)(a.onPointerLeave,()=>{c()})})}),eE=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,P.jsx)(y.WV.div,{"aria-hidden":!0,...n,ref:t})});eE.displayName="SelectSeparator";var e_="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=I(r),a=L(e_,r),l=q(e_,r);return a.open&&"popper"===l.position?(0,P.jsx)(v.Eh,{...o,...n,ref:t}):null}).displayName=e_;var eR=n.forwardRef(({__scopeSelect:e,value:t,...r},o)=>{let a=n.useRef(null),l=(0,u.e)(o,a),i=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return n.useEffect(()=>{let e=a.current;if(!e)return;let r=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(r,"value"),o=n.set;if(i!==t&&o){let r=new Event("change",{bubbles:!0});o.call(e,t),e.dispatchEvent(r)}},[i,t]),(0,P.jsx)(y.WV.select,{...r,style:{..._.C2,...r.style},ref:l,defaultValue:t})});function eS(e){return""===e||void 0===e}function eP(e){let t=(0,w.W)(e),r=n.useRef(""),o=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),l=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,a,l]}function eC(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0]),a=o?t[0]:t,l=r?e.indexOf(r):-1,i=(n=Math.max(l,0),e.map((t,r)=>e[(n+r)%e.length])),s=1===a.length;s&&(i=i.filter(e=>e!==r));let u=i.find(e=>e.textValue.toLowerCase().startsWith(a.toLowerCase()));return u!==r?u:void 0}eR.displayName="SelectBubbleInput";var ej=r(7605),eO=r(38),eM=r(1260),eT=r(4634),eN=r(3929),ek=r(7946),eA=r(6660);let eI={size:{type:"enum",className:"rt-r-size",values:["1","2","3"],default:"2",responsive:!0}},eD={variant:{type:"enum",className:"rt-variant",values:["classic","surface","soft","ghost"],default:"surface"},...eN.EG,...eA.I,placeholder:{type:"string"}},eL={variant:{type:"enum",className:"rt-variant",values:["solid","soft"],default:"solid"},...eN.EG,...ek.K};var eF=r(2426);let eW=n.createContext({}),eB=e=>{let{children:t,size:r=eI.size.default,...o}=e;return n.createElement(B,{...o},n.createElement(eW.Provider,{value:n.useMemo(()=>({size:r}),[r])},t))};eB.displayName="Select.Root";let eU=n.forwardRef((e,t)=>{let r=n.useContext(eW),{children:a,className:l,color:i,radius:s,placeholder:u,...c}=(0,eO.y)({size:r?.size,...e},{size:eI.size},eD,eM.E);return n.createElement(z,{asChild:!0},n.createElement("button",{"data-accent-color":i,"data-radius":s,...c,ref:t,className:o("rt-reset","rt-SelectTrigger",l)},n.createElement("span",{className:"rt-SelectTriggerInner"},n.createElement(V,{placeholder:u},a)),n.createElement($,{asChild:!0},n.createElement(eT.v4,{className:"rt-SelectIcon"}))))});eU.displayName="Select.Trigger";let ez=n.forwardRef((e,t)=>{let r=n.useContext(eW),{className:a,children:l,color:i,container:s,...u}=(0,eO.y)({size:r?.size,...e},{size:eI.size},eL),c=(0,eF.TC)(),d=i||c.accentColor;return n.createElement(Y,{container:s},n.createElement(eF.Q2,{asChild:!0},n.createElement(G,{"data-accent-color":d,sideOffset:4,...u,asChild:!1,ref:t,className:o({"rt-PopperContent":"popper"===u.position},"rt-SelectContent",a)},n.createElement(ej.fC,{type:"auto",className:"rt-ScrollAreaRoot"},n.createElement(eo,{asChild:!0,className:"rt-SelectViewport"},n.createElement(ej.l_,{className:"rt-ScrollAreaViewport",style:{overflowY:void 0}},l)),n.createElement(ej.LW,{className:"rt-ScrollAreaScrollbar rt-r-size-1",orientation:"vertical"},n.createElement(ej.bU,{className:"rt-ScrollAreaThumb"}))))))});ez.displayName="Select.Content";let eH=n.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return n.createElement(eh,{...l,asChild:!1,ref:t,className:o("rt-SelectItem",r)},n.createElement(ey,{className:"rt-SelectItemIndicator"},n.createElement(eT.dc,{className:"rt-SelectItemIndicatorIcon"})),n.createElement(ev,null,a))});eH.displayName="Select.Item";let eV=n.forwardRef(({className:e,...t},r)=>n.createElement(es,{...t,asChild:!1,ref:r,className:o("rt-SelectGroup",e)}));eV.displayName="Select.Group";let e$=n.forwardRef(({className:e,...t},r)=>n.createElement(ec,{...t,asChild:!1,ref:r,className:o("rt-SelectLabel",e)}));e$.displayName="Select.Label";let eY=n.forwardRef(({className:e,...t},r)=>n.createElement(eE,{...t,asChild:!1,ref:r,className:o("rt-SelectSeparator",e)}));eY.displayName="Select.Separator"},5351:(e,t,r)=>{"use strict";r.d(t,{g7:()=>o});var n=r(120);n.fC;let o=n.fC;n.A4},2602:(e,t,r)=>{"use strict";r.d(t,{x:()=>g});var n=r(8726),o=r(7253),a=r(120),l=r(38),i=r(1260),s=r(3564),u=r(3929),c=r(7946),d=r(8413),f=r(7756),p=r(5138),h=r(4108),m=r(6198);let v={as:{type:"enum",values:["span","div","label","p"],default:"span"},...s.C,size:{type:"enum",className:"rt-r-size",values:["1","2","3","4","5","6","7","8","9"],responsive:!0},...m.x,...f.O,...d.E,...h.w,...p.u,...u.EG,...c.K},g=n.forwardRef((e,t)=>{let{children:r,className:s,asChild:u,as:c="span",color:d,...f}=(0,l.y)(e,v,i.E);return n.createElement(a.fC,{"data-accent-color":d,...f,ref:t,className:o("rt-Text",s)},u?r:n.createElement(c,null,r))});g.displayName="Text"},2426:(e,t,r)=>{"use strict";r.d(t,{Q2:()=>$,TC:()=>V});var n=r(8726),o=r(7253),a=r(1193),l=r(7754),i=r(752),s=r(6387),u=(r(7098),r(4959)),c=(r(6147),r(7602)),d=r(4049),f=r(120),p=(r(9520),r(2014)),h=r(2395),[m,v]=(0,i.b)("Tooltip",[u.D7]),g=(0,u.D7)(),y="TooltipProvider",b="tooltip.open",[w,x]=m(y),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:l}=e,i=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(w,{scope:t,isOpenDelayedRef:i,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),i.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:a,children:l})};E.displayName=y;var _="Tooltip",[R,S]=m(_),P="TooltipTrigger";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...o}=e,i=S(P,r),s=x(P,r),c=g(r),f=n.useRef(null),p=(0,l.e)(t,f,i.onTriggerChange),m=n.useRef(!1),v=n.useRef(!1),y=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,h.jsx)(u.ee,{asChild:!0,...c,children:(0,h.jsx)(d.WV.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...o,ref:p,onPointerMove:(0,a.M)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(i.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,a.M)(e.onPointerLeave,()=>{i.onTriggerLeave(),v.current=!1}),onPointerDown:(0,a.M)(e.onPointerDown,()=>{i.open&&i.onClose(),m.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,a.M)(e.onFocus,()=>{m.current||i.onOpen()}),onBlur:(0,a.M)(e.onBlur,i.onClose),onClick:(0,a.M)(e.onClick,i.onClose)})})}).displayName=P;var[C,j]=m("TooltipPortal",{forceMount:void 0}),O="TooltipContent",M=n.forwardRef((e,t)=>{let r=j(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,l=S(O,e.__scopeTooltip);return(0,h.jsx)(c.z,{present:n||l.open,children:l.disableHoverableContent?(0,h.jsx)(I,{side:o,...a,ref:t}):(0,h.jsx)(T,{side:o,...a,ref:t})})}),T=n.forwardRef((e,t)=>{let r=S(O,e.__scopeTooltip),o=x(O,e.__scopeTooltip),a=n.useRef(null),i=(0,l.e)(t,a),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,f=a.current,{onPointerInTransitChange:p}=o,m=n.useCallback(()=>{u(null),p(!1)},[p]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect()),a=function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),l=function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect()),i=function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...a,...l]);u(i),p(!0)},[p]);return n.useEffect(()=>()=>m(),[m]),n.useEffect(()=>{if(c&&f){let e=e=>v(e,f),t=e=>v(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,v,m]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||f?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],s=l.x,u=l.y,c=i.x,d=i.y,f=u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s;f&&(o=!o)}return o}(r,s);n?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,d,m]),(0,h.jsx)(I,{...e,ref:i})}),[N,k]=m(_,{isInside:!1}),A=(0,f.sA)("TooltipContent"),I=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:l,onPointerDownOutside:i,...c}=e,d=S(O,r),f=g(r),{onClose:m}=d;return n.useEffect(()=>(document.addEventListener(b,m),()=>document.removeEventListener(b,m)),[m]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&m()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,m]),(0,h.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,h.jsxs)(u.VY,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(A,{children:o}),(0,h.jsx)(N,{scope:r,isInside:!0,children:(0,h.jsx)(p.fC,{id:d.contentId,role:"tooltip",children:a||o})})]})})});M.displayName=O;var D="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=g(r),a=k(D,r);return a.isInside?null:(0,h.jsx)(u.Eh,{...o,...n,ref:t})}).displayName=D;var L=r(7796),F=r(3564),W=r(3929),B=r(6660);let U={...F.C,hasBackground:{type:"boolean",default:!0},appearance:{type:"enum",values:["inherit","light","dark"],default:"inherit"},accentColor:{type:"enum",values:W.FN,default:"indigo"},grayColor:{type:"enum",values:W.ab,default:"auto"},panelBackground:{type:"enum",values:["solid","translucent"],default:"translucent"},radius:{type:"enum",values:B.p,default:"medium"},scaling:{type:"enum",values:["90%","95%","100%","105%","110%"],default:"100%"}},z=()=>{},H=n.createContext(void 0);function V(){let e=n.useContext(H);if(void 0===e)throw Error("`useThemeContext` must be used within a `Theme`");return e}let $=n.forwardRef((e,t)=>void 0===n.useContext(H)?n.createElement(E,{delayDuration:200},n.createElement(L.zt,{dir:"ltr"},n.createElement(Y,{...e,ref:t}))):n.createElement(K,{...e,ref:t}));$.displayName="Theme";let Y=n.forwardRef((e,t)=>{let{appearance:r=U.appearance.default,accentColor:o=U.accentColor.default,grayColor:a=U.grayColor.default,panelBackground:l=U.panelBackground.default,radius:i=U.radius.default,scaling:s=U.scaling.default,hasBackground:u=U.hasBackground.default,...c}=e,[d,f]=n.useState(r);n.useEffect(()=>f(r),[r]);let[p,h]=n.useState(o);n.useEffect(()=>h(o),[o]);let[m,v]=n.useState(a);n.useEffect(()=>v(a),[a]);let[g,y]=n.useState(l);n.useEffect(()=>y(l),[l]);let[b,w]=n.useState(i);n.useEffect(()=>w(i),[i]);let[x,E]=n.useState(s);return n.useEffect(()=>E(s),[s]),n.createElement(K,{...c,ref:t,isRoot:!0,hasBackground:u,appearance:d,accentColor:p,grayColor:m,panelBackground:g,radius:b,scaling:x,onAppearanceChange:f,onAccentColorChange:h,onGrayColorChange:v,onPanelBackgroundChange:y,onRadiusChange:w,onScalingChange:E})});Y.displayName="ThemeRoot";let K=n.forwardRef((e,t)=>{let r=n.useContext(H),{asChild:a,isRoot:l,hasBackground:i,appearance:s=r?.appearance??U.appearance.default,accentColor:u=r?.accentColor??U.accentColor.default,grayColor:c=r?.resolvedGrayColor??U.grayColor.default,panelBackground:d=r?.panelBackground??U.panelBackground.default,radius:p=r?.radius??U.radius.default,scaling:h=r?.scaling??U.scaling.default,onAppearanceChange:m=z,onAccentColorChange:v=z,onGrayColorChange:g=z,onPanelBackgroundChange:y=z,onRadiusChange:b=z,onScalingChange:w=z,...x}=e,E=a?f.fC:"div",_="auto"===c?function(e){switch(e){case"tomato":case"red":case"ruby":case"crimson":case"pink":case"plum":case"purple":case"violet":return"mauve";case"iris":case"indigo":case"blue":case"sky":case"cyan":return"slate";case"teal":case"jade":case"mint":case"green":return"sage";case"grass":case"lime":return"olive";case"yellow":case"amber":case"orange":case"brown":case"gold":case"bronze":return"sand";case"gray":return"gray"}}(u):c,R="light"===e.appearance||"dark"===e.appearance;return n.createElement(H.Provider,{value:n.useMemo(()=>({appearance:s,accentColor:u,grayColor:c,resolvedGrayColor:_,panelBackground:d,radius:p,scaling:h,onAppearanceChange:m,onAccentColorChange:v,onGrayColorChange:g,onPanelBackgroundChange:y,onRadiusChange:b,onScalingChange:w}),[s,u,c,_,d,p,h,m,v,g,y,b,w])},n.createElement(E,{"data-is-root-theme":l?"true":"false","data-accent-color":u,"data-gray-color":_,"data-has-background":(void 0===i?l||R:i)?"true":"false","data-panel-background":d,"data-radius":p,"data-scaling":h,ref:t,...x,className:o("radix-themes",{light:"light"===s,dark:"dark"===s},x.className)}))});K.displayName="ThemeImpl"},38:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var n=r(7253),o=r(9445),a=r(4188),l=r(5392);function i(e,...t){let r,s;let u={...e},c=function(...e){return Object.assign({},...e)}(...t);for(let e in c){let t=u[e],i=c[e];if(void 0!==i.default&&void 0===t&&(t=i.default),"enum"!==i.type||[i.default,...i.values].includes(t)||(0,a.d)(t)||(t=i.default),u[e]=t,"className"in i&&i.className){delete u[e];let c="responsive"in i;if(!t||(0,a.d)(t)&&!c)continue;if((0,a.d)(t)&&(void 0!==i.default&&void 0===t.initial&&(t.initial=i.default),"enum"===i.type&&([i.default,...i.values].includes(t.initial)||(t.initial=i.default))),"enum"===i.type){let e=(0,o.RE)({allowArbitraryValues:!1,value:t,className:i.className,propValues:i.values,parseValue:i.parseValue});r=n(r,e);continue}if("string"===i.type||"enum | string"===i.type){let e="string"===i.type?[]:i.values,[a,u]=(0,o.uq)({className:i.className,customProperties:i.customProperties,propValues:e,parseValue:i.parseValue,value:t});s=(0,l.y)(s,u),r=n(r,a);continue}if("boolean"===i.type&&t){r=n(r,i.className);continue}}}return u.className=n(r,e.className),u.style=(0,l.y)(s,e.style),u}},9445:(e,t,r)=>{"use strict";r.d(t,{RE:()=>i,uq:()=>l});var n=r(7235);function o(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var a=r(4188);function l({className:e,customProperties:t,...r}){let l=i({allowArbitraryValues:!0,className:e,...r}),s=function({customProperties:e,value:t,propValues:r,parseValue:l=e=>e}){let i={};if(!(!t||"string"==typeof t&&r.includes(t))){if("string"==typeof t&&(i=Object.fromEntries(e.map(e=>[e,t]))),(0,a.d)(t))for(let a in t){if(!o(t,a)||!n.A.includes(a))continue;let l=t[a];if(!r.includes(l))for(let t of e)i={["initial"===a?t:`${t}-${a}`]:l,...i}}for(let e in i){let t=i[e];void 0!==t&&(i[e]=l(t))}return i}}({customProperties:t,...r});return[l,s]}function i({allowArbitraryValues:e,value:t,className:r,propValues:l,parseValue:i=e=>e}){let u=[];if(t){if("string"==typeof t&&l.includes(t))return s(r,t,i);if((0,a.d)(t)){for(let a in t){if(!o(t,a)||!n.A.includes(a))continue;let c=t[a];if(void 0!==c){if(l.includes(c)){let e=s(r,c,i),t="initial"===a?e:`${a}:${e}`;u.push(t)}else if(e){let e="initial"===a?r:`${a}:${r}`;u.push(e)}}}return u.join(" ")}if(e)return r}}function s(e,t,r){let n=r(t),o=n?.startsWith("-"),a=o?n?.substring(1):n;return`${o?"-":""}${e}${e?"-":""}${a}`}},6959:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var n=r(8726);function o(e,t){let{asChild:r,children:o}=e;if(!r)return"function"==typeof t?t(o):t;let a=n.Children.only(o);return n.cloneElement(a,{children:"function"==typeof t?t(a.props.children):t})}},4188:(e,t,r)=>{"use strict";r.d(t,{d:()=>o});var n=r(7235);function o(e){return"object"==typeof e&&Object.keys(e).some(e=>n.A.includes(e))}},7507:(e,t,r)=>{"use strict";function n(e,t){if(void 0!==e)return"string"==typeof e?t(e):Object.fromEntries(Object.entries(e).map(([e,r])=>[e,t(r)]))}function o(e){return"3"===e?"3":"2"}function a(e){switch(e){case"1":return"1";case"2":case"3":return"2";case"4":return"3"}}r.d(t,{AG:()=>a,qz:()=>n,uJ:()=>o})},5392:(e,t,r)=>{"use strict";function n(...e){let t={};for(let r of e)r&&(t={...t,...r});return Object.keys(t).length?t:void 0}r.d(t,{y:()=>n})},8197:(e,t,r)=>{"use strict";r.d(t,{O:()=>o});var n=r(8726);let o=e=>{if(!n.isValidElement(e))throw Error(`Expected a single React Element child, but got: ${n.Children.toArray(e).map(e=>"object"==typeof e&&"type"in e&&"string"==typeof e.type?e.type:typeof e).join(", ")}`);return e}},3564:(e,t,r)=>{"use strict";r.d(t,{C:()=>n});let n={asChild:{type:"boolean"}}},3929:(e,t,r)=>{"use strict";r.d(t,{EG:()=>a,FN:()=>n,ab:()=>o,o3:()=>l});let n=["gray","gold","bronze","brown","yellow","amber","orange","tomato","red","ruby","crimson","pink","plum","purple","violet","iris","indigo","blue","cyan","teal","jade","green","grass","lime","mint","sky"],o=["auto","gray","mauve","slate","sage","olive","sand"],a={color:{type:"enum",values:n,default:void 0}},l={color:{type:"enum",values:n,default:""}}},666:(e,t,r)=>{"use strict";r.d(t,{c:()=>o});let n=["0","1","2","3","4","5","6","7","8","9"],o={gap:{type:"enum | string",className:"rt-r-gap",customProperties:["--gap"],values:n,responsive:!0},gapX:{type:"enum | string",className:"rt-r-cg",customProperties:["--column-gap"],values:n,responsive:!0},gapY:{type:"enum | string",className:"rt-r-rg",customProperties:["--row-gap"],values:n,responsive:!0}}},6350:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});let n={height:{type:"string",className:"rt-r-h",customProperties:["--height"],responsive:!0},minHeight:{type:"string",className:"rt-r-min-h",customProperties:["--min-height"],responsive:!0},maxHeight:{type:"string",className:"rt-r-max-h",customProperties:["--max-height"],responsive:!0}}},7946:(e,t,r)=>{"use strict";r.d(t,{K:()=>n});let n={highContrast:{type:"boolean",className:"rt-high-contrast",default:void 0}}},8683:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});var n=r(690),o=r(6350),a=r(5693);let l=["visible","hidden","clip","scroll","auto"],i=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],s={...n.i,...a.n,...o.F,position:{type:"enum",className:"rt-r-position",values:["static","relative","absolute","fixed","sticky"],responsive:!0},inset:{type:"enum | string",className:"rt-r-inset",customProperties:["--inset"],values:i,responsive:!0},top:{type:"enum | string",className:"rt-r-top",customProperties:["--top"],values:i,responsive:!0},right:{type:"enum | string",className:"rt-r-right",customProperties:["--right"],values:i,responsive:!0},bottom:{type:"enum | string",className:"rt-r-bottom",customProperties:["--bottom"],values:i,responsive:!0},left:{type:"enum | string",className:"rt-r-left",customProperties:["--left"],values:i,responsive:!0},overflow:{type:"enum",className:"rt-r-overflow",values:l,responsive:!0},overflowX:{type:"enum",className:"rt-r-ox",values:l,responsive:!0},overflowY:{type:"enum",className:"rt-r-oy",values:l,responsive:!0},flexBasis:{type:"string",className:"rt-r-fb",customProperties:["--flex-basis"],responsive:!0},flexShrink:{type:"enum | string",className:"rt-r-fs",customProperties:["--flex-shrink"],values:["0","1"],responsive:!0},flexGrow:{type:"enum | string",className:"rt-r-fg",customProperties:["--flex-grow"],values:["0","1"],responsive:!0},gridArea:{type:"string",className:"rt-r-ga",customProperties:["--grid-area"],responsive:!0},gridColumn:{type:"string",className:"rt-r-gc",customProperties:["--grid-column"],responsive:!0},gridColumnStart:{type:"string",className:"rt-r-gcs",customProperties:["--grid-column-start"],responsive:!0},gridColumnEnd:{type:"string",className:"rt-r-gce",customProperties:["--grid-column-end"],responsive:!0},gridRow:{type:"string",className:"rt-r-gr",customProperties:["--grid-row"],responsive:!0},gridRowStart:{type:"string",className:"rt-r-grs",customProperties:["--grid-row-start"],responsive:!0},gridRowEnd:{type:"string",className:"rt-r-gre",customProperties:["--grid-row-end"],responsive:!0}}},8413:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});let n={trim:{type:"enum",className:"rt-r-lt",values:["normal","start","end","both"],responsive:!0}}},1260:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});let n=["0","1","2","3","4","5","6","7","8","9","-1","-2","-3","-4","-5","-6","-7","-8","-9"],o={m:{type:"enum | string",values:n,responsive:!0,className:"rt-r-m",customProperties:["--m"]},mx:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mx",customProperties:["--ml","--mr"]},my:{type:"enum | string",values:n,responsive:!0,className:"rt-r-my",customProperties:["--mt","--mb"]},mt:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mt",customProperties:["--mt"]},mr:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mr",customProperties:["--mr"]},mb:{type:"enum | string",values:n,responsive:!0,className:"rt-r-mb",customProperties:["--mb"]},ml:{type:"enum | string",values:n,responsive:!0,className:"rt-r-ml",customProperties:["--ml"]}}},690:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});let n=["0","1","2","3","4","5","6","7","8","9"],o={p:{type:"enum | string",className:"rt-r-p",customProperties:["--p"],values:n,responsive:!0},px:{type:"enum | string",className:"rt-r-px",customProperties:["--pl","--pr"],values:n,responsive:!0},py:{type:"enum | string",className:"rt-r-py",customProperties:["--pt","--pb"],values:n,responsive:!0},pt:{type:"enum | string",className:"rt-r-pt",customProperties:["--pt"],values:n,responsive:!0},pr:{type:"enum | string",className:"rt-r-pr",customProperties:["--pr"],values:n,responsive:!0},pb:{type:"enum | string",className:"rt-r-pb",customProperties:["--pb"],values:n,responsive:!0},pl:{type:"enum | string",className:"rt-r-pl",customProperties:["--pl"],values:n,responsive:!0}}},7235:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=["initial","xs","sm","md","lg","xl"]},6660:(e,t,r)=>{"use strict";r.d(t,{I:()=>o,p:()=>n});let n=["none","small","medium","large","full"],o={radius:{type:"enum",values:n,default:void 0}}},7756:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});let n={align:{type:"enum",className:"rt-r-ta",values:["left","center","right"],responsive:!0}}},5138:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});let n={wrap:{type:"enum",className:"rt-r-tw",values:["wrap","nowrap","pretty","balance"],responsive:!0}}},4108:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});let n={truncate:{type:"boolean",className:"rt-truncate"}}},6198:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});let n={weight:{type:"enum",className:"rt-r-weight",values:["light","regular","medium","bold"],responsive:!0}}},5693:(e,t,r)=>{"use strict";r.d(t,{n:()=>n});let n={width:{type:"string",className:"rt-r-w",customProperties:["--width"],responsive:!0},minWidth:{type:"string",className:"rt-r-min-w",customProperties:["--min-width"],responsive:!0},maxWidth:{type:"string",className:"rt-r-max-w",customProperties:["--max-width"],responsive:!0}}},6481:(e,t,r)=>{"use strict";r.d(t,{XL:()=>q});var n,o=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,c=(e,t,r)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,d=(e,t)=>{for(var r in t||(t={}))s.call(t,r)&&c(e,r,t[r]);if(i)for(var r of i(t))u.call(t,r)&&c(e,r,t[r]);return e},f=(e,t)=>a(e,l(t)),p=class extends Error{constructor(e,t,r){super(t||e.toString(),{cause:r}),this.status=e,this.statusText=t,this.error=r}},h=async(e,t)=>{var r,n,o,a,l,i;let s=t||{},u={onRequest:[null==t?void 0:t.onRequest],onResponse:[null==t?void 0:t.onResponse],onSuccess:[null==t?void 0:t.onSuccess],onError:[null==t?void 0:t.onError],onRetry:[null==t?void 0:t.onRetry]};if(!t||!(null==t?void 0:t.plugins))return{url:e,options:s,hooks:u};for(let c of(null==t?void 0:t.plugins)||[]){if(c.init){let n=await (null==(r=c.init)?void 0:r.call(c,e.toString(),t));s=n.options||s,e=n.url}u.onRequest.push(null==(n=c.hooks)?void 0:n.onRequest),u.onResponse.push(null==(o=c.hooks)?void 0:o.onResponse),u.onSuccess.push(null==(a=c.hooks)?void 0:a.onSuccess),u.onError.push(null==(l=c.hooks)?void 0:l.onError),u.onRetry.push(null==(i=c.hooks)?void 0:i.onRetry)}return{url:e,options:s,hooks:u}},m=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(){return this.options.delay}},v=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(e){let t=Math.min(this.options.maxDelay,this.options.baseDelay*2**e);return t}},g=async e=>{let t={},r=async e=>"function"==typeof e?await e():e;if(null==e?void 0:e.auth){if("Bearer"===e.auth.type){let n=await r(e.auth.token);if(!n)return t;t.authorization=`Bearer ${n}`}else if("Basic"===e.auth.type){let n=r(e.auth.username),o=r(e.auth.password);if(!n||!o)return t;t.authorization=`Basic ${btoa(`${n}:${o}`)}`}else if("Custom"===e.auth.type){let n=r(e.auth.value);if(!n)return t;t.authorization=`${r(e.auth.prefix)} ${n}`}}return t},y=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function b(e){if(void 0===e)return!1;let t=typeof e;return"string"===t||"number"===t||"boolean"===t||null===t||"object"===t&&(!!Array.isArray(e)||!e.buffer&&(e.constructor&&"Object"===e.constructor.name||"function"==typeof e.toJSON))}function w(e){try{return JSON.parse(e)}catch(t){return e}}function x(e){return"function"==typeof e}async function E(e){let t=new Headers(null==e?void 0:e.headers),r=await g(e);for(let[e,n]of Object.entries(r||{}))t.set(e,n);if(!t.has("content-type")){let r=b(null==e?void 0:e.body)?"application/json":null;r&&t.set("content-type",r)}return t}var _=class e extends Error{constructor(t,r){super(r||JSON.stringify(t,null,2)),this.issues=t,Object.setPrototypeOf(this,e.prototype)}};async function R(e,t){let r=await e["~standard"].validate(t);if(r.issues)throw new _(r.issues);return r.value}var S=["get","post","put","patch","delete"],P=e=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(t,r){var n,o,a,l;let i=(null==(o=null==(n=e.plugins)?void 0:n.find(e=>{var r;return null!=(r=e.schema)&&!!r.config&&(t.startsWith(e.schema.config.baseURL||"")||t.startsWith(e.schema.config.prefix||""))}))?void 0:o.schema)||e.schema;if(i){let e=t;(null==(a=i.config)?void 0:a.prefix)&&e.startsWith(i.config.prefix)&&(e=e.replace(i.config.prefix,""),i.config.baseURL&&(t=t.replace(i.config.prefix,i.config.baseURL))),(null==(l=i.config)?void 0:l.baseURL)&&e.startsWith(i.config.baseURL)&&(e=e.replace(i.config.baseURL,""));let n=i.schema[e];if(n){let e=f(d({},r),{method:n.method,output:n.output});return(null==r?void 0:r.disableValidation)||(e=f(d({},e),{body:n.input?await R(n.input,null==r?void 0:r.body):null==r?void 0:r.body,params:n.params?await R(n.params,null==r?void 0:r.params):null==r?void 0:r.params,query:n.query?await R(n.query,null==r?void 0:r.query):null==r?void 0:r.query})),{url:t,options:e}}}return{url:t,options:r}}}),C=e=>async function(t,r){let n=f(d(d({},e),r),{plugins:[...(null==e?void 0:e.plugins)||[],P(e||{})]});return null==e||e.catchAllError,await j(t,n)},j=async(e,t)=>{var r,n,o,a,l,i,s,u;let{hooks:c,url:g,options:_}=await h(e,t),P=function(e){if(null==e?void 0:e.customFetchImpl)return e.customFetchImpl;if("undefined"!=typeof globalThis&&x(globalThis.fetch))return globalThis.fetch;if("undefined"!=typeof window&&x(window.fetch))return window.fetch;throw Error("No fetch implementation found")}(_),C=new AbortController,O=null!=(r=_.signal)?r:C.signal,M=function(e,t){let{baseURL:r,params:n,query:o}=t||{query:{},params:{},baseURL:""},a=e.startsWith("http")?e.split("/").slice(0,3).join("/"):r||"";if(e.startsWith("@")){let t=e.toString().split("@")[1].split("/")[0];S.includes(t)&&(e=e.replace(`@${t}/`,"/"))}a.endsWith("/")||(a+="/");let[l,i]=e.replace(a,"").split("?"),s=new URLSearchParams(i);for(let[e,t]of Object.entries(o||{}))null!=t&&s.set(e,String(t));if(n){if(Array.isArray(n)){let e=l.split("/").filter(e=>e.startsWith(":"));for(let[t,r]of e.entries()){let e=n[t];l=l.replace(r,e)}}else for(let[e,t]of Object.entries(n))l=l.replace(`:${e}`,String(t))}(l=l.split("/").map(encodeURIComponent).join("/")).startsWith("/")&&(l=l.slice(1));let u=s.toString();if(u=u.length>0?`?${u}`.replace(/\+/g,"%20"):"",!a.startsWith("http"))return`${a}${l}${u}`;let c=new URL(`${l}${u}`,a);return c}(g,_),T=function(e){if(!(null==e?void 0:e.body))return null;let t=new Headers(null==e?void 0:e.headers);if(b(e.body)&&!t.has("content-type")){for(let[t,r]of Object.entries(null==e?void 0:e.body))r instanceof Date&&(e.body[t]=r.toISOString());return JSON.stringify(e.body)}return e.body}(_),N=await E(_),k=function(e,t){var r;if(null==t?void 0:t.method)return t.method.toUpperCase();if(e.startsWith("@")){let n=null==(r=e.split("@")[1])?void 0:r.split("/")[0];return S.includes(n)?n.toUpperCase():(null==t?void 0:t.body)?"POST":"GET"}return(null==t?void 0:t.body)?"POST":"GET"}(g,_),A=f(d({},_),{url:M,headers:N,body:T,method:k,signal:O});for(let e of c.onRequest)if(e){let t=await e(A);t instanceof Object&&(A=t)}("pipeTo"in A&&"function"==typeof A.pipeTo||"function"==typeof(null==(n=null==t?void 0:t.body)?void 0:n.pipe))&&!("duplex"in A)&&(A.duplex="half");let{clearTimeout:I}=function(e,t){let r;return!(null==e?void 0:e.signal)&&(null==e?void 0:e.timeout)&&(r=setTimeout(()=>null==t?void 0:t.abort(),null==e?void 0:e.timeout)),{abortTimeout:r,clearTimeout:()=>{r&&clearTimeout(r)}}}(_,C),D=await P(A.url,A);I();let L={response:D,request:A};for(let e of c.onResponse)if(e){let r=await e(f(d({},L),{response:(null==(o=null==t?void 0:t.hookOptions)?void 0:o.cloneResponse)?D.clone():D}));r instanceof Response?D=r:r instanceof Object&&(D=r.response)}if(D.ok){let e="HEAD"!==A.method;if(!e)return{data:"",error:null};let r=function(e){let t=e.headers.get("content-type"),r=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!t)return"json";let n=t.split(";").shift()||"";return y.test(n)?"json":r.has(n)||n.startsWith("text/")?"text":"blob"}(D),n={data:"",response:D,request:A};if("json"===r||"text"===r){let e=await D.text(),t=null!=(a=A.jsonParser)?a:w,r=await t(e);n.data=r}else n.data=await D[r]();for(let e of((null==A?void 0:A.output)&&A.output&&!A.disableValidation&&(n.data=await R(A.output,n.data)),c.onSuccess))e&&await e(f(d({},n),{response:(null==(l=null==t?void 0:t.hookOptions)?void 0:l.cloneResponse)?D.clone():D}));return(null==t?void 0:t.throw)?n.data:{data:n.data,error:null}}let F=null!=(i=null==t?void 0:t.jsonParser)?i:w,W=await D.text(),B=function(e){try{return JSON.parse(e),!0}catch(e){return!1}}(W),U=B?await F(W):null,z={response:D,responseText:W,request:A,error:f(d({},U),{status:D.status,statusText:D.statusText})};for(let e of c.onError)e&&await e(f(d({},z),{response:(null==(s=null==t?void 0:t.hookOptions)?void 0:s.cloneResponse)?D.clone():D}));if(null==t?void 0:t.retry){let r=function(e){if("number"==typeof e)return new m({type:"linear",attempts:e,delay:1e3});switch(e.type){case"linear":return new m(e);case"exponential":return new v(e);default:throw Error("Invalid retry strategy")}}(t.retry),n=null!=(u=t.retryAttempt)?u:0;if(await r.shouldAttemptRetry(n,D)){for(let e of c.onRetry)e&&await e(L);let o=r.getDelay(n);return await new Promise(e=>setTimeout(e,o)),await j(e,f(d({},t),{retryAttempt:n+1}))}}if(null==t?void 0:t.throw)throw new p(D.status,D.statusText,B?U:W);return{data:null,error:f(d({},U),{status:D.status,statusText:D.statusText})}};let O=Object.create(null),M=e=>globalThis.process?.env||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?O:globalThis),T=new Proxy(O,{get(e,t){let r=M();return r[t]??O[t]},has(e,t){let r=M();return t in r||t in O},set(e,t,r){let n=M(!0);return n[t]=r,!0},deleteProperty(e,t){if(!t)return!1;let r=M(!0);return delete r[t],!0},ownKeys(){let e=M(!0);return Object.keys(e)}}),N="undefined"!=typeof process&&process.env&&"production"||"";"test"===N||T.TEST;class k extends Error{constructor(e,t){super(e),this.name="BetterAuthError",this.message=e,this.cause=t,this.stack=""}}function A(e,t="/api/auth"){let r=function(e){try{let t=new URL(e);return"/"!==t.pathname}catch(t){throw new k(`Invalid base URL: ${e}. Please provide a valid base URL.`)}}(e);return r?e:`${e.replace(/\/+$/,"")}${t=t.startsWith("/")?t:`/${t}`}`}let I=[],D=0,L=0,F=e=>{let t=[],r={get:()=>(r.lc||r.listen(()=>{})(),r.value),lc:0,listen:e=>(r.lc=t.push(e),()=>{for(let t=D+4;t<I.length;)I[t]===e?I.splice(t,4):t+=4;let n=t.indexOf(e);!~n||(t.splice(n,1),--r.lc||r.off())}),notify(e,n){L++;let o=!I.length;for(let o of t)I.push(o,r.value,e,n);if(o){for(D=0;D<I.length;D+=4)I[D](I[D+1],I[D+2],I[D+3]);I.length=0}},off(){},set(e){let t=r.value;t!==e&&(r.value=e,r.notify(t))},subscribe(e){let t=r.listen(e);return e(r.value),t},value:e};return r},W=(e,t,r,n)=>(e.events=e.events||{},e.events[r+10]||(e.events[r+10]=n(t=>{e.events[r].reduceRight((e,t)=>(t(e),e),{shared:{},...t})})),e.events[r]=e.events[r]||[],e.events[r].push(t),()=>{let n=e.events[r],o=n.indexOf(t);n.splice(o,1),n.length||(delete e.events[r],e.events[r+10](),delete e.events[r+10])}),B=(e,t)=>W(e,r=>{let n=t(r);n&&e.events[6].push(n)},5,t=>{let r=e.listen;e.listen=(...n)=>(e.lc||e.active||(e.active=!0,t()),r(...n));let n=e.off;return e.events[6]=[],e.off=()=>{n(),setTimeout(()=>{if(e.active&&!e.lc){for(let t of(e.active=!1,e.events[6]))t();e.events[6]=[]}},1e3)},()=>{e.listen=r,e.off=n}}),U="undefined"==typeof window,z=(e,t,r,n)=>{let o=F({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>a()}),a=()=>{let e="function"==typeof n?n({data:o.get().data,error:o.get().error,isPending:o.get().isPending}):n;return r(t,{...e,async onSuccess(t){o.set({data:t.data,error:null,isPending:!1,isRefetching:!1,refetch:o.value.refetch}),await e?.onSuccess?.(t)},async onError(t){let{request:r}=t,n="number"==typeof r.retry?r.retry:r.retry?.attempts,a=r.retryAttempt||0;n&&a<n||(o.set({error:t.error,data:null,isPending:!1,isRefetching:!1,refetch:o.value.refetch}),await e?.onError?.(t))},async onRequest(t){let r=o.get();o.set({isPending:null===r.data,data:r.data,error:null,isRefetching:!0,refetch:o.value.refetch}),await e?.onRequest?.(t)}})};e=Array.isArray(e)?e:[e];let l=!1;for(let t of e)t.subscribe(()=>{U||(l?a():B(o,()=>(setTimeout(()=>{a()},0),l=!0,()=>{o.off(),t.off()})))});return o},H={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},V=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,$={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},Y=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/,K={id:"redirect",name:"Redirect",hooks:{onSuccess(e){if(e.data?.url&&e.data?.redirect&&"undefined"!=typeof window&&window.location&&window.location)try{window.location.href=e.data.url}catch{}}}},G=e=>{let t="credentials"in Request.prototype,r=function(e,t,r){if(e)return A(e,t);let n=T.BETTER_AUTH_URL||T.NEXT_PUBLIC_BETTER_AUTH_URL||T.PUBLIC_BETTER_AUTH_URL||T.NUXT_PUBLIC_BETTER_AUTH_URL||T.NUXT_PUBLIC_AUTH_URL||("/"!==T.BASE_URL?T.BASE_URL:void 0);if(n)return A(n,t);let o=r?.headers.get("x-forwarded-host"),a=r?.headers.get("x-forwarded-proto");if(o&&a)return A(`${a}://${o}`,t);if(r){let e=function(e){try{let t=new URL(e);return t.origin}catch(e){return null}}(r.url);if(!e)throw new k("Could not get origin from request. Please provide a valid base URL.");return A(e,t)}if("undefined"!=typeof window&&window.location)return A(window.location.origin,t)}(e?.baseURL,e?.basePath),n=e?.plugins?.flatMap(e=>e.fetchPlugins).filter(e=>void 0!==e)||[],o={id:"lifecycle-hooks",name:"lifecycle-hooks",hooks:{onSuccess:e?.fetchOptions?.onSuccess,onError:e?.fetchOptions?.onError,onRequest:e?.fetchOptions?.onRequest,onResponse:e?.fetchOptions?.onResponse}},{onSuccess:a,onError:l,onRequest:i,onResponse:s,...u}=e?.fetchOptions||{},c=C({baseURL:r,...t?{credentials:"include"}:{},method:"GET",jsonParser:e=>e?function(e,t={strict:!0}){return function(e,t={}){let{strict:r=!1,warnings:n=!1,reviver:o,parseDates:a=!0}=t;if("string"!=typeof e)return e;let l=e.trim();if('"'===l[0]&&l.endsWith('"')&&!l.slice(1,-1).includes('"'))return l.slice(1,-1);let i=l.toLowerCase();if(i.length<=9&&i in $)return $[i];if(!V.test(l)){if(r)throw SyntaxError("[better-json] Invalid JSON");return e}let s=Object.entries(H).some(([e,t])=>{let r=t.test(l);return r&&n&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${e} pattern`),r});if(s&&r)throw Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(l,(e,t)=>{if("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t){n&&console.warn(`[better-json] Dropping "${e}" key to prevent prototype pollution`);return}if(a&&"string"==typeof t){let e=function(e){let t=Y.exec(e);if(!t)return null;let[,r,n,o,a,l,i,s,u,c,d]=t,f=new Date(Date.UTC(parseInt(r,10),parseInt(n,10)-1,parseInt(o,10),parseInt(a,10),parseInt(l,10),parseInt(i,10),s?parseInt(s.padEnd(3,"0"),10):0));if(u){let e=(60*parseInt(c,10)+parseInt(d,10))*("+"===u?-1:1);f.setUTCMinutes(f.getUTCMinutes()+e)}return f instanceof Date&&!isNaN(f.getTime())?f:null}(t);if(e)return e}return o?o(e,t):t})}catch(t){if(r)throw t;return e}}(e,t)}(e,{strict:!1}):null,customFetchImpl:async(e,t)=>{try{return await fetch(e,t)}catch(e){return Response.error()}},...u,plugins:[o,...u.plugins||[],...e?.disableDefaultFetchPlugins?[]:[K],...n]}),{$sessionSignal:d,session:f}=function(e){let t=F(!1),r=z(t,"/get-session",e,{method:"GET"});return{session:r,$sessionSignal:t}}(c),p=e?.plugins||[],h={},m={$sessionSignal:d,session:f},v={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"},g=[{signal:"$sessionSignal",matcher:e=>"/sign-out"===e||"/update-user"===e||e.startsWith("/sign-in")||e.startsWith("/sign-up")||"/delete-user"===e||"/verify-email"===e}];for(let e of p)e.getAtoms&&Object.assign(m,e.getAtoms?.(c)),e.pathMethods&&Object.assign(v,e.pathMethods),e.atomListeners&&g.push(...e.atomListeners);let y={notify:e=>{m[e].set(!m[e].get())},listen:(e,t)=>{m[e].subscribe(t)},atoms:m};for(let t of p)t.getActions&&Object.assign(h,t.getActions?.(c,y,e));return{pluginsActions:h,pluginsAtoms:m,pluginPathMethods:v,atomListeners:g,$fetch:c,$store:y}};var X=r(8726);function q(e){let{pluginPathMethods:t,pluginsActions:r,pluginsAtoms:n,$fetch:o,$store:a,atomListeners:l}=G(e),i={};for(let[e,t]of Object.entries(n))i[`use${e.charAt(0).toUpperCase()+e.slice(1)}`]=()=>(function(e,t={}){let r=(0,X.useRef)(e.get()),{keys:n,deps:o=[e,n]}=t,a=(0,X.useCallback)(t=>{let o=e=>{r.current!==e&&(r.current=e,t())};return(o(e.value),n?.length)?function(e,t,r){let n=new Set(t).add(void 0);return e.listen((e,t,o)=>{n.has(o)&&r(e,t,o)})}(e,n,o):e.listen(o)},o),l=()=>r.current;return(0,X.useSyncExternalStore)(a,l,l)})(t);let s={...r,...i,$fetch:o,$store:a},u=function e(r=[]){return new Proxy(function(){},{get(t,n){let o=[...r,n],a=s;for(let e of o)if(a&&"object"==typeof a&&e in a)a=a[e];else{a=void 0;break}return"function"==typeof a?a:e(o)},apply:async(e,a,i)=>{let s="/"+r.map(e=>e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)).join("/"),u=i[0]||{},c=i[1]||{},{query:d,fetchOptions:f,...p}=u,h={...c,...f},m=function(e,t,r){let n=t[e],{fetchOptions:o,query:a,...l}=r||{};return n||(o?.method?o.method:l&&Object.keys(l).length>0?"POST":"GET")}(s,t,u);return await o(s,{...h,body:"GET"===m?void 0:{...p,...h?.body||{}},query:d||h?.query,method:m,async onSuccess(e){await h?.onSuccess?.(e);let t=l?.find(e=>e.matcher(s));if(!t)return;let r=n[t.signal];if(!r)return;let o=r.get();setTimeout(()=>{r.set(!o)},10)}})}})}();return u}},6223:(e,t,r)=>{"use strict";r.d(t,{KO:()=>h,zt:()=>s});var n=r(8726),o=r(2584),a=r(6890);let l=(0,n.createContext)(void 0);function i(e){let t=(0,n.useContext)(l);return(null==e?void 0:e.store)||t||(0,o.K7)()}function s({children:e,store:t}){let r=(0,n.useRef)(void 0);return t||r.current||(r.current=(0,o.MT)()),(0,n.createElement)(l.Provider,{value:t||r.current},e)}let u=e=>"function"==typeof(null==e?void 0:e.then),c=e=>{e.status||(e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}))},d=n.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw c(e),e}),f=new WeakMap,p=(e,t)=>{let r=f.get(e);return r||(r=new Promise((n,o)=>{let l=e,i=e=>t=>{l===e&&n(t)},s=e=>t=>{l===e&&o(t)},c=()=>{try{let e=t();u(e)?(f.set(e,r),l=e,e.then(i(e),s(e)),(0,a.XB)(e,c)):n(e)}catch(e){o(e)}};e.then(i(e),s(e)),(0,a.XB)(e,c)}),f.set(e,r)),r};function h(e,t){return[function(e,t){let{delay:r,unstable_promiseStatus:o=!n.use}=t||{},a=i(t),[[l,s,f],h]=(0,n.useReducer)(t=>{let r=a.get(e);return Object.is(t[0],r)&&t[1]===a&&t[2]===e?t:[r,a,e]},void 0,()=>[a.get(e),a,e]),m=l;if((s!==a||f!==e)&&(h(),m=a.get(e)),(0,n.useEffect)(()=>{let t=a.sub(e,()=>{if(o)try{let t=a.get(e);u(t)&&c(p(t,()=>a.get(e)))}catch(e){}if("number"==typeof r){setTimeout(h,r);return}h()});return h(),t},[a,e,r,o]),(0,n.useDebugValue)(m),u(m)){let t=p(m,()=>a.get(e));return o&&c(t),d(t)}return m}(e,t),function(e,t){let r=i(t),o=(0,n.useCallback)((...t)=>{if(!("write"in e))throw Error("not writable atom");return r.set(e,...t)},[r,e]);return o}(e,t)]}},2584:(e,t,r)=>{"use strict";let n,o;r.d(t,{K7:()=>f,MT:()=>d,cn:()=>i});var a=r(6890);let l=0;function i(e,t){let r=`atom${++l}`,n={toString(){return this.debugLabel?r+":"+this.debugLabel:r}};return"function"==typeof e?n.read=e:(n.init=e,n.read=s,n.write=u),t&&(n.write=t),n}function s(e){return e(this)}function u(e,t,r){return t(this,"function"==typeof r?r(e(this)):r)}let c=()=>{let e=0,t=(0,a.YN)({}),r=new WeakMap,n=new WeakMap,o=(0,a.k_)(r,n,void 0,void 0,void 0,void 0,t,void 0,(t,r,n,...o)=>e?n(t,...o):t.write(r,n,...o)),l=new Set;return t.m.add(void 0,e=>{l.add(e);let t=r.get(e);t.m=n.get(e)}),t.u.add(void 0,e=>{l.delete(e);let t=r.get(e);delete t.m}),Object.assign(o,{dev4_get_internal_weak_map:()=>(console.log("Deprecated: Use devstore from the devtools library"),r),dev4_get_mounted_atoms:()=>l,dev4_restore_atoms:t=>{o.set({read:()=>null,write:(r,n)=>{++e;try{for(let[e,r]of t)"init"in e&&n(e,r)}finally{--e}}})}})};function d(){return n?n():c()}function f(){return o||(o=d(),globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=o),globalThis.__JOTAI_DEFAULT_STORE__!==o&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044")),o}},6890:(e,t,r)=>{"use strict";r.d(t,{XB:()=>x,YN:()=>w,k_:()=>b});let n=(e,t)=>e.unstable_is?e.unstable_is(t):t===e,o=e=>"init"in e,a=e=>!!e.write,l=e=>"v"in e||"e"in e,i=e=>{if("e"in e)throw e.e;if(!("v"in e))throw Error("[Bug] atom state is not initialized");return e.v},s=new WeakMap,u=e=>{var t;return f(e)&&!!(null==(t=s.get(e))?void 0:t[0])},c=e=>{let t=s.get(e);(null==t?void 0:t[0])&&(t[0]=!1,t[1].forEach(e=>e()))},d=(e,t)=>{let r=s.get(e);if(!r){r=[!0,new Set],s.set(e,r);let t=()=>{r[0]=!1};e.then(t,t)}r[1].add(t)},f=e=>"function"==typeof(null==e?void 0:e.then),p=(e,t,r)=>{r.p.has(e)||(r.p.add(e),t.then(()=>{r.p.delete(e)},()=>{r.p.delete(e)}))},h=(e,t,r)=>{let n=r(e),o="v"in n,a=n.v;if(f(t))for(let o of n.d.keys())p(e,t,r(o));n.v=t,delete n.e,o&&Object.is(a,n.v)||(++n.n,f(a)&&c(a))},m=(e,t,r)=>{var n;let o=new Set;for(let t of(null==(n=r.get(e))?void 0:n.t)||[])r.has(t)&&o.add(t);for(let e of t.p)o.add(e);return o},v=()=>{let e=new Set,t=()=>{e.forEach(e=>e())};return t.add=t=>(e.add(t),()=>{e.delete(t)}),t},g=()=>{let e={},t=new WeakMap,r=r=>{var n,o;null==(n=t.get(e))||n.forEach(e=>e(r)),null==(o=t.get(r))||o.forEach(e=>e())};return r.add=(r,n)=>{let o=r||e,a=(t.has(o)?t:t.set(o,new Set)).get(o);return a.add(n),()=>{null==a||a.delete(n),a.size||t.delete(o)}},r},y=Symbol(),b=(e=new WeakMap,t=new WeakMap,r=new WeakMap,s=new Set,c=new Set,v=new Set,g={},b=(e,...t)=>e.read(...t),w=(e,...t)=>e.write(...t),x=(e,t)=>{var r;return null==(r=e.unstable_onInit)?void 0:r.call(e,t)},E=(e,t)=>{var r;return null==(r=e.onMount)?void 0:r.call(e,t)},..._)=>{let R=_[0]||(t=>{if(!t)throw Error("Atom is undefined or null");let r=e.get(t);return r||(r={d:new Map,p:new Set,n:0},e.set(t,r),null==x||x(t,A)),r}),S=_[1]||(()=>{let e=[],r=t=>{try{t()}catch(t){e.push(t)}};do{g.f&&r(g.f);let e=new Set,n=e.add.bind(e);s.forEach(e=>{var r;return null==(r=t.get(e))?void 0:r.l.forEach(n)}),s.clear(),v.forEach(n),v.clear(),c.forEach(n),c.clear(),e.forEach(r),s.size&&P()}while(s.size||v.size||c.size);if(e.length)throw AggregateError(e)}),P=_[2]||(()=>{let e=[],n=new WeakSet,o=new WeakSet,a=Array.from(s);for(;a.length;){let l=a[a.length-1],i=R(l);if(o.has(l)){a.pop();continue}if(n.has(l)){if(r.get(l)===i.n)e.push([l,i]);else if(r.has(l))throw Error("[Bug] invalidated atom exists");o.add(l),a.pop();continue}for(let e of(n.add(l),m(l,i,t)))n.has(e)||a.push(e)}for(let t=e.length-1;t>=0;--t){let[n,o]=e[t],a=!1;for(let e of o.d.keys())if(e!==n&&s.has(e)){a=!0;break}a&&(C(n),M(n)),r.delete(n)}}),C=_[3]||(e=>{var c;let m,v;let y=R(e);if(l(y)&&(t.has(e)&&r.get(e)!==y.n||Array.from(y.d).every(([e,t])=>C(e).n===t)))return y;y.d.clear();let w=!0,x=()=>{t.has(e)&&(M(e),P(),S())},E=y.n;try{let r=b(e,r=>{var a;if(n(e,r)){let e=R(r);if(!l(e)){if(o(r))h(r,r.init,R);else throw Error("no atom init")}return i(e)}let s=C(r);try{return i(s)}finally{y.d.set(r,s.n),u(y.v)&&p(e,y.v,s),null==(a=t.get(r))||a.t.add(e),w||x()}},{get signal(){return m||(m=new AbortController),m.signal},get setSelf(){return a(e)||console.warn("setSelf function cannot be used with read-only atom"),!v&&a(e)&&(v=(...t)=>{if(w&&console.warn("setSelf function cannot be called in sync"),!w)try{return O(e,...t)}finally{P(),S()}}),v}});return h(e,r,R),f(r)&&(d(r,()=>null==m?void 0:m.abort()),r.then(x,x)),y}catch(e){return delete y.v,y.e=e,++y.n,y}finally{w=!1,E!==y.n&&r.get(e)===E&&(r.set(e,y.n),s.add(e),null==(c=g.c)||c.call(g,e))}}),j=_[4]||(e=>{let n=[e];for(;n.length;){let e=n.pop(),o=R(e);for(let a of m(e,o,t)){let e=R(a);r.set(a,e.n),n.push(a)}}}),O=_[5]||((e,...t)=>{let r=!0;try{return w(e,e=>i(C(e)),(t,...a)=>{var l;let i=R(t);try{if(!n(e,t))return O(t,...a);{if(!o(t))throw Error("atom not writable");let e=i.n,r=a[0];h(t,r,R),M(t),e!==i.n&&(s.add(t),null==(l=g.c)||l.call(g,t),j(t));return}}finally{r||(P(),S())}},...t)}finally{r=!1}}),M=_[6]||(e=>{var r;let n=R(e),o=t.get(e);if(o&&!u(n.v)){for(let[t,a]of n.d)if(!o.d.has(t)){let n=R(t),l=T(t);l.t.add(e),o.d.add(t),a!==n.n&&(s.add(t),null==(r=g.c)||r.call(g,t),j(t))}for(let t of o.d||[])if(!n.d.has(t)){o.d.delete(t);let r=N(t);null==r||r.t.delete(e)}}}),T=_[7]||(e=>{var r;let n=R(e),o=t.get(e);if(!o){for(let t of(C(e),n.d.keys())){let r=T(t);r.t.add(e)}o={l:new Set,d:new Set(n.d.keys()),t:new Set},t.set(e,o),null==(r=g.m)||r.call(g,e),a(e)&&c.add(()=>{let t=!0;try{let r=E(e,(...r)=>{try{return O(e,...r)}finally{t||(P(),S())}});r&&(o.u=()=>{t=!0;try{r()}finally{t=!1}})}finally{t=!1}})}return o}),N=_[8]||(e=>{var r;let n=R(e),o=t.get(e);if(o&&!o.l.size&&!Array.from(o.t).some(r=>{var n;return null==(n=t.get(r))?void 0:n.d.has(e)})){for(let a of(o.u&&v.add(o.u),o=void 0,t.delete(e),null==(r=g.u)||r.call(g,e),n.d.keys())){let t=N(a);null==t||t.t.delete(e)}return}return o}),k=[e,t,r,s,c,v,g,b,w,x,E,R,S,P,C,j,O,M,T,N],A={get:e=>i(C(e)),set:(e,...t)=>{try{return O(e,...t)}finally{P(),S()}},sub:(e,t)=>{let r=T(e),n=r.l;return n.add(t),S(),()=>{n.delete(t),N(e),S()}}};return Object.defineProperty(A,y,{value:k}),A},w=e=>(e.c||(e.c=g()),e.m||(e.m=g()),e.u||(e.u=g()),e.f||(e.f=v()),e),x=d},7055:(e,t,r)=>{"use strict";r.d(t,{O4:()=>i});var n=r(2584);let o=Symbol("RESET"),a=e=>"function"==typeof(null==e?void 0:e.then),l=function(e=()=>{try{return window.localStorage}catch(e){"undefined"!=typeof window&&console.warn(e);return}},t){var r,n;let o,l,i;let s={getItem:(r,n)=>{var i,s;let u=e=>{if(o!==(e=e||"")){try{l=JSON.parse(e,null==t?void 0:t.reviver)}catch(e){return n}o=e}return l},c=null!=(s=null==(i=e())?void 0:i.getItem(r))?s:null;return a(c)?c.then(u):u(c)},setItem:(r,n)=>{var o;return null==(o=e())?void 0:o.setItem(r,JSON.stringify(n,null==t?void 0:t.replacer))},removeItem:t=>{var r;return null==(r=e())?void 0:r.removeItem(t)}};try{i=null==(r=e())?void 0:r.subscribe}catch(e){}return!i&&"undefined"!=typeof window&&"function"==typeof window.addEventListener&&window.Storage&&(i=(t,r)=>{if(!(e() instanceof window.Storage))return()=>{};let n=n=>{n.storageArea===e()&&n.key===t&&r(n.newValue)};return window.addEventListener("storage",n),()=>{window.removeEventListener("storage",n)}}),i&&(s.subscribe=(n=i,(e,t,r)=>n(e,e=>{let n;try{n=JSON.parse(e||"")}catch(e){n=r}t(n)}))),s}();function i(e,t,r=l,i){let s=null==i?void 0:i.getOnInit,u=(0,n.cn)(s?r.getItem(e,t):t);u.debugPrivate=!0,u.onMount=n=>{let o;return n(r.getItem(e,t)),r.subscribe&&(o=r.subscribe(e,n,t)),o};let c=(0,n.cn)(e=>e(u),(n,l,i)=>{let s="function"==typeof i?i(n(u)):i;return s===o?(l(u,t),r.removeItem(e)):a(s)?s.then(t=>(l(u,t),r.setItem(e,t))):(l(u,s),r.setItem(e,s))});return c}},516:(e,t,r)=>{"use strict";r.d(t,{F:()=>c,f:()=>d});var n=r(8726),o=(e,t,r,n,o,a,l,i)=>{let s=document.documentElement,u=["light","dark"];function c(t){(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(s.classList.remove(...n),s.classList.add(a&&a[t]?a[t]:t)):s.setAttribute(e,t)}),i&&u.includes(t)&&(s.style.colorScheme=t)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},a=["light","dark"],l="(prefers-color-scheme: dark)",i="undefined"==typeof window,s=n.createContext(void 0),u={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=n.useContext(s))?e:u},d=e=>n.useContext(s)?n.createElement(n.Fragment,null,e.children):n.createElement(p,{...e}),f=["light","dark"],p=({forcedTheme:e,disableTransitionOnChange:t=!1,enableSystem:r=!0,enableColorScheme:o=!0,storageKey:i="theme",themes:u=f,defaultTheme:c=r?"system":"light",attribute:d="data-theme",value:p,children:y,nonce:b,scriptProps:w})=>{let[x,E]=n.useState(()=>m(i,c)),[_,R]=n.useState(()=>"system"===x?g():x),S=p?Object.values(p):u,P=n.useCallback(e=>{let n=e;if(!n)return;"system"===e&&r&&(n=g());let l=p?p[n]:n,i=t?v(b):null,s=document.documentElement,u=e=>{"class"===e?(s.classList.remove(...S),l&&s.classList.add(l)):e.startsWith("data-")&&(l?s.setAttribute(e,l):s.removeAttribute(e))};if(Array.isArray(d)?d.forEach(u):u(d),o){let e=a.includes(c)?c:null,t=a.includes(n)?n:e;s.style.colorScheme=t}null==i||i()},[b]),C=n.useCallback(e=>{let t="function"==typeof e?e(x):e;E(t);try{localStorage.setItem(i,t)}catch(e){}},[x]),j=n.useCallback(t=>{R(g(t)),"system"===x&&r&&!e&&P("system")},[x,e]);n.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(j),j(e),()=>e.removeListener(j)},[j]),n.useEffect(()=>{let e=e=>{e.key===i&&(e.newValue?E(e.newValue):C(c))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),n.useEffect(()=>{P(null!=e?e:x)},[e,x]);let O=n.useMemo(()=>({theme:x,setTheme:C,forcedTheme:e,resolvedTheme:"system"===x?_:x,themes:r?[...u,"system"]:u,systemTheme:r?_:void 0}),[x,C,e,_,r,u]);return n.createElement(s.Provider,{value:O},n.createElement(h,{forcedTheme:e,storageKey:i,attribute:d,enableSystem:r,enableColorScheme:o,defaultTheme:c,value:p,themes:u,nonce:b,scriptProps:w}),y)},h=n.memo(({forcedTheme:e,storageKey:t,attribute:r,enableSystem:a,enableColorScheme:l,defaultTheme:i,value:s,themes:u,nonce:c,scriptProps:d})=>{let f=JSON.stringify([r,t,i,e,u,s,a,l]).slice(1,-1);return n.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:`(${o.toString()})(${f})`}})}),m=(e,t)=>{let r;if(!i){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},v=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},7999:(e,t,r)=>{"use strict";r.d(t,{Am:()=>g,x7:()=>E});var n=r(8726),o=r(1298),a=e=>{switch(e){case"success":return s;case"info":return c;case"warning":return u;case"error":return d;default:return null}},l=Array(12).fill(0),i=({visible:e,className:t})=>n.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},n.createElement("div",{className:"sonner-spinner"},l.map((e,t)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),s=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),p=()=>{let[e,t]=n.useState(document.hidden);return n.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},h=1,m=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,o="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:h++,a=this.toasts.find(e=>e.id===o),l=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(o)&&this.dismissedToasts.delete(o),a?this.toasts=this.toasts.map(t=>t.id===o?(this.publish({...t,...e,id:o,title:r}),{...t,...e,id:o,dismissible:l,title:r}):t):this.addToast({title:r,...n,dismissible:l,id:o}),o},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r;if(!t)return;void 0!==t.loading&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let n=e instanceof Promise?e:e(),o=void 0!==r,a,l=n.then(async e=>{if(a=["resolve",e],re.isValidElement(e))o=!1,this.create({id:r,type:"default",message:e});else if(v(e)&&!e.ok){o=!1;let n="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,a="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:r,type:"error",message:n,description:a})}else if(void 0!==t.success){o=!1;let n="function"==typeof t.success?await t.success(e):t.success,a="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"success",message:n,description:a})}}).catch(async e=>{if(a=["reject",e],void 0!==t.error){o=!1;let n="function"==typeof t.error?await t.error(e):t.error,a="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"error",message:n,description:a})}}).finally(()=>{var e;o&&(this.dismiss(r),r=void 0),null==(e=t.finally)||e.call(t)}),i=()=>new Promise((e,t)=>l.then(()=>"reject"===a[0]?t(a[1]):e(a[1])).catch(t));return"string"!=typeof r&&"number"!=typeof r?{unwrap:i}:Object.assign(r,{unwrap:i})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||h++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},v=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,g=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||h++;return m.addToast({title:e,...t,id:r}),r},{success:m.success,info:m.info,warning:m.warning,error:m.error,custom:m.custom,message:m.message,promise:m.promise,dismiss:m.dismiss,loading:m.loading},{getHistory:()=>m.toasts,getToasts:()=>m.getActiveToasts()});function y(e){return void 0!==e.label}function b(...e){return e.filter(Boolean).join(" ")}!function(e,{insertAt:t}={}){if(!e||"undefined"==typeof document)return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===t&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var w=e=>{var t,r,o,l,s,u,c,d,h,m,v;let{invert:g,toast:w,unstyled:x,interacting:E,setHeights:_,visibleToasts:R,heights:S,index:P,toasts:C,expanded:j,removeToast:O,defaultRichColors:M,closeButton:T,style:N,cancelButtonStyle:k,actionButtonStyle:A,className:I="",descriptionClassName:D="",duration:L,position:F,gap:W,loadingIcon:B,expandByDefault:U,classNames:z,icons:H,closeButtonAriaLabel:V="Close toast",pauseWhenPageIsHidden:$}=e,[Y,K]=n.useState(null),[G,X]=n.useState(null),[q,Z]=n.useState(!1),[J,Q]=n.useState(!1),[ee,et]=n.useState(!1),[er,en]=n.useState(!1),[eo,ea]=n.useState(!1),[el,ei]=n.useState(0),[es,eu]=n.useState(0),ec=n.useRef(w.duration||L||4e3),ed=n.useRef(null),ef=n.useRef(null),ep=w.type,eh=!1!==w.dismissible,em=w.className||"",ev=w.descriptionClassName||"",eg=n.useMemo(()=>S.findIndex(e=>e.toastId===w.id)||0,[S,w.id]),ey=n.useMemo(()=>{var e;return null!=(e=w.closeButton)?e:T},[w.closeButton,T]),eb=n.useMemo(()=>w.duration||L||4e3,[w.duration,L]),ew=n.useRef(0),ex=n.useRef(0),eE=n.useRef(0),e_=n.useRef(null),[eR,eS]=F.split("-"),eP=n.useMemo(()=>S.reduce((e,t,r)=>r>=eg?e:e+t.height,0),[S,eg]),eC=p(),ej=w.invert||g,eO="loading"===ep;ex.current=n.useMemo(()=>eg*W+eP,[eg,eP]),n.useEffect(()=>{ec.current=eb},[eb]),n.useEffect(()=>{Z(!0)},[]),n.useEffect(()=>{let e=ef.current;if(e){let t=e.getBoundingClientRect().height;return eu(t),_(e=>[{toastId:w.id,height:t,position:w.position},...e]),()=>_(e=>e.filter(e=>e.toastId!==w.id))}},[_,w.id]),n.useLayoutEffect(()=>{if(!q)return;let e=ef.current,t=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=t,eu(r),_(e=>e.find(e=>e.toastId===w.id)?e.map(e=>e.toastId===w.id?{...e,height:r}:e):[{toastId:w.id,height:r,position:w.position},...e])},[q,w.title,w.description,_,w.id]);let eM=n.useCallback(()=>{Q(!0),ei(ex.current),_(e=>e.filter(e=>e.toastId!==w.id)),setTimeout(()=>{O(w)},200)},[w,O,_,ex]);return n.useEffect(()=>{let e;if((!w.promise||"loading"!==ep)&&w.duration!==1/0&&"loading"!==w.type)return j||E||$&&eC?(()=>{if(eE.current<ew.current){let e=new Date().getTime()-ew.current;ec.current=ec.current-e}eE.current=new Date().getTime()})():ec.current!==1/0&&(ew.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=w.onAutoClose)||e.call(w,w),eM()},ec.current)),()=>clearTimeout(e)},[j,E,w,ep,$,eC,eM]),n.useEffect(()=>{w.delete&&eM()},[eM,w.delete]),n.createElement("li",{tabIndex:0,ref:ef,className:b(I,em,null==z?void 0:z.toast,null==(t=null==w?void 0:w.classNames)?void 0:t.toast,null==z?void 0:z.default,null==z?void 0:z[ep],null==(r=null==w?void 0:w.classNames)?void 0:r[ep]),"data-sonner-toast":"","data-rich-colors":null!=(o=w.richColors)?o:M,"data-styled":!(w.jsx||w.unstyled||x),"data-mounted":q,"data-promise":!!w.promise,"data-swiped":eo,"data-removed":J,"data-visible":P+1<=R,"data-y-position":eR,"data-x-position":eS,"data-index":P,"data-front":0===P,"data-swiping":ee,"data-dismissible":eh,"data-type":ep,"data-invert":ej,"data-swipe-out":er,"data-swipe-direction":G,"data-expanded":!!(j||U&&q),style:{"--index":P,"--toasts-before":P,"--z-index":C.length-P,"--offset":`${J?el:ex.current}px`,"--initial-height":U?"auto":`${es}px`,...N,...w.style},onDragEnd:()=>{et(!1),K(null),e_.current=null},onPointerDown:e=>{eO||!eh||(ed.current=new Date,ei(ex.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(et(!0),e_.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,r,n;if(er||!eh)return;e_.current=null;let o=Number((null==(e=ef.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),a=Number((null==(t=ef.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),l=new Date().getTime()-(null==(r=ed.current)?void 0:r.getTime()),i="x"===Y?o:a;if(Math.abs(i)>=20||Math.abs(i)/l>.11){ei(ex.current),null==(n=w.onDismiss)||n.call(w,w),X("x"===Y?o>0?"right":"left":a>0?"down":"up"),eM(),en(!0),ea(!1);return}et(!1),K(null)},onPointerMove:t=>{var r,n,o,a;if(!e_.current||!eh||(null==(r=window.getSelection())?void 0:r.toString().length)>0)return;let l=t.clientY-e_.current.y,i=t.clientX-e_.current.x,s=null!=(n=e.swipeDirections)?n:function(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}(F);!Y&&(Math.abs(i)>1||Math.abs(l)>1)&&K(Math.abs(i)>Math.abs(l)?"x":"y");let u={x:0,y:0};"y"===Y?(s.includes("top")||s.includes("bottom"))&&(s.includes("top")&&l<0||s.includes("bottom")&&l>0)&&(u.y=l):"x"===Y&&(s.includes("left")||s.includes("right"))&&(s.includes("left")&&i<0||s.includes("right")&&i>0)&&(u.x=i),(Math.abs(u.x)>0||Math.abs(u.y)>0)&&ea(!0),null==(o=ef.current)||o.style.setProperty("--swipe-amount-x",`${u.x}px`),null==(a=ef.current)||a.style.setProperty("--swipe-amount-y",`${u.y}px`)}},ey&&!w.jsx?n.createElement("button",{"aria-label":V,"data-disabled":eO,"data-close-button":!0,onClick:eO||!eh?()=>{}:()=>{var e;eM(),null==(e=w.onDismiss)||e.call(w,w)},className:b(null==z?void 0:z.closeButton,null==(l=null==w?void 0:w.classNames)?void 0:l.closeButton)},null!=(s=null==H?void 0:H.close)?s:f):null,w.jsx||(0,n.isValidElement)(w.title)?w.jsx?w.jsx:"function"==typeof w.title?w.title():w.title:n.createElement(n.Fragment,null,ep||w.icon||w.promise?n.createElement("div",{"data-icon":"",className:b(null==z?void 0:z.icon,null==(u=null==w?void 0:w.classNames)?void 0:u.icon)},w.promise||"loading"===w.type&&!w.icon?w.icon||function(){var e,t,r;return null!=H&&H.loading?n.createElement("div",{className:b(null==z?void 0:z.loader,null==(e=null==w?void 0:w.classNames)?void 0:e.loader,"sonner-loader"),"data-visible":"loading"===ep},H.loading):B?n.createElement("div",{className:b(null==z?void 0:z.loader,null==(t=null==w?void 0:w.classNames)?void 0:t.loader,"sonner-loader"),"data-visible":"loading"===ep},B):n.createElement(i,{className:b(null==z?void 0:z.loader,null==(r=null==w?void 0:w.classNames)?void 0:r.loader),visible:"loading"===ep})}():null,"loading"!==w.type?w.icon||(null==H?void 0:H[ep])||a(ep):null):null,n.createElement("div",{"data-content":"",className:b(null==z?void 0:z.content,null==(c=null==w?void 0:w.classNames)?void 0:c.content)},n.createElement("div",{"data-title":"",className:b(null==z?void 0:z.title,null==(d=null==w?void 0:w.classNames)?void 0:d.title)},"function"==typeof w.title?w.title():w.title),w.description?n.createElement("div",{"data-description":"",className:b(D,ev,null==z?void 0:z.description,null==(h=null==w?void 0:w.classNames)?void 0:h.description)},"function"==typeof w.description?w.description():w.description):null),(0,n.isValidElement)(w.cancel)?w.cancel:w.cancel&&y(w.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:w.cancelButtonStyle||k,onClick:e=>{var t,r;y(w.cancel)&&eh&&(null==(r=(t=w.cancel).onClick)||r.call(t,e),eM())},className:b(null==z?void 0:z.cancelButton,null==(m=null==w?void 0:w.classNames)?void 0:m.cancelButton)},w.cancel.label):null,(0,n.isValidElement)(w.action)?w.action:w.action&&y(w.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:w.actionButtonStyle||A,onClick:e=>{var t,r;y(w.action)&&(null==(r=(t=w.action).onClick)||r.call(t,e),e.defaultPrevented||eM())},className:b(null==z?void 0:z.actionButton,null==(v=null==w?void 0:w.classNames)?void 0:v.actionButton)},w.action.label):null))};function x(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}var E=(0,n.forwardRef)(function(e,t){let{invert:r,position:a="bottom-right",hotkey:l=["altKey","KeyT"],expand:i,closeButton:s,className:u,offset:c,mobileOffset:d,theme:f="light",richColors:p,duration:h,style:v,visibleToasts:g=3,toastOptions:y,dir:b=x(),gap:E=14,loadingIcon:_,icons:R,containerAriaLabel:S="Notifications",pauseWhenPageIsHidden:P}=e,[C,j]=n.useState([]),O=n.useMemo(()=>Array.from(new Set([a].concat(C.filter(e=>e.position).map(e=>e.position)))),[C,a]),[M,T]=n.useState([]),[N,k]=n.useState(!1),[A,I]=n.useState(!1),[D,L]=n.useState("system"!==f?f:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),F=n.useRef(null),W=l.join("+").replace(/Key/g,"").replace(/Digit/g,""),B=n.useRef(null),U=n.useRef(!1),z=n.useCallback(e=>{j(t=>{var r;return null!=(r=t.find(t=>t.id===e.id))&&r.delete||m.dismiss(e.id),t.filter(({id:t})=>t!==e.id)})},[]);return n.useEffect(()=>m.subscribe(e=>{if(e.dismiss){j(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));return}setTimeout(()=>{o.flushSync(()=>{j(t=>{let r=t.findIndex(t=>t.id===e.id);return -1!==r?[...t.slice(0,r),{...t[r],...e},...t.slice(r+1)]:[e,...t]})})})}),[]),n.useEffect(()=>{if("system"!==f){L(f);return}if("system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?L("dark"):L("light")),"undefined"==typeof window)return;let e=window.matchMedia("(prefers-color-scheme: dark)");try{e.addEventListener("change",({matches:e})=>{L(e?"dark":"light")})}catch(t){e.addListener(({matches:e})=>{try{L(e?"dark":"light")}catch(e){console.error(e)}})}},[f]),n.useEffect(()=>{C.length<=1&&k(!1)},[C]),n.useEffect(()=>{let e=e=>{var t,r;l.every(t=>e[t]||e.code===t)&&(k(!0),null==(t=F.current)||t.focus()),"Escape"===e.code&&(document.activeElement===F.current||null!=(r=F.current)&&r.contains(document.activeElement))&&k(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[l]),n.useEffect(()=>{if(F.current)return()=>{B.current&&(B.current.focus({preventScroll:!0}),B.current=null,U.current=!1)}},[F.current]),n.createElement("section",{ref:t,"aria-label":`${S} ${W}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},O.map((t,o)=>{var a;let l;let[f,m]=t.split("-");return C.length?n.createElement("ol",{key:t,dir:"auto"===b?x():b,tabIndex:-1,ref:F,className:u,"data-sonner-toaster":!0,"data-theme":D,"data-y-position":f,"data-lifted":N&&C.length>1&&!i,"data-x-position":m,style:{"--front-toast-height":`${(null==(a=M[0])?void 0:a.height)||0}px`,"--width":"356px","--gap":`${E}px`,...v,...(l={},[c,d].forEach((e,t)=>{let r=1===t,n=r?"--mobile-offset":"--offset",o=r?"16px":"32px";function a(e){["top","right","bottom","left"].forEach(t=>{l[`${n}-${t}`]="number"==typeof e?`${e}px`:e})}"number"==typeof e||"string"==typeof e?a(e):"object"==typeof e?["top","right","bottom","left"].forEach(t=>{void 0===e[t]?l[`${n}-${t}`]=o:l[`${n}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]}):a(o)}),l)},onBlur:e=>{U.current&&!e.currentTarget.contains(e.relatedTarget)&&(U.current=!1,B.current&&(B.current.focus({preventScroll:!0}),B.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||U.current||(U.current=!0,B.current=e.relatedTarget)},onMouseEnter:()=>k(!0),onMouseMove:()=>k(!0),onMouseLeave:()=>{A||k(!1)},onDragEnd:()=>k(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||I(!0)},onPointerUp:()=>I(!1)},C.filter(e=>!e.position&&0===o||e.position===t).map((o,a)=>{var l,u;return n.createElement(w,{key:o.id,icons:R,index:a,toast:o,defaultRichColors:p,duration:null!=(l=null==y?void 0:y.duration)?l:h,className:null==y?void 0:y.className,descriptionClassName:null==y?void 0:y.descriptionClassName,invert:r,visibleToasts:g,closeButton:null!=(u=null==y?void 0:y.closeButton)?u:s,interacting:A,position:t,style:null==y?void 0:y.style,unstyled:null==y?void 0:y.unstyled,classNames:null==y?void 0:y.classNames,cancelButtonStyle:null==y?void 0:y.cancelButtonStyle,actionButtonStyle:null==y?void 0:y.actionButtonStyle,removeToast:z,toasts:C.filter(e=>e.position==o.position),heights:M.filter(e=>e.position==o.position),setHeights:T,expandByDefault:i,gap:E,loadingIcon:_,expanded:N,pauseWhenPageIsHidden:P,swipeDirections:e.swipeDirections})})):null}))})},1980:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__addDisposableResource:()=>I,__assign:()=>a,__asyncDelegator:()=>S,__asyncGenerator:()=>R,__asyncValues:()=>P,__await:()=>_,__awaiter:()=>h,__classPrivateFieldGet:()=>N,__classPrivateFieldIn:()=>A,__classPrivateFieldSet:()=>k,__createBinding:()=>v,__decorate:()=>i,__disposeResources:()=>L,__esDecorate:()=>u,__exportStar:()=>g,__extends:()=>o,__generator:()=>m,__importDefault:()=>T,__importStar:()=>M,__makeTemplateObject:()=>C,__metadata:()=>p,__param:()=>s,__propKey:()=>d,__read:()=>b,__rest:()=>l,__rewriteRelativeImportExtension:()=>F,__runInitializers:()=>c,__setFunctionName:()=>f,__spread:()=>w,__spreadArray:()=>E,__spreadArrays:()=>x,__values:()=>y,default:()=>W});var n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var a=function(){return(a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function i(e,t,r,n){var o,a=arguments.length,l=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,r,n);else for(var i=e.length-1;i>=0;i--)(o=e[i])&&(l=(a<3?o(l):a>3?o(t,r,l):o(t,r))||l);return a>3&&l&&Object.defineProperty(t,r,l),l}function s(e,t){return function(r,n){t(r,n,e)}}function u(e,t,r,n,o,a){function l(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var i,s=n.kind,u="getter"===s?"get":"setter"===s?"set":"value",c=!t&&e?n.static?e:e.prototype:null,d=t||(c?Object.getOwnPropertyDescriptor(c,n.name):{}),f=!1,p=r.length-1;p>=0;p--){var h={};for(var m in n)h[m]="access"===m?{}:n[m];for(var m in n.access)h.access[m]=n.access[m];h.addInitializer=function(e){if(f)throw TypeError("Cannot add initializers after decoration has completed");a.push(l(e||null))};var v=(0,r[p])("accessor"===s?{get:d.get,set:d.set}:d[u],h);if("accessor"===s){if(void 0===v)continue;if(null===v||"object"!=typeof v)throw TypeError("Object expected");(i=l(v.get))&&(d.get=i),(i=l(v.set))&&(d.set=i),(i=l(v.init))&&o.unshift(i)}else(i=l(v))&&("field"===s?o.unshift(i):d[u]=i)}c&&Object.defineProperty(c,n.name,d),f=!0}function c(e,t,r){for(var n=arguments.length>2,o=0;o<t.length;o++)r=n?t[o].call(e,r):t[o].call(e);return n?r:void 0}function d(e){return"symbol"==typeof e?e:"".concat(e)}function f(e,t,r){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function h(e,t,r,n){return new(r||(r=Promise))(function(o,a){function l(e){try{s(n.next(e))}catch(e){a(e)}}function i(e){try{s(n.throw(e))}catch(e){a(e)}}function s(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(l,i)}s((n=n.apply(e,t||[])).next())})}function m(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},l=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return l.next=i(0),l.throw=i(1),l.return=i(2),"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l;function i(i){return function(s){return function(i){if(r)throw TypeError("Generator is already executing.");for(;l&&(l=0,i[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}var v=Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]};function g(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||v(t,e,r)}function y(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function b(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),l=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)l.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return l}function w(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(b(arguments[t]));return e}function x(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),o=0,t=0;t<r;t++)for(var a=arguments[t],l=0,i=a.length;l<i;l++,o++)n[o]=a[l];return n}function E(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function _(e){return this instanceof _?(this.v=e,this):new _(e)}function R(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),a=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),l("next"),l("throw"),l("return",function(e){return function(t){return Promise.resolve(t).then(e,u)}}),n[Symbol.asyncIterator]=function(){return this},n;function l(e,t){o[e]&&(n[e]=function(t){return new Promise(function(r,n){a.push([e,t,r,n])>1||i(e,t)})},t&&(n[e]=t(n[e])))}function i(e,t){try{var r;(r=o[e](t)).value instanceof _?Promise.resolve(r.value.v).then(s,u):c(a[0][2],r)}catch(e){c(a[0][3],e)}}function s(e){i("next",e)}function u(e){i("throw",e)}function c(e,t){e(t),a.shift(),a.length&&i(a[0][0],a[0][1])}}function S(e){var t,r;return t={},n("next"),n("throw",function(e){throw e}),n("return"),t[Symbol.iterator]=function(){return this},t;function n(n,o){t[n]=e[n]?function(t){return(r=!r)?{value:_(e[n](t)),done:!1}:o?o(t):t}:o}}function P(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=y(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){(function(e,t,r,n){Promise.resolve(n).then(function(t){e({value:t,done:r})},t)})(n,o,(t=e[r](t)).done,t.value)})}}}function C(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var j=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},O=function(e){return(O=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)};function M(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=O(e),n=0;n<r.length;n++)"default"!==r[n]&&v(t,e,r[n]);return j(t,e),t}function T(e){return e&&e.__esModule?e:{default:e}}function N(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}function k(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r}function A(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function I(e,t,r){if(null!=t){var n,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(r){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(void 0===n){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(o=n)}if("function"!=typeof n)throw TypeError("Object not disposable.");o&&(n=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var D="function"==typeof SuppressedError?SuppressedError:function(e,t,r){var n=Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};function L(e){function t(t){e.error=e.hasError?new D(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var r,n=0;return function o(){for(;r=e.stack.pop();)try{if(!r.async&&1===n)return n=0,e.stack.push(r),Promise.resolve().then(o);if(r.dispose){var a=r.dispose.call(r.value);if(r.async)return n|=2,Promise.resolve(a).then(o,function(e){return t(e),o()})}else n|=1}catch(e){t(e)}if(1===n)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function F(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,r,n,o,a){return r?t?".jsx":".js":!n||o&&a?n+o+"."+a.toLowerCase()+"js":e}):e}let W={__extends:o,__assign:a,__rest:l,__decorate:i,__param:s,__esDecorate:u,__runInitializers:c,__propKey:d,__setFunctionName:f,__metadata:p,__awaiter:h,__generator:m,__createBinding:v,__exportStar:g,__values:y,__read:b,__spread:w,__spreadArrays:x,__spreadArray:E,__await:_,__asyncGenerator:R,__asyncDelegator:S,__asyncValues:P,__makeTemplateObject:C,__importStar:M,__importDefault:T,__classPrivateFieldGet:N,__classPrivateFieldSet:k,__classPrivateFieldIn:A,__addDisposableResource:I,__disposeResources:L,__rewriteRelativeImportExtension:F}}};