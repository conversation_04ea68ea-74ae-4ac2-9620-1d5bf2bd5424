(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function i(e){var t;const r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function c(e){const t=new Map;for(const r of e.split(/; */)){if(!r)continue;const e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}const[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function l(e){if(!e)return;const[[t,r],...n]=c(e),{domain:o,expires:s,httponly:a,maxage:i,path:l,samesite:u,secure:p}=Object.fromEntries(n.map((([e,t])=>[e.toLowerCase(),t])));return function(e){const t={};for(const r in e)e[r]&&(t[r]=e[r]);return t}({name:t,value:decodeURIComponent(r),domain:o,...s&&{expires:new Date(s)},...a&&{httpOnly:!0},..."string"==typeof i&&{maxAge:Number(i)},path:l,...u&&{sameSite:(f=u,f=f.toLowerCase(),d.includes(f)?f:void 0)},...p&&{secure:!0}});var f}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{RequestCookies:()=>p,ResponseCookies:()=>f,parseCookie:()=>c,parseSetCookie:()=>l,splitCookiesString:()=>u,stringifyCookie:()=>i}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of o(t))s.call(e,a)||undefined===a||r(e,a,{get:()=>t[a],enumerable:!(i=n(t,a))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t));var d=["strict","lax","none"];function u(e){if(!e)return[];var t,r,n,o,s,a=[],i=0;function c(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,s=!1;c();)if(","===(r=e.charAt(i))){for(n=i,i+=1,c(),o=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(s=!0,i=o,a.push(e.substring(t,n)),t=i):i=n+1}else i+=1;(!s||i>=e.length)&&a.push(e.substring(t,e.length))}return a}var p=class{constructor(e){this._parsed=new Map,this._headers=e;const t=e.get("cookie");if(t){const e=c(t);for(const[t,r]of e)this._parsed.set(t,{name:t,value:r})}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){const t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;const r=Array.from(this._parsed);if(!e.length)return r.map((([e,t])=>t));const n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter((([e])=>e===n)).map((([e,t])=>t))}has(e){return this._parsed.has(e)}set(...e){const[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map((([e,t])=>i(t))).join("; ")),this}delete(e){const t=this._parsed,r=Array.isArray(e)?e.map((e=>t.delete(e))):t.delete(e);return this._headers.set("cookie",Array.from(t).map((([e,t])=>i(t))).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map((e=>`${e.name}=${encodeURIComponent(e.value)}`)).join("; ")}},f=class{constructor(e){var t;this._parsed=new Map,this._headers=e;const r=null==(t=e.getSetCookie)?void 0:t.call(e);e.get("set-cookie");const n=Array.isArray(r)?r:u(r);for(const e of n){const t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){const t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;const r=Array.from(this._parsed.values());if(!e.length)return r;const n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter((e=>e.name===n))}has(e){return this._parsed.has(e)}set(...e){const[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),null!==e.path&&void 0!==e.path||(e.path="/"),e}({name:t,value:r,...n})),function(e,t){t.delete("set-cookie");for(const[,r]of e){const e=i(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){const[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},"./dist/compiled/bytes/index.js":e=>{(()=>{"use strict";var t={56:e=>{e.exports=function(e,t){return"string"==typeof e?a(e):"number"==typeof e?s(e,t):null},e.exports.format=s,e.exports.parse=a;var t=/\B(?=(\d{3})+(?!\d))/g,r=/(?:\.0*|(\.[^0]+)0+)$/,n={b:1,kb:1024,mb:1<<20,gb:1<<30,tb:Math.pow(1024,4),pb:Math.pow(1024,5)},o=/^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb|pb)$/i;function s(e,o){if(!Number.isFinite(e))return null;var s=Math.abs(e),a=o&&o.thousandsSeparator||"",i=o&&o.unitSeparator||"",c=o&&void 0!==o.decimalPlaces?o.decimalPlaces:2,l=Boolean(o&&o.fixedDecimals),d=o&&o.unit||"";d&&n[d.toLowerCase()]||(d=s>=n.pb?"PB":s>=n.tb?"TB":s>=n.gb?"GB":s>=n.mb?"MB":s>=n.kb?"KB":"B");var u=(e/n[d.toLowerCase()]).toFixed(c);return l||(u=u.replace(r,"$1")),a&&(u=u.split(".").map((function(e,r){return 0===r?e.replace(t,a):e})).join(".")),u+i+d}function a(e){if("number"==typeof e&&!isNaN(e))return e;if("string"!=typeof e)return null;var t,r=o.exec(e),s="b";return r?(t=parseFloat(r[1]),s=r[4].toLowerCase()):(t=parseInt(e,10),s="b"),Math.floor(n[s]*t)}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}void 0!==n&&(n.ab=__dirname+"/");var o=n(56);e.exports=o})()},"./dist/compiled/content-type/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{var e=t,r=/; *([!#$%&'*+.^_`|~0-9A-Za-z-]+) *= *("(?:[\u000b\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\u000b\u0020-\u00ff])*"|[!#$%&'*+.^_`|~0-9A-Za-z-]+) */g,n=/^[\u000b\u0020-\u007e\u0080-\u00ff]+$/,o=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+$/,s=/\\([\u000b\u0020-\u00ff])/g,a=/([\\"])/g,i=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;function c(e){var t=String(e);if(o.test(t))return t;if(t.length>0&&!n.test(t))throw new TypeError("invalid parameter value");return'"'+t.replace(a,"\\$1")+'"'}function l(e){this.parameters=Object.create(null),this.type=e}e.format=function(e){if(!e||"object"!=typeof e)throw new TypeError("argument obj is required");var t=e.parameters,r=e.type;if(!r||!i.test(r))throw new TypeError("invalid type");var n=r;if(t&&"object"==typeof t)for(var s,a=Object.keys(t).sort(),l=0;l<a.length;l++){if(s=a[l],!o.test(s))throw new TypeError("invalid parameter name");n+="; "+s+"="+c(t[s])}return n},e.parse=function(e){if(!e)throw new TypeError("argument string is required");var t="object"==typeof e?function(e){var t;if("function"==typeof e.getHeader?t=e.getHeader("content-type"):"object"==typeof e.headers&&(t=e.headers&&e.headers["content-type"]),"string"!=typeof t)throw new TypeError("content-type header is missing from object");return t}(e):e;if("string"!=typeof t)throw new TypeError("argument string is required to be a string");var n=t.indexOf(";"),o=-1!==n?t.substr(0,n).trim():t.trim();if(!i.test(o))throw new TypeError("invalid media type");var a=new l(o.toLowerCase());if(-1!==n){var c,d,u;for(r.lastIndex=n;d=r.exec(t);){if(d.index!==n)throw new TypeError("invalid parameter format");n+=d[0].length,c=d[1].toLowerCase(),'"'===(u=d[2])[0]&&(u=u.substr(1,u.length-2).replace(s,"$1")),a.parameters[c]=u}if(n!==t.length)throw new TypeError("invalid parameter format")}return a}})(),e.exports=t})()},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{var e=t;e.parse=function(e,t){if("string"!=typeof e)throw new TypeError("argument str must be a string");for(var n={},s=t||{},i=e.split(o),c=s.decode||r,l=0;l<i.length;l++){var d=i[l],u=d.indexOf("=");if(!(u<0)){var p=d.substr(0,u).trim(),f=d.substr(++u,d.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),null==n[p]&&(n[p]=a(f,c))}}return n},e.serialize=function(e,t,r){var o=r||{},a=o.encode||n;if("function"!=typeof a)throw new TypeError("option encode is invalid");if(!s.test(e))throw new TypeError("argument name is invalid");var i=a(t);if(i&&!s.test(i))throw new TypeError("argument val is invalid");var c=e+"="+i;if(null!=o.maxAge){var l=o.maxAge-0;if(isNaN(l)||!isFinite(l))throw new TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(o.domain){if(!s.test(o.domain))throw new TypeError("option domain is invalid");c+="; Domain="+o.domain}if(o.path){if(!s.test(o.path))throw new TypeError("option path is invalid");c+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw new TypeError("option expires is invalid");c+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(c+="; HttpOnly"),o.secure&&(c+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"strict":c+="; SameSite=Strict";break;case"none":c+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return c};var r=decodeURIComponent,n=encodeURIComponent,o=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function a(e,t){try{return t(e)}catch(t){return e}}})(),e.exports=t})()},"./dist/compiled/fresh/index.js":e=>{(()=>{"use strict";var t={695:e=>{var t=/(?:^|,)\s*?no-cache\s*?(?:,|$)/;function r(e){var t=e&&Date.parse(e);return"number"==typeof t?t:NaN}e.exports=function(e,n){var o=e["if-modified-since"],s=e["if-none-match"];if(!o&&!s)return!1;var a=e["cache-control"];if(a&&t.test(a))return!1;if(s&&"*"!==s){var i=n.etag;if(!i)return!1;for(var c=!0,l=function(e){for(var t=0,r=[],n=0,o=0,s=e.length;o<s;o++)switch(e.charCodeAt(o)){case 32:n===t&&(n=t=o+1);break;case 44:r.push(e.substring(n,t)),n=t=o+1;break;default:t=o+1}return r.push(e.substring(n,t)),r}(s),d=0;d<l.length;d++){var u=l[d];if(u===i||u==="W/"+i||"W/"+u===i){c=!1;break}}if(c)return!1}if(o){var p=n["last-modified"];if(!(p&&r(p)<=r(o)))return!1}return!0}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}void 0!==n&&(n.ab=__dirname+"/");var o=n(695);e.exports=o})()},"./dist/compiled/react-is/cjs/react-is.production.min.js":(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),d=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case i:case a:case p:case f:return e;default:switch(e=e&&e.$$typeof){case d:case l:case u:case h:case m:case c:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.ContextConsumer=l,t.ContextProvider=c,t.Element=n,t.ForwardRef=u,t.Fragment=s,t.Lazy=h,t.Memo=m,t.Portal=o,t.Profiler=i,t.StrictMode=a,t.Suspense=p,t.SuspenseList=f,t.isAsyncMode=function(){return!1},t.isConcurrentMode=function(){return!1},t.isContextConsumer=function(e){return v(e)===l},t.isContextProvider=function(e){return v(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return v(e)===u},t.isFragment=function(e){return v(e)===s},t.isLazy=function(e){return v(e)===h},t.isMemo=function(e){return v(e)===m},t.isPortal=function(e){return v(e)===o},t.isProfiler=function(e){return v(e)===i},t.isStrictMode=function(e){return v(e)===a},t.isSuspense=function(e){return v(e)===p},t.isSuspenseList=function(e){return v(e)===f},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===i||e===a||e===p||e===f||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===m||e.$$typeof===c||e.$$typeof===l||e.$$typeof===u||e.$$typeof===r||void 0!==e.getModuleId)},t.typeOf=v},"./dist/compiled/react-is/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react-is/cjs/react-is.production.min.js")},"./dist/compiled/strip-ansi/index.js":e=>{(()=>{"use strict";var t={511:e=>{e.exports=({onlyFirst:e=!1}={})=>{const t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}},532:(e,t,r)=>{const n=r(511);e.exports=e=>"string"==typeof e?e.replace(n(),""):e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}void 0!==n&&(n.ab=__dirname+"/");var o=n(532);e.exports=o})()},"./dist/esm/build/output/log.js":(e,t,r)=>{"use strict";let n;r.d(t,{ZK:()=>i}),n=r("./dist/esm/lib/web/chalk.js").Z;const o=n,s={wait:o.white(o.bold("○")),error:o.red(o.bold("X")),warn:o.yellow(o.bold("⚠")),ready:o.bold("▲"),info:o.white(o.bold(" ")),event:o.green(o.bold("✓")),trace:o.magenta(o.bold("»"))},a={log:"log",warn:"warn",error:"error"};function i(...e){!function(e,...t){""!==t[0]&&void 0!==t[0]||1!==t.length||t.shift();const r=e in a?a[e]:"log",n=s[e];0===t.length?console[r](""):console[r](" "+n,...t)}("warn",...e)}new Set},"./dist/esm/client/components/app-router-headers.js":(e,t,r)=>{"use strict";r.d(t,{H4:()=>n});const n="_rsc"},"./dist/esm/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{Ei:()=>c,Eo:()=>u,Lx:()=>d,Qq:()=>o,Wo:()=>a,lk:()=>p,oL:()=>i,q6:()=>l,wh:()=>s,y3:()=>n});const n="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",a="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",i="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",c="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",l="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",d="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",u="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",p="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member"},"./dist/esm/lib/non-nullable.js":(e,t,r)=>{"use strict";function n(e){return null!=e}r.d(t,{v:()=>n})},"./dist/esm/lib/web/chalk.js":(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});const n=new Proxy((e=>e),{get:(e,t)=>["hex","rgb","ansi256","bgHex","bgRgb","bgAnsi256"].includes(t)?()=>n:n}),o=n},"./dist/esm/server/api-utils/get-cookie-parser.js":(e,t,r)=>{"use strict";function n(e){return function(){const{cookie:t}=e;if(!t)return{};const{parse:n}=r("./dist/compiled/cookie/index.js");return n(Array.isArray(t)?t.join("; "):t)}}r.d(t,{a:()=>n})},"./dist/esm/server/api-utils/index.js":(e,t,r)=>{"use strict";r.d(t,{Di:()=>c,Iq:()=>s,Lm:()=>d,QM:()=>i,dS:()=>a,gk:()=>u});var n=r("./dist/esm/server/web/spec-extension/adapters/headers.js"),o=r("./dist/esm/lib/constants.js");function s(e,t){const r=n.h.from(e.headers);return{isOnDemandRevalidate:r.get(o.y3)===t.previewModeId,revalidateOnlyGenerated:r.has(o.Qq)}}const a="__prerender_bypass",i="__next_preview_data",c=Symbol(i),l=Symbol(a);function d(e,t={}){if(l in e)return e;const{serialize:n}=r("./dist/compiled/cookie/index.js"),o=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof o?[o]:Array.isArray(o)?o:[],n(a,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0}),n(i,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0})]),Object.defineProperty(e,l,{value:!0,enumerable:!1}),e}function u({req:e},t,r){const n={configurable:!0,enumerable:!0},o={...n,writable:!0};Object.defineProperty(e,t,{...n,get:()=>{const n=r();return Object.defineProperty(e,t,{...o,value:n}),n},set:r=>{Object.defineProperty(e,t,{...o,value:r})}})}},"./dist/esm/server/api-utils/node.js":(e,t,r)=>{"use strict";r.d(t,{RR:()=>a});var n=r("./dist/esm/server/api-utils/index.js"),o=(r("./dist/compiled/bytes/index.js"),r("./dist/esm/server/send-payload/index.js"),r("stream"),r("./dist/compiled/content-type/index.js"),r("./dist/esm/shared/lib/utils.js"),r("./dist/esm/server/api-utils/get-cookie-parser.js"),r("./lib/trace/tracer"),r("./dist/esm/server/lib/trace/constants.js"),r("./dist/esm/server/web/spec-extension/cookies.js")),s=r("./dist/esm/server/web/spec-extension/adapters/headers.js");function a(e,t,a){var i,c;if(a&&(0,n.Iq)(e,a).isOnDemandRevalidate)return!1;if(n.Di in e)return e[n.Di];const l=s.h.from(e.headers),d=new o.q(l),u=null==(i=d.get(n.dS))?void 0:i.value,p=null==(c=d.get(n.QM))?void 0:c.value;if(u&&!p&&u===a.previewModeId){const t={};return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}if(!u&&!p)return!1;if(!u||!p)return(0,n.Lm)(t),!1;if(u!==a.previewModeId)return(0,n.Lm)(t),!1;let f;try{f=r("next/dist/compiled/jsonwebtoken").verify(p,a.previewModeSigningKey)}catch{return(0,n.Lm)(t),!1}const{decryptWithSecret:m}=r("./dist/esm/server/crypto-utils.js"),h=m(Buffer.from(a.previewModeEncryptionKey),f.data);try{const t=JSON.parse(h);return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}catch{return!1}}r("./dist/esm/lib/constants.js")},"./dist/esm/server/crypto-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{decryptWithSecret:()=>p,encryptWithSecret:()=>u});const n=require("crypto");var o=r.n(n);const s="aes-256-gcm",a=32,i=16,c=16,l=64,d=1e5;function u(e,t){const r=o().randomBytes(i),n=o().randomBytes(l),c=o().pbkdf2Sync(e,n,d,a,"sha512"),u=o().createCipheriv(s,c,r),p=Buffer.concat([u.update(t,"utf8"),u.final()]),f=u.getAuthTag();return Buffer.concat([n,r,f,p]).toString("hex")}function p(e,t){const r=Buffer.from(t,"hex"),n=r.slice(0,l),u=r.slice(l,l+i),p=r.slice(l+i,l+i+c),f=r.slice(l+i+c),m=o().pbkdf2Sync(e,n,d,a,"sha512"),h=o().createDecipheriv(s,m,u);return h.setAuthTag(p),h.update(f)+h.final("utf8")}},"./dist/esm/server/font-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{calculateOverrideValues:()=>h,calculateSizeAdjustValues:()=>g,getFontDefinitionFromManifest:()=>p,getFontDefinitionFromNetwork:()=>u,getFontOverrideCss:()=>b});var n=r("./dist/esm/build/output/log.js"),o=r("./dist/esm/shared/lib/constants.js");const s=r("next/dist/server/capsize-font-metrics.json"),a=r("https"),i="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36",c="Mozilla/5.0 (Windows NT 10.0; Trident/7.0; rv:11.0) like Gecko";function l(e){return e.startsWith(o.Lw)}function d(e,t){return new Promise(((r,n)=>{let o="";a.get(e,{headers:{"user-agent":t}},(e=>{e.on("data",(e=>{o+=e})),e.on("end",(()=>{r(o.toString("utf8"))}))})).on("error",(e=>{n(e)}))}))}async function u(e){let t="";try{l(e)&&(t+=await d(e,c)),t+=await d(e,i)}catch(t){return n.ZK(`Failed to download the stylesheet for ${e}. Skipped optimizing this font.`),""}return t}function p(e,t){var r;return(null==(r=t.find((t=>!(!t||t.url!==e))))?void 0:r.content)||""}function f(e){return e.replace(/(?:^\w|[A-Z]|\b\w)/g,(function(e,t){return 0===t?e.toLowerCase():e.toUpperCase()})).replace(/\s+/g,"")}function m(e){return Math.abs(100*e).toFixed(2)}function h(e){const t=f(e),r=s[t];let{category:n,ascent:a,descent:i,lineGap:c,unitsPerEm:l}=r;const d="serif"===n?o.oh:o.dW;return a=m(a/l),i=m(i/l),c=m(c/l),{ascent:a,descent:i,lineGap:c,fallbackFont:d.name}}function g(e){const t=f(e),r=s[t];let{category:n,ascent:a,descent:i,lineGap:c,unitsPerEm:l,xWidthAvg:d}=r;const u=d/l,p="serif"===n?o.oh:o.dW,h=f(p.name),g=s[h],v=g.xWidthAvg/g.unitsPerEm;let y=d?u/v:1;return a=m(a/(l*y)),i=m(i/(l*y)),c=m(c/(l*y)),{ascent:a,descent:i,lineGap:c,fallbackFont:p.name,sizeAdjust:m(y)}}function v(e){const t=e.trim(),{ascent:r,descent:n,lineGap:o,fallbackFont:s}=h(t);return`\n    @font-face {\n      font-family: "${t} Fallback";\n      ascent-override: ${r}%;\n      descent-override: ${n}%;\n      line-gap-override: ${o}%;\n      src: local("${s}");\n    }\n  `}function y(e){const t=e.trim(),{ascent:r,descent:n,lineGap:o,fallbackFont:s,sizeAdjust:a}=g(t);return`\n    @font-face {\n      font-family: "${t} Fallback";\n      ascent-override: ${r}%;\n      descent-override: ${n}%;\n      line-gap-override: ${o}%;\n      size-adjust: ${a}%;\n      src: local("${s}");\n    }\n  `}function b(e,t,r=!1){if(!l(e))return"";const n=r?y:v;try{const e=function(e){const t=e.matchAll(/font-family: ([^;]*)/g),r=new Set;for(let e of t){const t=e[1].replace(/^['"]|['"]$/g,"");r.add(t)}return[...r]}(t);return e.reduce(((e,t)=>e+n(t)),"")}catch(e){return console.log("Error getting font override values - ",e),""}}},"./dist/esm/server/lib/trace/constants.js":(e,t,r)=>{"use strict";var n,o,s,a,i,c,l,d,u,p,f;r.d(t,{k0:()=>l,xj:()=>c}),function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(n||(n={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(o||(o={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(s||(s={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(a||(a={})),function(e){e.startServer="startServer.startServer"}(i||(i={})),function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(c||(c={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(l||(l={})),function(e){e.executeRoute="Router.executeRoute"}(d||(d={})),function(e){e.runHandler="Node.runHandler"}(u||(u={})),function(e){e.runHandler="AppRouteRouteHandlers.runHandler"}(p||(p={})),function(e){e.generateMetadata="ResolveMetadata.generateMetadata"}(f||(f={}))},"./dist/esm/server/node-polyfill-web-streams.js":(e,t,r)=>{if(!global.ReadableStream)if(r("stream/web").ReadableStream)global.ReadableStream=r("stream/web").ReadableStream;else{const{ReadableStream:e}=r("next/dist/compiled/@edge-runtime/ponyfill");global.ReadableStream=e}if(!global.TransformStream)if(r("stream/web").TransformStream)global.TransformStream=r("stream/web").TransformStream;else{const{TransformStream:e}=r("next/dist/compiled/@edge-runtime/ponyfill");global.TransformStream=e}},"./dist/esm/server/optimize-amp.js":(e,t,r)=>{"use strict";async function n(e,t){let n;try{n=r("next/dist/compiled/@ampproject/toolbox-optimizer")}catch(t){return e}return n.create(t).transformHtml(e,t)}r.d(t,{Z:()=>n})},"./dist/esm/server/post-process.js":(e,t,r)=>{"use strict";r.d(t,{X:()=>a});var n=r("./dist/esm/shared/lib/constants.js"),o=r("./dist/esm/lib/non-nullable.js");const s=[];async function a(e,t,n,{inAmpMode:a,hybridAmp:i}){const c=[a?async t=>{const o=r("./dist/esm/server/optimize-amp.js").Z;return t=await o(t,n.ampOptimizerConfig),!n.ampSkipValidation&&n.ampValidator&&await n.ampValidator(t,e),t}:null,n.optimizeFonts?async e=>await async function(e,t,n){if(!s[0])return e;const{parse:o}=r("next/dist/compiled/node-html-parser"),a=o(e);let i=e;async function c(e){const r=e.inspect(a,t);i=await e.mutate(i,r,t)}for(let e=0;e<s.length;e++){let t=s[e];t.condition&&!t.condition(n)||await c(s[e].middleware)}return i}(e,{getFontDefinition:e=>{if(n.fontManifest){const{getFontDefinitionFromManifest:t}=r("./dist/esm/server/font-utils.js");return t(e,n.fontManifest)}return""}},{optimizeFonts:n.optimizeFonts}):null,n.optimizeCss?async e=>{const t=new(r("critters"))({ssrMode:!0,reduceInlineStyles:!1,path:n.distDir,publicPath:`${n.assetPrefix}/_next/`,preload:"media",fonts:!1,...n.optimizeCss});return await t.process(e)}:null,a||i?e=>e.replace(/&amp;amp=1/g,"&amp=1"):null].filter(o.v);for(const e of c)e&&(t=await e(t));return t}var i,c;i=new class{inspect(e,t){if(!t.getFontDefinition)return;const r=[];return e.querySelectorAll("link").filter((e=>"stylesheet"===e.getAttribute("rel")&&e.hasAttribute("data-href")&&n.C7.some((({url:t})=>{const r=e.getAttribute("data-href");return!!r&&r.startsWith(t)})))).forEach((e=>{const t=e.getAttribute("data-href"),n=e.getAttribute("nonce");t&&r.push([t,n])})),r}constructor(){this.mutate=async(e,t,r)=>{let o=e,s=new Set;if(!r.getFontDefinition)return e;t.forEach((e=>{const[t,a]=e,i=`<link rel="stylesheet" href="${t}"/>`;if(o.indexOf(`<style data-href="${t}">`)>-1||o.indexOf(i)>-1)return;const c=r.getFontDefinition?r.getFontDefinition(t):null;if(c){const e=a?` nonce="${a}"`:"";let r="";c.includes("ascent-override")&&(r=' data-size-adjust="true"'),o=o.replace("</head>",`<style data-href="${t}"${e}${r}>${c}</style></head>`);const i=t.replace(/&/g,"&amp;").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),l=new RegExp(`<link[^>]*data-href="${i}"[^>]*/>`);o=o.replace(l,"");const d=n.C7.find((e=>t.startsWith(e.url)));d&&s.add(d.preconnect)}else o=o.replace("</head>",`${i}</head>`)}));let a="";return s.forEach((e=>{a+=`<link rel="preconnect" href="${e}" crossorigin />`})),o=o.replace('<meta name="next-font-preconnect"/>',a),o}}},c=e=>e.optimizeFonts||process.env.__NEXT_OPTIMIZE_FONTS,s.push({name:"Inline-Fonts",middleware:i,condition:c||null})},"./dist/esm/server/send-payload/index.js":(e,t,r)=>{"use strict";r("./dist/esm/shared/lib/utils.js"),r("./dist/compiled/fresh/index.js"),r("./dist/esm/client/components/app-router-headers.js")},"./dist/esm/server/web/spec-extension/adapters/headers.js":(e,t,r)=>{"use strict";r.d(t,{h:()=>s});var n=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.g.get(t,r,o);const s=r.toLowerCase(),a=Object.keys(e).find((e=>e.toLowerCase()===s));return void 0!==a?n.g.get(t,a,o):void 0},set(t,r,o,s){if("symbol"==typeof r)return n.g.set(t,r,o,s);const a=r.toLowerCase(),i=Object.keys(e).find((e=>e.toLowerCase()===a));return n.g.set(t,i??r,o,s)},has(t,r){if("symbol"==typeof r)return n.g.has(t,r);const o=r.toLowerCase(),s=Object.keys(e).find((e=>e.toLowerCase()===o));return void 0!==s&&n.g.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.g.deleteProperty(t,r);const o=r.toLowerCase(),s=Object.keys(e).find((e=>e.toLowerCase()===o));return void 0===s||n.g.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){const r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){const t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(const[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(const e of Object.keys(this.headers)){const t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(const e of Object.keys(this.headers)){const t=e.toLowerCase();yield t}}*values(){for(const e of Object.keys(this.headers)){const t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){const n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},"./dist/esm/server/web/spec-extension/cookies.js":(e,t,r)=>{"use strict";r.d(t,{q:()=>n.RequestCookies});var n=r("./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{C7:()=>u,Er:()=>m,Lw:()=>d,NO:()=>c,dW:()=>f,fn:()=>i,o3:()=>a,oh:()=>p,uY:()=>l,wU:()=>n}),r("./dist/esm/shared/lib/modern-browserslist-target.js");const n="__NEXT_BUILTIN_DOCUMENT__",o="main",s=o+"-app",a=(Symbol("polyfills"),307),i=308,c="__N_SSG",l="__N_SSP",d="https://fonts.googleapis.com/",u=[{url:d,preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],p={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},f={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},m=["/500"];new Set([o,"react-refresh","amp",s])},"./dist/esm/shared/lib/modern-browserslist-target.js":e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},"./dist/esm/shared/lib/utils.js":(e,t,r)=>{"use strict";function n(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function o(e){return e.finished||e.headersSent}async function s(e,t){const r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await s(t.Component,t.ctx)}:{};const a=await e.getInitialProps(t);if(r&&o(r))return a;if(!a){const t='"'+n(e)+'.getInitialProps()" should resolve to an object. But found "'+a+'" instead.';throw new Error(t)}return a}r.d(t,{Gf:()=>n,KM:()=>a,aC:()=>o,nq:()=>s}),"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every((e=>"function"==typeof performance[e]));class a extends Error{}},"./lib/trace/tracer":e=>{"use strict";e.exports=require("next/dist/server/lib/trace/tracer")},critters:e=>{"use strict";e.exports=require("critters")},"next/dist/compiled/@ampproject/toolbox-optimizer":e=>{"use strict";e.exports=require("next/dist/compiled/@ampproject/toolbox-optimizer")},"next/dist/compiled/@edge-runtime/ponyfill":e=>{"use strict";e.exports=require("next/dist/compiled/@edge-runtime/ponyfill")},"next/dist/compiled/@next/react-dev-overlay/dist/middleware":e=>{"use strict";e.exports=require("next/dist/compiled/@next/react-dev-overlay/dist/middleware")},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/node-html-parser":e=>{"use strict";e.exports=require("next/dist/compiled/node-html-parser")},"next/dist/compiled/raw-body":e=>{"use strict";e.exports=require("next/dist/compiled/raw-body")},"next/dist/server/capsize-font-metrics.json":e=>{"use strict";e.exports=require("next/dist/server/capsize-font-metrics.json")},https:e=>{"use strict";e.exports=require("https")},path:e=>{"use strict";e.exports=require("path")},querystring:e=>{"use strict";e.exports=require("querystring")},stream:e=>{"use strict";e.exports=require("stream")},"stream/web":e=>{"use strict";e.exports=require("stream/web")}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";r.r(n),r.d(n,{PagesRouteModule:()=>Ge,default:()=>Ve,renderToHTML:()=>We,vendored:()=>Je});var e={};r.r(e),r.d(e,{AmpStateContext:()=>C});var t={};r.r(t),r.d(t,{HeadManagerContext:()=>R});var o={};r.r(o),r.d(o,{LoadableContext:()=>$});var s={};r.r(s),r.d(s,{default:()=>I});var a={};r.r(a),r.d(a,{RouterContext:()=>F});var i={};r.r(i),r.d(i,{HtmlContext:()=>B,useHtmlContext:()=>W});var c={};r.r(c),r.d(c,{ImageConfigContext:()=>ue});var l={};r.r(l),r.d(l,{PathParamsContext:()=>ve,PathnameContext:()=>ge,SearchParamsContext:()=>he});var d={};r.r(d),r.d(d,{AppRouterContext:()=>Ce,CacheStates:()=>Te,GlobalLayoutRouterContext:()=>$e,LayoutRouterContext:()=>Re,TemplateContext:()=>Ae});var u={};r.r(u),r.d(u,{ServerInsertedHTMLContext:()=>Ue,useServerInsertedHTML:()=>Ze});var p={};r.r(p),r.d(p,{AmpContext:()=>e,AppRouterContext:()=>d,HeadManagerContext:()=>t,HooksClientContext:()=>l,HtmlContext:()=>i,ImageConfigContext:()=>c,Loadable:()=>s,LoadableContext:()=>o,RouterContext:()=>a,ServerInsertedHtml:()=>u});class f{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}var m=r("./dist/esm/server/api-utils/index.js"),h=r("./dist/esm/server/api-utils/get-cookie-parser.js");const g=require("react");var v=r.n(g);const y=require("react-dom/server.browser");var b=r.n(y);const w=require("styled-jsx");var x=r("./dist/esm/lib/constants.js"),S=r("./dist/esm/shared/lib/constants.js");function _(e){return Object.prototype.toString.call(e)}function P(e){if("[object Object]"!==_(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}const E=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class j extends Error{constructor(e,t,r,n){super(r?`Error serializing \`${r}\` returned from \`${t}\` in "${e}".\nReason: ${n}`:`Error serializing props returned from \`${t}\` in "${e}".\nReason: ${n}`)}}function T(e,t,r){if(!P(r))throw new j(e,t,"",`Props must be returned as a plain object from ${t}: \`{ props: { ... } }\` (received: \`${_(r)}\`).`);function n(r,n,o){if(r.has(n))throw new j(e,t,o,`Circular references cannot be expressed in JSON (references: \`${r.get(n)||"(self)"}\`).`);r.set(n,o)}return function r(o,s,a){const i=typeof s;if(null===s||"boolean"===i||"number"===i||"string"===i)return!0;if("undefined"===i)throw new j(e,t,a,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value.");if(P(s)){if(n(o,s,a),Object.entries(s).every((([e,t])=>{const n=E.test(e)?`${a}.${e}`:`${a}[${JSON.stringify(e)}]`,s=new Map(o);return r(s,e,n)&&r(s,t,n)})))return!0;throw new j(e,t,a,"invariant: Unknown error encountered in Object.")}if(Array.isArray(s)){if(n(o,s,a),s.every(((e,t)=>r(new Map(o),e,`${a}[${t}]`))))return!0;throw new j(e,t,a,"invariant: Unknown error encountered in Array.")}throw new j(e,t,a,"`"+i+"`"+("object"===i?` ("${Object.prototype.toString.call(s)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types.")}(new Map,r,"")}const C=v().createContext({}),R=v().createContext({});const $=v().createContext(null),A=[],k=[];let N=!1;function O(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then((e=>(r.loading=!1,r.loaded=e,e))).catch((e=>{throw r.loading=!1,r.error=e,e})),r}class L{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};const{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout((()=>{this._update({pastDelay:!0})}),t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout((()=>{this._update({timedOut:!0})}),t.timeout))),this._res.promise.then((()=>{this._update({}),this._clearTimeouts()})).catch((e=>{this._update({}),this._clearTimeouts()})),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach((e=>e()))}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function M(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function o(){if(!n){const t=new L(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}function s(e,t){!function(){o();const e=v().useContext($);e&&Array.isArray(r.modules)&&r.modules.forEach((t=>{e(t)}))}();const s=v().useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return v().useImperativeHandle(t,(()=>({retry:n.retry})),[]),v().useMemo((()=>{return s.loading||s.error?v().createElement(r.loading,{isLoading:s.loading,pastDelay:s.pastDelay,timedOut:s.timedOut,error:s.error,retry:n.retry}):s.loaded?v().createElement((t=s.loaded)&&t.default?t.default:t,e):null;var t}),[e,s])}return A.push(o),s.preload=()=>o(),s.displayName="LoadableComponent",v().forwardRef(s)}(O,e)}function q(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then((()=>{if(e.length)return q(e,t)}))}M.preloadAll=()=>new Promise(((e,t)=>{q(A).then(e,t)})),M.preloadReady=e=>(void 0===e&&(e=[]),new Promise((t=>{const r=()=>(N=!0,t());q(k,e).then(r,r)})));const I=M,F=v().createContext(null),D=/\/\[[^/]+?\](?=\/|$)/;function z(e){return D.test(e)}var H=r("./dist/esm/shared/lib/utils.js");const B=(0,g.createContext)(void 0);function W(){const e=(0,g.useContext)(B);if(!e)throw new Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page");return e}const U=Symbol.for("NextInternalRequestMeta");function Z(e,t){const r=e[U]||{};return"string"==typeof t?r[t]:r}const G=new Set([301,302,303,307,308]);function J(e){return e.statusCode||(e.permanent?S.fn:S.o3)}class V{static fromStatic(e){return new V(e)}constructor(e,{contentType:t,...r}={}){this.response=e,this.contentType=t,this.metadata=r}extendMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(){if("string"!=typeof this.response)throw new Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return this.response}async pipe(e){if(null===this.response)throw new Error("Invariant: response is null. This is a bug in Next.js");if("string"==typeof this.response)throw new Error("Invariant: static responses cannot be piped. This is a bug in Next.js");return await async function(e,t){const r=e.getReader();let n=!1,o=!1;function s(){o=!0,t.off("close",s),n||(n=!0,r.cancel().catch((()=>{})))}t.on("close",s);try{for(;;){const{done:e,value:s}=await r.read();if(n=e,e||o)break;s&&(t.write(Buffer.from(s)),null==t.flush||t.flush.call(t))}}catch(e){if(!function(e){return"AbortError"===(null==e?void 0:e.name)}(e))throw e}finally{t.off("close",s),n||r.cancel().catch((()=>{})),o||t.end()}}(this.response,e)}}var Q=r("./dist/esm/lib/non-nullable.js"),Y=r("./lib/trace/tracer"),K=r("./dist/esm/server/lib/trace/constants.js");function X(e){return(new TextEncoder).encode(e)}function ee(e,t){return t.decode(e,{stream:!0})}const te=setImmediate;function re(e){return new ReadableStream({start(t){t.enqueue(X(e)),t.close()}})}async function ne(e){const t=e.getReader(),r=new TextDecoder;let n="";for(;;){const{done:e,value:o}=await t.read();if(e)return n;n+=ee(o,r)}}function oe(){let e=new Uint8Array,t=null;return new TransformStream({transform(r,n){const o=new Uint8Array(e.length+r.byteLength);o.set(e),o.set(r,e.length),e=o,(r=>{t||(t=new Promise((n=>{te((()=>{r.enqueue(e),e=new Uint8Array,t=null,n()}))})))})(n)},flush(){if(t)return t}})}function se(e){return new TransformStream({async transform(t,r){const n=X(await e());r.enqueue(n),r.enqueue(t)}})}function ae(e){let t=!1,r=!1;const n=new TextDecoder;return new TransformStream({async transform(o,s){if(r)return void s.enqueue(o);const a=await e();if(t)s.enqueue(X(a)),s.enqueue(o),r=!0;else{const e=ee(o,n),i=e.indexOf("</head>");if(-1!==i){const n=e.slice(0,i)+a+e.slice(i);s.enqueue(X(n)),r=!0,t=!0}}t?te((()=>{r=!1})):s.enqueue(o)},async flush(t){const r=await e();r&&t.enqueue(X(r))}})}function ie(e){let t=!1,r=null;return new TransformStream({transform(n,o){o.enqueue(n),!t&&e.length&&(t=!0,r=new Promise((t=>{te((()=>{o.enqueue(X(e)),t()}))})))},flush(n){if(r)return r;!t&&e.length&&(t=!0,n.enqueue(X(e)))}})}function ce(e){let t=null;return new TransformStream({transform(r,n){if(n.enqueue(r),!t){const r=e.getReader();t=new Promise((e=>setTimeout((async()=>{try{for(;;){const{done:t,value:o}=await r.read();if(t)return e();n.enqueue(o)}}catch(e){n.error(e)}e()}),0)))}},flush(){if(t)return t}})}function le(e){let t=!1;const r=new TextDecoder;return new TransformStream({transform(n,o){if(!e||t)return o.enqueue(n);const s=ee(n,r);if(s.endsWith(e)){t=!0;const r=s.slice(0,-e.length);o.enqueue(X(r))}else o.enqueue(n)},flush(t){e&&t.enqueue(X(e))}})}function de(e="",t){let r=!1,n=!1;const o=new TextDecoder;return new TransformStream({async transform(e,t){if(!r||!n){const t=ee(e,o);!r&&t.includes("<html")&&(r=!0),!n&&t.includes("<body")&&(n=!0)}t.enqueue(e)},flush(o){if(!r||!n){const s=[r?null:"html",n?null:"body"].filter(Q.v);o.enqueue(X(`<script>self.__next_root_layout_missing_tags_error=${JSON.stringify({missingTags:s,assetPrefix:e??"",tree:t()})}<\/script>`))}}})}const ue=v().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1});var pe=r("./dist/compiled/strip-ansi/index.js"),fe=r.n(pe);const me=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound",r("./dist/esm/client/components/app-router-headers.js").H4],he=(0,g.createContext)(null),ge=(0,g.createContext)(null),ve=(0,g.createContext)(null),ye=["(..)(..)","(.)","(..)","(...)"],be=/[|\\{}()[\]^$+*?.-]/,we=/[|\\{}()[\]^$+*?.-]/g;function xe(e){return be.test(e)?e.replace(we,"\\$&"):e}function Se(e){const t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));const r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function _e(e){const{parameterizedRoute:t,groups:r}=function(e){const t=function(e){return e.replace(/\/$/,"")||"/"}(e).slice(1).split("/"),r={};let n=1;return{parameterizedRoute:t.map((e=>{const t=ye.find((t=>e.startsWith(t))),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){const{key:e,optional:s,repeat:a}=Se(o[1]);return r[e]={pos:n++,repeat:a,optional:s},"/"+xe(t)+"([^/]+?)"}if(o){const{key:e,repeat:t,optional:s}=Se(o[1]);return r[e]={pos:n++,repeat:t,optional:s},t?s?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}return"/"+xe(e)})).join(""),groups:r}}(e);return{re:new RegExp("^"+t+"(?:/)?$"),groups:r}}function Pe(e){return e.isReady&&e.query?(t=e.asPath,new URL(t,"http://n").searchParams):new URLSearchParams;var t}function Ee(e){if(!e.isReady||!e.query)return null;const t={},r=_e(e.pathname),n=Object.keys(r.groups);for(const r of n)t[r]=e.query[r];return t}function je(e){let{children:t,router:r,...n}=e;const o=(0,g.useRef)(n.isAutoExport),s=(0,g.useMemo)((()=>{const e=o.current;if(e&&(o.current=!1),z(r.pathname)){if(r.isFallback)return null;if(e&&!r.isReady)return null}let t;try{t=new URL(r.asPath,"http://f")}catch(e){return"/"}return t.pathname}),[r.asPath,r.isFallback,r.isReady,r.pathname]);return v().createElement(ge.Provider,{value:s},t)}var Te;!function(e){e.LAZY_INITIALIZED="LAZYINITIALIZED",e.DATA_FETCH="DATAFETCH",e.READY="READY"}(Te||(Te={}));const Ce=v().createContext(null),Re=v().createContext(null),$e=v().createContext(null),Ae=v().createContext(null);let ke,Ne,Oe;const Le="<!DOCTYPE html>";function Me(){throw new Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance')}async function qe(e){const t=await b().renderToReadableStream(e);return await t.allReady,ne(t)}r("./dist/esm/server/node-polyfill-web-streams.js"),ke=r("./dist/esm/server/api-utils/node.js").RR,Ne=r("./dist/esm/build/output/log.js").ZK,Oe=r("./dist/esm/server/post-process.js").X;class Ie{constructor(e,t,r,{isFallback:n},o,s,a,i,c,l,d,u){this.route=e.replace(/\/$/,"")||"/",this.pathname=e,this.query=t,this.asPath=r,this.isFallback=n,this.basePath=s,this.locale=a,this.locales=i,this.defaultLocale=c,this.isReady=o,this.domainLocales=l,this.isPreview=!!d,this.isLocaleDomain=!!u}push(){Me()}replace(){Me()}reload(){Me()}back(){Me()}forward(){Me()}prefetch(){Me()}beforePopState(){Me()}}function Fe(e,t,r){return v().createElement(e,{Component:t,...r})}const De=(e,t)=>{const r=`invalid-${e.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${e}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:\n\n\treturn { props: { title: 'My Title', content: '...' } }\n\nKeys that need to be moved: ${t.join(", ")}.\nRead more: https://nextjs.org/docs/messages/${r}`};function ze(e,t,r){const{destination:n,permanent:o,statusCode:s,basePath:a}=e;let i=[];const c=void 0!==s,l=void 0!==o;l&&c?i.push("`permanent` and `statusCode` can not both be provided"):l&&"boolean"!=typeof o?i.push("`permanent` must be `true` or `false`"):c&&!G.has(s)&&i.push(`\`statusCode\` must undefined or one of ${[...G].join(", ")}`);const d=typeof n;"string"!==d&&i.push(`\`destination\` should be string but received ${d}`);const u=typeof a;if("undefined"!==u&&"boolean"!==u&&i.push(`\`basePath\` should be undefined or a false, received ${u}`),i.length>0)throw new Error(`Invalid redirect object returned from ${r} for ${t.url}\n`+i.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp")}function He(e,t){return e?function(e){let t="server";return t=r("next/dist/compiled/@next/react-dev-overlay/dist/middleware").getErrorSource(e)||"server",{name:e.name,source:t,message:fe()(e.message),stack:e.stack,digest:e.digest}}(t):{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}}async function Be(e,t,n,o,s,a){var i;(0,m.gk)({req:e},"cookies",(0,h.a)(e.headers));const c={};c.assetQueryString=s.dev?s.assetQueryString||`?ts=${Date.now()}`:"",s.deploymentId&&(c.assetQueryString+=`${c.assetQueryString?"&":"?"}dpl=${s.deploymentId}`),o=Object.assign({},o);const{err:l,dev:d=!1,ampPath:u="",pageConfig:p={},buildManifest:f,reactLoadableManifest:g,ErrorDebug:y,getStaticProps:_,getStaticPaths:P,getServerSideProps:E,isDataReq:j,params:A,previewProps:k,basePath:N,images:O,runtime:L}=s,{App:M}=a,q=c.assetQueryString;let D=a.Document,W=s.Component;const U=W,G=!!o.__nextFallback,X=o.__nextNotFoundSrcPage;!function(e){for(const t of me)delete e[t]}(o);const ee=!!_,te=ee&&s.nextExport,pe=M.getInitialProps===M.origGetInitialProps,fe=!!(null==W?void 0:W.getInitialProps),ge=null==W?void 0:W.unstable_scriptLoader,ye=z(n),be="/_error"===n&&W.getInitialProps===W.origGetInitialProps;s.nextExport&&fe&&!be&&Ne(`Detected getInitialProps on page '${n}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.\nRead more: https://nextjs.org/docs/messages/get-initial-props-export`);const we=!fe&&pe&&!ee&&!E;if(fe&&ee)throw new Error(x.wh+` ${n}`);if(fe&&E)throw new Error(x.Wo+` ${n}`);if(E&&ee)throw new Error(x.oL+` ${n}`);if(E&&"export"===s.nextConfigOutput)throw new Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if(P&&!ye)throw new Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${n}'.\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`);if(P&&!ee)throw new Error(`getStaticPaths was added without a getStaticProps in ${n}. Without getStaticProps, getStaticPaths does nothing`);if(ee&&ye&&!P)throw new Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${n}'.\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`);let xe,Se,_e=s.resolvedAsPath||e.url;if(d){const{isValidElementType:t}=r("./dist/compiled/react-is/index.js");if(!t(W))throw new Error(`The default export is not a React Component in page: "${n}"`);if(!t(M))throw new Error('The default export is not a React Component in page: "/_app"');if(!t(D))throw new Error('The default export is not a React Component in page: "/_document"');if((we||G)&&(o={...o.amp?{amp:o.amp}:{}},_e=`${n}${e.url.endsWith("/")&&"/"!==n&&!ye?"/":""}`,e.url=n),"/404"===n&&(fe||E))throw new Error(`\`pages/404\` ${x.Ei}`);if(S.Er.includes(n)&&(fe||E))throw new Error(`\`pages${n}\` ${x.Ei}`)}for(const e of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==W?void 0:W[e])throw new Error(`page ${n} ${e} ${x.lk}`);await I.preloadAll(),!ee&&!E||G||(Se=ke(e,t,k),xe=!1!==Se);const Te=new Ie(n,o,_e,{isFallback:G},!(!E&&!fe&&(pe||ee)),N,s.locale,s.locales,s.defaultLocale,s.domainLocales,xe,Z(e,"__nextIsLocaleDomain")),Re=function(e){return{back(){e.back()},forward(){e.forward()},refresh(){e.reload()},push(t,r){let{scroll:n}=void 0===r?{}:r;e.push(t,void 0,{scroll:n})},replace(t,r){let{scroll:n}=void 0===r?{}:r;e.replace(t,void 0,{scroll:n})},prefetch(t){e.prefetch(t)}}}(Te);let $e={};const Ae=(0,w.createStyleRegistry)(),Me={ampFirst:!0===p.amp,hasQuery:Boolean(o.amp),hybrid:"hybrid"===p.amp},Be=function(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}(Me);let We=function(e){void 0===e&&(e=!1);const t=[v().createElement("meta",{charSet:"utf-8"})];return e||t.push(v().createElement("meta",{name:"viewport",content:"width=device-width"})),t}(Be);const Ue=[];let Ze={};ge&&(Ze.beforeInteractive=[].concat(ge()).filter((e=>"beforeInteractive"===e.props.strategy)).map((e=>e.props)));const Ge=({children:e})=>v().createElement(Ce.Provider,{value:Re},v().createElement(he.Provider,{value:Pe(Te)},v().createElement(je,{router:Te,isAutoExport:we},v().createElement(ve.Provider,{value:Ee(Te)},v().createElement(F.Provider,{value:Te},v().createElement(C.Provider,{value:Me},v().createElement(R.Provider,{value:{updateHead:e=>{We=e},updateScripts:e=>{$e=e},scripts:Ze,mountedInstances:new Set}},v().createElement($.Provider,{value:e=>Ue.push(e)},v().createElement(w.StyleRegistry,{registry:Ae},v().createElement(ue.Provider,{value:O},e)))))))))),Je=()=>null,Ve=({children:e})=>v().createElement(v().Fragment,null,v().createElement(Je,null),v().createElement(Ge,null,v().createElement(v().Fragment,null,d?v().createElement(v().Fragment,null,e,v().createElement(Je,null)):e,v().createElement(Je,null)))),Qe={err:l,req:we?void 0:e,res:we?void 0:t,pathname:n,query:o,asPath:_e,locale:s.locale,locales:s.locales,defaultLocale:s.defaultLocale,AppTree:e=>v().createElement(Ve,null,Fe(M,U,{...e,router:Te})),defaultGetInitialProps:async(e,t={})=>{const{html:r,head:n}=await e.renderPage({enhanceApp:e=>t=>v().createElement(e,t)}),o=Ae.styles({nonce:t.nonce});return Ae.flush(),{html:r,head:n,styles:o}}};let Ye;const Ke=!ee&&(s.nextExport||d&&(we||G));if(Ye=await(0,H.nq)(M,{AppTree:Qe.AppTree,Component:W,router:Te,ctx:Qe}),(ee||E)&&xe&&(Ye.__N_PREVIEW=!0),ee&&(Ye[S.NO]=!0),ee&&!G){let t;try{t=await(0,Y.getTracer)().trace(K.xj.getStaticProps,{spanName:`getStaticProps ${n}`,attributes:{"next.route":n}},(()=>_({...ye?{params:o}:void 0,...xe?{draftMode:!0,preview:!0,previewData:Se}:void 0,locales:s.locales,locale:s.locale,defaultLocale:s.defaultLocale})))}catch(e){throw e&&"ENOENT"===e.code&&delete e.code,e}if(null==t)throw new Error(x.q6);const r=Object.keys(t).filter((e=>"revalidate"!==e&&"props"!==e&&"redirect"!==e&&"notFound"!==e));if(r.includes("unstable_revalidate"))throw new Error(x.Eo);if(r.length)throw new Error(De("getStaticProps",r));if("notFound"in t&&t.notFound){if("/404"===n)throw new Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');c.isNotFound=!0}if("redirect"in t&&t.redirect&&"object"==typeof t.redirect){if(ze(t.redirect,e,"getStaticProps"),te)throw new Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${e.url})\nSee more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`);t.props={__N_REDIRECT:t.redirect.destination,__N_REDIRECT_STATUS:J(t.redirect)},void 0!==t.redirect.basePath&&(t.props.__N_REDIRECT_BASE_PATH=t.redirect.basePath),c.isRedirect=!0}if((d||te)&&!c.isNotFound&&!T(n,"getStaticProps",t.props))throw new Error("invariant: getStaticProps did not return valid props. Please report this.");if("revalidate"in t){if(t.revalidate&&"export"===s.nextConfigOutput)throw new Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if("number"==typeof t.revalidate){if(!Number.isInteger(t.revalidate))throw new Error(`A page's revalidate option must be seconds expressed as a natural number for ${e.url}. Mixed numbers, such as '${t.revalidate}', cannot be used.\nTry changing the value to '${Math.ceil(t.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`);if(t.revalidate<=0)throw new Error(`A page's revalidate option can not be less than or equal to zero for ${e.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.\n\nTo never revalidate, you can set revalidate to \`false\` (only ran once at build-time).\nTo revalidate as soon as possible, you can set the value to \`1\`.`);t.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${e.url}. This may have been done in error.\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`)}else if(!0===t.revalidate)t.revalidate=1;else{if(!1!==t.revalidate&&void 0!==t.revalidate)throw new Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(t.revalidate)}' for ${e.url}`);t.revalidate=!1}}else t.revalidate=!1;if(Ye.pageProps=Object.assign({},Ye.pageProps,"props"in t?t.props:void 0),c.revalidate="revalidate"in t?t.revalidate:void 0,c.pageData=Ye,c.isNotFound)return new V(null,c)}if(E&&(Ye[S.uY]=!0),E&&!G){let r,a=!0,i=t,l=!1;try{r=await(0,Y.getTracer)().trace(K.xj.getServerSideProps,{spanName:`getServerSideProps ${n}`,attributes:{"next.route":n}},(async()=>E({req:e,res:i,query:o,resolvedUrl:s.resolvedUrl,...ye?{params:A}:void 0,...!1!==Se?{draftMode:!0,preview:!0,previewData:Se}:void 0,locales:s.locales,locale:s.locale,defaultLocale:s.defaultLocale}))),a=!1}catch(e){throw function(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}(e)&&"ENOENT"===e.code&&delete e.code,e}if(null==r)throw new Error(x.Lx);r.props instanceof Promise&&(l=!0);const u=Object.keys(r).filter((e=>"props"!==e&&"redirect"!==e&&"notFound"!==e));if(r.unstable_notFound)throw new Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${n}`);if(r.unstable_redirect)throw new Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${n}`);if(u.length)throw new Error(De("getServerSideProps",u));if("notFound"in r&&r.notFound){if("/404"===n)throw new Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');return c.isNotFound=!0,new V(null,c)}if("redirect"in r&&"object"==typeof r.redirect&&(ze(r.redirect,e,"getServerSideProps"),r.props={__N_REDIRECT:r.redirect.destination,__N_REDIRECT_STATUS:J(r.redirect)},void 0!==r.redirect.basePath&&(r.props.__N_REDIRECT_BASE_PATH=r.redirect.basePath),c.isRedirect=!0),l&&(r.props=await r.props),(d||te)&&!T(n,"getServerSideProps",r.props))throw new Error("invariant: getServerSideProps did not return valid props. Please report this.");Ye.pageProps=Object.assign({},Ye.pageProps,r.props),c.pageData=Ye}if(j&&!ee||c.isRedirect)return new V(JSON.stringify(Ye),c);if(G&&(Ye.pageProps={}),(0,H.aC)(t)&&!ee)return new V(null,c);let Xe=f;if(we&&ye){const e=function(e){let t=e.replace(/\\/g,"/");return t.startsWith("/index/")&&!z(t)?t.slice(6):"/index"!==t?t:"/"}(function(e){const t=/^\/index(\/|$)/.test(e)&&!z(e)?"/index"+e:"/"===e?"/index":(n=e).startsWith("/")?n:"/"+n;var n;{const{posix:e}=r("path"),n=e.normalize(t);if(n!==t)throw new H.KM("Requested and resolved page mismatch: "+t+" "+n)}return t}(n));e in Xe.pages&&(Xe={...Xe,pages:{...Xe.pages,[e]:[...Xe.pages[e],...Xe.lowPriorityFiles.filter((e=>e.includes("_buildManifest")))]},lowPriorityFiles:Xe.lowPriorityFiles.filter((e=>!e.includes("_buildManifest")))})}const et=({children:e})=>Be?e:v().createElement("div",{id:"__next"},e);null==(i=(0,Y.getTracer)().getRootSpanAttributes())||i.set("next.route",s.page);const tt=await(0,Y.getTracer)().trace(K.xj.renderDocument,{spanName:`render route (pages) ${s.page}`,attributes:{"next.route":s.page}},(async()=>(async()=>{D[S.wU];const e=async(e,t)=>{const r=((e,t)=>{const r=e||M,n=t||W;return Qe.err&&y?v().createElement(et,null,v().createElement(y,{error:Qe.err})):v().createElement(et,null,v().createElement(Ve,null,Fe(r,n,{...Ye,router:Te})))})(e,t);return await function({ReactDOMServer:e,element:t,streamOptions:r}){return(0,Y.getTracer)().trace(K.k0.renderToReadableStream,(async()=>e.renderToReadableStream(t,r)))}({ReactDOMServer:b(),element:r})},r=(0,Y.getTracer)().wrap(K.xj.createBodyResult,((e,t)=>async function(e,{suffix:t,dataStream:r,generateStaticHTML:n,getServerInsertedHTML:o,serverInsertedHTMLToHead:s,validateRootLayout:a}){const i="</body></html>",c=t?t.split(i)[0]:null;return n&&await e.allReady,[oe(),o&&!s?se(o):null,null!=c?ie(c):null,r?ce(r):null,le(i),o&&s?ae(o):null,a?de(a.assetPrefix,a.getTree):null].filter(Q.v).reduce(((e,t)=>e.pipeThrough(t)),e)}(e,{suffix:t,dataStream:void 0,generateStaticHTML:!0,getServerInsertedHTML:async()=>qe((()=>{const e=Ae.styles();return Ae.flush(),v().createElement(v().Fragment,null,e)})()),serverInsertedHTMLToHead:!1}))),n=!!D.getInitialProps;let o,s;if(n){if(s=await async function(e){const r={...Qe,renderPage:async(t={})=>{if(Qe.err&&y)return e&&e(M,W),{html:await qe(v().createElement(et,null,v().createElement(y,{error:Qe.err}))),head:We};if(d&&(Ye.router||Ye.Component))throw new Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props");const{App:r,Component:n}=function(e,t,r){return"function"==typeof e?{App:t,Component:e(r)}:{App:e.enhanceApp?e.enhanceApp(t):t,Component:e.enhanceComponent?e.enhanceComponent(r):r}}(t,M,W);return e?e(r,n).then((async e=>(await e.allReady,{html:await ne(e),head:We}))):{html:await qe(v().createElement(et,null,v().createElement(Ve,null,Fe(r,n,{...Ye,router:Te})))),head:We}}},n=await(0,H.nq)(D,r);if((0,H.aC)(t)&&!ee)return null;if(!n||"string"!=typeof n.html){const e=`"${(0,H.Gf)(D)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`;throw new Error(e)}return{docProps:n,documentCtx:r}}(e),null===s)return null;const{docProps:n}=s;o=e=>r(re(n.html+e))}else{const t=await e(M,W);o=e=>r(t,e),s={}}const{docProps:a}=s||{};let i;return n?(i=a.styles,We=a.head):(i=Ae.styles(),Ae.flush()),{bodyResult:o,documentElement:e=>v().createElement(D,{...e,...a}),head:We,headTags:[],styles:i}})()));if(!tt)return new V(null,c);const rt=new Set,nt=new Set;for(const e of Ue){const t=g[e];t&&(rt.add(t.id),t.files.forEach((e=>{nt.add(e)})))}const ot=Me.hybrid,{assetPrefix:st,buildId:at,customServer:it,defaultLocale:ct,disableOptimizedLoading:lt,domainLocales:dt,locale:ut,locales:pt,runtimeConfig:ft}=s,mt={__NEXT_DATA__:{props:Ye,page:n,query:o,buildId:at,assetPrefix:""===st?void 0:st,runtimeConfig:ft,nextExport:!0===Ke||void 0,autoExport:!0===we||void 0,isFallback:G,dynamicIds:0===rt.size?void 0:Array.from(rt),err:s.err?He(d,s.err):void 0,gsp:!!_||void 0,gssp:!!E||void 0,customServer:it,gip:!!fe||void 0,appGip:!pe||void 0,locale:ut,locales:pt,defaultLocale:ct,domainLocales:dt,isPreview:!0===xe||void 0,notFoundSrcPage:X&&d?X:void 0},strictNextHead:s.strictNextHead,buildManifest:Xe,docComponentsRendered:{},dangerousAsPath:Te.asPath,canonicalBase:!s.ampPath&&Z(e,"__nextStrippedLocale")?`${s.canonicalBase||""}/${s.locale}`:s.canonicalBase,ampPath:u,inAmpMode:Be,isDevelopment:!!d,hybridAmp:ot,dynamicImports:Array.from(nt),assetPrefix:st,unstable_runtimeJS:p.unstable_runtimeJS,unstable_JsPreload:p.unstable_JsPreload,assetQueryString:q,scriptLoader:$e,locale:ut,disableOptimizedLoading:lt,head:tt.head,headTags:tt.headTags,styles:tt.styles,crossOrigin:s.crossOrigin,optimizeCss:s.optimizeCss,optimizeFonts:s.optimizeFonts,nextConfigOutput:s.nextConfigOutput,nextScriptWorkers:s.nextScriptWorkers,runtime:L,largePageDataBytes:s.largePageDataBytes,nextFontManifest:s.nextFontManifest},ht=v().createElement(C.Provider,{value:Me},v().createElement(B.Provider,{value:mt},tt.documentElement(mt))),gt=await(0,Y.getTracer)().trace(K.xj.renderToString,(async()=>qe(ht))),[vt,yt]=gt.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>");let bt="";gt.startsWith(Le)||(bt+=Le),bt+=vt,Be&&(bt+="\x3c!-- __NEXT_DATA__ --\x3e");const wt=[re(bt),await tt.bodyResult(yt)],xt=await ne(function(e){const{readable:t,writable:r}=new TransformStream;let n=Promise.resolve();for(let t=0;t<e.length;++t)n=n.then((()=>e[t].pipeTo(r,{preventClose:t+1<e.length})));return t}(wt)),St=await(e=>Oe(n,e,s,{inAmpMode:Be,hybridAmp:ot}))(xt);return new V(St,c)}async function We(e,t,r,n,o){return Be(e,t,r,n,o,o)}const Ue=v().createContext(null);function Ze(e){const t=(0,g.useContext)(Ue);t&&t(e)}class Ge extends f{constructor(e){super(e),this.components=e.components}render(e,t,r){return Be(e,t,r.page,r.query,r.renderOpts,{App:this.components.App,Document:this.components.Document})}}const Je={contexts:p},Ve=Ge})(),module.exports=n})();