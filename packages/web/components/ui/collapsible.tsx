'use client';

import * as React from 'react';

// Collapsible doesn't have a direct equivalent in Radix Themes
// We'll create a simple implementation using standard HTML elements
const Collapsible = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
  <div {...props}>{children}</div>
);

const CollapsibleTrigger = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
  <button {...props}>{children}</button>
);

const CollapsibleContent = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
  <div {...props}>{children}</div>
);

export { Collapsible, CollapsibleTrigger, CollapsibleContent };
