# SEO Setup for OnlyRules

## Overview
This document outlines the SEO enhancements implemented for the OnlyRules Next.js application.

## Implemented Features

### 1. Dynamic Sitemap Generation (`/app/api/sitemap.xml/route.ts`)
- Automatically generates a sitemap at `/sitemap.xml`
- Uses API routes for Next.js 13.5.1 compatibility
- Includes all static routes with appropriate priorities and change frequencies
- Extensible for future dynamic content (templates, user profiles, etc.)

**Current Routes in Sitemap:**
- `/` - Homepage (priority: 1.0, changeFrequency: daily)
- `/dashboard` - Dashboard (priority: 0.8, changeFrequency: daily)
- `/templates` - Templates (priority: 0.8, changeFrequency: weekly)
- `/tutorials` - Tutorials (priority: 0.7, changeFrequency: weekly)
- `/auth/signin` - Sign In (priority: 0.5, changeFrequency: monthly)
- `/auth/signup` - Sign Up (priority: 0.5, changeFrequency: monthly)

### 2. Robots.txt Configuration (`/app/api/robots.txt/route.ts`)
- Dynamically generates robots.txt
- Configured to:
  - Allow all crawlers to access public pages
  - Disallow access to `/api/`, `/dashboard/`, and `/admin/` directories
  - Special rules for Googlebot with more permissive access
  - References the sitemap location
  - Includes the host directive

### 3. Open Graph Configuration
- Open Graph metadata is configured in `app/layout.tsx`
- For dynamic OG image generation, upgrade to Next.js 14+ or use `@vercel/og` package

## Configuration

### Environment Variables
Make sure to set the following environment variable:
```env
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

This URL is used as the base for all sitemap and robots.txt URLs.

### URL Rewriting
The `next.config.js` has been configured with URL rewrites to serve the SEO files at their standard locations:
- `/sitemap.xml` → `/api/sitemap.xml`
- `/robots.txt` → `/api/robots.txt`

This ensures search engines can find these files at their expected locations.

## Testing the Implementation

1. **Sitemap**: Visit `/sitemap.xml` in your browser after deployment
2. **Robots.txt**: Visit `/robots.txt` in your browser
3. **Open Graph Image**: Use social media debuggers to preview:
   - Facebook: https://developers.facebook.com/tools/debug/
   - Twitter: https://cards-dev.twitter.com/validator
   - LinkedIn: https://www.linkedin.com/post-inspector/

## Future Enhancements

### Dynamic Content in Sitemap
To add dynamic content to the sitemap, modify the `/app/api/sitemap.xml/route.ts` file. For example:

```typescript
// Fetch dynamic content
const posts = await fetchBlogPosts()

// Add to sitemap XML
const dynamicUrls = posts.map(post => `
  <url>
    <loc>${baseUrl}/blog/${post.slug}</loc>
    <lastmod>${post.updatedAt.toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>
`).join('')
```

### Additional SEO Recommendations

1. **Structured Data**: Add JSON-LD structured data for better search engine understanding
2. **Meta Tags**: Ensure all pages have unique title and description tags
3. **Canonical URLs**: Add canonical tags to prevent duplicate content issues
4. **Performance**: Optimize Core Web Vitals for better SEO rankings
5. **XML Sitemap Index**: If the site grows beyond 50,000 URLs, implement sitemap index files

## Monitoring

Use these tools to monitor SEO performance:
- Google Search Console
- Bing Webmaster Tools
- Google PageSpeed Insights
- GTmetrix or similar performance monitoring

## Compliance

The current setup follows:
- Next.js 13+ App Router best practices
- Google's sitemap protocol
- Robots Exclusion Standard (robots.txt)
- Open Graph Protocol for social sharing