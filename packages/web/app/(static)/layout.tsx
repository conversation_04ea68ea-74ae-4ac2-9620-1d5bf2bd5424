import '../globals.css';
import type { Metadata } from 'next';
// import { ThemeProvider } from '@/components/providers/theme-provider';

export const metadata: Metadata = {
  title: 'OnlyRules - AI Prompt Management Platform',
  description: 'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
  keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy',
  authors: [{ name: 'OnlyRules Team' }],
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    type: 'website',
  },
};

export default function StaticLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <div className="dark">
          <div className="min-h-screen bg-background flex flex-col">
            <main className="flex-1">
              {children}
            </main>
            <footer className="border-t py-6 md:py-0">
              <div className="container flex flex-col items-center justify-between gap-4 md:h-16 md:flex-row">
                <div className="flex flex-col items-center gap-4 md:flex-row md:gap-2">
                  <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
                    Built with ❤️ for the AI coding community.
                  </p>
                </div>
                <div className="flex flex-col items-center gap-2 md:flex-row">
                  <span className="text-sm text-muted-foreground">Links:</span>
                  <a
                    href="https://toolsdk.ai/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:text-primary/80 transition-colors"
                  >
                    ToolSDK.ai
                  </a>
                </div>
              </div>
            </footer>
          </div>
        </div>
      </body>
    </html>
  );
}
