// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default function TutorialsPage() {
  const tutorials = [
    {
      title: "Getting Started with Cursor AI",
      description: "Learn the basics of setting up and using Cursor IDE with AI assistance",
      duration: "15 min",
      level: "Beginner",
    },
    {
      title: "Advanced Prompt Engineering in Cursor",
      description: "Master advanced techniques for writing effective AI prompts",
      duration: "30 min",
      level: "Advanced",
    },
    {
      title: "Windsurf IDE Introduction",
      description: "Get started with Windsurf IDE and its AI capabilities",
      duration: "18 min",
      level: "Beginner",
    },
  ];

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Tutorials & Guides</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Learn how to maximize your productivity with AI-powered IDEs and prompt engineering
        </p>
      </div>

      <div className="mt-16">
        <h2 className="text-2xl font-bold mb-8">All Tutorials</h2>
        <div className="grid md:grid-cols-2 gap-6">
          {tutorials.map((tutorial, index) => (
            <div key={index} className="border rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <h3 className="text-xl font-semibold">{tutorial.title}</h3>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                    {tutorial.level}
                  </span>
                </div>
                <p className="text-gray-600">{tutorial.description}</p>

                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span>{tutorial.duration}</span>
                </div>

                <button className="w-full bg-blue-500 text-white py-2 rounded hover:bg-blue-600">
                  Start Tutorial
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-16 text-center bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-semibold mb-4">Ready to Get Started?</h2>
        <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
          Join thousands of developers who are already using OnlyRules to enhance their AI coding experience.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
            Browse All Tutorials
          </button>
          <button className="border border-gray-300 px-6 py-2 rounded hover:bg-gray-100">
            Join Community
          </button>
        </div>
      </div>
    </div>
  );
}