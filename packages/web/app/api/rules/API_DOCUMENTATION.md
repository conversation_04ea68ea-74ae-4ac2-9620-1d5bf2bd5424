# Rules API Documentation

## Overview

The Rules API provides endpoints to fetch, download, and export public rules in various formats, including MDX (Markdown with JSX).

## Endpoints

### 1. Download Rules as MDX

**Endpoint:** `GET /api/rules/download`

Downloads public rules as MDX files with proper metadata and formatting.

#### Query Parameters

- `id` (optional): Specific rule ID to download. If not provided, downloads all public rules.
- `format` (optional): Output format. Options: `mdx` (default), `json`

#### Examples

```bash
# Download a specific rule as MDX
GET /api/rules/download?id=clg1234567890

# Download all public rules as MDX
GET /api/rules/download

# Get all public rules as JSON
GET /api/rules/download?format=json
```

#### Response

- Single rule: Returns MDX file with Content-Disposition header for download
- Multiple rules: Returns combined MDX file with all rules separated by dividers
- JSON format: Returns array of rule objects

### 2. Export Filtered Rules

**Endpoint:** `GET /api/rules/export`

Export multiple rules based on filters, with advanced search capabilities.

#### Query Parameters

- `ids` (optional): Comma-separated list of rule IDs
- `tags` (optional): Comma-separated list of tag names
- `ideType` (optional): Filter by IDE type (CURSOR, AUGMENT, WINDSURF, GENERAL, ALL)
- `search` (optional): Search term for title and description

#### Examples

```bash
# Export rules by specific IDs
GET /api/rules/export?ids=id1,id2,id3

# Export rules with specific tags
GET /api/rules/export?tags=react,typescript

# Export rules for specific IDE
GET /api/rules/export?ideType=CURSOR

# Export rules matching search term
GET /api/rules/export?search=react%20hooks

# Combine filters
GET /api/rules/export?tags=react&ideType=CURSOR&search=hooks
```

#### Response

- Returns MDX file(s) with appropriate filename based on filters
- Filename format: `rules-[search]-[ideType]-[tags]-[date].mdx`

### 3. Get Raw MDX Content

**Endpoint:** `GET /api/rules/raw`

Returns raw MDX content without forcing download, useful for programmatic access and CLI tools.

#### Query Parameters

- `id` (required): Rule ID to fetch

#### Examples

```bash
# Get raw MDX content for a rule
GET /api/rules/raw?id=clg1234567890

# Use with onlyrules CLI
npx onlyrules -f "https://yourdomain.com/api/rules/raw?id=clg1234567890"
```

#### Response

- Returns MDX content with `Content-Type: text/mdx`
- Includes Cache-Control header for performance

## MDX Format

All MDX exports include:

### Frontmatter

```yaml
---
id: "rule-id"
title: "Rule Title"
description: "Rule description"
author: "Author Name"
createdAt: "2024-01-01T00:00:00.000Z"
updatedAt: "2024-01-01T00:00:00.000Z"
ideType: "CURSOR"
visibility: "PUBLIC"
tags: ["tag1", "tag2"]
---
```

### Content Structure

```markdown
# Rule Title

> Rule description (if available)

## Metadata

- **IDE Type:** CURSOR
- **Author:** Author Name
- **Created:** 1/1/2024
- **Updated:** 1/1/2024
- **Tags:** tag1, tag2

## Rule Content

[Actual rule content here]
```

## Error Responses

All endpoints return appropriate HTTP status codes:

- `200 OK`: Successful response
- `400 Bad Request`: Missing required parameters
- `404 Not Found`: Rule not found or no rules match criteria
- `500 Internal Server Error`: Server error

Error response format:
```json
{
  "error": "Error message description"
}
```

## Usage Examples

### JavaScript/TypeScript

```typescript
// Download a specific rule
const response = await fetch('/api/rules/download?id=clg1234567890');
const mdxContent = await response.text();

// Export filtered rules
const exportUrl = new URL('/api/rules/export', window.location.origin);
exportUrl.searchParams.set('tags', 'react,hooks');
exportUrl.searchParams.set('ideType', 'CURSOR');
window.open(exportUrl.toString(), '_blank');

// Get raw MDX for processing
const rawResponse = await fetch('/api/rules/raw?id=clg1234567890');
const rawMdx = await rawResponse.text();
```

### CLI Usage with onlyrules

The raw endpoint is designed to work seamlessly with the onlyrules CLI tool:

```bash
# Use a rule directly from the platform
npx onlyrules -f "https://yourdomain.com/api/rules/raw?id=clg1234567890"

# Apply a rule to your project
npx onlyrules apply -f "https://yourdomain.com/api/rules/raw?id=clg1234567890"

# Preview a rule before applying
npx onlyrules preview -f "https://yourdomain.com/api/rules/raw?id=clg1234567890"
```

### Direct Browser Access

Simply navigate to the URLs in your browser to trigger downloads:

- `https://yourdomain.com/api/rules/download?id=clg1234567890`
- `https://yourdomain.com/api/rules/export?tags=react&ideType=CURSOR`

## Notes

- Only PUBLIC rules are accessible through these endpoints
- File names are automatically sanitized for safe downloads
- MDX files can be directly imported into MDX-compatible applications
- The raw endpoint is useful for integrating with other services or displaying content without download