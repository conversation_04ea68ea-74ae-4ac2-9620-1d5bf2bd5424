"use client";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  Badge,
  Button,
  TextField,
  Select,
  Tabs
} from "@radix-ui/themes";
import { 
  Search, 
  Download, 
  Copy, 
  Star, 
  Code, 
  Filter,
  BookOpen,
  Zap,
  Users
} from "lucide-react";
import { RuleCard } from "@/components/rule-card";
import { Rule, Tag } from "@/lib/store";
import { toast } from "sonner";

export default function TemplatesPage() {
  const [rules, setRules] = useState<Rule[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedIDE, setSelectedIDE] = useState("ALL");

  const fetchRules = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      params.set("visibility", "PUBLIC");
      if (searchQuery) params.set("search", searchQuery);
      if (selectedTags.length > 0) params.set("tags", selectedTags.join(","));
      if (selectedIDE !== "ALL") params.set("ideType", selectedIDE);

      const response = await fetch(`/api/rules?${params}`);
      const data = await response.json();
      setRules(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching rules:", error);
      toast.error("Failed to fetch templates");
      setRules([]);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, selectedTags, selectedIDE]);

  const fetchTags = async () => {
    try {
      const response = await fetch("/api/tags");
      const data = await response.json();
      setTags(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching tags:", error);
      setTags([]);
    }
  };

  useEffect(() => {
    fetchRules();
    fetchTags();
  }, [fetchRules, searchQuery, selectedTags, selectedIDE]);

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev =>
      prev.includes(tagName)
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  const featuredTemplates = Array.isArray(rules) ? rules.slice(0, 6) : [];
  const allTemplates = Array.isArray(rules) ? rules : [];

  const categories = [
    {
      name: "Code Generation",
      description: "Templates for generating code snippets and functions",
      icon: Code,
      count: allTemplates.filter(r => r.tags.some(t => t.tag.name.toLowerCase().includes("generation"))).length
    },
    {
      name: "Code Review",
      description: "Templates for automated code review and analysis",
      icon: BookOpen,
      count: allTemplates.filter(r => r.tags.some(t => t.tag.name.toLowerCase().includes("review"))).length
    },
    {
      name: "Optimization",
      description: "Templates for code optimization and performance",
      icon: Zap,
      count: allTemplates.filter(r => r.tags.some(t => t.tag.name.toLowerCase().includes("optimization"))).length
    },
    {
      name: "Documentation",
      description: "Templates for generating documentation",
      icon: Users,
      count: allTemplates.filter(r => r.tags.some(t => t.tag.name.toLowerCase().includes("documentation"))).length
    }
  ];

  return (
    <div className="container py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2 mb-4">
          <BookOpen className="h-8 w-8 text-primary" />
          <h1 className="text-4xl font-bold">Template Library</h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover and use community-created AI prompt rules to boost your coding productivity
        </p>
      </div>

      {/* Categories */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold">Browse by Category</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <Card key={category.name} className="hover:shadow-lg transition-shadow cursor-pointer">
              <div>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    <category.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="text-lg font-semibold">{category.name}</div>
                    <Badge variant="soft">{category.count} templates</Badge>
                  </div>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <TextField.Root
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select.Root value={selectedIDE} onValueChange={setSelectedIDE}>
          <Select.Trigger className="w-full md:w-48" placeholder="IDE Type" />
          <Select.Content>
            <Select.Item value="ALL">All IDEs</Select.Item>
            <Select.Item value="GENERAL">General</Select.Item>
            <Select.Item value="CURSOR">Cursor</Select.Item>
            <Select.Item value="AUGMENT">Augment Code</Select.Item>
            <Select.Item value="WINDSURF">Windsurf</Select.Item>
            <Select.Item value="CLAUDE">Claude</Select.Item>
            <Select.Item value="GITHUB_COPILOT">GitHub Copilot</Select.Item>
            <Select.Item value="GEMINI">Gemini</Select.Item>
            <Select.Item value="OPENAI_CODEX">OpenAI Codex</Select.Item>
            <Select.Item value="CLINE">Cline</Select.Item>
            <Select.Item value="JUNIE">Junie</Select.Item>
            <Select.Item value="TRAE">Trae</Select.Item>
            <Select.Item value="LINGMA">Lingma</Select.Item>
            <Select.Item value="KIRO">Kiro</Select.Item>
            <Select.Item value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</Select.Item>
          </Select.Content>
        </Select.Root>
      </div>

      {/* Tag Filters */}
      {tags.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span className="text-sm font-medium">Filter by tags:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag.id}
                variant={selectedTags.includes(tag.name) ? "solid" : "outline"}
                className="cursor-pointer"
                onClick={() => toggleTag(tag.name)}
                style={{
                  borderColor: tag.color,
                  backgroundColor: selectedTags.includes(tag.name) ? tag.color : "transparent",
                }}
              >
                {tag.name}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Content */}
      <Tabs.Root defaultValue="featured" className="space-y-6">
        <Tabs.List>
          <Tabs.Trigger value="featured">Featured</Tabs.Trigger>
          <Tabs.Trigger value="all">All Templates ({allTemplates.length})</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="featured" className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-xl font-semibold">Featured Templates</h3>
            {featuredTemplates.length === 0 ? (
              <Card className="py-12 text-center">
                <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No featured templates yet</h3>
                <p className="text-muted-foreground">
                  Check back later for curated templates from the community
                </p>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredTemplates.map((rule) => (
                  <RuleCard key={rule.id} rule={rule} />
                ))}
              </div>
            )}
          </div>
        </Tabs.Content>

        <Tabs.Content value="all" className="space-y-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading templates...</p>
            </div>
          ) : allTemplates.length === 0 ? (
            <Card className="py-12 text-center">
              <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No templates found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search criteria or check back later
              </p>
              <Button variant="outline" onClick={() => {
                setSearchQuery("");
                setSelectedTags([]);
                setSelectedIDE("ALL");
              }}>
                Clear Filters
              </Button>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {allTemplates.map((rule) => (
                <RuleCard key={rule.id} rule={rule} />
              ))}
            </div>
          )}
        </Tabs.Content>
      </Tabs.Root>
    </div>
  );
}