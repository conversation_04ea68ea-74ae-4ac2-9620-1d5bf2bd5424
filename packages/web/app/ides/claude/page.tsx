import { Metadata } from 'next';
import IDEPageTemplate from '@/components/ide-page-template';
import { Brain, MessageSquare, FileSearch, Lightbulb } from 'lucide-react';

export const metadata: Metadata = {
  title: '<PERSON> Dev Integration - Advanced AI Coding Assistant',
  description: 'Complete guide to using <PERSON> Dev with OnlyRules. Learn how to leverage Anthropic\'s <PERSON> for intelligent code generation, refactoring, and analysis.',
  keywords: '<PERSON>, <PERSON><PERSON><PERSON>, AI coding assistant, <PERSON><PERSON><PERSON>, VS Code Claude, intelligent code generation, AI refactoring, Claude integration',
  alternates: {
    canonical: '/ides/claude',
  },
  openGraph: {
    title: 'Claude Dev Integration Guide - OnlyRules',
    description: 'Master <PERSON> Dev for intelligent AI-assisted coding with OnlyRules templates and best practices.',
    images: ['/images/claude-integration-og.png'],
  },
};

const claudeData = {
  ide: {
    name: '<PERSON>',
    icon: '🤖',
    color: 'bg-orange-500',
    description: 'Anthropic\'s <PERSON> integrated into your development environment',
    website: 'https://marketplace.visualstudio.com/items?itemName=saoudrizwan.claude-dev',
    version: '1.0+',
  },
  installation: {
    steps: [
      {
        title: 'Install VS Code',
        description: '<PERSON> Dev is available as a VS Code extension. Ensure you have VS Code installed.',
        command: 'https://code.visualstudio.com/download',
      },
      {
        title: 'Install Claude Dev Extension',
        description: 'Search for "Claude Dev" in the VS Code Extensions marketplace and install it.',
        command: 'ext install saoudrizwan.claude-dev',
      },
      {
        title: 'Get API Key',
        description: 'Obtain your Claude API key from Anthropic Console.',
        command: 'https://console.anthropic.com/api-keys',
        note: 'You\'ll need an Anthropic account with API access. Free tier available with usage limits.',
      },
      {
        title: 'Configure Extension',
        description: 'Add your API key to Claude Dev settings and configure model preferences.',
        command: 'Cmd/Ctrl + , → Extensions → Claude Dev → API Key',
      },
    ],
  },
  integration: {
    steps: [
      {
        title: 'Browse OnlyRules Claude Templates',
        description: 'Find Claude-optimized prompts and patterns on OnlyRules.',
        code: 'https://onlyrules.app/templates?ide=CLAUDE',
      },
      {
        title: 'Create Claude Instructions',
        description: 'Set up a .claude/instructions.md file for project-specific AI behavior.',
        code: `mkdir .claude
touch .claude/instructions.md`,
      },
      {
        title: 'Define Project Context',
        description: 'Add comprehensive instructions for Claude to understand your project.',
        code: `# .claude/instructions.md

## Project Overview
This is a [Your Project Type] built with [Tech Stack].

## Coding Standards

### Architecture Principles
- Clean Architecture with clear separation of concerns
- Domain-Driven Design for business logic
- SOLID principles throughout
- Comprehensive error handling

### Code Style
- TypeScript with strict mode enabled
- Functional programming where appropriate
- Immutable data structures
- Pure functions preferred

### AI Assistant Guidelines
When generating code:
1. Always consider existing patterns in the codebase
2. Include comprehensive error handling
3. Add detailed TypeScript types
4. Write self-documenting code
5. Include unit tests for new functions

### Security Considerations
- Never expose sensitive data
- Validate all inputs
- Use parameterized queries
- Implement proper authentication checks`,
      },
      {
        title: 'Use Claude Dev Commands',
        description: 'Access Claude through VS Code command palette for various tasks.',
        code: 'Cmd/Ctrl + Shift + P → Claude:',
      },
    ],
  },
  features: [
    {
      title: 'Deep Reasoning',
      description: 'Advanced reasoning capabilities for complex problem-solving',
      icon: <Brain className="h-5 w-5" />,
    },
    {
      title: 'Long Context Window',
      description: 'Process entire codebases with 200k token context window',
      icon: <FileSearch className="h-5 w-5" />,
    },
    {
      title: 'Natural Conversation',
      description: 'Discuss code changes and architecture in natural language',
      icon: <MessageSquare className="h-5 w-5" />,
    },
  ],
  examples: [
    {
      title: 'Architecture Analysis',
      description: 'Analyze and improve your application architecture',
      prompt: `Analyze my application architecture and suggest improvements:
- Review the current folder structure
- Identify coupling and cohesion issues
- Suggest design pattern implementations
- Recommend refactoring strategies
- Create a migration plan for improvements`,
      result: 'Detailed architectural analysis with actionable improvement plan',
    },
    {
      title: 'Complex Refactoring',
      description: 'Refactor legacy code with modern patterns',
      prompt: `Refactor this legacy module to modern standards:
- Convert callbacks to async/await
- Extract business logic to separate layer
- Add comprehensive error handling
- Implement dependency injection
- Create unit tests for all functions
- Document the refactoring decisions`,
      result: 'Cleanly refactored code with tests and documentation',
    },
    {
      title: 'Code Review Assistant',
      description: 'Get thorough code reviews with actionable feedback',
      prompt: `Review this pull request for:
- Logic errors and edge cases
- Performance optimizations
- Security vulnerabilities
- Code style consistency
- Test coverage gaps
- Documentation completeness
Provide specific suggestions for each issue found`,
      result: 'Comprehensive code review with specific improvement suggestions',
    },
  ],
  tips: [
    'Provide Claude with comprehensive context about your project structure and conventions',
    'Use Claude\'s reasoning capabilities for architectural decisions, not just code generation',
    'Leverage the long context window by including relevant files in your prompts',
    'Ask Claude to explain its reasoning for better understanding of suggestions',
    'Use Claude for code reviews to catch issues human reviewers might miss',
    'Create project-specific instruction files that Claude can reference',
    'Combine OnlyRules templates with custom instructions for consistent results',
    'Use Claude\'s ability to understand nuance for complex refactoring tasks',
    'Ask for multiple solutions to compare different approaches',
    'Utilize Claude\'s ethical training for security and privacy considerations',
  ],
};

export default function ClaudePage() {
  return <IDEPageTemplate {...claudeData} />;
}