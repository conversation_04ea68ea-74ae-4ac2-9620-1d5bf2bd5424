"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Container,
  Flex,
  Grid,
  <PERSON>ing,
  Text,
  TextField,
  Dialog,
  Badge,
  Select,
  Tabs,
  DataList,
  Theme,
} from "@radix-ui/themes";
import { useState } from "react";

export default function TestThemePage() {
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <Container size="3" py="8">
      <Flex direction="column" gap="8">
        {/* Hero Section */}
        <Box>
          <Heading size="8" mb="4">
            Radix UI Theme v3 Test Page
          </Heading>
          <Text size="4" color="gray">
            This page demonstrates the proper implementation of Radix UI Theme v3 components
            with consistent styling and design patterns.
          </Text>
        </Box>

        {/* Cards Grid */}
        <Grid columns={{ initial: "1", md: "3" }} gap="4">
          <Card>
            <Flex direction="column" gap="3">
              <Heading size="4">Classic Card</Heading>
              <Text color="gray">
                This is a classic card with default styling from Radix UI Themes.
              </Text>
              <Button variant="soft">Learn More</Button>
            </Flex>
          </Card>

          <Card variant="surface">
            <Flex direction="column" gap="3">
              <Heading size="4">Surface Card</Heading>
              <Text color="gray">
                Surface variant provides a subtle background color.
              </Text>
              <Button variant="surface">Explore</Button>
            </Flex>
          </Card>

          <Card variant="ghost">
            <Flex direction="column" gap="3">
              <Heading size="4">Ghost Card</Heading>
              <Text color="gray">
                Ghost variant has minimal visual styling.
              </Text>
              <Button variant="outline">Discover</Button>
            </Flex>
          </Card>
        </Grid>

        {/* Form Section */}
        <Card size="3">
          <Heading size="5" mb="4">
            Form Elements
          </Heading>
          <Flex direction="column" gap="4">
            <Box>
              <Text as="label" size="2" weight="medium" htmlFor="name">
                Name
              </Text>
              <TextField.Root id="name" placeholder="Enter your name" mt="1" />
            </Box>

            <Box>
              <Text as="label" size="2" weight="medium" htmlFor="role">
                Role
              </Text>
              <Select.Root defaultValue="developer">
                <Select.Trigger id="role" placeholder="Select a role" />
                <Select.Content>
                  <Select.Item value="developer">Developer</Select.Item>
                  <Select.Item value="designer">Designer</Select.Item>
                  <Select.Item value="manager">Manager</Select.Item>
                </Select.Content>
              </Select.Root>
            </Box>

            <Flex gap="3" mt="4">
              <Button variant="solid">Submit</Button>
              <Button variant="soft" color="gray">
                Cancel
              </Button>
            </Flex>
          </Flex>
        </Card>

        {/* Tabs Section */}
        <Card>
          <Tabs.Root defaultValue="overview">
            <Tabs.List>
              <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
              <Tabs.Trigger value="details">Details</Tabs.Trigger>
              <Tabs.Trigger value="settings">Settings</Tabs.Trigger>
            </Tabs.List>

            <Box pt="3">
              <Tabs.Content value="overview">
                <Text size="2">
                  This is the overview tab content. It demonstrates how tabs work in Radix UI
                  Themes with proper spacing and typography.
                </Text>
              </Tabs.Content>

              <Tabs.Content value="details">
                <DataList.Root>
                  <DataList.Item>
                    <DataList.Label>Status</DataList.Label>
                    <DataList.Value>
                      <Badge color="green">Active</Badge>
                    </DataList.Value>
                  </DataList.Item>
                  <DataList.Item>
                    <DataList.Label>Version</DataList.Label>
                    <DataList.Value>3.0.0</DataList.Value>
                  </DataList.Item>
                  <DataList.Item>
                    <DataList.Label>Theme</DataList.Label>
                    <DataList.Value>Radix UI</DataList.Value>
                  </DataList.Item>
                </DataList.Root>
              </Tabs.Content>

              <Tabs.Content value="settings">
                <Text size="2">Settings configuration would go here.</Text>
              </Tabs.Content>
            </Box>
          </Tabs.Root>
        </Card>

        {/* Dialog Example */}
        <Flex gap="3">
          <Dialog.Root open={dialogOpen} onOpenChange={setDialogOpen}>
            <Dialog.Trigger>
              <Button>Open Dialog</Button>
            </Dialog.Trigger>

            <Dialog.Content maxWidth="450px">
              <Dialog.Title>Example Dialog</Dialog.Title>
              <Dialog.Description size="2" mb="4">
                This dialog demonstrates proper theming with Radix UI v3.
              </Dialog.Description>

              <Flex direction="column" gap="3">
                <TextField.Root placeholder="Enter some text..." />
                <Text size="2" color="gray">
                  Dialog content is properly styled and accessible.
                </Text>
              </Flex>

              <Flex gap="3" mt="4" justify="end">
                <Dialog.Close>
                  <Button variant="soft" color="gray">
                    Cancel
                  </Button>
                </Dialog.Close>
                <Button>Save Changes</Button>
              </Flex>
            </Dialog.Content>
          </Dialog.Root>
        </Flex>

        {/* Color Showcase */}
        <Box>
          <Heading size="5" mb="4">
            Color System
          </Heading>
          <Flex gap="2" wrap="wrap">
            <Badge>Default</Badge>
            <Badge color="blue">Blue</Badge>
            <Badge color="green">Green</Badge>
            <Badge color="red">Red</Badge>
            <Badge color="orange">Orange</Badge>
            <Badge color="purple">Purple</Badge>
            <Badge color="pink">Pink</Badge>
            <Badge color="yellow">Yellow</Badge>
          </Flex>
        </Box>
      </Flex>
    </Container>
  );
}