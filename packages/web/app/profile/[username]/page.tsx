import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { Avatar, Button } from "@radix-ui/themes";
import { RuleCard } from "@/components/rule-card";
import {
  Calendar,
  Code,
  Globe,
  Github,
  Twitter,
  Link as LinkIcon
} from "lucide-react";
import Link from "next/link";
import prisma from "@/lib/prisma";

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

interface PageProps {
  params: {
    username: string;
  };
}

// Generate metadata for SEO - simplified to avoid build-time database issues
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  return {
    title: `${params.username} - OnlyRules`,
    description: `View ${params.username}'s IDE rules and configurations.`,
    openGraph: {
      title: `${params.username} - OnlyRules`,
      description: `View ${params.username}'s IDE rules and configurations.`,
      type: "profile",
    },
    twitter: {
      card: "summary",
      title: `${params.username} - Only<PERSON><PERSON>`,
      description: `View ${params.username}'s IDE rules and configurations.`,
    },
  };
}

export default async function ProfilePage({ params }: PageProps) {
  // Temporary placeholder during build - this will be replaced with proper implementation
  return (
    <div className="container max-w-4xl py-8">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">User Profile</h1>
        <p className="text-muted-foreground">Username: {params.username}</p>
        <p className="text-sm text-muted-foreground mt-4">
          This page is being built. Please check back soon.
        </p>
      </div>
    </div>
  );
}