"use client";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON>, Card, Callout } from "@radix-ui/themes";
import { signIn } from "@/lib/auth-client";
import { Code, Github } from "lucide-react";

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleGitHubSignIn = async () => {
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn.social({
        provider: "github",
        callbackURL: "/dashboard",
      });

      if (result.error) {
        setError(result.error.message || "Failed to sign in with GitHub");
      }
    } catch (err) {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-muted/50 py-12 px-4">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <Link href="/" className="inline-flex items-center gap-2 mb-8">
            <Code className="h-8 w-8 text-primary" />
            <span className="font-bold text-2xl">OnlyRules</span>
          </Link>
        </div>

        <Card className="space-y-4">
          <div className="space-y-1">
            <div className="text-2xl text-center font-semibold">Welcome back</div>
            <div className="text-center text-muted-foreground">
              Sign in with your GitHub account to continue
            </div>
          </div>
          <div className="space-y-4">
            {error && (
              <Callout.Root color="red">
                <Callout.Text>{error}</Callout.Text>
              </Callout.Root>
            )}

            <Button
              onClick={handleGitHubSignIn}
              className="w-full"
              disabled={isLoading}
              size="3"
            >
              <Github className="mr-2 h-5 w-5" />
              {isLoading ? "Signing in..." : "Continue with GitHub"}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              By signing in, you agree to our terms of service and privacy policy.
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}