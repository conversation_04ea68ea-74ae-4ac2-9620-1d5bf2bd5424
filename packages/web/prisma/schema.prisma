
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  rules    Rule[]
  sessions Session[]

  emailVerified Boolean
  image         String?
  accounts      Account[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  updatedAt DateTime
  ipAddress String?
  userAgent String?

  @@map("sessions")
}

model Rule {
  id          String     @id @default(cuid())
  title       String
  description String?
  content     String
  ideType     IDEType
  visibility  Visibility @default(PRIVATE)
  shareToken  String?    @unique
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  userId      String

  user User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  tags RuleTag[]

  @@map("rules")
}

model Tag {
  id    String @id @default(cuid())
  name  String @unique
  color String @default("#3B82F6")

  rules RuleTag[]

  @@map("tags")
}

model RuleTag {
  ruleId String
  tagId  String

  rule Rule @relation(fields: [ruleId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([ruleId, tagId])
  @@map("rule_tags")
}

enum IDEType {
  CURSOR
  AUGMENT
  WINDSURF
  CLAUDE
  GITHUB_COPILOT
  GEMINI
  OPENAI_CODEX
  CLINE
  JUNIE
  TRAE
  LINGMA
  KIRO
  TENCENT_CODEBUDDY
  GENERAL
}

enum Visibility {
  PRIVATE
  PUBLIC
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}
