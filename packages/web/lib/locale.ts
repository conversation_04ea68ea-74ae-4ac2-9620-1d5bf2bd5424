import { cookies, headers } from 'next/headers'
import { defaultLocale, locales, type Locale } from './i18n'

const LOCALE_COOKIE_NAME = 'locale'

export function getLocale(): Locale {
  // Try to get locale from cookie first
  const cookieStore = cookies()
  const localeCookie = cookieStore.get(LOCALE_COOKIE_NAME)
  
  if (localeCookie && locales.includes(localeCookie.value as Locale)) {
    return localeCookie.value as Locale
  }

  // Try to detect from Accept-Language header
  const headerStore = headers()
  const acceptLanguage = headerStore.get('accept-language')
  
  if (acceptLanguage) {
    const detectedLocale = acceptLanguage
      .split(',')
      .map(lang => lang.split(';')[0].trim().toLowerCase())
      .find(lang => {
        // Direct match
        if (locales.includes(lang as Locale)) {
          return true
        }
        
        // Map language codes to our supported locales
        if (lang.startsWith('zh')) {
          if (lang.includes('hk') || lang.includes('tw')) {
            return locales.includes('zh-HK')
          }
          return locales.includes('zh-CN')
        }
        
        // Check for language without region (e.g., 'en' for 'en-US')
        const langWithoutRegion = lang.split('-')[0]
        return locales.includes(langWithoutRegion as Locale)
      })
    
    if (detectedLocale) {
      if (detectedLocale.startsWith('zh')) {
        return (detectedLocale.includes('hk') || detectedLocale.includes('tw')) ? 'zh-HK' : 'zh-CN'
      }
      
      const langWithoutRegion = detectedLocale.split('-')[0]
      if (locales.includes(langWithoutRegion as Locale)) {
        return langWithoutRegion as Locale
      }
      
      if (locales.includes(detectedLocale as Locale)) {
        return detectedLocale as Locale
      }
    }
  }

  return defaultLocale
}

export function setLocaleCookie(locale: Locale) {
  cookies().set(LOCALE_COOKIE_NAME, locale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    sameSite: 'lax',
  })
}