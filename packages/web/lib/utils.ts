import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
// Import shared utilities from monorepo
import { cn as sharedCn, formatIDEType, getIDETypeColor } from '@onlyrules/shared/utils';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Re-export shared utilities for convenience
export { formatIDEType, getIDETypeColor } from '@onlyrules/shared/utils';
