import { i18n } from '@lingui/core'

export const locales = ['en', 'zh-CN', 'zh-HK'] as const
export type Locale = typeof locales[number]

export const defaultLocale: Locale = 'en'

export const localeNames: Record<Locale, string> = {
  'en': 'English',
  'zh-CN': '简体中文',
  'zh-HK': '繁體中文'
}

export const localeNamesAbbreviated: Record<Locale, string> = {
  'en': 'EN',
  'zh-CN': '简',
  'zh-HK': '繁'
}

export async function loadCatalog(locale: Locale) {
  try {
    const { messages } = await import(`@/src/locales/${locale}/messages.js`)
    return messages
  } catch (error) {
    console.warn(`Failed to load locale ${locale}, falling back to default messages`)
    return {}
  }
}

export async function setupI18n(locale: Locale = defaultLocale) {
  const messages = await loadCatalog(locale)
  i18n.load(locale, messages)
  i18n.activate(locale)
  return i18n
}