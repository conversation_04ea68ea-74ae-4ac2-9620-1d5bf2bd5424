/**
 * Demonstration of using shared utilities from the monorepo
 * This file shows how the web package can import and use shared utilities
 */

import { 
  formatIDEType, 
  getIDETypeColor, 
  generateSlug, 
  truncateText,
  formatDate,
  formatRelativeTime,
  buildFilterQuery,
  IDE_TYPES,
  VISIBILITY_TYPES,
  DEFAULT_RULE_CONTENT,
  APP_CONFIG
} from '@onlyrules/shared';

import type { 
  IDEType, 
  VisibilityType, 
  BaseRule, 
  CreateRuleInput,
  RuleFilters 
} from '@onlyrules/shared/types';

// Example usage of shared constants
export const availableIDEs = Object.keys(IDE_TYPES) as IDEType[];
export const visibilityOptions = Object.keys(VISIBILITY_TYPES) as VisibilityType[];

// Example usage of shared utility functions
export function createRuleSlug(title: string): string {
  return generateSlug(title);
}

export function formatRuleDescription(description: string, maxLength: number = 100): string {
  return truncateText(description, maxLength);
}

export function getIDEDisplayInfo(ideType: IDEType) {
  return {
    name: formatIDEType(ideType),
    color: getIDETypeColor(ideType),
  };
}

export function formatRuleDate(date: Date | string): string {
  return formatDate(date);
}

export function formatRuleRelativeTime(date: Date | string): string {
  return formatRelativeTime(date);
}

export function buildRuleSearchQuery(filters: RuleFilters): string {
  return buildFilterQuery(filters);
}

// Example of creating a new rule with shared types and constants
export function createDefaultRule(ideType: IDEType): Partial<CreateRuleInput> {
  return {
    title: `New ${formatIDEType(ideType)} Rule`,
    description: `A new rule for ${formatIDEType(ideType)}`,
    content: DEFAULT_RULE_CONTENT,
    ideType,
    visibility: 'PRIVATE',
    tags: [],
  };
}

// Example of using shared app configuration
export function getAppInfo() {
  return {
    name: APP_CONFIG.NAME,
    description: APP_CONFIG.DESCRIPTION,
    version: APP_CONFIG.VERSION,
    githubUrl: APP_CONFIG.GITHUB_URL,
  };
}

// Example of type-safe rule processing
export function processRule(rule: BaseRule): {
  displayName: string;
  colorCode: string;
  shortDescription: string;
  formattedDate: string;
  relativeTime: string;
} {
  return {
    displayName: formatIDEType(rule.ideType),
    colorCode: getIDETypeColor(rule.ideType),
    shortDescription: rule.description ? truncateText(rule.description, 150) : 'No description',
    formattedDate: formatDate(rule.createdAt),
    relativeTime: formatRelativeTime(rule.createdAt),
  };
}

// This demonstrates how the monorepo structure enables:
// 1. Type safety across packages
// 2. Shared business logic
// 3. Consistent data formatting
// 4. Centralized constants and configuration
// 5. Reusable utility functions
