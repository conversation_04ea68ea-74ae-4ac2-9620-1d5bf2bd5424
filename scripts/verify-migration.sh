#!/bin/bash

# Radix Themes Migration Verification Script
# This script verifies that the migration from custom UI components to Radix Themes is complete

echo "🔍 Verifying Radix Themes Migration..."
echo "=================================="

# Check for any remaining imports of removed components
echo "1. Checking for imports of removed components..."

REMOVED_COMPONENTS=(
  "button" "card" "input" "select" "dialog" "badge" "tabs" "avatar"
  "checkbox" "progress" "radio-group" "separator" "skeleton" "slider"
  "switch" "textarea" "tooltip" "alert-dialog" "aspect-ratio"
  "hover-card" "popover" "scroll-area" "dropdown-menu" "toggle" "alert" "label"
)

FOUND_ISSUES=0

for component in "${REMOVED_COMPONENTS[@]}"; do
  # Check for imports of removed components (excluding the ui directory itself)
  MATCHES=$(find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | grep -v "components/ui/" | xargs grep -l "@/components/ui/$component" 2>/dev/null || true)
  
  if [ ! -z "$MATCHES" ]; then
    echo "❌ Found imports of removed component '$component' in:"
    echo "$MATCHES"
    FOUND_ISSUES=$((FOUND_ISSUES + 1))
  fi
done

if [ $FOUND_ISSUES -eq 0 ]; then
  echo "✅ No imports of removed components found"
else
  echo "❌ Found $FOUND_ISSUES issues with removed component imports"
fi

echo ""

# Check that removed component files are actually gone
echo "2. Verifying removed component files..."
MISSING_REMOVALS=0

for component in "${REMOVED_COMPONENTS[@]}"; do
  if [ -f "components/ui/$component.tsx" ]; then
    echo "❌ Component file still exists: components/ui/$component.tsx"
    MISSING_REMOVALS=$((MISSING_REMOVALS + 1))
  fi
done

if [ $MISSING_REMOVALS -eq 0 ]; then
  echo "✅ All wrapper component files successfully removed"
else
  echo "❌ $MISSING_REMOVALS component files still exist"
fi

echo ""

# Check for Radix Themes imports
echo "3. Checking for Radix Themes usage..."
RADIX_IMPORTS=$(find . -name "*.tsx" -o -name "*.ts" | grep -v node_modules | xargs grep -l "@radix-ui/themes" | wc -l)
echo "✅ Found $RADIX_IMPORTS files using @radix-ui/themes"

echo ""

# List remaining UI components (should be specialized ones)
echo "4. Remaining UI components (specialized):"
if [ -d "components/ui" ]; then
  ls -1 components/ui/*.tsx 2>/dev/null | sed 's|components/ui/||' | sed 's|\.tsx||' | while read component; do
    echo "✅ $component (specialized component - kept)"
  done
else
  echo "❌ components/ui directory not found"
fi

echo ""

# Summary
echo "5. Migration Summary:"
echo "===================="

TOTAL_ISSUES=$((FOUND_ISSUES + MISSING_REMOVALS))

if [ $TOTAL_ISSUES -eq 0 ]; then
  echo "🎉 MIGRATION VERIFICATION PASSED!"
  echo "✅ All wrapper components successfully removed"
  echo "✅ All imports updated to use @radix-ui/themes"
  echo "✅ Specialized components preserved"
  echo ""
  echo "The migration to Radix Themes is COMPLETE! 🚀"
else
  echo "❌ MIGRATION VERIFICATION FAILED!"
  echo "Found $TOTAL_ISSUES issues that need to be resolved."
  exit 1
fi
