/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // images: { unoptimized: true }, // Disabled to avoid static export behavior
  // Configure which pages should be statically generated
  generateBuildId: async () => {
    return 'onlyrules-build'
  },
  // Use standalone output to avoid static export
  output: 'standalone',
  trailingSlash: false,
  experimental: {
    serverComponentsExternalPackages: ['better-auth'],
  },
  async rewrites() {
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap.xml',
      },
      {
        source: '/robots.txt',
        destination: '/api/robots.txt',
      },
    ]
  },
};

module.exports = nextConfig;
