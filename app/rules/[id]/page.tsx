import { Metadata } from "next";

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

interface PageProps {
  params: {
    id: string;
  };
}

// Static metadata for the page
export const metadata: Metadata = {
  title: "OnlyRules - AI Prompt Rules",
  description: "AI prompt rules for developers"
};

export default async function RulePage({ params }: PageProps) {
  // Temporary placeholder during build - this will be replaced with proper implementation
  return (
    <div className="container max-w-4xl py-8">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Loading Rule...</h1>
        <p className="text-muted-foreground">Rule ID: {params.id}</p>
        <p className="text-sm text-muted-foreground mt-4">
          This page is being built. Please check back soon.
        </p>
      </div>
    </div>
  );
}

