import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ruleId = searchParams.get('id');
    const format = searchParams.get('format') || 'mdx';

    // If a specific rule ID is provided, return just that rule
    if (ruleId) {
      const rule = await prisma.rule.findFirst({
        where: {
          id: ruleId,
          visibility: 'PUBLIC',
        },
        include: {
          tags: {
            include: {
              tag: true,
            },
          },
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!rule) {
        return NextResponse.json(
          { error: 'Rule not found or not public' },
          { status: 404 }
        );
      }

      // Generate MDX content for the rule
      const mdxContent = generateMDXContent(rule);
      
      // Return as downloadable file
      return new NextResponse(mdxContent, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Content-Disposition': `attachment; filename="${sanitizeFilename(rule.title)}.mdx"`,
        },
      });
    }

    // If no specific ID, return all public rules
    const rules = await prisma.rule.findMany({
      where: {
        visibility: 'PUBLIC',
      },
      include: {
        tags: {
          include: {
            tag: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    });

    if (format === 'json') {
      // Return as JSON for programmatic access
      return NextResponse.json(rules);
    }

    // Generate combined MDX content for all rules
    const combinedMDX = rules.map(rule => generateMDXContent(rule)).join('\n\n---\n\n');
    
    return new NextResponse(combinedMDX, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Content-Disposition': 'attachment; filename="public-rules.mdx"',
      },
    });

  } catch (error) {
    console.error('Error downloading rules:', error);
    return NextResponse.json(
      { error: 'Failed to download rules' },
      { status: 500 }
    );
  }
}

function generateMDXContent(rule: any): string {
  const tags = rule.tags.map((t: any) => t.tag.name).join(', ');
  const author = rule.user?.name || 'Unknown';
  const date = new Date(rule.createdAt).toLocaleDateString();
  
  // Generate MDX frontmatter and content
  const mdx = `---
title: "${rule.title}"
description: "${rule.description || ''}"
author: "${author}"
date: "${date}"
ideType: "${rule.ideType}"
tags: [${rule.tags.map((t: any) => `"${t.tag.name}"`).join(', ')}]
---

# ${rule.title}

${rule.description ? `> ${rule.description}\n\n` : ''}

**IDE Type:** ${rule.ideType}  
**Author:** ${author}  
**Created:** ${date}  
${tags ? `**Tags:** ${tags}  ` : ''}

## Rule Content

${rule.content}
`;

  return mdx;
}

function sanitizeFilename(filename: string): string {
  // Remove or replace characters that are not safe for filenames
  return filename
    .replace(/[^a-z0-9]/gi, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .toLowerCase()
    .substring(0, 100); // Limit length
}