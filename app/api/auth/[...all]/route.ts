import { getAuthInstance } from "@/lib/auth";
import { toNextJsHandler } from "better-auth/next-js";
import { NextRequest } from "next/server";

// Get the actual auth instance
const handler = toNextJsHandler(getAuthInstance());

export async function GET(request: NextRequest): Promise<Response> {
  return handler.GET(request);
}

export async function POST(request: NextRequest): Promise<Response> {
  return handler.POST(request);
}