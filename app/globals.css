@import "@radix-ui/themes/styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* Font family configuration */
.radix-themes {
  --default-font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  font-family: var(--default-font-family);
}

@layer base {
  /* Remove Tailwind's default color variables to avoid conflicts */
  /* Light theme */
  :root {
    /* Background colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary colors */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors */
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    /* Muted colors */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    /* Accent colors */
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Border and input colors */
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Border radius */
    --radius: 0.5rem;
  }

  /* Dark theme */
  .dark {
    /* Background colors */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    /* Card colors */
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    /* Popover colors */
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary colors */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors */
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    /* Muted colors */
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    /* Accent colors */
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* Border and input colors */
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217 91% 60%;

    /* Chart colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Typography scale */
  h1 {
    @apply text-4xl font-bold tracking-tight;
  }

  h2 {
    @apply text-3xl font-semibold tracking-tight;
  }

  h3 {
    @apply text-2xl font-semibold tracking-tight;
  }

  h4 {
    @apply text-xl font-semibold tracking-tight;
  }

  h5 {
    @apply text-lg font-semibold;
  }

  h6 {
    @apply text-base font-semibold;
  }

  p {
    @apply leading-7;
  }

  /* Link styles */
  a {
    @apply transition-colors hover:text-primary;
  }

  /* Code styles */
  code {
    @apply bg-muted px-[0.3rem] py-[0.2rem] rounded text-sm font-mono;
  }

  pre code {
    @apply bg-transparent p-0;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/30;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-in {
    animation: animateIn 0.3s ease-out;
  }

  .animate-out {
    animation: animateOut 0.3s ease-in;
  }

  @keyframes animateIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes animateOut {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(10px);
    }
  }

  /* Focus utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }
}
