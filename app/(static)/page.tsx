import Link from "next/link";
import { StaticNavbar } from "@/components/layout/static-navbar";
import { But<PERSON> } from "@/components/ui/button";

// Allow dynamic rendering
export const dynamic = 'auto';

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col">
      <StaticNavbar />
      <div className="container mx-auto px-4 py-16 flex-1">
        <div className="flex flex-col items-center gap-6 text-center">
          <h1 className="text-6xl font-bold tracking-tight">
            OnlyRules
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl">
            AI Prompt Management Platform
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <Button asChild size="3" variant="solid" color="blue">
              <Link href="/demo">
                View Demo
              </Link>
            </Button>
            <Button asChild size="3" variant="soft">
              <Link href="/auth/signin">
                Get Started
              </Link>
            </Button>
            <Button asChild size="3" variant="outline">
              <Link href="/templates">
                Browse Templates
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
