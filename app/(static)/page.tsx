import Link from "next/link";
import { StaticNavbar } from "@/components/layout/static-navbar";

// Allow dynamic rendering
export const dynamic = 'auto';

export default function HomePage() {
  return (
    <>
      <StaticNavbar />
      <div className="container mx-auto px-4 py-16 flex-1 flex items-center">
        <div className="flex flex-col items-center gap-6 text-center w-full">
          <h1 className="text-6xl font-bold tracking-tight">
            OnlyRules
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl">
            AI Prompt Management Platform
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <Link
              href="/demo"
              className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              View Demo
            </Link>
            <Link
              href="/auth/signin"
              className="inline-block px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Get Started
            </Link>
            <Link
              href="/templates"
              className="inline-block px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Browse Templates
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
