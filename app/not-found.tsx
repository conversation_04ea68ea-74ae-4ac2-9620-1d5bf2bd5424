import { Box, Button, Container, Flex, Heading, Text } from '@radix-ui/themes';
import Link from 'next/link';

export default function NotFound() {
  return (
    <Container size="2">
      <Flex direction="column" align="center" justify="center" style={{ minHeight: '60vh' }}>
        <Box mb="6" style={{ textAlign: 'center' }}>
          <Heading size="9" mb="2">
            404
          </Heading>
          <Heading size="6" mb="4">
            Page Not Found
          </Heading>
          <Text size="4" color="gray" mb="6">
            The page you&apos;re looking for doesn&apos;t exist or has been moved.
          </Text>
          <Button asChild size="3">
            <Link href="/">
              Go Home
            </Link>
          </Button>
        </Box>
      </Flex>
    </Container>
  );
}
