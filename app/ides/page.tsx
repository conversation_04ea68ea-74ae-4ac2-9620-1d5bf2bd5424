// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Supported AI Code IDEs',
  description: 'Explore OnlyRules integration guides for all supported AI-powered code IDEs. Learn how to enhance your coding workflow with AI assistance.',
  alternates: {
    canonical: '/ides',
  },
};

interface IDE {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  features: string[];
}

const supportedIDEs: IDE[] = [
  {
    id: 'cursor',
    name: 'Cursor',
    description: 'The AI-first code editor built for pair programming with AI',
    icon: '🔷',
    color: 'bg-blue-500',
    features: ['AI-powered code completion', 'Natural language to code', 'Intelligent refactoring'],
  },
  {
    id: 'augment',
    name: 'Augment Code',
    description: 'Enhance your coding experience with advanced AI assistance',
    icon: '🟣',
    color: 'bg-purple-500',
    features: ['Smart code suggestions', 'Context-aware completions', 'Code optimization'],
  },
  {
    id: 'windsurf',
    name: '<PERSON>su<PERSON>',
    description: 'Ride the wave of AI-powered development with Windsurf IDE',
    icon: '🌊',
    color: 'bg-cyan-500',
    features: ['Flow-based coding', 'AI pair programming', 'Real-time collaboration'],
  },
  {
    id: 'claude',
    name: 'Claude Dev',
    description: 'Anthropic\'s Claude integrated into your development environment',
    icon: '🤖',
    color: 'bg-orange-500',
    features: ['Advanced reasoning', 'Long context windows', 'Ethical AI coding'],
  },
  {
    id: 'github-copilot',
    name: 'GitHub Copilot',
    description: 'Your AI pair programmer powered by OpenAI Codex',
    icon: '🐙',
    color: 'bg-gray-700',
    features: ['Code suggestions', 'Multi-language support', 'Test generation'],
  },
  {
    id: 'gemini',
    name: 'Gemini Code Assist',
    description: 'Google\'s multimodal AI for enhanced code development',
    icon: '✨',
    color: 'bg-indigo-500',
    features: ['Multimodal understanding', 'Code explanation', 'Bug detection'],
  },
  {
    id: 'openai-codex',
    name: 'OpenAI Codex',
    description: 'The AI system that powers GitHub Copilot and more',
    icon: '🧠',
    color: 'bg-green-600',
    features: ['Natural language to code', 'Code translation', 'Documentation generation'],
  },
  {
    id: 'cline',
    name: 'Cline',
    description: 'Autonomous AI assistant for complex coding tasks',
    icon: '🎯',
    color: 'bg-red-500',
    features: ['Autonomous coding', 'Multi-file editing', 'Project understanding'],
  },
  {
    id: 'tencent-codebuddy',
    name: 'Tencent CodeBuddy',
    description: 'Enterprise-grade AI coding assistant from Tencent Cloud',
    icon: '☁️',
    color: 'bg-blue-600',
    features: ['Enterprise features', 'Security focused', 'Cloud integration'],
  },
];

export default function IDEsPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold">Supported AI Code IDEs</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mt-4">
          OnlyRules seamlessly integrates with the most popular AI-powered code editors.
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {supportedIDEs.map((ide) => (
          <div key={ide.id} className="border rounded-lg p-6 hover:shadow-lg transition-shadow">
            <div className="mb-4">
              <span className="text-3xl">{ide.icon}</span>
            </div>
            <h3 className="text-xl font-semibold mb-2">{ide.name}</h3>
            <p className="text-gray-600 mb-4">{ide.description}</p>

            <div className="space-y-2 mb-4">
              {ide.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <div className="h-1.5 w-1.5 rounded-full bg-blue-500" />
                  <span className="text-gray-600">{feature}</span>
                </div>
              ))}
            </div>

            <Link
              href={`/ides/${ide.id}`}
              className="block w-full bg-blue-500 text-white text-center py-2 rounded hover:bg-blue-600"
            >
              View Integration Guide
            </Link>
          </div>
        ))}
      </div>

      <div className="mt-16 text-center bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-semibold mb-4">Don&apos;t see your IDE?</h2>
        <p className="text-gray-600 mb-6">
          We&apos;re constantly adding support for new AI-powered development tools.
        </p>
        <button className="border border-gray-300 px-6 py-2 rounded hover:bg-gray-100">
          Request IDE Support
        </button>
      </div>
    </div>
  );
}