import { Metadata } from 'next';
import IDEPageTemplate from '@/components/ide-page-template';
import { Code2, <PERSON><PERSON><PERSON>, <PERSON>pu, GitBranch } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Cursor AI IDE Integration - Complete Setup Guide',
  description: 'Learn how to integrate OnlyRules with Cursor AI IDE. Step-by-step installation guide, best practices, and example prompts for enhanced AI-powered coding.',
  keywords: 'Cursor AI, Cursor IDE, AI code editor, OnlyRules integration, Cursor setup, AI programming, code completion, prompt engineering, Cursor tutorial',
  alternates: {
    canonical: '/ides/cursor',
  },
  openGraph: {
    title: 'Cursor AI IDE Integration Guide - OnlyRules',
    description: 'Master Cursor AI IDE with OnlyRules integration. Enhance your AI coding experience with custom prompts and best practices.',
    images: ['/images/cursor-integration-og.png'],
  },
};

const cursorData = {
  ide: {
    name: '<PERSON>ursor',
    icon: '🔷',
    color: 'bg-blue-500',
    description: 'The AI-first code editor built for pair programming with AI',
    website: 'https://cursor.sh',
    version: '0.40+',
  },
  installation: {
    steps: [
      {
        title: 'Download Cursor',
        description: 'Visit the official Cursor website and download the installer for your operating system.',
        command: 'https://cursor.sh/download',
        note: 'Cursor is available for Windows, macOS, and Linux. Choose the appropriate version for your system.',
      },
      {
        title: 'Install Cursor',
        description: 'Run the downloaded installer and follow the on-screen instructions. The installation process is straightforward and typically takes just a few minutes.',
      },
      {
        title: 'Launch and Configure',
        description: 'Open Cursor and complete the initial setup. You\'ll be prompted to sign in or create an account to access AI features.',
        note: 'Cursor offers a free tier with limited AI requests, and paid plans for unlimited usage.',
      },
      {
        title: 'Connect AI Models',
        description: 'Configure your preferred AI models in Cursor settings. You can use GPT-4, Claude, or other supported models.',
        command: 'Cmd/Ctrl + , → AI Settings',
      },
    ],
  },
  integration: {
    steps: [
      {
        title: 'Access OnlyRules',
        description: 'Open your web browser and navigate to OnlyRules. Browse or search for Cursor-specific prompt rules.',
        code: 'https://onlyrules.app/templates?ide=CURSOR',
      },
      {
        title: 'Create a Rules File',
        description: 'In your project root, create a .cursorrules file to store your custom prompts and instructions.',
        code: 'touch .cursorrules',
      },
      {
        title: 'Copy Rules from OnlyRules',
        description: 'Find relevant rules on OnlyRules and copy them to your .cursorrules file. Cursor will automatically detect and use these rules.',
        code: `# Example .cursorrules content
# React Component Generation
When creating React components:
- Use functional components with TypeScript
- Include proper prop types
- Add JSDoc comments
- Follow naming conventions

# Code Style
- Use 2 spaces for indentation
- Prefer const over let
- Use meaningful variable names`,
      },
      {
        title: 'Test Your Rules',
        description: 'Use Cursor\'s AI features (Cmd/Ctrl+K for inline editing, Cmd/Ctrl+L for chat) to test your custom rules.',
      },
    ],
  },
  features: [
    {
      title: 'AI Chat',
      description: 'Interactive AI assistant that understands your entire codebase',
      icon: <Sparkles className="h-5 w-5" />,
    },
    {
      title: 'Inline Editing',
      description: 'AI-powered code generation and editing directly in your files',
      icon: <Code2 className="h-5 w-5" />,
    },
    {
      title: 'Codebase Awareness',
      description: 'AI that understands your project structure and dependencies',
      icon: <GitBranch className="h-5 w-5" />,
    },
  ],
  examples: [
    {
      title: 'React Component Generation',
      description: 'Generate a complete React component with TypeScript and proper styling',
      prompt: `Create a UserProfile component that:
- Accepts user prop with name, email, avatar
- Uses TypeScript interfaces
- Includes Tailwind CSS styling
- Has loading and error states
- Is fully accessible`,
      result: 'A complete, production-ready UserProfile component with all requested features',
    },
    {
      title: 'API Integration',
      description: 'Generate API integration code with error handling',
      prompt: `Create a custom hook useUserData that:
- Fetches user data from /api/users/:id
- Includes loading, error, and data states
- Uses proper TypeScript types
- Implements retry logic
- Caches results`,
      result: 'A robust custom hook with all error handling and caching logic implemented',
    },
    {
      title: 'Test Generation',
      description: 'Generate comprehensive tests for your components',
      prompt: `Write Jest tests for the UserProfile component that:
- Test all props variations
- Test loading and error states
- Test accessibility
- Use React Testing Library
- Include edge cases`,
      result: 'Complete test suite with high coverage and edge case handling',
    },
  ],
  tips: [
    'Use the .cursorrules file in your project root to maintain consistent AI behavior across your team',
    'Leverage Cursor\'s codebase indexing by letting it scan your entire project for better context',
    'Use Cmd/Ctrl+K for quick inline edits and Cmd/Ctrl+L for more complex discussions',
    'Reference specific files or functions in your prompts using @filename or @functionName',
    'Enable "Copilot++" mode for enhanced code suggestions that understand your coding style',
    'Use the "Apply" feature to automatically implement AI suggestions across multiple files',
    'Create project-specific rules that align with your team\'s coding standards',
    'Regularly update your .cursorrules file as your project evolves',
    'Use Cursor\'s diff view to review AI-generated changes before applying them',
    'Combine OnlyRules templates with your custom instructions for maximum effectiveness',
  ],
};

export default function CursorPage() {
  return <IDEPageTemplate {...cursorData} />;
}