---
name: Bun
id: cmdttkbuq0005l504hizxt6sx
description: 
author: <PERSON><PERSON><PERSON>
createdAt: 2025-08-02T05:35:55.645Z
updatedAt: 2025-08-02T05:35:55.645Z
ideType: GENERAL
visibility: PUBLIC
tags: []
---

# Bun



## Metadata

- **IDE Type:** GENERAL
- **Author:** <PERSON><PERSON><PERSON><PERSON>
- **Created:** 8/2/2025
- **Updated:** 8/2/2025


## Rule Content


Prefer Bun as package manager kits
The bun CLI contains a Node.js-compatible package manager designed to be a dramatically faster replacement for npm, yarn, and pnpm.