# Internationalization (i18n) Guide

This project uses Lingui.js for internationalization, supporting English (en), Simplified Chinese (zh-CN), and Traditional Chinese (zh-HK).

## Quick Start

### Using Translations in Components

1. **Client Components** - Use the `useLingui` hook from `@lingui/react`:

```tsx
'use client'

import { useLingui } from '@lingui/react'

export function MyComponent() {
  const { i18n } = useLingui()
  
  return (
    <div>
      <h1>{i18n._("hero.title")}</h1>
      <p>{i18n._("hero.description")}</p>
    </div>
  )
}
```

2. **Dynamic Values** - Use template literals with the i18n instance:

```tsx
'use client'

import { useLingui } from '@lingui/react'

export function MyComponent() {
  const { i18n } = useLingui()
  const userName = "John"
  
  // For simple interpolation
  return (
    <p>{i18n._("welcome.message", { name: userName })}</p>
  )
}
```

### Adding New Translations

1. Add your translation keys to the `.json` files in `src/locales/[locale]/messages.json`:

```json
{
  "my.new.key": "My translated text"
}
```

2. Run the compile command to generate the JavaScript files:

```bash
npm run lingui:compile
```

### Language Switching

The language switcher is available in the navigation bar. It:
- Saves the user's preference in a cookie
- Auto-detects language from browser settings on first visit
- Supports manual switching between languages

### File Structure

```
src/locales/
├── en/
│   ├── messages.json   # English translations
│   └── messages.js     # Compiled translations (auto-generated)
├── zh-CN/
│   ├── messages.json   # Simplified Chinese translations
│   └── messages.js     # Compiled translations (auto-generated)
└── zh-HK/
    ├── messages.json   # Traditional Chinese translations
    └── messages.js     # Compiled translations (auto-generated)
```

### Available Scripts

- `npm run lingui:extract` - Extract messages from source code
- `npm run lingui:compile` - Compile .json files to JavaScript

### Best Practices

1. **Use descriptive IDs**: Use dot notation for organization (e.g., `nav.home`, `auth.signIn`)
2. **Keep translations updated**: Run compile command after adding new translations
3. **Test all languages**: Always test your UI in all supported languages
4. **Consistent naming**: Keep translation keys consistent across all locales

### Server Components

For server components, pass the locale from a parent server component:

```tsx
// Server component wrapper
import { getLocale } from '@/lib/locale'
import { ClientComponent } from './client-component'

export function ServerComponentWrapper() {
  const locale = getLocale()
  
  return <ClientComponent locale={locale} />
}
```

### Common Translation Keys

Here are some commonly used translation keys already available:

- `app.title` - Application title
- `app.description` - Application description
- `nav.home` - Home navigation
- `nav.dashboard` - Dashboard navigation
- `nav.templates` - Templates navigation
- `nav.tutorials` - Tutorials navigation
- `hero.title` - Hero section title
- `hero.subtitle` - Hero section subtitle
- `hero.getStarted` - Get started button
- `hero.browseTemplates` - Browse templates button
- `auth.signIn` - Sign in
- `auth.signUp` - Sign up
- `auth.signOut` - Sign out

### Adding New Languages

To add a new language:

1. Update `lingui.config.js` to include the new locale
2. Update `lib/i18n.ts` to add the locale code and display name
3. Create a new directory in `src/locales/` with the locale code
4. Add a `messages.json` file with translations
5. Run `npm run lingui:compile` to generate the JavaScript files