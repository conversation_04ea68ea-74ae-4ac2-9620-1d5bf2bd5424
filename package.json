{"name": "<PERSON><PERSON><PERSON>-monore<PERSON>", "version": "0.1.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "npm --prefix packages/web run dev", "build": "npm --prefix packages/web run build", "start": "npm --prefix packages/web run start", "lint": "npm --prefix packages/web run lint", "test": "npm --prefix packages/web run test", "test:ui": "npm --prefix packages/web run test:ui", "test:run": "npm --prefix packages/web run test:run", "test:coverage": "npm --prefix packages/web run test:coverage", "postinstall": "npm --prefix packages/web run postinstall", "install:clean": "rm -rf node_modules package-lock.json packages/*/node_modules packages/*/package-lock.json && npm install", "type-check": "npm --prefix packages/web run type-check", "db:generate": "npm --prefix packages/web run db:generate", "db:push": "npm --prefix packages/web run db:push", "db:studio": "npm --prefix packages/web run db:studio", "db:migrate": "npm --prefix packages/web run db:migrate", "db:migrate:deploy": "npm --prefix packages/web run db:migrate:deploy", "db:migrate:reset": "npm --prefix packages/web run db:migrate:reset", "db:seed": "npm --prefix packages/web run db:seed", "lingui:extract": "npm --prefix packages/web run lingui:extract", "lingui:compile": "npm --prefix packages/web run lingui:compile"}, "devDependencies": {"@types/node": "24.1.0", "typescript": "^5.8.3"}}