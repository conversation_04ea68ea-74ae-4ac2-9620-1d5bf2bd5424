import { IDEPreference, IDE_METADATA, IDEType } from "./store";

/**
 * Generate an npx command for a specific IDE and rule URL
 */
export function generateIDECommand(ruleUrl: string, ideType: IDEType): string {
  const ideCommand = IDE_METADATA[ideType].command;
  return `npx onlyrules -f "${ruleUrl}" --target ${ideCommand}`;
}

/**
 * Generate the basic npx command without IDE target (current behavior)
 */
export function generateBasicCommand(ruleUrl: string): string {
  return `npx onlyrules -f "${ruleUrl}"`;
}

/**
 * Get the display name for an IDE type
 */
export function getIDEDisplayName(ideType: IDEType): string {
  return IDE_METADATA[ideType].name;
}

/**
 * Get the description for an IDE type
 */
export function getIDEDescription(ideType: IDEType): string {
  return IDE_METADATA[ideType].description;
}

/**
 * Check if an IDE type is valid
 */
export function isValidIDEType(ideType: string): ideType is IDEType {
  return ideType in IDE_METADATA;
}

/**
 * Get all available IDE types
 */
export function getAllIDETypes(): IDEType[] {
  return Object.keys(IDE_METADATA) as IDEType[];
}

/**
 * Sort IDE preferences with default first
 */
export function sortIDEPreferences(
  preferences: IDEPreference[], 
  defaultIDE?: string
): IDEPreference[] {
  return [...preferences].sort((a, b) => {
    // Default IDE comes first
    if (a.id === defaultIDE) return -1;
    if (b.id === defaultIDE) return 1;
    
    // Then sort alphabetically by name
    return a.name.localeCompare(b.name);
  });
}

/**
 * Generate commands for all preferred IDEs
 */
export function generateAllIDECommands(
  ruleUrl: string, 
  preferences: IDEPreference[]
): Array<{ ide: IDEPreference; command: string }> {
  return preferences.map(ide => ({
    ide,
    command: generateIDECommand(ruleUrl, ide.type)
  }));
}

/**
 * Get the default IDE from preferences
 */
export function getDefaultIDE(
  preferences: IDEPreference[], 
  defaultIDE?: string
): IDEPreference | undefined {
  return preferences.find(ide => ide.id === defaultIDE);
}

/**
 * Copy text to clipboard with error handling
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}

/**
 * Format IDE command for display (truncate if too long)
 */
export function formatCommandForDisplay(command: string, maxLength: number = 60): string {
  if (command.length <= maxLength) return command;
  return command.substring(0, maxLength - 3) + '...';
}
